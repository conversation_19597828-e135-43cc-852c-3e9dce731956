﻿;怪物召喚及び出産
;ARG==0のとき召喚、ARG==1以上のとき出産（ARGはNo）ARG:0=-1のとき、弱召喚
;SUMMON_POWは怪物レベル（階層）SUMMON_ITEMがアイテム番号
;SUMMON_NUMは召喚回数、基本は５ SUMMON_MONは怪物の召喚数
@SUMMON_MONSTER(ARG)
#DIM SUMMON_POW
#DIM SUMMON_NUM
#DIM SUMMON_ITEM
#DIM SUMMON_MON

DRAWLINE

SUMMON_NUM = 5

;魔王のレベルが１０、３０、５０、７０、１００になるごとに召喚回数が1回ずつプラスされる。
;魔界知识を持っていれば召喚回数が+1回
SIF CFLAG:MASTER:9 >= 10
	SUMMON_NUM += 1
SIF CFLAG:MASTER:9 >= 30
	SUMMON_NUM += 1
SIF CFLAG:MASTER:9 >= 50
	SUMMON_NUM += 1
SIF CFLAG:MASTER:9 >= 70
	SUMMON_NUM += 1
SIF CFLAG:MASTER:9 >= 100
	SUMMON_NUM += 1
SIF TALENT:MASTER:325 == 1
	SUMMON_NUM += 1
SIF ARG:0 == -1
	SUMMON_NUM /= 2
FOR LOCAL, 0 , SUMMON_NUM
	;20141001改変
	;SUMMON_POW = RAND:9
	;SUMMON_ITEM = SUMMON_POW * 10
	;SUMMON_ITEM += RAND:5
	;SUMMON_ITEM += 100
	;怪物の抽選。
	CALL RAND_MONSTER_NUMBER
	SUMMON_ITEM = RESULT
    SUMMON_POW = (SUMMON_ITEM - 100) / 10
	IF ARG == 0 || ARG == -1
		SUMMON_MON = 25
		;通常召喚。ランダムで怪物レベル(SUMMON_POW)に応じて召喚数(SUMMON_MON)が変わる。基本25
		SUMMON_MON = RAND:(SUMMON_MON - (SUMMON_POW * 3))
		SUMMON_MON += 1
		;魔王のレベルが２０、４０、６０、８０、１００になるごとに怪物の召喚数が増える。
		SIF CFLAG:MASTER:9 >= 20
			SUMMON_MON += 1
		SIF CFLAG:MASTER:9 >= 40
			SUMMON_MON += 1
		SIF CFLAG:MASTER:9 >= 60
			SUMMON_MON += 1
		SIF CFLAG:MASTER:9 >= 80
			SUMMON_MON += 1
		SIF CFLAG:MASTER:9 >= 100
			SUMMON_MON += 1
		;淫魔知识で+1匹追加
		SIF TALENT:MASTER:327 == 1
			SUMMON_MON += 1
		;菲亚魔界公主时*1.5
		
		SIF GETCHARA(35) <= 0
		   GOTO SKIPIT
		   SIF	TALENT:GETCHARA(35):1254 == 1 
			  SUMMON_MON = SUMMON_MON*150/100
	    $SKIPIT
		;银黑桃忍军1.5*
		IF GETCHARA(21) != -1
			IF TALENT:GETCHARA(21):474 == 1 && (SUMMON_ITEM == 160 || SUMMON_ITEM == 170)
				SUMMON_MON = SUMMON_MON*150/100
			ENDIF
		ENDIF
		PRINTFORML %ITEMNAME:(SUMMON_ITEM)%召唤出{SUMMON_MON}只
		ITEM:SUMMON_ITEM += SUMMON_MON
		SIF ITEM:SUMMON_ITEM > 999
			ITEM:SUMMON_ITEM = 999
	ELSE
	;出産。3つ子まで
		SUMMON_MON = RAND:3
		SUMMON_MON += 1
		;父親モンスターが決まっている場合、対象初期化
		;初期化のまま通った場合、キャラとして子を授かる
		;SIF CFLAG:ARG:112 > 0
			;SUMMON_ITEM = 0
		;確率で近衛兵
		SIF RAND:2 == 0
			SUMMON_ITEM = 191 + RAND:3
		;父親が你なら近衛兵
		IF CFLAG:ARG:111 == 0
			SUMMON_ITEM = 191 + RAND:3
		;父親が野良犬で母親がドラゴンならヘルハウンドかキマイラ
		ELSEIF CFLAG:ARG:111 == -2 && TALENT:ARG:314 == 5
			IF RAND:2 ==0
				SUMMON_ITEM = 141
				;犬はぽこぽこ産む。追加分あわせて5つ子まで
				SUMMON_MON += RAND:3
			ELSE
				SUMMON_ITEM = 151
			ENDIF
		;父親が野良犬ならゾンビハウンドかヘルハウンド
		ELSEIF CFLAG:ARG:111 == -2
			IF RAND:2 ==0
				SUMMON_ITEM = 114
			ELSE
				SUMMON_ITEM = 141
			ENDIF
			;犬はぽこぽこ産む。追加分あわせて5つ子まで
			SUMMON_MON += RAND:3
		ENDIF
		;NTR中以外なら怪物が増える。
		IF CFLAG:ARG:1 == 9
			;父親が助手か奴隷か狂王か見ず知らずの男
			IF CFLAG:ARG:102 == 7 || CFLAG:ARG:102 == 2 || CFLAG:ARG:102 == 3 || CFLAG:ARG:102 == 4
				PRINTFORML %SAVESTR:ARG%生下的孩子被拿到不知何处了。
			ELSE
				PRINTFORML %SAVESTR:ARG%生下的{SUMMON_MON}只%ITEMNAME:(SUMMON_ITEM)%全部被处理掉了。
			ENDIF
		;父親が助手か奴隷
		ELSEIF CFLAG:ARG:102 == 2 || CFLAG:ARG:102 == 3
			PRINTFORML %SAVESTR:ARG%生下的孩子启程了。
		;父親が狂王か見ず知らずの男
		ELSEIF CFLAG:ARG:102 == 7 || CFLAG:ARG:102 == 4
			PRINTFORML %SAVESTR:ARG%生下的孩子启程了。
		ELSE
			PRINTFORML %SAVESTR:ARG%生下的{SUMMON_MON}只%ITEMNAME:(SUMMON_ITEM)%增加到战斗力里了。
			ITEM:SUMMON_ITEM += SUMMON_MON
			SIF ITEM:SUMMON_ITEM > 999
				ITEM:SUMMON_ITEM = 999
		;父親が野良犬ではなく母親が吸血鬼かデュラハンで出産した子供が1匹だった時は確率で二卵性の双子
		IF CFLAG:ARG:111 != -2 && (TALENT:ARG:314 == 3 || TALENT:ARG:314 == 4) && SUMMON_MON == 1 && RAND:2 == 0
			PRINTFORM 哦呀？！生了双胞胎呢！
			;母親が吸血鬼
			IF TALENT:ARG:314 == 3 
				PRINTFORML 生了一只吸血鬼
				SIF ITEM:172 < 999
					ITEM:172 += 1
			;母親がデュラハン
			ELSEIF TALENT:ARG:314 == 4
				PRINTFORML 生了一只无头骑士
				SIF ITEM:171 < 999
					ITEM:171 += 1
			ENDIF
		ENDIF
		ENDIF
		BREAK
	ENDIF
NEXT
WAIT
RETURN 0

@PREGNANCY_MASTER(ARG)
JUMP PREGNANCY_MASTER(MASTER)

@SUMMON_MONSTER_MASTER(ARG = 0)
;母親が你で出産
PRINTL  
DRAWLINE
LOCAL:0 += RAND:3 + 1
;必ず近衛兵
LOCAL:1 = 191 + RAND:3
PRINTFORML %SAVESTR:ARG%生下了{LOCAL:0}只%ITEMNAME:(LOCAL:1)%。
ITEM:(LOCAL:1) += LOCAL:0
SIF ITEM:(LOCAL:1) > 999
	ITEM:(LOCAL:1) = 999
RETURN 0

;20160116改変
;-------------------------------------
@MONSTER_TOTAL_COUNT
#DIM MONSTER_ALL
#DIM MON_ID
;-------------------------------------
;怪物の総数を返します。
MONSTER_ALL = 0
FOR LOCAL:0, 0, 5
	FOR LOCAL:1, 0, 9
        MON_ID = 100 + LOCAL:1 * 10 + LOCAL:0
    	MONSTER_ALL += ITEM:MON_ID
    NEXT
NEXT
RETURN MONSTER_ALL

;20160116改変
;-------------------------------------
@RAND_MONSTER_NUMBER
#DIM MONSTER_ALL
#DIM MON_ID
;-------------------------------------
;怪物の番号を抽選し、返します。
;抽選された番号の怪物を既に最大数(999体)まで所持している場合は、
;再度抽選を行い、未だ最大数に達していない怪物を優先します。
;全ての種類の怪物を既に最大まで所持している場合だけは、
;(9階層 x 5種類 x 999体 = 4455)
;怪物の番号の重複を許容し、抽選された番号をそのまま返します。

;怪物の総数を取得します。
CALL MONSTER_TOTAL_COUNT
MONSTER_ALL = RESULT

;怪物番号を抽選します。
$RAND_MONSTER_NUMBER_LOOP
MON_ID = RAND:9 * 10  ;階層を抽選
MON_ID += RAND:5      ;階層における種類
MON_ID += 100         ;怪物番号の基本数

;コンフィグでオプション化設定。
;IF FLAG:?? == 0

	;抽選された番号の怪物の数が、既に最大数(99体)で、
	;かつ、怪物の総数が最大に達していない場合は、
	;再度抽選を行います。
	IF ITEM:MON_ID >= 999 && MONSTER_ALL < 44955
		GOTO RAND_MONSTER_NUMBER_LOOP
	ENDIF

;コンフィグでオプション化設定。
;ENDIF

RETURN MON_ID
