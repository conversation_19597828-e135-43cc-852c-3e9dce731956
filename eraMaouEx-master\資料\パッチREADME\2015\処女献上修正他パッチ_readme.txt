﻿処女献上イベントが「貞操帯の鍵を捨て、更にそれを奴隷が見つけている場合のみ発生する」
という狙わないとまず発生しない状態だったのでそれを修正…と同時に
ただ修正するだけだと処女プレイ時に毎晩のようにねだってきては従順を減らされて（回避に貞操帯が必須となり）それはそれで問題なので
発生に制限を設けるコンフィグも追加（暫定的にFLAG:38・CFLAG:62使用　使用FLAGに問題がある・位置的に変更したいという場合はお任せします）

同時に逆レイプでまおーさまのV経験が0から増えないために毎回処女扱いされてるのも修正
とはいえ実行判定や地の文では「TALENT:PLAYER:0」で代用できたけど
口上では処理順の関係で処女喪失済みになるからそうもいかないのが悩みどころ…
（実際高貴口上で死に分岐になってるのを確認　そもそもオトコまおーさまをTSするか処女助手を襲わせるかしか処女に対して逆レイプさせる手段無いけど）


ついでに朝フェラと夜這いをオトコだけでなくふたなりでも発生するように
ふたなりまおーさまいいよね…



ここまでで終わりのつもりだったのに売春がどうのという話から該当項目を見に行って
「そういえば現状だとシュートとかの進行度次第で発動しない罠が発動しなかった時に自動購入オンだと無駄に買っちゃうんだっけ」
と思い出してそこにも手を加えることに…
最初は「処女献上の修正ついでに細々した所も修正しよう」と思っていたはずなのにどんどん雑多に…

そしてシュート周りで上手くPT分断されないのを思い出して色々見ていたら修正したい箇所が増えてこれは…泥沼
でもこれで「シュートしたのに元の階に戻ってる（全員付いていってる）」という事態はなくなった…と思いたい


とりあえずの動作確認はしたしそこまで大規模に手は加えていないから問題はない…はずとはいえ
新たなバグが生まれていたらすまぬ…すまぬ…



※再配布・改変などはご自由に



以下変更箇所

COMF24_逆レイプ.ERB
・135行目　「IF EXP:PLAYER:0 == 0」を「IF TALENT:PLAYER:0」に
本当はここを変えるより調教側にも経験が入る仕様にしたほうがスマートなんだろうけど
eramaouではまおーさまに射精経験以外入らないようにしたいようにも見えるのでこの形に
（そもそも「逆レイプ」と「助手を犯させる」以外に調教側の射精以外の経験が上がりそうなのって「逆アナルレイプ」くらいしか思いつかないけど）

CONFIG.ERB
・処女献上の発生を制御するためのコンフィグ追加（FLAG:38使用）
追加箇所…は見れば判るよねうｎ
この辺りの構文の書き方は製作者の個性が出る部分だと思う

DUNGEON.ERB
・291～294行目　シュートでPTが分断された時のために罠判定後に「SIDEA」「SIDEB」を再定義
基本的に「CFLAG:531～532」ではなく「SIDEA～B」で現在処理中のPTMの管理をしていた結果
直接CFLAGを変更する「@PARTY_DEL」でのPT離脱に対応できなかった…というのがカルマパッチのreadme末端で触れられていたシュートバグの元凶っぽい
ので上記の対応でその部分は問題なくなったはず…
それとは別に「1/3の確率で～」系統が現状だと「SIDEAのみの2人PT時は2/3の確率でリーダーが対象になる」というのが気になったけど
そこを修正する（AとBの判定順を入れ替える）と今度はシュート等でSIDEBのみの2人PTになった時に同様のことになるのでこれは保留

DUNGEON_TRAP.ERB
・プライベート変数「TRAP_NOUSE」を追加
作動に条件のある罠（現状では一方通行とシュート）が作動したかどうかを格納するための変数
・上記変数を使用しての処理の最適化
具体的には罠が作動しなかった場合個数を増やすのを廃止し、代わりに個数を減らす処理を飛ばすように
同時に同条件下では自動購入も飛ばすように
（変更・追加行：6・12・54～56・105～110・114・303～305・927～929行目）

…動作確認に古いデータを引っ張りだしてみたら淫虫の罠がやたら数多くて何事かと思ったら
110行目の条件式のミスで淫虫の罠が消費されない状態になってたこれー！？
…他の部分の動作確認してから修正して再圧縮しておこううん…

EVENT_NEXTDAY.ERB
・処女献上が狙わなくても処女を維持してさえいれば発生し得るように（676～678・706～707・709～715・722～724・816～817・819～821・938～941行目）
何度も発生してしまうのはコンフィグで抑制可能にすることで対処（発生済みかどうかの判断にCFLAG:62を使用）
同時に貞操帯ありのパターンで潜在的に問題になりそうな部分も修正
722行目のコメントはちょっと魔が差した結果意味が分かり辛くなってしまってすまない…
・朝フェラ・夜這いの対象にふたなりも含めるように（467～468・958～959行目）
見たまんまなので省略

EVENT_TRAIN_MESSAGE_B.ERB
・920行目　「IF EXP:PLAYER:0 == 0」を「IF TALENT:PLAYER:0 && CFLAG:PLAYER:71 == 0」に
「EXP:PLAYER:0」と違い単独では初めて（再生ではない純粋な処女）かどうかを判断できないので「CFLAG:PLAYER:71」も条件に追加
937行目に関してはその先のELSEIFに「PLAYER == MASTER && TALENT:MASTER:0 == 0」もあるし助手用分岐と判断し保留
