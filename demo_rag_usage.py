#!/usr/bin/env python3
"""
演示修复后的RAG系统使用方法
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from core.rag import get_rag_system

def demo_rag_queries():
    """演示RAG查询功能"""
    print("=== ERA RAG系统使用演示 ===\n")
    
    # 加载环境变量
    load_dotenv()
    
    # 获取RAG系统
    rag = get_rag_system()
    
    # 初始化RAG系统
    era_syntax_path = os.getenv("ERA_SYNTAX_DATA_PATH", "./ERA語法資料")
    print(f"正在初始化RAG系统，数据路径: {era_syntax_path}")
    
    if not rag.initialize(era_syntax_path):
        print("❌ RAG系统初始化失败")
        return
        
    print("✅ RAG系统初始化成功\n")
    
    # 显示知识库统计
    stats = rag.get_statistics()
    print("📊 知识库统计信息:")
    print(f"   总文档数: {stats.get('total_documents', 0)}")
    print(f"   已嵌入文档数: {stats.get('embedded_documents', 0)}")
    print(f"   嵌入覆盖率: {stats.get('embedding_coverage', 0):.1%}")
    print(f"   类别分布: {stats.get('categories', {})}")
    print()
    
    # 演示不同类型的查询
    demo_queries = [
        {
            "question": "如何在ERB中定义函数？",
            "category": None,
            "description": "查询ERB函数定义方法"
        },
        {
            "question": "CSV文件的数据格式规范",
            "category": "formatting",
            "description": "查询特定类别的格式信息"
        },
        {
            "question": "变量的作用域和使用规则",
            "category": "variables", 
            "description": "查询变量相关信息"
        },
        {
            "question": "游戏逻辑实现的最佳实践",
            "category": "game_design",
            "description": "查询游戏设计相关信息"
        }
    ]
    
    for i, query_info in enumerate(demo_queries, 1):
        print(f"🔍 查询 {i}: {query_info['description']}")
        print(f"   问题: {query_info['question']}")
        if query_info['category']:
            print(f"   类别: {query_info['category']}")
        print()
        
        # 执行查询
        result = rag.query(
            question=query_info['question'],
            category=query_info['category'],
            top_k=2
        )
        
        if result.get("error"):
            print(f"   ❌ 查询失败: {result['error']}")
        else:
            sources = result.get('sources', [])
            context = result.get('context', '')
            
            print(f"   ✅ 找到 {len(sources)} 个相关文档:")
            
            for j, source in enumerate(sources, 1):
                print(f"      {j}. {source['title']} (类别: {source['category']}, 相似度: {source['score']:.3f})")
            
            if context:
                # 显示上下文的前200个字符
                preview = context[:200] + "..." if len(context) > 200 else context
                print(f"   📄 上下文预览:")
                print(f"      {preview}")
        
        print("-" * 80)
        print()

def demo_era_guidance():
    """演示ERA开发指导功能"""
    print("=== ERA开发指导演示 ===\n")
    
    # 获取RAG系统
    rag = get_rag_system()
    
    # 演示不同任务类型的指导
    task_types = [
        ("erb_script", "ERB脚本编写"),
        ("csv_data", "CSV数据文件"),
        ("system_flow", "系统流程设计"),
        ("character", "角色定义"),
        ("game_logic", "游戏逻辑实现")
    ]
    
    for task_type, description in task_types:
        print(f"📋 任务类型: {description} ({task_type})")
        
        guidance = rag.get_era_guidance(task_type, "基本语法和注意事项")
        
        # 显示指导信息的前300个字符
        preview = guidance[:300] + "..." if len(guidance) > 300 else guidance
        print(f"   指导信息: {preview}")
        print("-" * 60)
        print()

def interactive_query():
    """交互式查询演示"""
    print("=== 交互式查询演示 ===")
    print("输入你的问题，输入 'quit' 退出\n")
    
    # 获取RAG系统
    rag = get_rag_system()
    
    while True:
        try:
            question = input("🤔 请输入你的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break
                
            if not question:
                continue
                
            print(f"\n🔍 正在查询: {question}")
            
            result = rag.query(question, top_k=3)
            
            if result.get("error"):
                print(f"❌ 查询失败: {result['error']}")
            else:
                sources = result.get('sources', [])
                context = result.get('context', '')
                
                print(f"✅ 找到 {len(sources)} 个相关文档")
                
                if context:
                    print(f"\n📄 相关信息:")
                    print(context)
                    
                    print(f"\n📚 信息来源:")
                    for i, source in enumerate(sources, 1):
                        print(f"   {i}. {source['title']} (相似度: {source['score']:.3f})")
                else:
                    print("❌ 未找到相关信息")
            
            print("\n" + "="*60 + "\n")
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 查询出错: {e}")

def main():
    """主函数"""
    print("🚀 ERA RAG系统演示程序\n")
    
    demos = [
        ("1", "基本查询演示", demo_rag_queries),
        ("2", "开发指导演示", demo_era_guidance), 
        ("3", "交互式查询", interactive_query),
        ("q", "退出", None)
    ]
    
    while True:
        print("请选择演示模式:")
        for key, desc, _ in demos:
            print(f"  {key}. {desc}")
        
        choice = input("\n请输入选择 (1-3, q): ").strip().lower()
        
        if choice == 'q':
            print("👋 再见！")
            break
            
        demo_func = None
        for key, desc, func in demos:
            if choice == key:
                demo_func = func
                break
                
        if demo_func:
            print("\n" + "="*80)
            try:
                demo_func()
            except Exception as e:
                print(f"❌ 演示出错: {e}")
            print("="*80 + "\n")
        else:
            print("❌ 无效选择，请重试\n")

if __name__ == "__main__":
    main()
