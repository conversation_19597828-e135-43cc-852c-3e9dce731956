﻿﻿@TRAIN



; 训练主程序



$chara_status = @GET_CHARA_STATUS()



print "角色状态："

print $chara_status



; 显示调教选项菜单

print "请选择调教选项："

print "1. 力量训练"

print "2. 敏捷训练"

print "3. 智力训练"

print "4. 魅力训练"

print "5. 返回"



$choice = input()



switch $choice

case 1

    @TRAIN_STRENGTH()

case 2

    @TRAIN_AGILITY()

case 3

    @TRAIN_INTELLIGENCE()

case 4

    @TRAIN_CHARM()

case 5

    return

default

    print "无效选择！"

endswitch



return





@GET_CHARA_STATUS



; 获取角色状态信息



$status = {}

$status["力量"] = 10 ; 初始力量值

$status["敏捷"] = 10 ; 初始敏捷值

$status["智力"] = 10 ; 初始智力值

$status["魅力"] = 10 ; 初始魅力值



return $status





@TRAIN_STRENGTH



; 力量训练



print "开始力量训练！"

$chara_status["力量"] += 1

print "力量提升1点！"

return





@TRAIN_AGILITY



; 敏捷训练



print "开始敏捷训练！"

$chara_status["敏捷"] += 1

print "敏捷提升1点！"

return





@TRAIN_INTELLIGENCE



; 智力训练



print "开始智力训练！"

$chara_status["智力"] += 1

print "智力提升1点！"

return





@TRAIN_CHARM



; 魅力训练



print "开始魅力训练！"

$chara_status["魅力"] += 1

print "魅力提升1点！"

return