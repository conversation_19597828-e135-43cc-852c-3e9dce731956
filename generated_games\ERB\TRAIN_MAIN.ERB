; TRAIN_MAIN.ERB
; 调教/训练系统的主要逻辑

@TRAIN
; 调教主循环
$TRAIN_LOOP
; 清屏
CLEARLINE 50

; 显示角色状态
CALL SHOW_CHARACTER_STATUS

; 显示调教选项菜单
PRINTFORML [0] 结束调教
PRINTFORML [1] 进行调教
PRINTFORML [2] 休息

; 获取玩家输入
INPUT
; 处理玩家输入
SELECTCASE RESULT
    CASE 0
        ; 结束调教，返回主循环
        RETURN
    CASE 1
        ; 进入具体调教命令选择
        CALL TRAIN_SELECT_COMMAND
        GOTO TRAIN_LOOP ; 返回主菜单
    CASE 2
        ; 休息
        CALL TRAIN_REST
        GOTO TRAIN_LOOP ; 返回主菜单
    CASEELSE
        ; 无效输入
        PRINTFORML 无效的指令。
        WAIT
        GOTO TRAIN_LOOP
ENDSELECT


; --------------------------------------------------
; 辅助函数：显示角色状态
; --------------------------------------------------
@SHOW_CHARACTER_STATUS
; 显示角色名字
PRINTFORML %CALLNAME:TARGET% 的状态：
DRAWLINE
; 示例：显示一些基本能力和状态
; 假设 BASE:0 是HP, BASE:1 是MP
; 假设 ABL:0 是顺从, ABL:1 是欲求
; 假设 EXP:0 是总经验
PRINTFORML HP: %BASE:TARGET:0%/%MAXBASE:TARGET:0%  MP: %BASE:TARGET:1%/%MAXBASE:TARGET:1%
PRINTFORML 顺从: %ABL:TARGET:0%  欲求: %ABL:TARGET:1%
PRINTFORML 经验: %EXP:TARGET:0%
DRAWLINE
PRINTL


; --------------------------------------------------
; 辅助函数：选择具体调教命令
; --------------------------------------------------
@TRAIN_SELECT_COMMAND
$TRAIN_SELECT_COMMAND_LOOP
; 清屏
CLEARLINE 50
; 显示具体调教命令菜单
PRINTFORML 请选择调教指令：
DRAWLINE
PRINTFORML [0] 返回主菜单
PRINTFORML [1] 基本调教 (增加顺从)
PRINTFORML [2] 特殊调教 (增加欲求)
DRAWLINE

; 获取玩家输入
INPUT
SELECTCASE RESULT
    CASE 0
        RETURN ; 返回到@TRAIN的主循环
    CASE 1
        CALL TRAIN_COMMAND_BASIC
    CASE 2
        CALL TRAIN_COMMAND_SPECIAL
    CASEELSE
        PRINTFORML 无效的指令。
        WAIT
        GOTO TRAIN_SELECT_COMMAND_LOOP
ENDSELECT
RETURN ; 返回到@TRAIN的主循环


; --------------------------------------------------
; 辅助函数：执行基本调教
; (这些函数通常会在单独的ERB文件中定义)
; --------------------------------------------------
@TRAIN_COMMAND_BASIC
PRINTFORML 正在进行基本调教...
; 示例：增加顺从，减少HP/MP
ABL:TARGET:0 += 1 ; 顺从
BASE:TARGET:0 -= 5 ; HP
BASE:TARGET:1 -= 3 ; MP
IF BASE:TARGET:0 < 0 ; 确保HP不为负
    BASE:TARGET:0 = 0
ENDIF
IF BASE:TARGET:1 < 0 ; 确保MP不为负
    BASE:TARGET:1 = 0
ENDIF

PRINTFORML %CALLNAME:TARGET% 的顺从增加了！
PRINTFORML HP: %BASE:TARGET:0%/%MAXBASE:TARGET:0%  MP: %BASE:TARGET:1%/%MAXBASE:TARGET:1%
WAIT
RETURN

; --------------------------------------------------
; 辅助函数：执行特殊调教
; --------------------------------------------------
@TRAIN_COMMAND_SPECIAL
PRINTFORML 正在进行特殊调教...
; 示例：增加欲求，减少HP/MP
ABL:TARGET:1 += 1 ; 欲求
BASE:TARGET:0 -= 10 ; HP
BASE:TARGET:1 -= 8 ; MP
IF BASE:TARGET:0 < 0
    BASE:TARGET:0 = 0
ENDIF
IF BASE:TARGET:1 < 0
    BASE:TARGET:1 = 0
ENDIF

PRINTFORML %CALLNAME:TARGET% 的欲求增加了！
PRINTFORML HP: %BASE:TARGET:0%/%MAXBASE:TARGET:0%  MP: %BASE:TARGET:1%/%MAXBASE:TARGET:1%
WAIT
RETURN

; --------------------------------------------------
; 辅助函数：休息
; --------------------------------------------------
@TRAIN_REST
PRINTFORML 正在休息...
; 示例：恢复HP/MP
BASE:TARGET:0 = MAXBASE:TARGET:0 ; HP完全恢复
BASE:TARGET:1 = MAXBASE:TARGET:1 ; MP完全恢复
PRINTFORML %CALLNAME:TARGET% 恢复了体力与魔力。
PRINTFORML HP: %BASE:TARGET:0%/%MAXBASE:TARGET:0%  MP: %BASE:TARGET:1%/%MAXBASE:TARGET:1%
WAIT
RETURN