﻿Emuera 用语集
==================================================
Wiki用语总结，将随Emuera开发而扩展。


Outline
==================================================
启动模式
	普通模式
	解析模式
	调试模式
	
窗口·对话框
	主窗口
	主控制台
	调试窗口
	调试控制台
	设置对话框
	剪贴板对话框

函数
	命令
	函数
	事件函数
	预处理指令
	属性（预处理）
	定义（预处理）
	方法
	内置方法
	用户定义函数
	#FUNCTION(S)函数
	
行·文·式
	行
	文
	式
	数式
	文字式
	
变量
	模拟变量
	数组变量
	角色变量
	双重数组变量
	多元数组变量
	角色多元数组变量
	局部变量
	广域变量
	全局变量
	私有变量
	
启动模式
==================================================

## 普通模式

双击EmueraXXXX.exe来启动的模式。

## 解析模式

通过命令行参数来提交想要分析的ERB文件或文件夹。
Emuera会检查文件语法，并会在加载时列出文件中所有函数。
可能会需要CSV文件。

## 调试模式

使用命令行参数-Debug来启动Emuera的模式。
详细见[[调试模式]]。


窗口·对话框
==================================================

## 主窗口

通常启动时最先打开的窗口。

## 主控制台

主窗口中进行输入输出的部分。

## 调试窗口

仅在调试模式下才能打开的一个窗口。

## 调试控制台

调试窗口“控制台”标签页中进行输入输出的部分。

## 设置对话框

主窗口“帮助”菜单中“设置”菜单项打开的对话框。

## 剪贴板对话框

在主控制台按下 Ctrl + C 打开的对话框。


函数
==================================================

## 命令

包括PRINT和WAIT等。
注意区分命令与函数。

## 函数

ERB脚本中，以 @ ~ ~ 的形式定义，通过CALL命令来调用的东西。
广义的函数也包括“方法”——以 @ ~ ~ 形式定义，但直接在表达式中使用。

## 事件函数

当某一事件发生时，Emuera会尝试调用的函数。
若有复数定义则全部会被调用。

## 预处理指令

ERB文件中，文件加载时执行的指令。
包括 #开始的行 和 被[]封闭的行。
 #开始的行，可以划分为属性和定义。详细参考下面内容。
[] 封闭的行，表示特殊的语句。详细见[[exfunc]]

## 属性（预处理）

 #开始的行，用来指定函数的样式和动作的预处理指令。
指定事件函数的执行方式，包括#PRI, #LATER, #SINGLE, #ONLY
指定方法的类型，#FUNCTION, #FUNCTIONS

## 定义（预处理）

 #开始的行，定义变量的名字以及大小的预处理指令。
指定局部变量LOCAL, LOCALS的元素数目的#LOCALSIZE, #LOCALSSIZE
定义变量，包括#DIM, #DIMS
ERH文件中定义宏，#DEFINE

## 方法

式中函数，能够直接在表达式中调用的“函数”。
包括后面要提到的内置方法。
与一般编程语言中的匿名函数、内联函数无关
注意区分方法与侠义的函数。

## 内置方法

或者可以叫做“内置式中函数”、“式中命令”。
被编入了Emuera中的方法。
包括ABS(X)、GETTIME()等。
注意区分命令与内置方法。

## 用户定义函数

用户定义的函数，ERB脚本中以 @ ~ ~ 形式定义的，通过CALL命令调用的东西。
也就是脚本中的函数。

## #FUNCTION(S)函数

以 @ ~ ~ 形式定义的、具有 #FUNCTION(S) 属性的方法。
简单的说就是用户定义的两种方法。


行·文·式
==================================================

## 行

到下一个换行符为止的内容，物理行。

## 文

Emuera中的一个处理单位，逻辑行。
ERB脚本中通常一文一行，行文大致相同，故以下不做区分。

## 式

由变量、常数、方法、非代入运算符、括号等组合成的内容，即表达式。
代入运算符，如赋值语句的等号，不能在表达式中使用。

## 数式

运算结果为数值的表达式，数值表达式，算式。

## 文本式

运算结果为字符串的表达式，文本表达式。


变量
==================================================

## 模拟变量

像RAND、CHARANUM这样以变量的形式表达的东西。
实际内部操作更接近于方法。

## 数组变量

包含复数个元素的变量。
数组变量只能在VariableSize.csv中指定数组大小，在脚本中增减元素。
但局部变量可以在脚本中指定大小。

## 角色变量

记录角色状态的变量。
与一般编程语言中的Char类型变量无关。
当通过ADDCHARA和DELCHARA删减角色时，角色变量也会随之增减。
例如NO:TARGET，和数组变量具有相同的形式。
eramaker中将其作为数组变量中的一部分，本wiki将其与数组变量区别开来。

## 双重数组变量

角色变量，并且是数组变量。
例如CFLAG:TARGET:2，有两个参数（可省略）。
因为是角色变量，所以第一个参数是角色的序号。
第一维元素数量会随角色删减而变化。
第二维元素容量只能在VariableSize.csv中指定，在脚本中增减元素。

## 多元数组变量

DITEMTYPE等二元数组变量和TA等三元数组。
DA:0:1和TA:1:2:3分别有两个和三个参数。
多元数组变量只能在VariableSize.csv中指定数组大小，在脚本中增减元素。
另外多元数组变量的参数是不可以省略的。

## 角色多元数组变量

字符变量，并且是多元数组变量。
例如CDFLAG:TARGET:0:2，有三个参数（可省略）。
因为是角色变量，所以第一个参数是角色的序号。
第一维元素数量会随角色删减而变化。
第二维、第三维元素容量只能在VariableSize.csv中指定，在脚本中增减元素。

ver1819目前只有CDFLAG是这种变量。

## 局部变量

LOCAL、LOCALS和私有变量，仅在当前函数（函数名）内部有效的变量。
关于私有变量请参考下面内容。
私有变量之外的LOCAL与LOCALS并不是真正意义上的局部变量，
而是以“LOCAL@函数名”和“LOCALS@函数名”为名的公共静态变量（public static）。
在函数外可以直接使用这两个名字来访问变量。
此外，递归运算时的复数次调用函数的情况下，这两个变量也是共有通用。

## 广域变量

LOCAL、LOCALS和私有变量以外几乎所有的变量，所有函数共享的变量。
也就是一般编程语言中“全局变量”的概念。
此外，ERH文件中可以用#DIM、#DIMS来定义广域变量。
详细参考[[头文件（ERH）]]。


## 全局变量

不同存档之间可以共享的变量。全局变量是广域变量中的一种。
与通常的变量的保存、加载、初始化的时间是不一样的。
这与一般编程语言中的全局变量的概念并不相同。


## 私有变量

在函数中通过#DIM、#DIMS来定义的变量。
是局部变量的一种，仅在当前函数（函数名）内部有效。
但并不能像LOCAL一样通过@函数名来从函数外部访问。
详细参考[[用户定义变量]]
