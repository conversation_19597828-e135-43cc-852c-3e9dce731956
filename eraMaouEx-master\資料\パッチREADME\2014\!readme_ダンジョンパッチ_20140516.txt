﻿eramaou_ダンジョンパッチ_20140516

※(eramaou　ver.0.300+[erama<PERSON>]戦闘ログでのSKIP中断.zipの適用を推奨)

加筆・改変・再アップロードはご自由にどうぞ。

■前提条件
　[eramaou]戦闘ログでのSKIP中断.zipの適用。

■使い方
　全てのERBファイルをERBフォルダに上書きコピーしてください。

■仕様
・処女なのにファーストキスの相手がいた不具合を修正。
・侵略時に侵攻可能な勇者がいない場合のメッセージを追記。
・素質分類表示に[魔の刻印]を追加。
・迎撃時のメッセージの不具合を修正。
・勇者の迎撃敗北時に＜帰還中＞のフラグが立ったまま部屋に戻っていたのとご褒美orお仕置きが出来なかったのを修正。
・一方通行の罠、シュートの罠に引っかかっても迷わなかった不具合を修正。
・巡回ゴーレムのダメージがなかった不具合を修正。
・調教中による奴隷死亡時の不具合を修正。

■追加・修正箇所
　■CHAR_MAKE.ERB
　　135行目に以下を追記
　　;処女の場合ファーストキスはまだ
	SIF TALENT:A:0 == 1
		CFLAG:A:16 = -1

　■DUNGEON.ERB
　　16行目～25行目に迎撃時のメッセージを追記。「迎撃時体力が回復していると迎撃再開」を統合。
　　277行目～288行目に敗北時にNTR以外なら勇者討伐数をチェックしてご褒美の有無を追記。NTRれたなら勇者討伐数を０にするチェックを追記。

　■DUNGEON_AFTER.ERB
　　20行目280行目に改行を追記、281行目に加筆。
　　
　■DUNGEON_BATLLE2.ERB
　　24行目～28行目に街への帰還フラグ(CFLAG:507)が立っていた場合のメッセージを追記。
　　178行目～186行目に勇者が健在で、迎撃側が敗北した場合の３パターン（勇者逃亡、NTR、敗北）の分岐を追記。
　　
　■DUNGEON_ROOM.ERB
　　567～572行目にDMG変数が1以上あった場合にダメージを受けるように追記。

　■DUNGEON_TRAP.ERB
　　一方通行の罠：255行目に迷いフラグ（D:2 = 1）を追記
　　シュートの罠：829～853行目に第9階層、第8階層、その他の階層の分岐に分けて、迷いフラグ（D:2 = 1）を追記

　■EVENT_AFTERTRAIN.ERB
　　;奴隷死亡許可
　　IF FLAG:5 & 256
　　	RETURN 0
　　ENDIF
　　を以下に書き換え。
　　IF FLAG:35
　　	SIF BASE:0 < 1
　　		BASE:0 = 1
　　	RETURN 0
　　ENDIF
　　瀕死時に調教を自動終了（FLAG:35）フラグが立っていた場合、体力が0以下になっても1残るようになります。

　■INFO.ERB
　　348～350行目、[魔の刻印]を素質分類表示に出るように追加。

　■INVASION.ERB
　　195～198行目、侵攻可能な勇者がいない場合のメッセージを追記。

　■SHOP_2.ERB
　　176行目を以下に修正
　　PRINTFORML 魔王の力で%SAVESTR:RESULT%を好きな階に転送することができます。
	↓
　　PRINTFORML 魔王の力で%SAVESTR:SELECT%を好きな階に転送することができます。
　　




