﻿= Emuera扩展语法 =

== 行末注释 ==

 	A = B	;将B代入A

Emuera允许在行的末尾插入注释。
PRINT系等指令除外。

 	PRINT foobar	;一种播放器

上面脚本的输出结果为<code>foobar	;一种播放器</code>。


== 行连接 ==
 	{
 		#DIM CONST HOGE = 
 			1,2,3,4
 	}

上面的脚本等同于<code>#DIM CONST HOGE = 1,2,3,4</code>。

<b>注意：</b>
* <code>{</code>与<code>}</code>必须单独成行，不能包含注释等内容
* 第二行会被直接连接在第一行的末尾，包括行首的制表符
* 不应当用换行来分割任何变量名、函数名
* 应当避免对PRINT指令使用行连接
* 应当避免在行连接内使用注释


 	{
 		#DIM CONST HOGE =
 			1,2,3,4 ;注释
 			,5,6,7,8
 	}
 
上面的脚本等同于<code>#DIM CONST HOGE = 1,2,3,4 ;注释 ,5,6,7,8</code>。
<code>,5,6,7,8</code>会被识别为注释而忽略。


== 特殊注释行 ==

===;!;===
与<code>;#;</code>类似。

===;#;===
<code>;#;</code>开始的行仅在调试模式下会被执行，非调试模式下会被作为注释而忽略。

== 角色CSV ==

== 数值范围 ==
eramaker使用Int32来存储数值，范围为-2147483648～2147483647。

Emuera使用Int64来存储数值，范围为-9223372036854775808～9223372036854775807。

== 数组变量的连续赋值 ==
 	A:10 = 1,2,3
 	DA:0:0 = 1,2,3

上面脚本执行后，<code>A:10</code>~<code>A:12</code>被分别赋值为<code>1</code>、<code>2</code>、<code>3</code>；
<code>DA:0:0</code>~<code>DA:0:2</code>被分别赋值为<code>1</code>、<code>2</code>、<code>3</code>。

字符串类型的变量与之类似，但应当使用<code>'=</code>赋值符号。

 	STR:20 = 草莓,蜜瓜,蓝色夏威夷
 	;STR:20被赋值为「草莓,蜜瓜,蓝色夏威夷」的字符串
 	
 	STR:20 '= "草莓", "蜜瓜", "蓝色夏威夷"
 	;STR:20~STR:22被分别赋值为「草莓」「蜜瓜」「蓝色夏威夷」的字符串

== 使用FORM语法为字符串变量赋值 ==

为字符串变量赋值时可以直接使用带FORM语法的文本。
 	SAVESTR:0 = %RESULTS%
上面的脚本中，变量<code>RESULTS</code>中的值被赋予给变量<code>SAVESTR:0</code>。

若想要赋值的<code>%RESULTS%</code>自身，而非<code>RESULTS</code>的值，则需要使用转义符号。
 	SAVESTR:0 = \%RESULT\%

== 使用文本表达式为字符串变量赋值 ==

ver1813后的Emuera允许使用新的赋值运算符<code>'=</code>来为字符串变量赋值。
 	STR '= "暮雪"
 	;与「STR = 暮雪」等同
 	STR '= TSTR:0 + "非雪"
 	;与「STR = %TSTR:0%非雪」等同

== 通过文本索引来引用数组变量的元素 ==

下面变量的元素可以通过在*.csv中声明的文本索引来引用。
 	ITEM (item.csv)
 	ITEMSALES (item.csv)
 	LOSEBASE (base.csv)
 	BASE (base.csv)
 	MAXBASE (base.csv)
 	ABL (abl.csv)
 	TALENT (talent.csv)
 	EXP (exp.csv)
 	MARK (mark.csv)
 	RELATION (chara*.csv)
 	UP (palam.csv)
 	DOWN (palam.csv)
 	PALAM (palam.csv)
 	JUEL (palam.csv)
 	GOTJUEL (palam.csv)
 	STAIN (stain.csv)
 	SOURCE (source.csv)
 	EX (ex.csv)
 	NOWEX (ex.csv)
 	TEQUIP (tequip.csv)
 	EQUIP (equip.csv)
 	FLAG (flag.csv)
 	TFLAG (tflag.csv)
 	CFLAG (cflag.csv)
 	STR (strname.csv)
 	SAVESTR (savestr.csv)
 	;以下为Emuera追加的变量
 	ITEMPRICE (item.csv)
 	DOWNBASE (base.csv)
 	CUP (palam.csv)
 	CDOWN (palam.csv)
 	TCVAR (tcvar.csv)
 	TSTR (tstr.csv)
 	CSTR (cstr.csv)
 	CDFLAG (cdflag1.csv, cdflag2.csv)
 	GLOBAL (global.csv)
 	GLOBALS (globals.csv)

例如，若abl.csv中声明了<code>2, 技巧</code>，则以下四行脚本的意义相同。
 	ABL:技巧 += 1
 	ABL:2 += 1
 	ABL:"技巧" += 1
 	ABL:(ABLNAME:2) += 1

RELATION的元素也可以通过NAME、CALLNAME来引用。

如果同一变量多个元素定义了相同的文本索引，则最先定义的元素会被引用。

例如，若abl.csv同时定义了<code>2, 技巧</code>和<code>4, 技巧</code>，
则所有<code>ABL:技巧</code>会被识别为<code>ABL:2</code>。

可以使用字符串值类型的变量作为文本索引，根据情况可能需要添加<code>()</code>。
 	ABL:(RESULTS:0) = ABL:(RESULTS:0) + 1

当<code>()</code>省略时，且字符串索引与变量名称相同时，优先解释变量名。

例如，abl.csv中定义了<code>0, ローター</code>的情况下
 	@HOGE
 	#DIM ローター, 0
 	ローター = 1
 	PRINTFORML {ABL:ローター}

在这种情况下，<code>ABL:ローター</code>将解释为<code>ABL:1</code>而非<code>ABL:0</code>。
{{UNCERTAIN}}

类似的，当字符串索引为数字时，将优先解析数值。

例如abl.csv中定义了<code>0, 10</code>，则<code>ABL:10</code>将解释为“编号10的ABL”而非“编号0的ABL”。
{{UNCERTAIN}}

字符串索引同样可以在chara*.csv中使用。
例如若abl.csv中定义了<code>2, 技巧</code>，则下面两行的意义相同。
 	能力,2,2
 	能力,技巧,2

但“相性（RELATION）”不可以这样使用，
由于在读取chara*.csv阶段中Emuera尚未获得每个角色的NAME/CALLNAME与NO的对应关系。

== 带格式的文本（FORM语法）的扩展 ==

== 文本表达式中带格式的文本（FORM语法）的使用 ==

见 [[FORM语法]]

== INPUTS系指令中宏语法的使用 ==


== 参见 ==

* [[文本术语规范]]
* [[FORM语法]]


== 外部链接 ==
* {{jp}} Emuera Wiki (2015), Emueraで追加された拡張文法, [https://osdn.jp/projects/emuera/wiki/exetc 一般]

[[分类:EmueraWiki]]