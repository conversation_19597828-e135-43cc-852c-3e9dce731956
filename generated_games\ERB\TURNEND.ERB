﻿;-------------------------------------------------
; TURNEND.ERB
; 回合结束处理函数
;-------------------------------------------------
@TURNEND
; 增加日期
DAY++
; 重置时间
TIME = 0

; 清屏
CLEARLINE 50

; 显示回合结束信息
PRINTL "--------------------"
PRINTL "第%DAY%天"
PRINTL "--------------------"

; 调用全局事件回合结束处理
CALL EVENTTURNEND

; 调用训练回合结束处理 (根据features启用)
CALL TRAINEND

; 调用商店回合结束处理 (根据features启用)
CALL SHOPEND

; 调用状态显示函数，更新显示玩家和角色状态
CALL STATUS

; 确保用户可以看到回合结束信息，然后等待输入
PRINTL
PRINTL "按下任意键继续..."
INPUT

RETURN