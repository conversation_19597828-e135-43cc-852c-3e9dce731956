@EVENT_CHARA_LEAVE(ARG, CHARA)
#DIM CHARA
	STR:ARG += @"{NO:CHARA}_"
	STR:ARG += @"{CFLAG:CHARA:9}_"
	STR:ARG += @"%NICKNAME:CHARA%_"
	FOR ARG:1, 0, VARSIZE("ABL")
		SIF ABL:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{ABL:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("BASE")
		SIF BASE:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{BASE:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("BASE")
		SIF MAXBASE:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{MAXBASE:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZ<PERSON>("CFLAG")
		SIF CFLAG:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{CFLAG:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("EXP")
		SIF EXP:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{EXP:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("EQUIP")
		SIF EQUIP:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{EQUIP:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("PALAM")
		SIF JUEL:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{JUEL:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("TALENT")
		SIF TALENT:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{TALENT:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("MARK")
		SIF MARK:CHARA:(ARG:1)
			STR:ARG += @"{ARG:1},{MARK:CHARA:(ARG:1)}/"
	NEXT
	STR:ARG += "_"
	FOR ARG:1, 0, VARSIZE("CSTR")
		SIF STRLENS(CSTR:CHARA:(ARG:1))
			STR:ARG += @"{ARG:1},%CSTR:CHARA:(ARG:1)%/"
	NEXT
;前回の助手・調教対象だった場合はフラグを空に
SIF FLAG:1 == CHARA
	FLAG:1 = -1
SIF FLAG:2 == CHARA
	FLAG:2 = -1

;前回の助手・調教対象より前だった場合はフラグを減算
SIF FLAG:1 == CHARA
	FLAG:1 = 0
SIF FLAG:2 > CHARA
	FLAG:2 = 0

CALL PARTY_CHAR_DEL, CHARA

DELCHARA CHARA
	
@EVENT_CHARA_RETURN(ARG,SETLV = 0)
#DIM LV
#DIM SETLV
#DIM CHARA
#DIMS NUMS,10
;キャラ確定
SPLIT STR:(ARG), "_", LOCALS
;空白キャラを作成
ADDVOIDCHARA
CHARA = CHARANUM - 1
;キャラ番号
NO:CHARA = TOINT(LOCALS:1)
;名前の設定
SAVESTR:CHARA = %LOCALS:3%
CALLNAME:CHARA = CSVCALLNAME(NO:CHARA, 0)
NAME:CHARA = CALLNAME(NO:CHARA, 0)
;ABL
SPLIT LOCALS:4, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	ABL:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:5, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	BASE:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:6, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	MAXBASE:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:7, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	CFLAG:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:8, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	EXP:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:9, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	EQUIP:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:10, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	JUEL:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:11, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	TALENT:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:12, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	MARK:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:13, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	CSTR:CHARA:TOINT(NUMS) = %NUMS:1%
NEXT

;侵入階層?侵攻度?侵攻中設定
CFLAG:CHARA:501 = 0
CFLAG:CHARA:502 = 0
CFLAG:CHARA:1 = 0

LV = CFLAG:CHARA:9
SIF LV < SETLV 
	LV = SETLV - LV
	
REPEAT LV
	CALL ST_UP, CHARA
REND

BASE:CHARA:0 = MAXBASE:CHARA:0
BASE:CHARA:1 = MAXBASE:CHARA:1
;一応

SIF CFLAG:(CHARA):451 == 0
	CALL CHAR_BODY_GENERATE_WAPPED, CHARA

STR:ARG = 