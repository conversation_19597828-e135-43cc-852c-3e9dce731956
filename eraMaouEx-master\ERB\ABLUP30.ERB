﻿;性交中毒のLvUP処理とその可否判定
;eraIm@s_ver.0.17βdのスクリプトをベースに処理を簡略化
;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;性交中毒のLvUP
;-------------------------------------------------
;性交中毒
@ABLUP30
DRAWLINE
;PRINTL 奴隶的性交成瘾加深了。
;PRINTL 性交中毒越高，越容易在性爱中感到满足，
;PRINTL 只有频繁而激烈的交合，能安抚她那躁动的心。
;CUSTOMDRAWLINE ‥
;性交中毒はLv5が上限
;[爱慕][淫乱][献身的][接受快感][性爱狂][尻穴狂]が付いている場合はLv10まで开放
IF ABL:30 >= 5 && (TALENT:85 == 0 && TALENT:76 == 0 && TALENT:63 == 0 && TALENT:70 == 0 && TALENT:75 == 0 && TALENT:77 == 0)
	PRINTW 需要特殊素质才能继续提升
	RETURN 0
ELSEIF ABL:30 >= 10
	PRINTW 已达最高级
	RETURN 0
;性交中毒＋自慰中毒は11以上にならない
;でも、珠が沢山あるの場合はレベルアップできる。
ELSEIF ABL:30 + ABL:31 >= 10
	IF JUEL:6 < ABL:30 * ABL:30 * 1000 || JUEL:5 < ABL:30 * ABL:30 * 300
	PRINTFORMW 性交中毒({ABL:30})＋自慰中毒({ABL:31})上限为10
	PRINTFORML 至少达成%PALAMNAME:5%点数{ABL:30 * ABL:30 * 1000}点或%PALAMNAME:6%点数{ABL:30 * ABL:30 * 300}点的其中一项
	PRINTFORMW 方可提升当前性交中毒的等级
	RETURN 0
	ENDIF
ENDIF

;必要な欲情点数
A = 0
;必要な屈服点数
B = 0
;必要な性交经验回数
C = 0
;必要な异常经验回数
F = 0

CALL DECIDE_ABLUP30

;ＬＶ２から３、ＬＶ３から４、４から５に上げるときは异常经验必要（素質：[开放][淫乱][容易上瘾][疯狂]なら無視できる）
SIF F > 0
	PRINTFORML %EXPNAME:50%{F}以上(现在{EXP:50})且

;侍奉精神が性交中毒+1Lv必要
PRINTFORML %ABLNAME:16%LV{ABL:30 + 1}以上(现在LV{ABL:16})且

;性交经验が普通に必要な場合
PRINTFORM [0] - %PALAMNAME:5%点数×{JUEL:5}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 
PRINTFORML 　　　%PALAMNAME:6%点数×{JUEL:6}/{B}
PRINTFORML 　　　%EXPNAME:5%　{EXP:5}/{C}

;半分の性交经验で上げる場合
PRINTFORM [1] - %PALAMNAME:5%点数×{JUEL:5}/{A * 3} ……
PRINTV GET_ABLUP_STATE(J)
PRINTL 
PRINTFORML 　　　%PALAMNAME:6%点数×{JUEL:6}/{B * 3}
PRINTFORML 　　　%EXPNAME:5%　{EXP:5}/{C / 2}

PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 1) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF J != 0 && RESULT == 1
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:30 += 1
IF RESULT == 0
	JUEL:5 -= A
	JUEL:6 -= B
ELSEIF RESULT == 1
	JUEL:5 -= A*3
	JUEL:6 -= B*3
ENDIF

PRINTFORML %ABLNAME:30%变为LV{ABL:30}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP30
;-------------------------------------------------
ABL:30 ++

IF I == 0
	JUEL:5 -= A
	JUEL:6 -= B
ELSEIF J == 0
	JUEL:5 -= A*3
	JUEL:6 -= B*3
ENDIF


;--------------------------------------------------
;レベルアップに必要な珠計算、レベルアップ予告処理
;--------------------------------------------------
@DECIDE_ABLUP30
SIF ABL:30 >= 10
	RETURN 0
SIF ABL:30 >= 5 && (TALENT:85 == 0 && TALENT:76 == 0 && TALENT:63 == 0 && TALENT:70 == 0 && TALENT:75 == 0 && TALENT:77 == 0)
	RETURN 0
;性交中毒LV＋自慰中毒LVは10が上限
SIF ABL:30 + ABL:31 >= 20
	RETURN 0

;条件別にＯＫかダメかを記録する
A = 0
B = 0
C = 0
F = 0
I = 0
J = 0

IF ABL:30 == 0
	A = 3000
	B = 10000
	C = 10
ELSEIF ABL:30 == 1
	A = 8000
	B = 25000
	C = 25
ELSEIF ABL:30 == 2
	A = 15000
	B = 50000
	C = 40
ELSEIF ABL:30 == 3
	A = 30000
	B = 100000
	C = 80
ELSEIF ABL:30 == 4
	A = 55000
	B = 200000
	C = 200
ELSEIF ABL:30 == 5
	A = 70000
	B = 300000
	C = 400
ELSEIF ABL:30 == 6
	A = 90000
	B = 400000
	C = 800
ELSEIF ABL:30 == 7
	A = 120000
	B = 550000
	C = 1200
ELSEIF ABL:30 == 8
	A = 150000
	B = 700000
	C = 1500
ELSEIF ABL:30 == 9
	A = 200000
	B = 900000
	C = 2000
ENDIF

;戒备森严
IF TALENT:27
	IF ABL:30 == 3
		TIMES A , 1.50
		TIMES B , 1.50
		TIMES C , 1.50
	ELSEIF ABL:30 == 4
		TIMES A , 2.00
		TIMES B , 2.00
		TIMES C , 2.00
	ELSEIF ABL:30 == 5
		TIMES A , 2.50
		TIMES B , 2.50
		TIMES C , 2.50
	ELSEIF ABL:30 >= 6
		TIMES A , 3.00
		TIMES B , 3.00
		TIMES C , 3.00
	ENDIF
ENDIF

;刚强
IF TALENT:12
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
ENDIF
;克制
IF TALENT:20
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
ENDIF
;冷漠
IF TALENT:21
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
ENDIF
;保守的
IF TALENT:24
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
ENDIF

;看重贞操
IF TALENT:30
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
;看轻贞操
ELSEIF TALENT:31
	TIMES A , 0.90
	TIMES B , 0.90
	TIMES C , 0.90
ENDIF

;压抑
IF TALENT:32
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
;开放
ELSEIF TALENT:33
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES C , 0.80
ENDIF

;抵抗
IF TALENT:34
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
ENDIF

;害羞
IF TALENT:35
	TIMES A , 1.10
	TIMES B , 1.10
	TIMES C , 1.10
;不知羞耻
ELSEIF TALENT:36
	TIMES A , 0.95
	TIMES B , 0.95
	TIMES C , 0.95
ENDIF

;接受快感
IF TALENT:70
	TIMES A , 0.90
	TIMES B , 0.90
	TIMES C , 0.90
;否定快感
ELSEIF TALENT:71
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 1.20
ENDIF
;容易上瘾
IF TALENT:72
	TIMES A , 0.60
	TIMES B , 0.60
	TIMES C , 0.60
ENDIF
;容易陷落
IF TALENT:73
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
ENDIF
;淫乱
IF TALENT:76
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES C , 0.80
ENDIF
;小恶魔
IF TALENT:87
	TIMES A , 0.90
	TIMES B , 0.90
	TIMES C , 0.90
ENDIF
;疯狂
IF TALENT:123
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES C , 0.80
ENDIF
;崩坏
IF TALENT:9
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES C , 0.80
ENDIF

;最低でも1回・1個は必要
SIF A < 1
	A = 1
SIF B < 1
	B = 1
SIF C < 1
	C = 1

;ＬＶ２から３、ＬＶ３から４、４から５に上げるときは异常经验必要（素質：[开放][淫乱][容易上瘾][疯狂]なら無視できる）
IF ABL:30 >= 2 && (TALENT:33 == 0 && TALENT:72 == 0 && TALENT:76 == 0 && TALENT:123 == 0)
	F = ABL:30 - 1
	IF EXP:50 < F
		;异常经验が不足
		I |= 2
		J |= 2
	ENDIF
ENDIF

;侍奉精神が性交中毒+1Lv必要
IF ABL:16 < ABL:30 + 1
	I |= 4
	J |= 4
ENDIF

;欲情点数は足りている？
SIF JUEL:5 < A
	I |= 1
;屈服点数は足りている？
SIF JUEL:6 < B
	I |= 1
;性交经验が必要
SIF EXP:5 < C
	I |= 2

;欲情点数は足りている？
SIF JUEL:5 < A * 3
	J |= 1
;充実点数は足りている？
SIF JUEL:6 < B * 3
	J |= 1
;性交经验が必要
SIF EXP:5 < C / 2
	J |= 2

IF I == 0 || J == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF


