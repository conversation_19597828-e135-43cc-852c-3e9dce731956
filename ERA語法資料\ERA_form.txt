﻿
带格式的文本（FORM语法文本）
==================================================
PRINTFORM等指令通常会使用一种特殊的包含{}、%%等符号的带格式的文本。
我们暂且将之称为FORM语法文本。


格式化数值的{}
--------------------------------------------------
格式：{数值变量或数值表达式, 文字长度, 对齐方向（LEFT或RIGHT）}
例如：
		A = 123456
		PRINTFORML [{A}]
		PRINTFORML [{A,10}]
		PRINTFORML [{123000+456,10,LEFT}]
		PRINTFORML [{A,2}]
		; => [123456]
		; => [    123456]
		; => [123456    ]
		; => [123456]


		
格式化字符串的%%
--------------------------------------------------
格式：%文本值变量或文本表达式, 文字长度, 对齐方向（LEFT或RIGHT）%
例如：
		STR:0 = 主人公
		PRINTFORML [%STR:0%]
		PRINTFORML [%STR:0,10%]
		PRINTFORML [%"主人"+"公",10,LEFT%]
		PRINTFORML [%STR:0,2%]
		; => [主人公]
		; => [    主人公]
		; => [主人公    ]
		; => [主人公]



三元判断运算 \@\@
--------------------------------------------------
格式：\@<条件> ? <真值> # <假值>\@
例如：
		PRINTFORML \@ 1+1==2 ? 真 # 假 \@
		; => 真

	
	
转义符号 \
--------------------------------------------------
对特殊字符进行转义。
特殊的转义字符包括\t, \s, \n

		PRINTFORML \%STR:0\%
		; => %STR:0%

		
		PRINTL [\t][\s][\n]
		PRINTFORML [\t][\s][\n]
		; => [\t][\s][\n]
		; => [	][ ][
		; => ]

		
		
文本表达式
==================================================
表达式是指由变量、常数、方法、非代入运算符、括号等组合成的内容。
代入运算符，如赋值语句的等号，不能在表达式中使用。
其中运算结果为字符串的表达式被称为文本表达式。

一些文本表达式：
		LOCALS
		"文本。"
		ARGS + "。"
		"啊" * 10
		TOSTR(16325, "X")
		@"[%"主人公",10,LEFT%]"

		
		
PRINT系指令
--------------------------------------------------
与 PRINTFORM 不同， PRINTS 指令的参数要求为文本表达式。

		LOCALS = 文本
		PRINTSL LOCALS
		PRINTFORML LOCALS
		PRINTFORML %LOCALS%
		; => 文本
		; => LOCALS
		; => 文本
		PRINTSL %LOCALS%
		;错误：表达式异常

		
		
双引号 ""
--------------------------------------------------
双引号用于封闭一段字符串。
若没有双引号，则会将其作为变量名或函数名等标识符来识别。

		PRINTSL "文本"
		PRINTSL 文本
		; => 文本
		; 错误：未能识别的标识符“文本”！


AT双引号 @""
--------------------------------------------------
AT双引号用于封闭一段FORM语法的文本。

		LOCALS = 文本
		PRINTSL "一段%LOCALS%"
		; => 一段%LOCALS%
		PRINTSL @"一段%LOCALS%"
		; => 一段文本


加号 +
--------------------------------------------------
字符串的连接。

		PRINTSL "一段"+"文本"
		; => 一段文本

		
		
乘号 *
--------------------------------------------------
字符串与整数的乘法运算，用于重复字符串。

		PRINTSL "啊" * 5
		; => 啊啊啊啊啊



三元判断运算 \@\@
--------------------------------------------------
格式：\@<条件> ? <真值> # <假值>\@
三元判断既可以作为FORM语法，又可以作为文本运算。
其中<真值>和<假值>类型为FORM语法文本。

		PRINTSL \@ 1+1==2 ? 真 # 假 \@
		PRINTFORML \@ 1+1==2 ? 真 # 假 \@
		; => 真
		; => 真
		
		
		
文本的赋值
==================================================

等号 =
--------------------------------------------------
格式：<变量名> = <FORM语法文本>
直接赋值支持FORM语法。
例如：
		LOCALS = 一段文本
		;一段文本
		LOCALS = "一段文本"
		;"一段文本"
		LOCALS = 一段%"文"+"本"%
		;一段文本
		LOCALS = 一段文本 ;这不是注释
		;一段文本 ;这不是注释
		LOCALS = 
		;

		
		
引号等号 '=
--------------------------------------------------
格式：<变量名> '= <文本表达式>
例如：
		LOCALS '= "一段"+"文本"
		;一段文本

		
		
加号等号 +=
--------------------------------------------------
追加运算，格式：<变量名> += <文本表达式>
例如：
		LOCALS '= "一段"
		LOCALS += "文"+"本"
		;一段文本
		
		
