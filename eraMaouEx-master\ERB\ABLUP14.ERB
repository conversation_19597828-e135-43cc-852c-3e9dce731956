﻿;性交技术のLvUP処理とその可否判定
;eraIm@s_ver.0.17βdのスクリプトをベースに処理を簡略化
;eraIM@Sから導入しました(eramaou)


;-------------------------------------------------
;性交技术のLvUP
;-------------------------------------------------
@ABLUP14
#DIM TEMP
TEMP = ABL:14
DRAWLINE
;PRINTL 奴隶的性交技术提升了。
;PRINTL 性交技术越高，性爱时越容易满足对方。
;CUSTOMDRAWLINE ‥
;性交技术の上限は10
IF ABL:14 >= 10
	PRINTW 已达最高级
	RETURN 0
;侍奉技术＋性交技术は11以上にならない
;でも、珠が沢山あるの場合はレベルアップできる。
ELSEIF ABL:13 + ABL:14 >= 10
	SIF ABL:14 < ABL:13
		TEMP = ABL:13
	IF JUEL:7 < TEMP * TEMP * 500
	PRINTFORML 侍奉技术({ABL:13})＋性交技术({ABL:14})上限为10
	PRINTFORMW %PALAMNAME:7%点数至少达到{TEMP * TEMP * 500}点、方可突破技术等级限制
	RETURN 0
	ENDIF
ENDIF

;必要な习得点数
A = 0
;必要な性交经验
B = 0

;条件別にＯＫかダメかを記録する
;习得点数による可否（I=0:可、I&1:点数不足、I&2:経験不足）
I = 0

CALL DECIDE_ABLUP14

;Lv5までは技巧が性交技术+1Lvでないといけない
SIF ABL:14 < 5
	PRINTFORML %ABLNAME:12%LV{ABL:14 + 1}以上(现在LV{ABL:12})且

PRINTFORM [0] - %PALAMNAME:7%点数×{JUEL:7}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 
PRINTFORML       %EXPNAME:5%　{EXP:5}/{B}

PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 0) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:14 += 1
SIF RESULT == 0
	JUEL:7 -= A

PRINTFORML %ABLNAME:14%变为LV{ABL:14}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP14
;-------------------------------------------------
ABL:14 ++

IF I == 0
	JUEL:7 -= A
ENDIF



;--------------------------------------------------
;性交技术のLvUP可否判定
;--------------------------------------------------
@DECIDE_ABLUP14
#DIM TEMP
TEMP = ABL:14
SIF ABL:14 < ABL:13
	TEMP = ABL:13
SIF ABL:13 + ABL:14 >= 10 && JUEL:7 < TEMP * TEMP * 500
	RETURN 0

;判定変数を空に
A = 0
B = 0
I = 0

IF ABL:14 == 0
	A = 1
	B = 3
ELSEIF ABL:14 == 1
	A = 10
	B = 10
ELSEIF ABL:14 == 2
	A = 100
	B = 30
ELSEIF ABL:14 == 3
	A = 1500
	B = 80
ELSEIF ABL:14 == 4
	A = 4000
	B = 100
ELSEIF ABL:14 == 5
	A = 5000
	B = 130
ELSEIF ABL:14 == 6
	A = 6500
	B = 160
ELSEIF ABL:14 == 7
	A = 8000
	B = 200
ELSEIF ABL:14 == 8
	A = 10000
	B = 250
ELSEIF ABL:14 == 9
	A = 15000
	B = 300
ENDIF

SIF ABL:13 + ABL:14 >= 10
	A = TEMP * TEMP * 500

;戒备森严
IF TALENT:27
	IF ABL:14 == 3
		TIMES A , 1.50
		TIMES B , 1.50
	ELSEIF ABL:14 == 4
		TIMES A , 2.00
		TIMES B , 2.00
	ELSEIF ABL:14 == 5
		TIMES A , 2.50
		TIMES B , 2.50
	ELSEIF ABL:14 >= 6
		TIMES A , 3.00
		TIMES B , 3.00
	ENDIF
ENDIF

;坦率
IF TALENT:13
	TIMES A , 0.90
	TIMES B , 0.95
ENDIF
;冷漠
IF TALENT:21
	TIMES A , 1.10
	TIMES B , 1.05
ENDIF
;感情淡薄
IF TALENT:22
	TIMES A , 1.10
	TIMES B , 1.05
ENDIF
;好奇心
IF TALENT:23
	TIMES A , 0.95
	TIMES B , 0.95
ENDIF
;保守的
IF TALENT:24
	TIMES A , 1.20
	TIMES B , 1.10
ENDIF

;压抑
IF TALENT:32
	TIMES A , 1.10
	TIMES B , 1.05
;开放
ELSEIF TALENT:33
	TIMES A , 0.90
	TIMES B , 0.95
ENDIF

;抵抗
IF TALENT:34
	TIMES A , 1.20
	TIMES B , 1.10
ENDIF

;害羞
IF TALENT:35
	TIMES A , 1.10
	TIMES B , 1.05
;不知羞耻
ELSEIF TALENT:36
	TIMES A , 0.95
	TIMES B , 0.95
ENDIF

;快速学习
IF TALENT:50
	TIMES A , 0.80
	TIMES B , 0.80
;学习缓慢
ELSEIF TALENT:51
	TIMES A , 1.50
	TIMES B , 1.20
ENDIF

;献身的
IF TALENT:63
	TIMES A , 0.95
	TIMES B , 0.95
ENDIF
;不怕脏
IF TALENT:64
	TIMES A , 0.95
	TIMES B , 0.95
ENDIF

;性交中毒
IF ABL:30 < 3
	TIMES A , 1.00
	TIMES B , 1.00
ELSEIF ABL:30 < 6
	TIMES A , 0.95
	TIMES B , 0.95
ELSEIF ABL:30 < 8
	TIMES A , 0.90
	TIMES B , 0.90
ELSEIF ABL:30 < 10
	TIMES A , 0.85
	TIMES B , 0.85
ELSE
	TIMES A , 0.80
	TIMES B , 0.80
ENDIF

;最低でも1回・1個は必要
SIF A < 1
	A = 1
SIF B < 1
	B = 1

;习得点数は足りている？
SIF JUEL:7 < A
	I |= 1
;性交经验が必要
SIF EXP:5 < B
	I |= 2
;Lv4までは技巧が必要
SIF ABL:12 < 5 && ABL:12 < ABL:14 + 1
	I |= 4

IF I == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF
;
;
;