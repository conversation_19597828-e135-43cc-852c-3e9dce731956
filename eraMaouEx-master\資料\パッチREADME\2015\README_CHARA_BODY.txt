﻿2015/06/27

先日のアレをゲーム内に反映したものになります
ついては以下のフラグを使用しています


　FLAG:5 = ゲーム設定(ビット演算)
	12ビット(& 4096):キャラ設定の年齢表示
	13ビット(& 8192):キャラ設定の種族年齢使用可否
	14ビット(&16384):キャラ設定の種族年齢使用時の人間換算年齢表示
	15ビット(&32768):キャラ設定の身長・体重・スリーサイズ表示

　FLAG:26～27 = 各種族の年齢設定

　CFLAG:451 = 年齢
　CFLAG:452 = 種族年齢
　CFLAG:453 = 身長
　CFLAG:454 = 体重
　CFLAG:455 = B
　CFLAG:456 = W
　CFLAG:457 = H



	CHAR_MAKE.ERB
@CHAR_MAKE
表示設定している場合はキャラ追加時に各数値を決定


	CHARA_INFO_SHOW.ERB
@SHOW_CHARA_INFO(ARG)
各数値の表示


	CONFIG.ERB
@CONFIG 
年齢等のコンフィグ項目の表示および呼び出し
ちょっと大きくなりすぎたのでメインの処理はCHARA_BODY.ERBに置きました

CHARA_BODY.ERB
新規
各数値の決定やらコンフィグやら
