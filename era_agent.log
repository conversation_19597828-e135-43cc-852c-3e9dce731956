2025-07-28 15:36:40,732 - root - INFO - Configuration initialized successfully
2025-07-28 15:36:40,733 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:36:40,751 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:36:49,329 - root - INFO - Configuration initialized successfully
2025-07-28 15:36:49,330 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:36:49,349 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:36:50,778 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:36:50,779 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:50,779 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:50,780 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:36:50,780 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:50,812 - core.models - INFO - Model connection test successful: OK
</think>
<answer>
OK
</answer>...
2025-07-28 15:36:50,812 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:36:50,813 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:50,813 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:36:51,987 - data.database - INFO - Database initialized successfully
2025-07-28 15:36:51,989 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:36:55,075 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:36:55,076 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:55,076 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:55,077 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:36:55,077 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:55,085 - core.models - INFO - Model connection test successful: OK
</think>
<answer>
OK
</answer>...
2025-07-28 15:36:55,085 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:36:55,086 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:36:55,086 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:36:55,087 - data.database - INFO - Database initialized successfully
2025-07-28 15:36:55,087 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:37:39,230 - root - INFO - Configuration initialized successfully
2025-07-28 15:37:39,230 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:37:39,247 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:37:44,955 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:37:44,956 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:37:44,957 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:37:44,957 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:37:44,957 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:37:44,966 - core.models - INFO - Model connection test successful: OK
</think>
<answer>
OK
</answer>...
2025-07-28 15:37:44,967 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:37:44,967 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:37:44,968 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:37:44,968 - data.database - INFO - Database initialized successfully
2025-07-28 15:37:44,969 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:37:44,969 - root - INFO - Initialized MCP tools
2025-07-28 15:37:44,993 - core.workflow - ERROR - Workflow execution failed: '_GeneratorContextManager' object has no attribute 'get_next_version'
2025-07-28 15:37:56,906 - root - INFO - Configuration initialized successfully
2025-07-28 15:37:56,907 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:37:56,923 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:38:02,444 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:38:02,445 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:38:02,445 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:38:02,445 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:38:02,446 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:38:02,453 - core.models - INFO - Model connection test successful: OK
</think>
<answer>
OK
</answer>...
2025-07-28 15:38:02,453 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:38:02,454 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:38:02,455 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:38:02,455 - data.database - INFO - Database initialized successfully
2025-07-28 15:38:02,455 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:38:02,456 - root - INFO - Initialized MCP tools
2025-07-28 15:38:02,474 - core.workflow - ERROR - Workflow execution failed: '_GeneratorContextManager' object has no attribute 'get_next_version'
2025-07-28 15:40:25,209 - root - INFO - Configuration initialized successfully
2025-07-28 15:40:25,210 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:40:25,226 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:40:33,781 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:40:33,782 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:33,782 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:33,782 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:40:33,783 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:33,791 - core.models - INFO - Model connection test successful: OK

Okay, I can understand that message.
</think>
...
2025-07-28 15:40:33,792 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:40:33,792 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:33,793 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:40:33,794 - data.database - INFO - Database initialized successfully
2025-07-28 15:40:33,794 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:40:33,794 - root - INFO - Initialized MCP tools
2025-07-28 15:40:33,815 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:40:33,817 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:40:33,817 - core.workflow - INFO - Generating project structure
2025-07-28 15:40:33,818 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:40:33,819 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:40:33,819 - core.workflow - INFO - Implementing system flow
2025-07-28 15:40:33,820 - core.workflow - INFO - Creating game characters
2025-07-28 15:40:33,821 - core.workflow - INFO - Integrating game logic
2025-07-28 15:40:33,821 - core.workflow - INFO - Validating and finalizing game
2025-07-28 15:40:33,822 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 15:40:40,994 - root - INFO - Configuration initialized successfully
2025-07-28 15:40:40,995 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:40:41,012 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:40:46,262 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:40:46,263 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:46,263 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:46,263 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:40:46,264 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:46,272 - core.models - INFO - Model connection test successful: OK
</think>
<answer>
OK
</answer>...
2025-07-28 15:40:46,272 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:40:46,273 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:40:46,273 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:40:46,274 - data.database - INFO - Database initialized successfully
2025-07-28 15:40:46,274 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:40:46,274 - root - INFO - Initialized MCP tools
2025-07-28 15:40:46,294 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:40:46,295 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:40:46,296 - core.workflow - INFO - Generating project structure
2025-07-28 15:40:46,297 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:40:46,297 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:40:46,298 - core.workflow - INFO - Implementing system flow
2025-07-28 15:40:46,298 - core.workflow - INFO - Creating game characters
2025-07-28 15:40:46,299 - core.workflow - INFO - Integrating game logic
2025-07-28 15:40:46,300 - core.workflow - INFO - Validating and finalizing game
2025-07-28 15:40:46,300 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 15:42:21,256 - root - INFO - Configuration initialized successfully
2025-07-28 15:42:21,256 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:42:21,272 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:42:34,904 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:42:34,905 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:42:34,905 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:42:34,906 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:42:34,906 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:42:34,916 - core.models - INFO - Model connection test successful: OK

</<think>

</think>
<answer>
Hello there!
</an...
2025-07-28 15:42:34,916 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:42:34,917 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:42:34,917 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:42:34,918 - data.database - INFO - Database initialized successfully
2025-07-28 15:42:34,918 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:42:34,918 - root - INFO - Initialized MCP tools
2025-07-28 15:42:34,938 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:42:34,940 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:42:34,941 - core.workflow - INFO - Generating project structure
2025-07-28 15:42:34,941 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:42:34,942 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:42:34,942 - core.workflow - INFO - Implementing system flow
2025-07-28 15:42:34,943 - core.workflow - INFO - Creating game characters
2025-07-28 15:42:34,944 - core.workflow - INFO - Integrating game logic
2025-07-28 15:42:34,944 - core.workflow - INFO - Validating and finalizing game
2025-07-28 15:42:34,944 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 15:44:04,870 - root - INFO - Configuration initialized successfully
2025-07-28 15:44:04,870 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:44:04,887 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = openai
2025-07-28 15:44:10,303 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:44:10,304 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:44:10,304 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:44:10,305 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:44:10,305 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:44:10,312 - core.models - INFO - Model connection test successful: OK
</think>
<answer>
Hello there!
</answer>...
2025-07-28 15:44:10,312 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:44:10,313 - LiteLLM - INFO - selected model name for cost calculation: openai/hunyuan-a13b-instruct
2025-07-28 15:44:10,313 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:44:10,314 - data.database - INFO - Database initialized successfully
2025-07-28 15:44:10,314 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:44:10,314 - root - INFO - Initialized MCP tools
2025-07-28 15:44:10,333 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:44:10,334 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:44:10,335 - core.workflow - INFO - Generating project structure
2025-07-28 15:44:10,335 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:44:10,336 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:44:10,337 - core.workflow - INFO - Implementing system flow
2025-07-28 15:44:10,338 - core.workflow - INFO - Creating game characters
2025-07-28 15:44:10,338 - core.workflow - INFO - Integrating game logic
2025-07-28 15:44:10,339 - core.workflow - INFO - Validating and finalizing game
2025-07-28 15:44:10,339 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 15:44:59,524 - root - INFO - Configuration initialized successfully
2025-07-28 15:44:59,525 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:44:59,541 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:45:06,044 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:45:06,046 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:45:06,046 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:45:06,046 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:45:06,046 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:45:06,053 - core.models - INFO - Model connection test successful: OK

</<think>

</think>
<answer>
Hello there!
</an...
2025-07-28 15:45:06,053 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:45:06,054 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:45:06,055 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:45:06,055 - data.database - INFO - Database initialized successfully
2025-07-28 15:45:06,055 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:45:06,055 - root - INFO - Initialized MCP tools
2025-07-28 15:45:06,075 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:45:06,076 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:45:06,077 - core.workflow - INFO - Generating project structure
2025-07-28 15:45:06,078 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:45:06,078 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:45:06,079 - core.workflow - INFO - Implementing system flow
2025-07-28 15:45:06,080 - core.workflow - INFO - Creating game characters
2025-07-28 15:45:06,080 - core.workflow - INFO - Integrating game logic
2025-07-28 15:45:06,081 - core.workflow - INFO - Validating and finalizing game
2025-07-28 15:45:06,081 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 15:46:47,278 - root - INFO - Configuration initialized successfully
2025-07-28 15:46:47,279 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:46:47,294 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:46:52,565 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:46:52,566 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:46:52,566 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:46:52,566 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:46:52,566 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:46:52,574 - core.models - INFO - Model connection test successful: OK...
2025-07-28 15:46:52,574 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:46:52,575 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:46:52,575 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:46:52,576 - data.database - INFO - Database initialized successfully
2025-07-28 15:46:52,576 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:46:52,577 - root - INFO - Initialized MCP tools
2025-07-28 15:46:52,597 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:46:52,599 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:46:52,600 - core.workflow - INFO - Generating project structure
2025-07-28 15:46:52,601 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:46:52,601 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:46:52,602 - core.workflow - INFO - Implementing system flow
2025-07-28 15:46:52,603 - core.workflow - INFO - Creating game characters
2025-07-28 15:46:52,603 - core.workflow - INFO - Integrating game logic
2025-07-28 15:46:52,604 - core.workflow - INFO - Validating and finalizing game
2025-07-28 15:46:52,604 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 15:47:47,080 - root - INFO - Configuration initialized successfully
2025-07-28 15:47:47,080 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:47:47,097 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:47:52,353 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:47:52,354 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:47:52,354 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:47:52,355 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:47:52,355 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:47:52,362 - core.models - INFO - Model connection test successful: OK...
2025-07-28 15:47:52,362 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:47:52,363 - data.database - INFO - Database initialized successfully
2025-07-28 15:47:52,363 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:47:52,364 - root - INFO - Initialized MCP tools
2025-07-28 15:47:52,364 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:47:52,364 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:47:52,383 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:47:52,385 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:47:52,386 - core.workflow - INFO - Generating project structure
2025-07-28 15:47:52,386 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:47:52,387 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:47:52,389 - core.workflow - ERROR - ERB script generation failed: No module named 'era_ai_agent'
2025-07-28 15:48:26,530 - root - INFO - Configuration initialized successfully
2025-07-28 15:48:26,530 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:48:26,547 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:48:32,264 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:48:32,265 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:48:32,265 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:48:32,266 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:48:32,266 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:48:32,274 - core.models - INFO - Model connection test successful: OK...
2025-07-28 15:48:32,274 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:48:32,275 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:48:32,275 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:48:32,276 - data.database - INFO - Database initialized successfully
2025-07-28 15:48:32,276 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:48:32,277 - root - INFO - Initialized MCP tools
2025-07-28 15:48:32,299 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:48:32,301 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:48:32,302 - core.workflow - INFO - Generating project structure
2025-07-28 15:48:32,302 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:48:32,303 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:48:32,305 - core.workflow - ERROR - ERB script generation failed: No module named 'era_ai_agent'
2025-07-28 15:48:34,382 - root - INFO - Configuration initialized successfully
2025-07-28 15:48:34,382 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:48:34,398 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:49:19,791 - root - INFO - Configuration initialized successfully
2025-07-28 15:49:19,792 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:49:19,812 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:49:33,846 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:49:33,847 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:33,847 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:33,848 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:49:33,848 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:33,855 - core.models - INFO - Model connection test successful: It seems like your message got cut off or there mi...
2025-07-28 15:49:33,856 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:49:33,857 - data.database - INFO - Database initialized successfully
2025-07-28 15:49:33,857 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:33,858 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:49:33,859 - root - INFO - Initialized MCP tools
2025-07-28 15:49:33,859 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:49:33,884 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:49:33,886 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:49:33,887 - core.workflow - INFO - Generating project structure
2025-07-28 15:49:33,888 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:49:33,889 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:49:33,897 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:49:37,691 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:49:37,692 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:37,693 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:37,693 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:49:37,694 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:37,701 - core.models - INFO - Model connection test successful: OK...
2025-07-28 15:49:37,701 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:49:37,702 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:49:37,703 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:49:37,704 - data.database - INFO - Database initialized successfully
2025-07-28 15:49:37,704 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:49:37,704 - root - INFO - Initialized MCP tools
2025-07-28 15:49:37,732 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:49:37,734 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:49:37,736 - core.workflow - INFO - Generating project structure
2025-07-28 15:49:37,737 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:49:37,738 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:49:37,739 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:53:38,151 - root - INFO - Configuration initialized successfully
2025-07-28 15:53:38,151 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 15:53:38,167 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 15:53:50,523 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 15:53:50,524 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:53:50,525 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:53:50,525 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:53:50,525 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:53:50,533 - core.models - INFO - Model connection test successful: OK...
2025-07-28 15:53:50,534 - root - INFO - LM Studio models initialized successfully
2025-07-28 15:53:50,535 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 15:53:50,535 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 15:53:50,536 - data.database - INFO - Database initialized successfully
2025-07-28 15:53:50,536 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 15:53:50,536 - root - INFO - Initialized MCP tools
2025-07-28 15:53:50,556 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 15:53:50,557 - core.workflow - INFO - Analyzing game requirements
2025-07-28 15:53:50,558 - core.workflow - INFO - Generating project structure
2025-07-28 15:53:50,559 - core.workflow - INFO - Creating CSV data files
2025-07-28 15:53:50,559 - core.workflow - INFO - Generating ERB scripts
2025-07-28 15:53:50,561 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:00:48,993 - root - INFO - Configuration initialized successfully
2025-07-28 16:00:48,994 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 16:00:49,010 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:01:01,582 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:01:01,583 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:01:01,583 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:01:01,584 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 16:01:01,584 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:01:01,591 - core.models - INFO - Model connection test successful: Hello there!...
2025-07-28 16:01:01,591 - root - INFO - LM Studio models initialized successfully
2025-07-28 16:01:01,592 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:01:01,593 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 16:01:01,593 - data.database - INFO - Database initialized successfully
2025-07-28 16:01:01,593 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:01:01,593 - root - INFO - Initialized MCP tools
2025-07-28 16:01:01,614 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:01:01,616 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:01:01,617 - core.workflow - INFO - Generating project structure
2025-07-28 16:01:01,617 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:01:01,618 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:01:01,620 - core.workflow - INFO - Starting game generation for: 騎士與玩偶
2025-07-28 16:01:01,621 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:02:25,171 - root - INFO - Configuration initialized successfully
2025-07-28 16:02:25,171 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 16:02:25,191 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:06:05,760 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:06:05,761 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:06:05,762 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:06:05,762 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 16:06:05,762 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:06:05,769 - core.models - INFO - Model connection test successful: Hello there!...
2025-07-28 16:06:05,770 - root - INFO - LM Studio models initialized successfully
2025-07-28 16:06:05,771 - LiteLLM - INFO - selected model name for cost calculation: lm_studio/hunyuan-a13b-instruct
2025-07-28 16:06:05,771 - LiteLLM - INFO - selected model name for cost calculation: hunyuan-a13b-instruct
2025-07-28 16:06:05,771 - data.database - INFO - Database initialized successfully
2025-07-28 16:06:05,772 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:06:05,772 - root - INFO - Initialized MCP tools
2025-07-28 16:06:05,794 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:06:05,796 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:06:05,797 - core.workflow - INFO - Generating project structure
2025-07-28 16:06:05,797 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:06:05,798 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:06:05,799 - core.workflow - INFO - Starting game generation for: 騎士與玩偶
2025-07-28 16:06:05,800 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:06:53,930 - root - INFO - Configuration initialized successfully
2025-07-28 16:06:53,931 - core.models - INFO - Configured LM Studio client with base URL: http://localhost:1234/v1
2025-07-28 16:06:53,952 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:07:57,002 - core.models - ERROR - Sync chat completion failed: litellm.BadRequestError: Lm_studioException - Error code: 400 - {'error': 'Model unloaded.'}
2025-07-28 16:07:57,002 - core.models - ERROR - Model connection test failed: litellm.BadRequestError: Lm_studioException - Error code: 400 - {'error': 'Model unloaded.'}
2025-07-28 16:07:57,002 - root - ERROR - Failed to connect to LM Studio
2025-07-28 16:07:57,532 - core.models - ERROR - Chat completion failed: litellm.BadRequestError: Lm_studioException - Error code: 400 - {'error': 'Model unloaded.'}
2025-07-28 16:07:57,532 - agents.era_generators.erb_script_agent - ERROR - Content generation failed: litellm.BadRequestError: Lm_studioException - Error code: 400 - {'error': 'Model unloaded.'}
2025-07-28 16:07:57,532 - agents.era_generators - ERROR - Game generation failed: 'MCPToolManager' object has no attribute 'call_tool'
2025-07-28 16:07:57,532 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results', 'error']
2025-07-28 16:07:57,533 - core.workflow - ERROR - Game generation failed: 'MCPToolManager' object has no attribute 'call_tool'
2025-07-28 16:12:32,338 - root - INFO - Configuration initialized successfully
2025-07-28 16:12:32,338 - core.models - INFO - Configured LM Studio client with base URL: https://generativelanguage.googleapis.com
2025-07-28 16:12:32,354 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.5-pro; provider = lm_studio
2025-07-28 16:12:33,134 - core.models - ERROR - Sync chat completion failed: litellm.NotFoundError: NotFoundError: Lm_studioException - Error code: 404
2025-07-28 16:12:33,134 - core.models - ERROR - Model connection test failed: litellm.NotFoundError: NotFoundError: Lm_studioException - Error code: 404
2025-07-28 16:12:33,134 - root - ERROR - Failed to connect to LM Studio
2025-07-28 16:13:08,115 - root - INFO - Configuration initialized successfully
2025-07-28 16:13:08,115 - core.models - INFO - Configured LM Studio client with base URL: https://generativelanguage.googleapis.com
2025-07-28 16:13:08,132 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.5-pro; provider = gemini
2025-07-28 16:13:08,618 - core.models - ERROR - Sync chat completion failed: litellm.APIConnectionError: Invalid port: 'generateContent'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 409, in normalize_port
    port_as_int = int(port)
                  ^^^^^^^^^
ValueError: invalid literal for int() with base 10: 'generateContent'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2509, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1809, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 709, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 685, in post
    req = self.client.build_request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 366, in build_request
    url = self._merge_url(url)
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 396, in _merge_url
    merge_url = URL(url)
                ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urls.py", line 117, in __init__
    self._uri_reference = urlparse(url, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 321, in urlparse
    parsed_port: int | None = normalize_port(port, scheme)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 411, in normalize_port
    raise InvalidURL(f"Invalid port: {port!r}")
httpx.InvalidURL: Invalid port: 'generateContent'

2025-07-28 16:13:08,618 - core.models - ERROR - Model connection test failed: litellm.APIConnectionError: Invalid port: 'generateContent'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 409, in normalize_port
    port_as_int = int(port)
                  ^^^^^^^^^
ValueError: invalid literal for int() with base 10: 'generateContent'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2509, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1809, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 709, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 685, in post
    req = self.client.build_request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 366, in build_request
    url = self._merge_url(url)
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 396, in _merge_url
    merge_url = URL(url)
                ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urls.py", line 117, in __init__
    self._uri_reference = urlparse(url, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 321, in urlparse
    parsed_port: int | None = normalize_port(port, scheme)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 411, in normalize_port
    raise InvalidURL(f"Invalid port: {port!r}")
httpx.InvalidURL: Invalid port: 'generateContent'

2025-07-28 16:13:08,619 - root - ERROR - Failed to connect to LM Studio
2025-07-28 16:25:52,935 - root - INFO - Configuration initialized successfully
2025-07-28 16:25:52,936 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 16:25:52,936 - core.models - ERROR - Model connection test failed: 'ModelManager' object has no attribute 'default_model'
2025-07-28 16:26:52,627 - root - INFO - Configuration initialized successfully
2025-07-28 16:26:52,627 - core.models - INFO - Configured lm_studio with model: hunyuan-a13b-instruct
2025-07-28 16:26:52,628 - core.models - ERROR - Model connection test failed: 'ModelManager' object has no attribute 'default_model'
2025-07-28 16:27:05,049 - core.models - INFO - Configured lm_studio with model: hunyuan-a13b-instruct
2025-07-28 16:27:05,049 - core.models - ERROR - Model connection test failed: 'ModelManager' object has no attribute 'default_model'
2025-07-28 16:30:15,137 - core.models - INFO - Configured lm_studio with model: hunyuan-a13b-instruct
2025-07-28 16:30:15,150 - LiteLLM - INFO - 
LiteLLM completion() model= hunyuan-a13b-instruct; provider = lm_studio
2025-07-28 16:30:19,692 - openai._base_client - INFO - Retrying request to /chat/completions in 0.474367 seconds
2025-07-28 16:30:24,246 - openai._base_client - INFO - Retrying request to /chat/completions in 0.846689 seconds
2025-07-28 16:30:29,214 - core.models - ERROR - Sync chat completion failed: litellm.InternalServerError: InternalServerError: Lm_studioException - Connection error.
2025-07-28 16:30:29,214 - core.models - ERROR - Model connection test failed: litellm.InternalServerError: InternalServerError: Lm_studioException - Connection error.
2025-07-28 16:30:29,220 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_sync\connection.py", line 101, in handle_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_sync\connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_sync\connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [WinError 10061] 無法連線，因為目標電腦拒絕連線。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 972, in request
    response = self._client.send(
               ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [WinError 10061] 無法連線，因為目標電腦拒絕連線。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\openai\openai.py", line 725, in completion
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\openai\openai.py", line 653, in completion
    ) = self.make_sync_openai_chat_completion_request(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\logging_utils.py", line 149, in sync_wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\openai\openai.py", line 471, in make_sync_openai_chat_completion_request
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\openai\openai.py", line 453, in make_sync_openai_chat_completion_request
    raw_response = openai_client.chat.completions.with_raw_response.create(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_legacy_response.py", line 364, in wrapped
    return cast(LegacyAPIResponse[R], func(*args, **kwargs))
                                      ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\openai\_base_client.py", line 1004, in request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1896, in completion
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1869, in completion
    response = openai_chat_completions.completion(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\openai\openai.py", line 736, in completion
    raise OpenAIError(
litellm.llms.openai.common_utils.OpenAIError: Connection error.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 159, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 127, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 479, in exception_type
    raise InternalServerError(
litellm.exceptions.InternalServerError: litellm.InternalServerError: InternalServerError: Lm_studioException - Connection error.

2025-07-28 16:33:07,910 - root - INFO - Configuration initialized successfully
2025-07-28 16:33:07,911 - core.models - INFO - Configured gemini with model: gemini-2.5-pro
2025-07-28 16:33:07,926 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.5-pro; provider = vertex_ai
2025-07-28 16:33:08,211 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 1 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:33:09,267 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 2 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:33:11,180 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 3 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:33:11,180 - google.auth._default - WARNING - Authentication failed using Compute Engine authentication due to unavailable metadata server.
2025-07-28 16:33:11,180 - LiteLLM - ERROR - Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-07-28 16:33:11,221 - core.models - ERROR - Sync chat completion failed: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-07-28 16:33:11,222 - core.models - ERROR - Model connection test failed: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-07-28 16:33:11,226 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 159, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 127, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2247, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-07-28 16:34:49,617 - root - INFO - Configuration initialized successfully
2025-07-28 16:34:49,617 - core.models - INFO - Configured openai with model: gemini-2.5-pro
2025-07-28 16:34:49,633 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.5-pro; provider = vertex_ai
2025-07-28 16:34:49,704 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 1 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:34:50,678 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 2 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:34:52,541 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 3 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:34:52,542 - google.auth._default - WARNING - Authentication failed using Compute Engine authentication due to unavailable metadata server.
2025-07-28 16:34:52,543 - LiteLLM - ERROR - Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-07-28 16:34:52,557 - core.models - ERROR - Sync chat completion failed: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-07-28 16:34:52,558 - core.models - ERROR - Model connection test failed: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-07-28 16:34:52,562 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 159, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 127, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2247, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-07-28 16:35:51,726 - root - INFO - Configuration initialized successfully
2025-07-28 16:35:51,727 - core.models - INFO - Configured openai with model: gemini-2.5-pro
2025-07-28 16:35:51,744 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.5-pro; provider = vertex_ai
2025-07-28 16:35:51,820 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 1 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:35:52,799 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 2 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:35:54,966 - google.auth.compute_engine._metadata - WARNING - Compute Engine Metadata server unavailable on attempt 3 of 3. Reason: [WinError 10051] 通訊端操作嘗試連線到一個無法連線的網路。
2025-07-28 16:35:54,967 - google.auth._default - WARNING - Authentication failed using Compute Engine authentication due to unavailable metadata server.
2025-07-28 16:35:54,968 - LiteLLM - ERROR - Failed to load vertex credentials. Check to see if credentials containing partial/invalid information. Error: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
2025-07-28 16:35:54,985 - core.models - ERROR - Sync chat completion failed: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-07-28 16:35:54,985 - core.models - ERROR - Model connection test failed: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

2025-07-28 16:35:54,990 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 161, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 129, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2247, in exception_type
    raise APIConnectionError(
litellm.exceptions.APIConnectionError: litellm.APIConnectionError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2584, in completion
    model_response = vertex_chat_completion.completion(  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1730, in completion
    _auth_header, vertex_project = self._ensure_access_token(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 249, in _ensure_access_token
    return self.get_access_token(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 410, in get_access_token
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 403, in get_access_token
    _credentials, credential_project_id = self.load_auth(
                                          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 102, in load_auth
    creds, creds_project_id = self._credentials_from_default_auth(
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\vertex_llm_base.py", line 143, in _credentials_from_default_auth
    return google_auth.default(scopes=scopes)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\google\auth\_default.py", line 697, in default
    raise exceptions.DefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)
google.auth.exceptions.DefaultCredentialsError: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.


2025-07-28 16:36:31,870 - root - INFO - Configuration initialized successfully
2025-07-28 16:36:31,870 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 16:36:31,887 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:36:32,886 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:36:32,887 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:32,887 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:32,889 - core.models - INFO - Model connection test successful: OK...
2025-07-28 16:36:32,890 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:32,891 - data.database - INFO - Database initialized successfully
2025-07-28 16:36:32,891 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:36:32,891 - root - INFO - Initialized MCP tools
2025-07-28 16:36:32,911 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:36:32,912 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:36:32,913 - core.workflow - INFO - Generating project structure
2025-07-28 16:36:32,914 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:36:32,914 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:36:32,918 - core.workflow - INFO - Starting game generation for: 騎士與玩偶
2025-07-28 16:36:32,919 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:36:41,671 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:41,671 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:41,672 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:36:41,673 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:41,674 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:42,372 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001EF2C379A50>
2025-07-28 16:36:53,392 - root - INFO - Configuration initialized successfully
2025-07-28 16:36:53,392 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 16:36:53,408 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:36:54,429 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:36:54,431 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:54,431 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:54,432 - core.models - INFO - Model connection test successful: OK...
2025-07-28 16:36:54,433 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:54,434 - data.database - INFO - Database initialized successfully
2025-07-28 16:36:54,434 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:36:54,435 - root - INFO - Initialized MCP tools
2025-07-28 16:36:54,453 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:36:54,455 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:36:54,455 - core.workflow - INFO - Generating project structure
2025-07-28 16:36:54,456 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:36:54,457 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:36:54,458 - core.workflow - INFO - Starting game generation for: 騎士與玩偶
2025-07-28 16:36:54,459 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:36:59,857 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:59,858 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:59,860 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:36:59,861 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:36:59,861 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:07,478 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:07,478 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:07,481 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:07,482 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:07,482 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:12,889 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:12,889 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:12,891 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:12,891 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:12,891 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:16,614 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:16,614 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:16,616 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:16,616 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:16,617 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:25,567 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:25,567 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:25,568 - agents.era_generators.character_agent - ERROR - Character parsing failed: Extra data: line 46 column 1 (char 856)
2025-07-28 16:37:25,568 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:25,569 - agents.era_generators.csv_data_agent - ERROR - CSV generation failed: 'MCPToolManager' object has no attribute 'call_tool'
2025-07-28 16:37:25,570 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:25,570 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:32,398 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:32,398 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:32,399 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:32,400 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:32,400 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:37,348 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:37,348 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:37,352 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:37,352 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:37,354 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:40,309 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:40,310 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:40,312 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:40,313 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:40,313 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:44,284 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:44,284 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:44,286 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:44,287 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:44,287 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:50,948 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:50,948 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:50,949 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:50,950 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:50,950 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:56,090 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:56,090 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:56,093 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:56,094 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:37:56,094 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:58,751 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:58,751 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:58,753 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:58,754 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:37:58,754 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:38:03,068 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:03,068 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:03,069 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:03,070 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:38:03,070 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:06,861 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:06,862 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 16:38:06,861 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:06,862 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 16:38:06,864 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 4
2025-07-28 16:38:06,863 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:06,872 - agents.era_generators - INFO - Saved 17 game files
2025-07-28 16:38:06,872 - core.workflow - INFO - Generated and saved 17 files to ./generated_games
2025-07-28 16:38:06,872 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 16:38:06,872 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 16:38:06,873 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 16:38:06,873 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 16:38:06,873 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 16:38:06,873 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 16:38:06,873 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 16:38:06,873 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 16:38:06,874 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 16:38:06,874 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 16:38:06,874 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 16:38:06,874 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 16:38:06,874 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 16:38:06,874 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 16:38:06,875 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 16:38:06,875 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 16:38:06,875 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 16:38:06,876 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:38:06,877 - core.workflow - INFO - Implementing system flow
2025-07-28 16:38:06,878 - core.workflow - INFO - Creating game characters
2025-07-28 16:38:06,879 - core.workflow - INFO - Integrating game logic
2025-07-28 16:38:06,880 - core.workflow - INFO - Validating and finalizing game
2025-07-28 16:38:06,880 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 16:38:07,118 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000014D329FDBD0>
2025-07-28 16:38:07,118 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000014D32BC67B0>, 31298.578)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000014D2EADB190>
2025-07-28 16:40:48,963 - root - INFO - Configuration initialized successfully
2025-07-28 16:40:48,963 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 16:40:48,979 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:40:49,973 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:40:49,974 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:40:49,974 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:40:49,975 - core.models - INFO - Model connection test successful: OK...
2025-07-28 16:40:49,976 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:40:49,977 - data.database - INFO - Database initialized successfully
2025-07-28 16:40:49,977 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:40:49,978 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 16:40:49,978 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 16:40:49,978 - root - INFO - Initialized MCP tools
2025-07-28 16:40:50,000 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:40:50,003 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:40:50,004 - core.workflow - INFO - Generating project structure
2025-07-28 16:40:50,005 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:40:50,005 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:40:50,009 - core.workflow - INFO - Starting game generation for: 騎士與玩偶
2025-07-28 16:40:50,010 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:01,201 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:01,202 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:01,204 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:01,205 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:01,206 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:08,083 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:08,083 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:08,087 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:08,088 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:08,088 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:14,086 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:14,086 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:14,087 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:14,088 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:14,088 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:18,401 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:18,401 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:18,402 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:18,403 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:18,403 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:28,773 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:28,773 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:28,776 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:28,777 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:28,777 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:37,794 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:37,795 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:37,797 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:37,798 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:37,798 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:44,111 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:44,111 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:44,113 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:44,113 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:44,113 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:50,197 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:50,197 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:50,198 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:50,199 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:50,199 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:59,513 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:59,514 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:59,517 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:41:59,519 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:41:59,520 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:09,153 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:09,153 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:09,156 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:42:09,157 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:09,158 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:15,488 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:15,489 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:15,491 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:15,492 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:42:15,493 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:20,325 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:20,326 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:20,328 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:20,330 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:20,330 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:42:24,600 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:24,600 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:24,604 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:24,605 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:42:24,605 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:27,308 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:27,308 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:27,310 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 16:42:27,311 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 16:42:27,312 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 4
2025-07-28 16:42:27,311 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:27,320 - agents.era_generators - INFO - Saved 17 game files
2025-07-28 16:42:27,320 - core.workflow - INFO - Generated and saved 17 files to ./generated_games
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 16:42:27,321 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 16:42:27,322 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 16:42:27,323 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:42:27,324 - core.workflow - INFO - Implementing system flow
2025-07-28 16:42:27,325 - core.workflow - INFO - Creating game characters
2025-07-28 16:42:27,325 - core.workflow - INFO - Integrating game logic
2025-07-28 16:42:27,326 - core.workflow - INFO - Validating and finalizing game
2025-07-28 16:42:27,326 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 16:42:27,552 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000025763EA23D0>
2025-07-28 16:42:27,552 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000025763F7F2A0>, 31559.031)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000025762DC79D0>
2025-07-28 16:46:51,621 - root - INFO - Configuration initialized successfully
2025-07-28 16:46:51,622 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 16:46:51,639 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:46:52,669 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:46:52,670 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:46:52,670 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:46:52,672 - core.models - INFO - Model connection test successful: OK...
2025-07-28 16:46:52,673 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:46:52,674 - data.database - INFO - Database initialized successfully
2025-07-28 16:46:52,674 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:46:52,674 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 16:46:52,674 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 16:46:52,674 - root - INFO - Initialized MCP tools
2025-07-28 16:46:52,693 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:46:52,695 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:46:52,696 - core.workflow - INFO - Generating project structure
2025-07-28 16:46:52,697 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:46:52,698 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:46:52,699 - core.workflow - INFO - Starting game generation for: 測試遊戲
2025-07-28 16:46:52,700 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:46:57,024 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:46:57,024 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:46:57,026 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:46:57,027 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:46:57,027 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:02,346 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:02,347 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:02,350 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:02,350 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:02,351 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:07,065 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:07,066 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:07,069 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:07,069 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:07,071 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:11,736 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:11,736 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:11,737 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:11,737 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:11,738 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:19,141 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:19,141 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:19,143 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:19,144 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:19,145 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:23,141 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:23,142 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:23,144 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:23,146 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:23,147 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:27,466 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:27,466 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:27,468 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:27,468 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:27,468 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:30,683 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:30,683 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:30,685 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:30,685 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:30,686 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:37,035 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:37,035 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:37,037 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:37,038 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:37,038 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:45,850 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:45,850 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:45,853 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:45,853 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:45,854 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:49,174 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:49,175 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:49,177 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:49,178 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:49,179 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:54,222 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:54,222 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:54,225 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:47:54,225 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:47:54,226 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:02,062 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:02,062 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:02,065 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:48:02,065 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:02,067 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:05,104 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:05,104 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:05,105 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 16:48:05,106 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 16:48:05,106 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:05,106 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 4
2025-07-28 16:48:05,113 - agents.era_generators - INFO - Saved 17 game files
2025-07-28 16:48:05,113 - core.workflow - INFO - Generated and saved 17 files to ./generated_games
2025-07-28 16:48:05,113 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 16:48:05,113 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 16:48:05,114 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 16:48:05,115 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 16:48:05,117 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:48:05,118 - core.workflow - INFO - Implementing system flow
2025-07-28 16:48:05,119 - core.workflow - INFO - Creating game characters
2025-07-28 16:48:05,119 - core.workflow - INFO - Integrating game logic
2025-07-28 16:48:05,120 - core.workflow - INFO - Validating and finalizing game
2025-07-28 16:48:05,120 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 16:48:05,121 - core.models - INFO - Model manager resources cleaned up
2025-07-28 16:48:05,528 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000018C2773E4D0>
2025-07-28 16:48:05,529 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000018C2899ADD0>, 31896.828)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000018C6DA38A10>
2025-07-28 16:49:07,090 - root - INFO - Configuration initialized successfully
2025-07-28 16:49:07,091 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 16:49:07,108 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:08,116 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 16:49:08,118 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:08,118 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:08,120 - core.models - INFO - Model connection test successful: OK...
2025-07-28 16:49:08,122 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:08,123 - data.database - INFO - Database initialized successfully
2025-07-28 16:49:08,123 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 16:49:08,123 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 16:49:08,123 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 16:49:08,123 - root - INFO - Initialized MCP tools
2025-07-28 16:49:08,143 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 16:49:08,145 - core.workflow - INFO - Analyzing game requirements
2025-07-28 16:49:08,146 - core.workflow - INFO - Generating project structure
2025-07-28 16:49:08,147 - core.workflow - INFO - Creating CSV data files
2025-07-28 16:49:08,147 - core.workflow - INFO - Generating ERB scripts
2025-07-28 16:49:08,148 - core.workflow - INFO - Starting game generation for: 最終測試
2025-07-28 16:49:08,149 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:14,571 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:14,572 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:14,574 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:14,575 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:14,575 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 16:49:14,820 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "44s"
      }
    ]
  }
}

2025-07-28 16:49:14,820 - agents.era_generators.erb_script_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "44s"
      }
    ]
  }
}

2025-07-28 16:49:14,821 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:15,639 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "44s"
      }
    ]
  }
}

2025-07-28 16:49:15,640 - agents.era_generators.erb_script_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "44s"
      }
    ]
  }
}

2025-07-28 16:49:15,641 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:15,894 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "43s"
      }
    ]
  }
}

2025-07-28 16:49:15,894 - agents.era_generators.erb_script_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "43s"
      }
    ]
  }
}

2025-07-28 16:49:15,895 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:16,709 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "42s"
      }
    ]
  }
}

2025-07-28 16:49:16,710 - agents.era_generators.character_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "42s"
      }
    ]
  }
}

2025-07-28 16:49:16,712 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:16,963 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "42s"
      }
    ]
  }
}

2025-07-28 16:49:16,963 - agents.era_generators.csv_data_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "42s"
      }
    ]
  }
}

2025-07-28 16:49:16,964 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:17,751 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:17,751 - agents.era_generators.csv_data_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:17,752 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:17,975 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:17,976 - agents.era_generators.csv_data_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:17,977 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:18,204 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:18,205 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:18,206 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:18,436 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:18,436 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:18,437 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:18,675 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:18,675 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "41s"
      }
    ]
  }
}

2025-07-28 16:49:18,677 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:18,907 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "40s"
      }
    ]
  }
}

2025-07-28 16:49:18,907 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "40s"
      }
    ]
  }
}

2025-07-28 16:49:18,908 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:19,144 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "40s"
      }
    ]
  }
}

2025-07-28 16:49:19,144 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "40s"
      }
    ]
  }
}

2025-07-28 16:49:19,145 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 16:49:19,404 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "40s"
      }
    ]
  }
}

2025-07-28 16:49:19,404 - agents.era_generators.game_logic_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "40s"
      }
    ]
  }
}

2025-07-28 16:49:19,405 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 16:49:19,405 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 16:49:19,405 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 4
2025-07-28 16:49:19,411 - agents.era_generators - INFO - Saved 17 game files
2025-07-28 16:49:19,411 - core.workflow - INFO - Generated and saved 17 files to ./generated_games
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 16:49:19,412 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 16:49:19,413 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 16:49:19,414 - core.workflow - INFO - Implementing system flow
2025-07-28 16:49:19,415 - core.workflow - INFO - Creating game characters
2025-07-28 16:49:19,416 - core.workflow - INFO - Integrating game logic
2025-07-28 16:49:19,416 - core.workflow - INFO - Validating and finalizing game
2025-07-28 16:49:19,416 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 16:49:20,080 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:01:51,941 - root - INFO - Configuration initialized successfully
2025-07-28 17:01:51,941 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 17:01:51,960 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:01:52,890 - core.models - ERROR - Sync chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "6s"
      }
    ]
  }
}

2025-07-28 17:01:52,890 - core.models - ERROR - Model connection test failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "6s"
      }
    ]
  }
}

2025-07-28 17:01:52,895 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1809, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 707, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 689, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=AIzaSyA9G7bboRt7f3aEl58ECFmp0Js6yPuqwts'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2509, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1813, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "6s"
      }
    ]
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1308, in exception_type
    raise RateLimitError(
litellm.exceptions.RateLimitError: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "6s"
      }
    ]
  }
}


2025-07-28 17:02:26,928 - root - INFO - Configuration initialized successfully
2025-07-28 17:02:26,929 - core.models - INFO - Configured gemini with model: gemini-2.0-flash-exp
2025-07-28 17:02:26,945 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:27,989 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 17:02:27,990 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:27,990 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:27,992 - core.models - INFO - Model connection test successful: OK...
2025-07-28 17:02:27,993 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:27,993 - data.database - INFO - Database initialized successfully
2025-07-28 17:02:27,994 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 17:02:27,994 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 17:02:27,995 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 17:02:27,995 - root - INFO - Initialized MCP tools
2025-07-28 17:02:28,017 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 17:02:28,019 - core.workflow - INFO - Analyzing game requirements
2025-07-28 17:02:28,020 - core.workflow - INFO - Generating project structure
2025-07-28 17:02:28,021 - core.workflow - INFO - Creating CSV data files
2025-07-28 17:02:28,021 - core.workflow - INFO - Generating ERB scripts
2025-07-28 17:02:28,025 - core.workflow - INFO - Starting game generation for: 骑士与玩偶
2025-07-28 17:02:28,026 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:28,923 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:28,924 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:28,926 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:28,927 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:28,928 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:30,279 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:30,279 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:30,280 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:30,281 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:30,281 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:32,285 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:32,285 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:32,286 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:32,287 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:32,287 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:37,621 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:37,621 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:37,624 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:37,624 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:37,625 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:42,382 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:42,383 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:42,387 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:42,388 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:42,388 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:44,600 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:44,600 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:44,601 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:44,602 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:44,602 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:49,220 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:49,220 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:49,222 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:49,222 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:49,223 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:53,761 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:53,761 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:53,762 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:53,763 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:53,763 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:57,703 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:57,703 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:57,706 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:02:57,707 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:02:57,708 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:03:03,720 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:03:03,720 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:03:03,722 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:03:03,722 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:03:03,723 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-2.0-flash-exp
2025-07-28 17:03:03,960 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:03,961 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:03,962 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:03:04,197 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:04,198 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:04,199 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:03:04,423 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:04,424 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-2.0-flash-exp",
              "location": "global"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:04,425 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.0-flash-exp; provider = gemini
2025-07-28 17:03:04,660 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:04,661 - agents.era_generators.game_logic_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerMinutePerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-2.0-flash-exp"
            },
            "quotaValue": "10"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "55s"
      }
    ]
  }
}

2025-07-28 17:03:04,661 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 17:03:04,662 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 17:03:04,662 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 6
2025-07-28 17:03:04,669 - agents.era_generators - INFO - Saved 19 game files
2025-07-28 17:03:04,669 - core.workflow - INFO - Generated and saved 19 files to ./generated_games
2025-07-28 17:03:04,669 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 17:03:04,670 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara1.csv
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara2.csv
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 17:03:04,671 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 17:03:04,672 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 17:03:04,672 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 17:03:04,672 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 17:03:04,673 - core.workflow - INFO - Implementing system flow
2025-07-28 17:03:04,673 - core.workflow - INFO - Creating game characters
2025-07-28 17:03:04,674 - core.workflow - INFO - Integrating game logic
2025-07-28 17:03:04,675 - core.workflow - INFO - Validating and finalizing game
2025-07-28 17:03:04,675 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 17:03:05,359 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:04:03,553 - root - INFO - Configuration initialized successfully
2025-07-28 17:04:03,553 - core.models - INFO - Configured gemini with model: gemini-2.5-pro
2025-07-28 17:04:03,571 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-2.5-pro; provider = gemini
2025-07-28 17:04:06,592 - core.models - ERROR - Sync chat completion failed: litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-28 17:04:06,592 - core.models - ERROR - Model connection test failed: litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-07-28 17:04:06,598 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1809, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 707, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 689, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Server error '503 Service Unavailable' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent?key=AIzaSyAKQ7bGgAAEEDTk75K6EAWCQx8n74SpGUY'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/503

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2509, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1813, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1262, in exception_type
    raise litellm.InternalServerError(
litellm.exceptions.InternalServerError: litellm.InternalServerError: litellm.InternalServerError: VertexAIException - {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}


2025-07-28 17:05:01,064 - root - INFO - Configuration initialized successfully
2025-07-28 17:05:01,065 - core.models - INFO - Configured gemini with model: gemini-pro
2025-07-28 17:05:01,082 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-pro; provider = gemini
2025-07-28 17:05:01,851 - core.models - ERROR - Sync chat completion failed: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-07-28 17:05:01,851 - core.models - ERROR - Model connection test failed: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}

2025-07-28 17:05:01,857 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1809, in completion
    response = client.post(url=url, headers=headers, json=data)  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 707, in post
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\custom_httpx\http_handler.py", line 689, in post
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '404 Not Found' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyAKQ7bGgAAEEDTk75K6EAWCQx8n74SpGUY'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/404

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 2509, in completion
    response = vertex_chat_completion.completion(  # type: ignore
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\llms\vertex_ai\gemini\vertex_and_google_ai_studio_gemini.py", line 1813, in completion
    raise VertexAIError(
litellm.llms.vertex_ai.common_utils.VertexAIError: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
          ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 2271, in exception_type
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\exception_mapping_utils.py", line 1293, in exception_type
    raise NotFoundError(
litellm.exceptions.NotFoundError: litellm.NotFoundError: VertexAIException - {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}


2025-07-28 17:05:41,044 - root - INFO - Configuration initialized successfully
2025-07-28 17:05:41,044 - core.models - INFO - Configured gemini with model: gemini-1.5-flash
2025-07-28 17:05:41,062 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:41,987 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 17:05:41,988 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:41,988 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:41,989 - core.models - INFO - Model connection test successful: OK...
2025-07-28 17:05:41,991 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:41,992 - data.database - INFO - Database initialized successfully
2025-07-28 17:05:41,992 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 17:05:41,993 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 17:05:41,993 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 17:05:41,993 - root - INFO - Initialized MCP tools
2025-07-28 17:05:42,014 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 17:05:42,015 - core.workflow - INFO - Analyzing game requirements
2025-07-28 17:05:42,016 - core.workflow - INFO - Generating project structure
2025-07-28 17:05:42,017 - core.workflow - INFO - Creating CSV data files
2025-07-28 17:05:42,017 - core.workflow - INFO - Generating ERB scripts
2025-07-28 17:05:42,018 - core.workflow - INFO - Starting game generation for: 骑士与玩偶
2025-07-28 17:05:42,019 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:44,830 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:44,830 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:44,831 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:44,832 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:44,833 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:46,579 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:46,580 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:46,583 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:46,583 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:46,584 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:49,539 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:49,540 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:49,541 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:49,541 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:49,542 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:51,543 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:51,543 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:51,547 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:51,547 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:51,548 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:53,936 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:53,937 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:53,939 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:53,939 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:53,941 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:55,777 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:55,777 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:55,779 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:05:55,779 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:05:55,780 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:00,210 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:00,210 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:00,212 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:00,212 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:00,213 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:03,505 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:03,505 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:03,506 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:03,507 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:03,507 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:08,366 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:08,367 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:08,368 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:08,369 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:08,369 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:12,517 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:12,517 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:12,518 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:12,519 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:12,519 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:16,654 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:16,654 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:16,656 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:16,658 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:16,661 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:18,785 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:18,785 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:18,787 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:18,787 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:18,787 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:21,016 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:21,016 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:21,018 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:21,018 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:06:21,019 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:23,146 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:23,147 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 17:06:23,146 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:23,148 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 17:06:23,149 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:23,150 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 6
2025-07-28 17:06:23,160 - agents.era_generators - INFO - Saved 19 game files
2025-07-28 17:06:23,160 - core.workflow - INFO - Generated and saved 19 files to ./generated_games
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 17:06:23,161 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara1.csv
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara2.csv
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 17:06:23,162 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 17:06:23,163 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 17:06:23,163 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:06:23,164 - core.workflow - INFO - Implementing system flow
2025-07-28 17:06:23,165 - core.workflow - INFO - Creating game characters
2025-07-28 17:06:23,166 - core.workflow - INFO - Integrating game logic
2025-07-28 17:06:23,166 - core.workflow - INFO - Validating and finalizing game
2025-07-28 17:06:23,166 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 17:06:23,870 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:10:35,341 - root - INFO - Configuration initialized successfully
2025-07-28 17:10:35,341 - core.models - INFO - Configured gemini with model: gemini-1.5-flash
2025-07-28 17:10:35,372 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:36,383 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 17:10:36,384 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:36,384 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:36,385 - core.models - INFO - Model connection test successful: OK...
2025-07-28 17:10:36,386 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:36,387 - data.database - INFO - Database initialized successfully
2025-07-28 17:10:36,387 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 17:10:36,388 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 17:10:36,388 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 17:10:36,388 - root - INFO - Initialized MCP tools
2025-07-28 17:10:36,407 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 17:10:36,410 - core.workflow - INFO - Analyzing game requirements
2025-07-28 17:10:36,411 - core.workflow - INFO - Generating project structure
2025-07-28 17:10:36,412 - core.workflow - INFO - Creating CSV data files
2025-07-28 17:10:36,412 - core.workflow - INFO - Generating ERB scripts
2025-07-28 17:10:36,416 - core.workflow - INFO - Starting game generation for: 骑士与玩偶
2025-07-28 17:10:36,417 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:39,771 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:39,772 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:39,773 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:39,774 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:39,775 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:40,727 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:40,727 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:40,730 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:40,730 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:40,731 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:43,664 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:43,664 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:43,666 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:43,666 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:43,667 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:48,670 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:48,670 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:48,671 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:48,672 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:48,672 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:52,319 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:52,319 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:52,323 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:52,323 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:52,324 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:54,232 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:54,232 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:54,234 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:54,235 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:10:54,235 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:59,771 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:59,772 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:59,774 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:59,775 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:10:59,776 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:02,330 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:02,330 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:02,332 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:02,333 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:02,333 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:05,111 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:05,111 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:05,112 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:05,113 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:05,113 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:08,290 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:08,291 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:08,293 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:08,293 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:08,294 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:11,673 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:11,673 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:11,675 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:11,676 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:11,677 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:13,247 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:13,247 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:13,249 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:13,249 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:13,250 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:17,002 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:17,003 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:17,005 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:17,005 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:11:17,006 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:18,760 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:18,761 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 17:11:18,761 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:18,761 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 17:11:18,763 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 6
2025-07-28 17:11:18,762 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:18,771 - agents.era_generators - INFO - Saved 19 game files
2025-07-28 17:11:18,771 - core.workflow - INFO - Generated and saved 19 files to ./generated_games
2025-07-28 17:11:18,771 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 17:11:18,772 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 17:11:18,772 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 17:11:18,772 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 17:11:18,772 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara1.csv
2025-07-28 17:11:18,773 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara2.csv
2025-07-28 17:11:18,774 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 17:11:18,774 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 17:11:18,774 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 17:11:18,774 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 17:11:18,774 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 17:11:18,775 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 17:11:18,775 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 17:11:18,776 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:11:18,777 - core.workflow - INFO - Implementing system flow
2025-07-28 17:11:18,779 - core.workflow - INFO - Creating game characters
2025-07-28 17:11:18,780 - core.workflow - INFO - Integrating game logic
2025-07-28 17:11:18,781 - core.workflow - INFO - Validating and finalizing game
2025-07-28 17:11:18,781 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 17:11:19,460 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:18:53,332 - root - INFO - Configuration initialized successfully
2025-07-28 17:18:53,332 - core.models - INFO - Configured gemini with model: gemini-1.5-flash
2025-07-28 17:18:53,350 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:18:54,319 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 17:18:54,320 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:54,320 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:54,321 - core.models - INFO - Model connection test successful: OK...
2025-07-28 17:18:54,322 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:54,323 - data.database - INFO - Database initialized successfully
2025-07-28 17:18:54,324 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 17:18:54,324 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 17:18:54,324 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 17:18:54,324 - root - INFO - Initialized MCP tools
2025-07-28 17:18:54,348 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 17:18:54,349 - core.workflow - INFO - Analyzing game requirements
2025-07-28 17:18:54,350 - core.workflow - INFO - Generating project structure
2025-07-28 17:18:54,351 - core.workflow - INFO - Creating CSV data files
2025-07-28 17:18:54,351 - core.workflow - INFO - Generating ERB scripts
2025-07-28 17:18:54,355 - core.workflow - INFO - Starting game generation for: 骑士与玩偶
2025-07-28 17:18:54,356 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:18:58,047 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:58,047 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:58,049 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:18:58,051 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:58,051 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:59,603 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:59,603 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:59,605 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:18:59,605 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:18:59,606 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:02,708 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:02,709 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:02,712 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:02,713 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:02,713 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:06,695 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:06,695 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:06,696 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:06,696 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:06,697 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:11,841 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:11,841 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:11,844 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:11,845 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:11,845 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:12,973 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:12,973 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:12,974 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:12,975 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:12,976 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:14,506 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:14,507 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:14,508 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:14,508 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:14,509 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:16,164 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:16,165 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:16,167 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:16,168 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:16,169 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:20,450 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:20,450 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:20,451 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:20,451 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:20,452 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:23,868 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:23,868 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:23,869 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:23,869 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:23,870 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:28,528 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:28,528 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:28,529 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:28,530 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:28,530 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:30,872 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:30,872 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:30,873 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:30,874 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:30,874 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:33,706 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:33,706 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:33,708 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:19:33,708 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:33,709 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:35,717 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:35,718 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 17:19:35,718 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:35,718 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 17:19:35,719 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 6
2025-07-28 17:19:35,719 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:35,728 - agents.era_generators - INFO - Saved 19 game files
2025-07-28 17:19:35,728 - core.workflow - INFO - Generated and saved 19 files to ./generated_games
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 17:19:35,728 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara1.csv
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara2.csv
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 17:19:35,730 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 17:19:35,731 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 17:19:35,731 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 17:19:35,732 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:19:35,733 - core.workflow - INFO - Implementing system flow
2025-07-28 17:19:35,734 - core.workflow - INFO - Creating game characters
2025-07-28 17:19:35,734 - core.workflow - INFO - Integrating game logic
2025-07-28 17:19:35,735 - core.workflow - INFO - Validating and finalizing game
2025-07-28 17:19:35,735 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 17:19:36,423 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:22:00,505 - root - INFO - Configuration initialized successfully
2025-07-28 17:22:00,505 - core.models - INFO - Configured gemini with model: gemini-1.5-flash
2025-07-28 17:22:00,523 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:22:01,488 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 17:22:01,489 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:22:01,489 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:22:01,490 - core.models - INFO - Model connection test successful: OK...
2025-07-28 17:22:01,491 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:22:01,492 - data.database - INFO - Database initialized successfully
2025-07-28 17:22:01,492 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 17:22:01,492 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 17:22:01,492 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 17:22:01,492 - root - INFO - Initialized MCP tools
2025-07-28 17:22:01,514 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 17:22:01,516 - core.workflow - INFO - Analyzing game requirements
2025-07-28 17:22:01,517 - core.workflow - INFO - Generating project structure
2025-07-28 17:22:01,517 - core.workflow - INFO - Creating CSV data files
2025-07-28 17:22:01,518 - core.workflow - INFO - Generating ERB scripts
2025-07-28 17:22:01,520 - core.workflow - ERROR - ERB script generation failed: f-string: empty expression not allowed (era_generators.py, line 369)
2025-07-28 17:22:02,176 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:23:09,560 - root - INFO - Configuration initialized successfully
2025-07-28 17:23:09,561 - core.models - INFO - Configured gemini with model: gemini-1.5-flash
2025-07-28 17:23:09,578 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:10,552 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-28 17:23:10,552 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:10,552 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:10,554 - core.models - INFO - Model connection test successful: OK...
2025-07-28 17:23:10,555 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:10,556 - data.database - INFO - Database initialized successfully
2025-07-28 17:23:10,556 - core.rag - INFO - ERA RAG system initialized successfully
2025-07-28 17:23:10,557 - tools.mcp_tools - INFO - Registered tool: create_character_data
2025-07-28 17:23:10,557 - tools.mcp_tools - INFO - Registered tool: format_csv_data
2025-07-28 17:23:10,557 - root - INFO - Initialized MCP tools
2025-07-28 17:23:10,578 - core.workflow - INFO - Starting ERA game generation workflow
2025-07-28 17:23:10,580 - core.workflow - INFO - Analyzing game requirements
2025-07-28 17:23:10,581 - core.workflow - INFO - Generating project structure
2025-07-28 17:23:10,581 - core.workflow - INFO - Creating CSV data files
2025-07-28 17:23:10,582 - core.workflow - INFO - Generating ERB scripts
2025-07-28 17:23:10,586 - core.workflow - INFO - Starting game generation for: 骑士与玩偶
2025-07-28 17:23:10,586 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:14,066 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:14,066 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:14,067 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:14,068 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:14,069 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:15,985 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:15,985 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:15,986 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:15,987 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:15,987 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:19,676 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:19,676 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:19,678 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:19,678 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:19,678 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:23,581 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:23,581 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:23,582 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:23,583 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:23,583 - LiteLLM - INFO - selected model name for cost calculation: gemini/gemini-1.5-flash
2025-07-28 17:23:23,824 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-1.5-flash",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "35s"
      }
    ]
  }
}

2025-07-28 17:23:23,824 - agents.era_generators.character_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-1.5-flash",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "35s"
      }
    ]
  }
}

2025-07-28 17:23:23,825 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:24,053 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "35s"
      }
    ]
  }
}

2025-07-28 17:23:24,054 - agents.era_generators.csv_data_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "35s"
      }
    ]
  }
}

2025-07-28 17:23:24,055 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:24,283 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "35s"
      }
    ]
  }
}

2025-07-28 17:23:24,283 - agents.era_generators.csv_data_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "35s"
      }
    ]
  }
}

2025-07-28 17:23:24,284 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:25,088 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "34s"
      }
    ]
  }
}

2025-07-28 17:23:25,088 - agents.era_generators.csv_data_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "34s"
      }
    ]
  }
}

2025-07-28 17:23:25,089 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:25,310 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-1.5-flash",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "34s"
      }
    ]
  }
}

2025-07-28 17:23:25,310 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "model": "gemini-1.5-flash",
              "location": "global"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "34s"
      }
    ]
  }
}

2025-07-28 17:23:25,312 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:26,133 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "33s"
      }
    ]
  }
}

2025-07-28 17:23:26,134 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "33s"
      }
    ]
  }
}

2025-07-28 17:23:26,135 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:26,356 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "33s"
      }
    ]
  }
}

2025-07-28 17:23:26,356 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "33s"
      }
    ]
  }
}

2025-07-28 17:23:26,357 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:27,173 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "32s"
      }
    ]
  }
}

2025-07-28 17:23:27,173 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "32s"
      }
    ]
  }
}

2025-07-28 17:23:27,175 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:27,387 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "32s"
      }
    ]
  }
}

2025-07-28 17:23:27,387 - agents.era_generators.system_flow_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "32s"
      }
    ]
  }
}

2025-07-28 17:23:27,389 - LiteLLM - INFO - 
LiteLLM completion() model= gemini-1.5-flash; provider = gemini
2025-07-28 17:23:28,201 - core.models - ERROR - Async chat completion failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "31s"
      }
    ]
  }
}

2025-07-28 17:23:28,201 - agents.era_generators.game_logic_agent - ERROR - Content generation failed: litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.QuotaFailure",
        "violations": [
          {
            "quotaMetric": "generativelanguage.googleapis.com/generate_content_free_tier_requests",
            "quotaId": "GenerateRequestsPerDayPerProjectPerModel-FreeTier",
            "quotaDimensions": {
              "location": "global",
              "model": "gemini-1.5-flash"
            },
            "quotaValue": "50"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Learn more about Gemini API quotas",
            "url": "https://ai.google.dev/gemini-api/docs/rate-limits"
          }
        ]
      },
      {
        "@type": "type.googleapis.com/google.rpc.RetryInfo",
        "retryDelay": "31s"
      }
    ]
  }
}

2025-07-28 17:23:28,202 - agents.era_generators - INFO - ERA game generation completed successfully
2025-07-28 17:23:28,202 - core.workflow - INFO - Game generation completed. Results keys: ['erb_files', 'csv_files', 'config_files', 'characters', 'validation_results']
2025-07-28 17:23:28,202 - core.workflow - INFO - No errors in results. ERB files: 9, CSV files: 6
2025-07-28 17:23:28,210 - agents.era_generators - INFO - Saved 19 game files
2025-07-28 17:23:28,211 - core.workflow - INFO - Generated and saved 19 files to ./generated_games
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\SYSTEM.ERB
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\TITLE.ERB
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\VARIABLES.ERH
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\TRAIN_MAIN.ERB
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\EVENTFIRST.ERB
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\SHOP.ERB
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\ABLUP.ERB
2025-07-28 17:23:28,211 - core.workflow - INFO - Saved file: generated_games\ERB\AFTERTRAIN.ERB
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\ERB\TURNEND.ERB
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara0.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara1.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\CSV\Chara\Chara2.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\CSV\Abl.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\CSV\Talent.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\CSV\Train.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\_default.config
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\_fixed.config
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\_replace.csv
2025-07-28 17:23:28,212 - core.workflow - INFO - Saved file: generated_games\resources\img.csv
2025-07-28 17:23:28,213 - core.workflow - INFO - Implementing system flow
2025-07-28 17:23:28,214 - core.workflow - INFO - Creating game characters
2025-07-28 17:23:28,214 - core.workflow - INFO - Integrating game logic
2025-07-28 17:23:28,215 - core.workflow - INFO - Validating and finalizing game
2025-07-28 17:23:28,215 - core.workflow - INFO - ERA game generation completed successfully
2025-07-28 17:23:28,884 - core.models - WARNING - Error during cleanup: Requested settings, but settings are not configured. You must either define the environment variable DJANGO_SETTINGS_MODULE or call settings.configure() before accessing settings.
2025-07-28 17:29:53,167 - root - INFO - Configuration initialized successfully
2025-07-28 17:29:53,167 - core.models - INFO - Configured openai with model: qwen/qwen3-coder:free
2025-07-28 17:29:53,200 - core.models - ERROR - Sync chat completion failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:29:53,200 - core.models - ERROR - Model connection test failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:29:53,203 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1052, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-07-28 17:30:21,101 - root - INFO - Configuration initialized successfully
2025-07-28 17:30:21,101 - core.models - INFO - Configured openai with model: /qwen/qwen3-coder:free
2025-07-28 17:30:21,124 - core.models - ERROR - Sync chat completion failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=/qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:30:21,124 - core.models - ERROR - Model connection test failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=/qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:30:21,127 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1052, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=/qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-07-28 17:31:04,127 - root - INFO - Configuration initialized successfully
2025-07-28 17:31:04,127 - core.models - INFO - Configured openrouter with model: qwen/qwen3-coder:free
2025-07-28 17:31:04,153 - core.models - ERROR - Sync chat completion failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:31:04,153 - core.models - ERROR - Model connection test failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:31:04,155 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1052, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-07-28 17:31:21,526 - root - INFO - Configuration initialized successfully
2025-07-28 17:31:21,526 - core.models - INFO - Configured openrouter with model: qwen/qwen3-coder:free
2025-07-28 17:31:21,551 - core.models - ERROR - Sync chat completion failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:31:21,551 - core.models - ERROR - Model connection test failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:31:21,553 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1052, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen/qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

2025-07-28 17:33:00,524 - root - INFO - Configuration initialized successfully
2025-07-28 17:33:00,524 - core.models - INFO - Configured openrouter/qwen with model: qwen3-coder:free
2025-07-28 17:33:00,551 - core.models - ERROR - Sync chat completion failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:33:00,551 - core.models - ERROR - Model connection test failed: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers
2025-07-28 17:33:00,554 - core.models - ERROR - Full traceback: Traceback (most recent call last):
  File "F:\limiya\era_ai_agent\core\models.py", line 164, in test_connection
    response = self.chat_completion_sync(test_messages)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\limiya\era_ai_agent\core\models.py", line 132, in chat_completion_sync
    response = completion(**params)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1285, in wrapper
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\utils.py", line 1163, in wrapper
    result = original_function(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 3304, in completion
    raise exception_type(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\main.py", line 1052, in completion
    model, custom_llm_provider, dynamic_api_key, api_base = get_llm_provider(
                                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 373, in get_llm_provider
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\litellm\litellm_core_utils\get_llm_provider_logic.py", line 350, in get_llm_provider
    raise litellm.exceptions.BadRequestError(  # type: ignore
litellm.exceptions.BadRequestError: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=qwen3-coder:free
 Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers

