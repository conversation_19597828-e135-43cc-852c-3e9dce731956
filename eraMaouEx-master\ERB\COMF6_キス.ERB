﻿;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;キス
;爱抚系コマンド：調教者が調教対象の口を口で刺激
;-------------------------------------------------
@COM6

;-------------------------------------------------
;跳转到进阶指令
;-------------------------------------------------
LOCAL = 6
CALL GET_ADV_COM,LOCAL
SIF RESULT != LOCAL
	JUMPFORM COM{RESULT}



PRINTL 接吻

;-------------------------------------------------
;実行できるかの判定
;-------------------------------------------------
A = 0
S = 0
Y = 0
;爱液の汚れ
SIF STAIN:PLAYER:0 & 1
	Y += 1
;精液の汚れ
SIF STAIN:PLAYER:0 & 4
	Y += 3
;アナルの汚れ
SIF STAIN:PLAYER:0 & 8
	Y += 7
;母乳の汚れ
SIF STAIN:PLAYER:0 & 16
	Y += 1
;尿の汚れ
SIF STAIN:PLAYER:0 & 32
	Y += 3

;兽奸の場合は汚れ7で固定
SIF TEQUIP:89
	Y = 7

SIF TALENT:61
	Y /= 3
SIF TALENT:62
	Y *= 2

;キスでは汚れがそれほど影響しない
Y /= 2

;顺从が2以上か侍奉精神が3以上か爱がある場合は自動成功
SIF ABL:10 >= 2 || ABL:16 >= 3 || TALENT:85
	GOTO AUTO_SUCCESS

;すべての命令に共通の要素を考慮
;(顺从が高いと命令に従いやすいなど)
CALL COM_ORDER

;ABL:欲望
IF ABL:11
	SIF S
		PRINT  + 
	A += ABL:11 * 1
	PRINTS ABLNAME:11
	PRINTV 'LV,ABL:11,'(,ABL:11 * 1,')
	S = 1
ENDIF

;ABL:侍奉精神
IF ABL:16
	SIF S
		PRINT  + 
	A += ABL:16 * 4
	PRINTS ABLNAME:16
	PRINTV 'LV,ABL:16,'(,ABL:16 * 4,')
	S = 1
ENDIF

;MARK:快乐刻印
IF MARK:1
	SIF S
		PRINT  + 
	A += MARK:1 * 2
	PRINTS MARKNAME:1
	PRINTV 'LV,MARK:1,'(,MARK:1 * 2,')
	S = 1
ENDIF

;PALAM:欲情
IF PALAM:5 < PALAMLV:1
	L = 0
ELSEIF PALAM:5 < PALAMLV:2
	L = 1
ELSEIF PALAM:5 < PALAMLV:3
	L = 2
ELSEIF PALAM:5 < PALAMLV:4
	L = 3
ELSEIF PALAM:5 < PALAMLV:5
	L = 4
ELSE
	L = 5
ENDIF
IF L
	SIF S
		PRINT  + 
	A += L * 1
	PRINTS PALAMNAME:5
	PRINTV 'LV,L,'(,L * 1,')
	S = 1
ENDIF

;害羞
IF TALENT:35
	PRINT  - 
	A -= 1
	PRINTS TALENTNAME:35
	PRINTV '(,1,')
	S = 1
ENDIF
;不怕污臭
IF TALENT:61
	SIF S
		PRINT  + 
	A += 1
	PRINTS TALENTNAME:61
	PRINTV '(,1,')
	S = 1
ENDIF
;反感污臭
IF TALENT:62
	PRINT  - 
	A -= 1
	PRINTS TALENTNAME:62
	PRINTV '(,1,')
	S = 1
ENDIF
;献身的
IF TALENT:63
	SIF S
		PRINT  + 
	A += 6
	PRINTS TALENTNAME:63
	PRINTV '(,6,')
	S = 1
ENDIF
;否定快感
IF TALENT:71
	PRINT  - 
	A -= 1
	PRINTS TALENTNAME:71
	PRINTV '(,1,')
	S = 1
ENDIF
;爱慕
IF TALENT:85 && ASSIPLAY == 0
	SIF S
		PRINT  + 
	A += 5
	PRINTS TALENTNAME:85
	PRINTV '(,5,')
	S = 5
ENDIF
;兽奸
IF TEQUIP:89 && TALENT:136 == 0
	SIF S
		PRINT  - 
	A -= 15
	PRINTS ITEMNAME:22
	PRINTV '(,10,')
	S = 1
ENDIF

Y = 0
;爱液の汚れ
SIF STAIN:PLAYER:0 & 1
	Y += 1
;精液の汚れ
SIF STAIN:PLAYER:0 & 4
	Y += 3
;アナルの汚れ
SIF STAIN:PLAYER:0 & 8
	Y += 7
;母乳の汚れ
SIF STAIN:PLAYER:0 & 16
	Y += 1
;尿の汚れ
SIF STAIN:PLAYER:0 & 32
	Y += 3

;兽奸の場合は汚れ7で固定
SIF TEQUIP:89
	Y = 7

SIF TALENT:61
	Y /= 3
SIF TALENT:62
	Y *= 2

;キスでは汚れがそれほど影響しない
Y /= 2

;汚れあり
IF Y
	PRINT  - 
	A -= Y
	;不怕污臭
	IF TALENT:61
		PRINT 脏、
		PRINTS TALENTNAME:61
	ELSEIF TALENT:62
		PRINT 脏、
		PRINTS TALENTNAME:62
	ELSE
		PRINT 脏、
	ENDIF
	PRINTV '(,Y,')
	S = 1
ENDIF


;合計を表示(15以上で実行)
PRINT  = 
PRINTV A

V = 15
SIF A < V
	PRINT  < 
SIF A == V
	PRINT  = 
SIF A > V
	PRINT  > 
PRINT 实行值
PRINTV V

WAIT

;実行できない
SIF A < V
	RETURN 0

$AUTO_SUCCESS

;SAVESTR:22 = 接吻
CALL TRAIN_MESSAGE_B

;-------------------------------------------------
;実行決定
;-------------------------------------------------
;-------------------------------------------------
;ソースの計算
;-------------------------------------------------
LOSEBASE:0 += 5
LOSEBASE:1 += 50

;上のほうで計算した汚れデータ
SOURCE:8 = Y*20 + 10

;ABL:侍奉精神をみる
IF ABL:16 == 0
	SOURCE:4 = 50
	SOURCE:5 = 10
	TIMES SOURCE:8 , 4.00
ELSEIF ABL:16 == 1
	SOURCE:4 = 150
	SOURCE:5 = 50
	TIMES SOURCE:8 , 2.50
ELSEIF ABL:16 == 2
	SOURCE:4 = 200
	SOURCE:5 = 100
	TIMES SOURCE:8 , 1.50
ELSEIF ABL:16 == 3
	SOURCE:4 = 250
	SOURCE:5 = 180
	TIMES SOURCE:8 , 1.00
ELSEIF ABL:16 == 4
	SOURCE:4 = 300
	SOURCE:5 = 300
	TIMES SOURCE:8 , 0.50
ELSE
	SOURCE:4 = 350
	SOURCE:5 = 500
	TIMES SOURCE:8 , 0.10
ENDIF

;ABL:技巧をみる
IF ABL:12 == 0
	TIMES SOURCE:4 , 0.50
	TIMES SOURCE:5 , 0.50
ELSEIF ABL:12 == 1
	TIMES SOURCE:4 , 0.80
	TIMES SOURCE:5 , 0.80
ELSEIF ABL:12 == 2
	TIMES SOURCE:4 , 1.00
	TIMES SOURCE:5 , 1.00
ELSEIF ABL:12 == 3
	TIMES SOURCE:4 , 1.50
	TIMES SOURCE:5 , 1.50
ELSEIF ABL:12 == 4
	TIMES SOURCE:4 , 2.50
	TIMES SOURCE:5 , 2.50
ELSE
	TIMES SOURCE:4 , 4.00
	TIMES SOURCE:5 , 4.00
ENDIF

;兽奸の場合はここで兽奸中毒他を判定して終了
IF TEQUIP:89
	IF ABL:39 == 0
		TIMES SOURCE:8 , 2.00
	ELSEIF ABL:39 == 1
		TIMES SOURCE:8 , 1.00
	ELSEIF ABL:39 == 2
		TIMES SOURCE:8 , 0.80
		TFLAG:100 = 1
	ELSEIF ABL:39 == 3
		SOURCE:3 = 100
		TIMES SOURCE:8 , 0.50
		TFLAG:100 = 1
	ELSEIF ABL:39 == 4
		SOURCE:3 = 300
		SOURCE:10 = 100
		TIMES SOURCE:8 , 0.30
		TFLAG:100 = 1
	ELSE
		SOURCE:3 = 800
		SOURCE:10 = 200
		TIMES SOURCE:8 , 0.10
		TFLAG:100 = 1
	ENDIF

	;犬と初吻
	IF CFLAG:16 == -1
		TFLAG:13 = 1
		CFLAG:16 = 998
		;屈服刻印２に相当
		TFLAG:200 = 2
	ENDIF

	RETURN 1
ENDIF

;プレイヤーのABL:技巧をみる
IF ABL:PLAYER:12 == 0
	SOURCE:3 = 100
	SOURCE:10 = 0
	A = 0
ELSEIF ABL:PLAYER:12 == 1
	SOURCE:3 = 150
	SOURCE:10 = 0
	A = 0
ELSEIF ABL:PLAYER:12 == 2
	SOURCE:3 = 200
	SOURCE:10 = 0
	A = 0
ELSEIF ABL:PLAYER:12 == 3
	SOURCE:3 = 300
	SOURCE:10 = 50
	A = 50
ELSEIF ABL:PLAYER:12 == 4
	SOURCE:3 = 500
	SOURCE:10 = 100
	A = 100
ELSE
	SOURCE:3 = 800
	SOURCE:10 = 200
	A = 300
ENDIF

;TALENT:爱慕をみる
SIF TALENT:85
	TIMES SOURCE:3 , 2.00

;-------------------------------------------------
;汚れの処理
;-------------------------------------------------
;奴隷の口⇔調教者の口の汚れが移動
STAIN:0 |= STAIN:PLAYER:0
STAIN:PLAYER:0 |= STAIN:0

;-------------------------------------------------
;経験上昇
;-------------------------------------------------
;レズ・ホモ経験
IF TALENT:122 == 0 && TALENT:PLAYER:122 == 0
	PRINTS EXPNAME:40
	PRINTL +3
	EXP:40 += 3
ELSEIF TALENT:122 == 1 && TALENT:PLAYER:122 == 1
	PRINTS EXPNAME:41
	PRINTL +3
	EXP:41 += 3
ENDIF

;爱情经验
;基本値
E = 1

;爱かつ主人とのキスなら基本値に＋２
SIF TALENT:85 && ASSIPLAY == 0
	E += 2

;初吻
IF CFLAG:16 == -1
	TFLAG:13 = 1
	CFLAG:16 =  1
	CSTR:4 = %SAVESTR:PLAYER%
	;初吻の場合は入る爱情经验にボーナス+20
	E += 20
	IF ASSIPLAY && ASSI  > 0
		R = NO:ASSI
		RELATION:R += 10
		SIF RELATION:R == 10
			RELATION:R = 110
		SIF RELATION:R > 200
			RELATION:R = 200
	ENDIF
	;屈服刻印１に相当
	TFLAG:200 = 1
ENDIF
;調教者の初吻
IF CFLAG:PLAYER:16 == -1
	CFLAG:PLAYER:16 =  1
	CSTR:PLAYER:4 = %SAVESTR:TARGET%
ENDIF

IF CFLAG:2 >= 1000 && ASSIPLAY == 0
	PRINTFORML %EXPNAME:23%+{E}
	EXP:23 += E
ENDIF
E = 0

;-------------------------------------------------
;その他の処理
;-------------------------------------------------
TFLAG:100 = 1

SIF ASSIPLAY == 0
	TFLAG:30 += 1

RETURN 1
