﻿;=================================================
;アイテム購入用スクリプト
;=================================================
;eratohoRR1317系列スクリプトを流用し改造
;>複数調教と大人数対応を合成。複数調教用キャラカードチェックを削除
;>コマンドメニュー及び条件を変更
;>｢@SELECT_TARGET｣を削除
;>｢@SHOW_CHALADATA｣＝ステータス表示
;>｢@CHARA_SALE｣＝キャラ売却
;>｢@SALEITEM_CHECK｣｢@USE_ITEM｣＝アイテムの購入と使用

;eraIM@Sから流用（eramaou）

;=================================================
;アイテム購入・売却処理画面
;=================================================
@ITEM_SHOP
#DIM ICOUNT_A
#DIM ICOUNT_B
CUSTOMDRAWLINE =
PRINTL 黑市商人
PRINTL 《可以购买用于调教的物品》
DRAWLINE
PRINTV DAY+1
PRINT 日
IF TIME == 0
	PRINTL  午前
ELSE
	PRINTL  午后
ENDIF

PRINTFORML [所持金:{MONEY}点]

SETCOLORBYNAME LightSalmon
PRINTFORML [技巧Lv:{ABL:MASTER:12}]
PRINTL [调教道具一览]
RESETCOLOR
ICOUNT_B = 0
FOR ICOUNT_A,0,24
	SIF ITEM:ICOUNT_A == 0
		CONTINUE
	PRINTFORM [%ITEMNAME:ICOUNT_A,10,LEFT%]
	ICOUNT_B += 1
	SIF ICOUNT_B % 5 == 0
		PRINTL 
NEXT
SIF ICOUNT_B % 5 > 0
	PRINTL 
SETCOLORBYNAME LightSalmon
PRINTL [消耗型调教道具一览]
RESETCOLOR
ICOUNT_B = 0
FOR ICOUNT_A,24,36
	SIF ITEM:ICOUNT_A == 0
		CONTINUE
	SIF ICOUNT_A >= 29 && ICOUNT_A <= 31
		CONTINUE
	PRINTFORM [%ITEMNAME:ICOUNT_A + @"(所持:{ITEM:ICOUNT_A})",20,LEFT%]
	ICOUNT_B += 1
	SIF ICOUNT_B % 5 == 0
		PRINTL 
NEXT
SIF ICOUNT_B % 5 > 0
	PRINTL 
DRAWLINE

CALL SALEITEM_CHECK
;所持点を一時保存
TFLAG:15 = MONEY

$INPUT_LOOP
;以下に自動的に売り物が表示される
PRINT_SHOPITEM

PRINTL 《请输入要购买的道具的编号》

DRAWLINE
PRINTLC [998] - 陷阱
PRINTLC [999] - 返回
PRINTL
;
;=================================================
;アイテム購入時に呼び出される関数
;=================================================
@EVENTBUY
;購入確認
;複数持てるアイテム

IF BOUGHT == 24 || BOUGHT == 25 || BOUGHT == 26 || BOUGHT == 27 || BOUGHT == 28 || BOUGHT == 34 || BOUGHT == 35 || BOUGHT == 53 || BOUGHT == 55 || BOUGHT >= 60 && BOUGHT != 90
	CALL BUY_PLURAL
	RETURN 1
;その場で使うアイテム
ELSEIF BOUGHT == 29 || BOUGHT == 30 || BOUGHT == 31 || BOUGHT == 32 || BOUGHT == 33 || BOUGHT == 40 || BOUGHT == 41
	CALL USE_ITEM
	ITEM:BOUGHT = 0
	RETURN 1
ENDIF

$INPUT_LOOP
PRINTFORML 确定购买%ITEMNAME:BOUGHT%？
PRINTL [0] - 好的
PRINTL [1] - 不要
INPUT
IF RESULT == 1
	ITEM:BOUGHT -= 1
	BOUGHT = 0
	;退避していた点を戻す
	MONEY = TFLAG:15
	EX_FLAG:4444 = TFLAG:15 - 8766
	RETURN 0
ELSEIF RESULT != 0
	GOTO INPUT_LOOP
ENDIF

PRINTFORML 《购买了%ITEMNAME:BOUGHT%》
SIF BOUGHT == 0
    EX_FLAG:4444 -= 200
SIF BOUGHT == 1
    EX_FLAG:4444 -= 500
SIF BOUGHT == 2
    EX_FLAG:4444 -= 2000
SIF BOUGHT == 3
    EX_FLAG:4444 -= 5000
SIF BOUGHT == 4
    EX_FLAG:4444 -= 2000
SIF BOUGHT == 5
    EX_FLAG:4444 -= 4000
SIF BOUGHT == 6
    EX_FLAG:4444 -= 7000
SIF BOUGHT == 7
    EX_FLAG:4444 -= 2000
SIF BOUGHT == 8
    EX_FLAG:4444 -= 3000
SIF BOUGHT == 9
    EX_FLAG:4444 -= 1000
SIF BOUGHT == 10
    EX_FLAG:4444 -= 200
SIF BOUGHT == 11
    EX_FLAG:4444 -= 2000
SIF BOUGHT == 12
    EX_FLAG:4444 -= 4000
SIF BOUGHT == 13
    EX_FLAG:4444 -= 5000
SIF BOUGHT == 14
    EX_FLAG:4444 -= 3000
SIF BOUGHT == 15
    EX_FLAG:4444 -= 8000
SIF BOUGHT == 16
    EX_FLAG:4444 -= 30000
SIF BOUGHT == 17
    EX_FLAG:4444 -= 1500
SIF BOUGHT == 18
    EX_FLAG:4444 -= 5000
SIF BOUGHT == 19
    EX_FLAG:4444 -= 10000
SIF BOUGHT == 20
    EX_FLAG:4444 -= 7000
SIF BOUGHT == 21
    EX_FLAG:4444 -= 50000
SIF BOUGHT == 22
    EX_FLAG:4444 -= 3000
SIF BOUGHT == 23
    EX_FLAG:4444 -= 10000
SIF BOUGHT == 37
    EX_FLAG:4444 -= 1000

;素質アイテム・ラブダイナミックス
IF BOUGHT == 38
	EX_FLAG:4444 -= 100000
	PRINTFORML 《%NAME:MASTER%掌握了【%TALENTNAME:91%】》
	TALENT:MASTER:91 = 1
	ITEM:BOUGHT = 0
ENDIF
;素質アイテム【秘密知識】
IF BOUGHT == 39
	EX_FLAG:4444 -= 100000
	TALENT:MASTER:325 = 1
	ITEM:BOUGHT = 0
ENDIF
;素質アイテム【调合知识】
IF BOUGHT == 42
	EX_FLAG:4444 -= 40000
	TALENT:MASTER:55 = 1
	ITEM:BOUGHT = 0
ENDIF
;素質アイテム【技巧LV】
IF BOUGHT == 52
	CALL TECHNIQUE_OF_MASTER_UP
	;EASYなら1つで1Lv上がる
	;IF FLAG:5 == 1
	;	CALL TECHNIQUE_OF_MASTER_UP
	;NORMAL/EXTRAならLv3までは1つで1Lv上がり、それ以降は技巧Lv-1分必要になる
	;ELSEIF FLAG:5 == 2 || FLAG:5 == 9
	;	IF ABL:MASTER:12 >= 3
	;		F = ABL:MASTER:12 - 1
	;		CALL TECHNIQUE_OF_MASTER
	;	ELSE
	;		CALL TECHNIQUE_OF_MASTER_UP
	;	ENDIF
	;HARDならLv2までは1つで1Lv上がり、それ以降は技巧Lv*2分必要になる
	;ELSEIF FLAG:5 == 3
	;	IF ABL:MASTER:12 >= 2
	;		F = ABL:MASTER:12
	;		F *= 2
	;		CALL TECHNIQUE_OF_MASTER
	;	ELSE
	;		CALL TECHNIQUE_OF_MASTER_UP
	;	ENDIF
	;POWERFULならLv1までは1つで1Lv上がり、それ以降は技巧Lv*5分必要になる
	;ELSEIF FLAG:5 == 4
	;	IF ABL:MASTER:12 >= 1
	;		F = ABL:MASTER:12
	;		F *= 5
	;		CALL TECHNIQUE_OF_MASTER
	;	ELSE
	;		CALL TECHNIQUE_OF_MASTER_UP
	;	ENDIF
	;PHANTASM以上なら技巧Lv*100分必要になる
	;ELSE
	;	F = ABL:MASTER:12
	;	F *= 100
	;	CALL TECHNIQUE_OF_MASTER
	;ENDIF
ENDIF


;素質アイテム【淫魔知识】
IF BOUGHT == 54
	EX_FLAG:4444 -= 100000
	TALENT:MASTER:327 = 1
	ITEM:BOUGHT = 0
	ITEMSALES:54 = 0
	PRINTL * 可以购买淫魔的陷阱了 *
ENDIF

;素質アイテム【魔虫知识】
IF BOUGHT == 56
	EX_FLAG:4444 -= 10000
	TALENT:MASTER:328 = 1
	ITEM:BOUGHT = 0
	ITEMSALES:56 = 0
	PRINTL * 一些陷阱被强化了 *
ENDIF


WAIT

RETURN 1
;
;-------------------------------------------------
;アイテム出現条件
;-------------------------------------------------
@SALEITEM_CHECK
;初期状態で販売されているアイテム
REPEAT 24
	ITEMSALES:COUNT = 1
REND

;围裙、电极接头、拘束衣スーツ
ITEMSALES:19 = 1
ITEMSALES:21 = 1
ITEMSALES:23 = 1

;营养剂は【调合知识所持】所持でEXTRAでのみ
ITEMSALES:30 = 0
M = 0
IF TALENT:MASTER:55
	M = 1
ENDIF
REPEAT CHARANUM
	SIF COUNT == 0
		CONTINUE
	IF CFLAG:COUNT:1 >= 1 && ISASSI:COUNT == 1
		SIF TALENT:COUNT:55
			M = 1
	ENDIF
REND
IF M == 1
	ITEMSALES:30 = 1
ENDIF

;ビデオカメラ・搾乳器・肛珠
ITEMSALES:6 = 1
ITEMSALES:17 = 1
ITEMSALES:20 = 1

;もう持っている非消耗アイテムを消す
REPEAT 24
	SIF ITEM:COUNT == 1
		ITEMSALES:COUNT = 0
REND

;消耗系アイテムを追加
ITEMSALES:24 = 1
ITEMSALES:25 = 1
ITEMSALES:34 = 1
ITEMSALES:35 = 1
;ビデオカメラがあればビデオテープを販売
SIF ITEM:6
	ITEMSALES:28 = 1

;主人か助手の誰かが【调合知识】を持っているなら
;薬品系アイテム追加
ITEMSALES:26 = 0
ITEMSALES:27 = 0
ITEMSALES:29 = 0
ITEMSALES:31 = 0
ITEMSALES:40 = 0
ITEMSALES:41 = 0
M = 0
IF TALENT:MASTER:55
	M = 1
ENDIF
REPEAT CHARANUM
	SIF COUNT == 0
		CONTINUE
	IF CFLAG:COUNT:1 >= 1 && ISASSI:COUNT == 1
		SIF TALENT:COUNT:55
			M = 1
	ENDIF
REND
IF M == 1
	ITEMSALES:26 = 1
	ITEMSALES:27 = 1
	ITEMSALES:29 = 1
	ITEMSALES:31 = 1
	ITEMSALES:40 = 1
	ITEMSALES:41 = 1		
ENDIF

;熏香はHARDでは1日3個、POWERFULでは1個まで
;SIF (FLAG:5 == 3 && FLAG:61 >= 3) || (FLAG:5 == 4 && FLAG:61 >= 1)
;	ITEMSALES:31 = 0

;主人か助手の誰かが【秘密知識】を持っているなら
;秘密アイテム追加
ITEMSALES:33 = 0
M = 0
IF TALENT:MASTER:325
	M = 1
ENDIF
REPEAT CHARANUM
	SIF COUNT == 0
		CONTINUE
	IF CFLAG:COUNT:1 >= 1 && ISASSI:COUNT == 1
		SIF TALENT:COUNT:325
			M = 1
	ENDIF
REND
IF M == 1
	ITEMSALES:33 = 1
ENDIF

;コンドーム、ローション、媚薬、利尿剤、ビデオテープ、ピアスリング、观战卷は99個まで
SIF ITEM:24 >= 99
	ITEMSALES:24 = 0
SIF ITEM:25 >= 99
	ITEMSALES:25 = 0
SIF ITEM:26 >= 99
	ITEMSALES:26 = 0
SIF ITEM:27 >= 99
	ITEMSALES:27 = 0
SIF ITEM:28 >= 99
	ITEMSALES:28 = 0
SIF ITEM:34 >= 99
	ITEMSALES:34 = 0
SIF ITEM:35 >= 99
	ITEMSALES:35 = 0
	
;好感测定仪
ITEMSALES:37 = 1
SIF ITEM:37
	ITEMSALES:37 = 0


;素質アイテム「ラブダイナミックス」
ITEMSALES:38 = 1
SIF TALENT:MASTER:91 == 1
	ITEMSALES:38 = 0
;ラブダイナミックスはHARD・POWERFULでは販売しない
SIF FLAG:5 == 3 || FLAG:5 == 4
	ITEMSALES:38 = 0

;素質アイテム【秘密知識】
ITEMSALES:39 = 1
SIF TALENT:MASTER:325 == 1
	ITEMSALES:39 = 0

;素質アイテム【调合知识】
ITEMSALES:42 = 1
SIF TALENT:MASTER:55 == 1
	ITEMSALES:42 = 0

;素質アイテム【技巧等级】
ITEMSALES:52 = 1
;技巧Lvの上限は10、また堕とした人数+2までしか上げられない
SIF ABL:MASTER:12 >= 10 || ABL:MASTER:12 > FLAG:30 + 1
	ITEMSALES:52 = 0
;素質アイテム【经验值】
ITEMSALES:53 = 1
;-------------------------------------------------
;複数個所持可能なアイテムの処理
;-------------------------------------------------
@BUY_PLURAL
#DIM NO_PAGE
#DIM L_LCOUNT
;现在の購入可能数、Bは所持可能最大数（现在100個）、Cは所持金によるもの
;Aはアイテムの単価、DはBとCのうち小さい方、EはB<=Cであれば0、B>Cであれば1
;陷阱の値段はDUNGEON_TRAP参照
SIF BOUGHT == 24
	A = 100
SIF BOUGHT == 25
	A = 200
SIF BOUGHT == 26
	A = 2000
SIF BOUGHT == 27
	A = 1000
SIF BOUGHT == 28
	A = 500
SIF BOUGHT == 34
	A = 5000
SIF BOUGHT == 35
	A = 700
SIF BOUGHT == 53
	A = 1000
SIF BOUGHT == 55
	A = 5000

IF BOUGHT >= 60 && BOUGHT < 90
	P = BOUGHT
	CALL TRAP_PRICE
	A = RESULT
ENDIF

SIF BOUGHT == 91
	A = 100
B = 100-ITEM:BOUGHT
C = MONEY/A+1
IF B <= C && BOUGHT != 53
	D = B
	E = 0
ELSE
	D = C
	E = 1
ENDIF

SIF BOUGHT == 55 && (FLAG:85+D > CFLAG:0:9)
	D = CFLAG:0:9 - FLAG:85

DRAWLINE
PRINTFORM 要买多少%ITEMNAME:BOUGHT%？ （1-
PRINTV D
PRINTL 、0返回）
PRINTFORM [0] - [1] - [5] - [10] - [20] - [

;全財産の半分を費やす
IF D/2 > 20
	PRINTV D/2
	PRINT ] - [
ENDIF

PRINTV D
PRINTL ] 买空钱包

$INPUT_LOOP
INPUT
IF RESULT == 0
	ITEM:BOUGHT -= 1
	;払い戻し
	MONEY += A
	;EX_FLAG:4444 += A
	RETURN 0
ELSEIF RESULT < 0
	GOTO INPUT_LOOP
ELSEIF RESULT > D
	IF RESULT > D && E == 0
		PRINTL 不能持有这么多
	ELSEIF RESULT > D && E == 1
		PRINTL 哪怕是魔王，也不能赊账啊
	ENDIF
	DRAWLINE
	PRINTFORM 要购买多少%ITEMNAME:BOUGHT%？ （1-
	PRINTV D
	PRINTL 、0返回）
	PRINTFORM [0] - [1] - [5] - [10] - [20] - [
	PRINTV D
	PRINTL ] 买空钱包
	GOTO INPUT_LOOP
ELSEIF RESULT == 1
    EX_FLAG:4444 -= A
	PRINTFORML 《购买了%ITEMNAME:BOUGHT%》
	WAIT
ELSE
	PRINTFORML 《购买了{RESULT}个%ITEMNAME:BOUGHT%》
	WAIT
	ITEM:BOUGHT += RESULT-1
	MONEY -= A*(RESULT-1)
	EX_FLAG:4444 -= A* RESULT
ENDIF

IF BOUGHT == 53
	;经验值強化アイテム
	ITEM:BOUGHT = 0
	ITEMSALES:BOUGHT = 1
	E = RESULT * 10
	
$INPUT_LOOP_MENU
DRAWLINE

	PRINTFORML 要让谁使用%ITEMNAME:BOUGHT%？
	
	CALL LIFE_LIST(NO_PAGE,0)
	
PRINTLC [1000] - 上一页
PRINTLC [1001] - 下一页
	$INPUT_LOOP_2
	INPUT


IF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO INPUT_LOOP_MENU
	ENDIF
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * 20 <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO INPUT_LOOP_MENU
	ENDIF
ENDIF
	SIF RESULT < 0 || RESULT >= CHARANUM
		GOTO INPUT_LOOP_2
	IF CFLAG:RESULT:1 != 0
		PRINTW 此人物尚不可选择
		GOTO INPUT_LOOP_2
	ENDIF
	SIF RESULT == 0
		RESULT = MASTER
	EXP:RESULT:80 += E
	PRINTFORMW 得到了{E}点经验值
ENDIF

SIF RESULT == 1000 || RESULT == 1001
	GOTO INPUT_LOOP_MENU

;素質アイテム【陷阱LV】
IF BOUGHT == 55
	LOCAL = FLAG:85 + RESULT
	IF LOCAL <= CFLAG:0:9 || LOCAL <= CFLAG:MASTER:9
		PRINTFORML 《陷阱上升到Lv{RESULT}》
		FLAG:85 = LOCAL
	ELSE
		PRINTL *必须先提高魔王的等级！*
		ITEM:BOUGHT -= 1
		BOUGHT = 0
		;退避していた点を戻す
		MONEY = TFLAG:15
		EX_FLAG:4444 = TFLAG:15 - 8766
		RETURN 0
	ENDIF
	ITEM:BOUGHT = 0
	ITEMSALES:BOUGHT = 1
ENDIF


IF BOUGHT == 91
	;指輪
	ITEM:BOUGHT = 0
	ITEMSALES:BOUGHT = 1
	ITEM:300 += RESULT
	IF ITEM:300 > 99
		X = ITEM:300 - 99
		MONEY += X * 100
		EX_FLAG:4444 += X * 100
		ITEM:300 = 99
		PRINTW 退还了多余的戒指
	ENDIF
ENDIF

RETURN 0

;-------------------------------------------------
;その場で使用するアイテムの処理
;-------------------------------------------------
@USE_ITEM
#DIM NO_PAGE
#DIM L_LCOUNT
$INPUT_LOOP_MENU
DRAWLINE
;アイテムの効果を表示する
SIF BOUGHT == 29
	PRINTFORML %ITEMNAME:BOUGHT%:从寄生状态中恢复
SIF BOUGHT == 30
	PRINTFORML %ITEMNAME:BOUGHT%:回复体力
SIF BOUGHT == 31
	PRINTFORML %ITEMNAME:BOUGHT%:否定点数减半
SIF BOUGHT == 33
	PRINTFORML %ITEMNAME:BOUGHT%:延续对象的寿命
SIF BOUGHT == 40
	PRINTFORML %ITEMNAME:BOUGHT%:增加怀孕的几率
SIF BOUGHT == 41
	PRINTFORML %ITEMNAME:BOUGHT%:使对象开始生长阴毛

PRINTFORML 要让谁使用%ITEMNAME:BOUGHT%？
DRAWLINE
CALL LIFE_LIST,NO_PAGE

PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页

$INPUT_LOOP
INPUT

IF RESULT == 999
	ITEM:BOUGHT = 0
	;払い戻し
	SIF BOUGHT == 29
		MONEY += 500
		;EX_FLAG:4444 += 500
	SIF BOUGHT == 30
		MONEY += 1000
		;EX_FLAG:4444 += 1000
	;払い戻し
	SIF BOUGHT == 31
		MONEY += 3000
		;EX_FLAG:4444 += 3000
	;払い戻し
	SIF BOUGHT == 33
		MONEY += 100000
		;EX_FLAG:4444 += 100000
	;払い戻し
	SIF BOUGHT == 40
		MONEY += 2000
		;EX_FLAG:4444 += 2000
	;払い戻し
	SIF BOUGHT == 41
		MONEY += 2000
		;EX_FLAG:4444 += 2000
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO INPUT_LOOP_MENU
	ENDIF
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * 20 <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO INPUT_LOOP_MENU
	ENDIF
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;主人公は排除,绑定当前魔王
ELSEIF  RESULT == 0
	RESULT = MASTER
;売却済み・臨死中のキャラは排除
ELSEIF  BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
;体力ＭＡＸのキャラにパワビタは使えない
ELSEIF BOUGHT == 30 && BASE:RESULT:0 == MAXBASE:RESULT:0
	PRINTFORML %NAME:RESULT%的体力已经达到了最大值
	WAIT
	GOTO INPUT_LOOP
;否定点数を持ってないキャラに熏香は使えない
ELSEIF BOUGHT == 31 && JUEL:RESULT:100 < 1
	PRINTFORML %NAME:RESULT%的%PALAMNAME:100%点数已经不能再减少了
	WAIT 
	GOTO INPUT_LOOP
;【爱慕】持ちかつ寿命持ちでないとWG电池は使えない
ELSEIF BOUGHT == 33 && (BASE:RESULT:10 == 0 || TALENT:RESULT:85 == 0)
	PRINTFORML %NAME:RESULT%已经不受寿命限制了
	WAIT 
	GOTO INPUT_LOOP
;妊娠中・育儿中のキャラに排卵促進剤は使えない
ELSEIF BOUGHT == 40 && TALENT:RESULT:153 || TALENT:RESULT:154
	IF TALENT:RESULT:153
		PRINTFORML 怀孕中的%NAME:RESULT%不能使用%ITEMNAME:BOUGHT%
	ELSEIF TALENT:RESULT:154
		PRINTFORML 育儿中的%NAME:RESULT%不能使用%ITEMNAME:BOUGHT%
	ENDIF
	WAIT 
	GOTO INPUT_LOOP
ENDIF

SIF RESULT == 1000 || RESULT == 1001
	GOTO INPUT_LOOP_MENU

;寄生回復
IF BOUGHT == 29
    EX_FLAG:4444 -= 500
	CALL ITEM_DETOX,RESULT
	WAIT
;体力回復
ELSEIF BOUGHT == 30
	EX_FLAG:4444 -= 1000
	PRINTFORML 《%SAVESTR:RESULT%的体力恢复了300点》
	BASE:RESULT:0 += 300
	SIF BASE:RESULT:0 > MAXBASE:RESULT:0
		BASE:RESULT:0 = MAXBASE:RESULT:0
	WAIT
;否定点数半減
ELSEIF BOUGHT == 31
	EX_FLAG:4444 -= 3000
	PRINTFORML 《%SAVESTR:RESULT%的否定点数减半了》
	PRINTFORML  否定点数:{JUEL:RESULT:100} -> {JUEL:RESULT:100 / 2}
	JUEL:RESULT:100 /= 2
	FLAG:61 += 1
	WAIT
;寿命制限削除
ELSEIF BOUGHT == 33
	EX_FLAG:4444 -= 100000
	PRINTFORML 《%SAVESTR:RESULT%不再有寿命限制了》
	BASE:RESULT:10 = 0
	WAIT
;排卵促進
ELSEIF BOUGHT == 40
	EX_FLAG:4444 -= 2000
	PRINTFORML 《%SAVESTR:RESULT%更容易怀孕了》
	CFLAG:RESULT:109 = 1
	WAIT
;生毛剂
ELSEIF BOUGHT == 41
	EX_FLAG:4444 -= 2000
	IF TALENT:RESULT:311 > 200
		PRINTFORMW …涂上之后似乎没什么效果
		TALENT:RESULT:311 = 201
	ELSE
		PRINTFORML 《%SAVESTR:RESULT%可以长出更多阴毛了》
		TALENT:RESULT:311 += 50
	;白虎を消す
	TALENT:RESULT:125 = 0
	ENDIF
	WAIT
ENDIF


;-------------------------------------------------
;素質アイテム【技巧等级】処理
;-------------------------------------------------
@TECHNIQUE_OF_MASTER
IF FLAG:33 == F - 1
	CALL TECHNIQUE_OF_MASTER_UP
ELSE
	FLAG:33 += 1
	PRINTL 
	PRINTFORML 为了提高技巧LV，需要 {F - FLAG:33} 個。
	PRINTL 买光剩余的吗？
	PRINTL  [0] - 好的
	PRINTL  [1] - 不要
	$INPUT_LOOP
	INPUT
	IF RESULT == 0
		IF MONEY < (F - FLAG:33) * 5000
			PRINTL 哪怕是魔王大人，也是不能赊账的
		ELSE
			MONEY -= (F - FLAG:33) * 5000
			EX_FLAG:4444 -= (F - FLAG:33) * 5000
			CALL TECHNIQUE_OF_MASTER_UP
		ENDIF
	ELSEIF RESULT == 1
		RETURN 0
	ELSE
		GOTO INPUT_LOOP
	ENDIF
ENDIF

@TECHNIQUE_OF_MASTER_UP
ABL:MASTER:12 += 1
FLAG:33 = 0
ITEM:BOUGHT = 0
EX_FLAG:4444 -= 5000
PRINTFORML 《%NAME:MASTER%的技巧LV{ABL:MASTER:12}了》

;-------------------------------------------------
;販売アイテムフラグ全削除処理
;-------------------------------------------------
@CLEAR_SHOP

;一旦購入可能アイテムを空に
REPEAT 300
	ITEMSALES:COUNT = 0
REND

;-------------------------------------------------
;虫下し
;-------------------------------------------------
@ITEM_DETOX,ARG

PRINTFORML 《%SAVESTR:ARG%从寄生状态中恢复了》

SIF TALENT:ARG:190
	PRINTFORM *从私处寄生中恢复* 
SIF TALENT:ARG:191
	PRINTFORM *从肛门寄生中恢复* 
SIF TALENT:ARG:192
	PRINTFORM *蠕虫被排出了* 
SIF TALENT:ARG:193
	PRINTFORM *肛门虫被排出了* 
TALENT:ARG:190 = 0
TALENT:ARG:191 = 0
TALENT:ARG:192 = 0
TALENT:ARG:193 = 0

PRINTL  

RETURN 0

;
;