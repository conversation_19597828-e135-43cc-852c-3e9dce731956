﻿用户定义变量
==================================================

预编译指令#DIM和#DIM用来定义变量。
变量定义一般发生在函数中，
此外也可以在头文件（ERH）中定义广域变量。



书写格式（局部变量）
==================================================

#DIM(S) <变量名>, <元素数>,{,<元素数>{,<元素数>}}

<变量名> 与函数命名规则相同，以非数字的字符开始的任意字符串
<元素数> 100万以内。默认1。最多3维。

	#DIM定义数值变量，#DIMS定义字符串变量。
	初始值 0。
	@FIND_CSTR(KEY, VALUE)
	#FUNCTION
	#DIM LCOUNT
	#DIM KEY
	#DIMS VALUE
	SIF KEY < 0 || KEY >= VARSIZE("CSTR")
		RETURNF -1
	FOR LCOUNT, 0, CHARANUM
		SIF LCOUNT == MASTER
			CONTINUE
		SIF CSTR:LCOUNT:KEY == VALUE
			RETURNF LCOUNT
	NEXT
	

	
初始值设定
==================================================
也可以在声明变量的同时赋值。

	;省略元素数量，HOGE元素数量3
	#DIM HOGE = 1,2,3

	;不省略元素数量，PUGE元素数量100
	#DIM PUGE,100 = 4,5,6

	;错误（初始值的数量超出指定的元素数）
	#DIM HIGE,1 = 7,8,9

	;文字列変数でも可能（文字列式で指定）
	#DIMS SHOGE = "A", "B", "C"
	
	
动态变量
==================================================

#DIM(S) DYNAMIC <变量名>,<元素数>



常数
==================================================

CONST不能与GLOABL, SAVEDATA, REF, DYNAMIC关键字同时使用。

	;定义了一元常数数组
	#DIM CONST HOGE = 1,2,3

	;错误（初始值的数量与元素数量不一致）
	#DIM CONST PUGE,100 = 4,5,6

	;文字列変数でも可能（文字列式で指定）
	#DIMS CONST SHOGE = "A", "B", "C"
	
	
引用型变量
==================================================

数值型1~3元数组，字符串1~3元数组的声明

	#DIM REF HOGE1DIM,0
	#DIM REF HOGE2DIM,0,0
	#DIM REF HOGE3DIM,0,0,0
	#DIMS REF PUGE1DIM,0
	#DIMS REF PUGE2DIM,0,0
	#DIMS REF PUGE3DIM,0,0,0
	
一元数组的情况下可以省略0。
引用型变量用于函数传递引用参数。
	
	
	
书写格式（广域变量）
==================================================
见 头文件（ERH）
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	