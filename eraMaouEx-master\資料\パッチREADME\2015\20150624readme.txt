﻿

◆パッチの目的

欠点
・1階層ばかりうろついてテンポが悪い！

原因
・勇者の目標階層設定がダンジョンレベルや所持金の影響を受けて、条件が揃わない限り1階層になることが多い

改善
・前回の探索で到達した階層+1を目標に据えるようにして、所持金やダンジョンレベルの差の影響を無くす

結論：勇者が自力でダンジョンを切り開いていけるようにシミュレーションできたら幸い

◆前回の到達階層+1を目標に設定
DUNGEON_TOWN.ERB
パーティの最低レベルや所持金は考えないように変更

◆到達深度が減った場合、目標を修正するように
DUNGEON.ERB
CFLAG:520を減少式から現在階層と比較式に変更
挫折した階層を記憶するように修正


◆ついでにバグフィックス
DUNGEON_TOWN.ERB 343行
LOCALの引数が違っていたバグ修正

◆ついでに戦闘結果の情報を改良
DUNGEON.ERB
何階攻略中なのか、等

2015/6/24 maou「」





