﻿Emuera有一个简单的宏功能。
Emuera在等待输入的时候，可以输入一些特殊记号来模拟按键输入。
“\e”表示输入了一次[Esc]键，与鼠标右键一样，Emuera会直接跳转到下次输入。
“\n”表示输入了一次[Enter]键，“\n”前面的文本会作为输入值提交。
例如“0\e\n1\e\n”，相当于“输入0，跳到下次输入，输入1，跳到下次输入”。
此外，“(~~)*n”表示同一个输入连续进行n次。
例如“(0\e\n)*3”相当于“0\e\n0\e\n0\e\n”，连续输入三次0。
在执行宏的过程中可以通过[Esc]键来终止执行。


创建的宏可以保存起来并与[F1]~[F12]键相关联。
[Shift] + [Fn]即可将当前输入的内容保存到[Fn]键。
之后再按[F1]~[F12]就可以调用保存的宏了。
Emuera关闭后，宏将保存到macro.txt中。
（注意必须启用设置中的『启用键盘宏』选项）


另外，[Ctrl] + [0~9]可以切换宏组。
（Emuera启动后使用的是第0组宏）
在输入框的右键菜单中你可以看到详细的每个宏与宏组。


或者你也可以直接编辑macro.txt。