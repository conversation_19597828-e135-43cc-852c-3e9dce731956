﻿;eraIM@Sから導入しました

;DAY:1は现在の月
;DAY:2は现在の日
;２月のうるう月は考えてません
;存在しない日付を弾く部分、初心者丸出しの処理です……
;もっとスマートなやり方がきっとあるはずorz
;-------------------------------------------------
;各日の末日ならば月が替わる
;EVENT_TURNEND.ERBから呼び出される関数
;-------------------------------------------------
@EVENT_NEXTMONTH
#DIM AGE_COUNT
IF DAY:1 == 2
	DAY:1 = DAY:1 + 1
	DAY:2 = 1
	PRINTFORML 明天就是{DAY:1}月了，是个适合调教的月份呢……
ELSEIF DAY:2 > 30 && ( DAY:1 == 4 || DAY:1 == 6 || DAY:1 == 9 || DAY:1 == 11 )
	DAY:1 = DAY:1 + 1
	DAY:2 = 1
	PRINTFORML 明天就是{DAY:1}月了，是个适合调教的月份呢……
ELSEIF DAY:2 > 31 && ( DAY:1 == 1 || DAY:1 == 3 || DAY:1 == 5 || DAY:1 == 7 || DAY:1 == 8 || DAY:1 == 10 )
	DAY:1 = DAY:1 + 1
	DAY:2 = 1
	PRINTFORML 明天就是{DAY:1}月了，是个适合调教的月份呢……
ELSEIF DAY:2 > 31 && DAY:1 == 12
	DAY:1 = 1
	DAY:2 = 1
	PRINTFORML 明天就是新一年的开始了，再努力地把邪恶传播到各处吧！
	FOR AGE_COUNT, 1, CHARANUM
		CFLAG:AGE_COUNT:452 += 1
		CALL HUMAN_AGE_GENERATE , CFLAG:AGE_COUNT:452 , AGE_COUNT
		CFLAG:AGE_COUNT:451 = RESULT
		RESULT = 0
	NEXT	
ENDIF	


