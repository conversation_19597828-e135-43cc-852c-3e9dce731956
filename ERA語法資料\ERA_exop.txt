﻿==一般运算==

=== 算术运算 ===

下面的5个算术运算符都是二项运算符。

; +
: 数值的加法运算，以及字符串的连接运算。
: 数值与字符串的相加会抛出错误。
; -
: 数值的减法运算。
; *
: 数值的乘法运算。
: 字符串与数值相乘表示字符串的重复运算。
; /
: 数值的整除运算。
; %
: 数值的取余运算。

=== 逻辑运算 ===

以下逻辑运算符除<code>!</code>是单项运算符以外都是二项运算符。

; !
: 逻辑非（NOT）。
; &&
: 逻辑和（AND）。
; ||
: 逻辑或（OR）。
;^^
: 逻辑异或（XOR）。
;!&
:逻辑与非（NAND）。
;!|
: 逻辑或非（NOR）。

=== 位运算 ===
以下位运算符除<code>~</code>是单项运算符以外都是二项运算符。
; ~
: 按位取反（NOT）
; &
: 按位与（AND）
; |
: 按位或（OR）
; ^
: 按位异或（XOR）
; <<
: 左移
; >>
: 右移


=== 比较运算 ===
; ==
: 相等判断
; !=
: 不等判断
; <  >  <=  >=
: 大于、小于、小于等于、大于等于

以上的比较运算既可以比较两个数值，也可以比较两个字符串。


== 三元判断运算 ==

三元判断书写格式如下：
 <条件> ? <真值> # <假值>
当值为字符串时，需要额外添加<code>\@~\@</code>：
 \@ <条件> ? <真值> # <假值> \@

对于赋值语句
 <数值变量> = <条件> ? <真值> # <假值>
 <字符串变量> = \@ <条件> ? <真值> # <假值> \@
 
等同于
 	IF <条件>
 		<数值变量> = <真值>
 		;或 <字符串变量> '= <真值>
 	ELSE
 		<数值变量> = <假值>
 		;或 <字符串变量> '= <假值>
 	ENDIF


== 赋值运算 ==

===数值赋值===
 	A = 5
 	A = 1 + 2

=== 文本赋值 ===
 	STR = あいう
 	STR = %TSTR:0%いろは
另见[[Emuera扩展语法#使用FORM语法为字符串变量赋值|使用FORM语法为字符串变量赋值]]

=== 字符串赋值 ===
 	STR '= "あいう"
 	STR '= TSTR:0 + "いろは"
另见[[Emuera扩展语法#使用文本表达式为字符串变量赋值|使用文本表达式为字符串变量赋值]]

=== 赋值复合运算 ===

* ++  自加运算
* --  自减运算
* 算术运算
** *= /= %=
** += -=
* 位移运算
** <<= >>=
* 位运算
** &= |= ^=


== 运算顺序 ==

{|class="wikitable" 
! 分类 !! 优先度 !! 赋值复合运算 !! 符号
|-
| 否定运算符	|| 高	|| × 
| <code>~</code> <code>!</code>
|-
| 算术运算符	|| ↑	|| √ 
| <code>*</code> <code>/</code> <code>%</code>
|-
| 				||		|| √ 
| <code>+</code> <code>-</code>
|-
| 位移运算符	||		|| √ 
| <code>&lt;&lt;</code> <code>&gt;&gt;</code>
|-
| 比较运算符	||		|| × 
| <code>&lt;</code> <code>&gt;</code> <code>&lt;=</code> <code>&gt;=</code>
|-
|				||		|| ×
| <code>==</code> <code>!=</code>
|-
| 逻辑运算符	||		|| √
|<code>&</code> <code>|</code> <code>^</code>
|-
|				||	↓	|| ×
|<code>&&</code> <code>!&</code> <code>|</code> <code>!|</code> <code>^^</code>
|-
| 三项判断运算符	||	低	|| ×
| <code>~?…#_</code>
|-
| 赋值运算符 || 最低 || ×
| <code>=</code>
|}


== 参见 ==
*[[FORM语法]]
*[[Emuera扩展语法]]

== 外部链接 ==
* Emuera Wiki(2015), [https://osdn.jp/projects/emuera/wiki/exop 演算]{{Jp}}


[[分类:EmueraWiki]]

