﻿;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;撕破衣服
;着衣・脱衣関連コマンド：調教対象の撕破衣服
;-------------------------------------------------
@COM111

$INPUT_LOOP

PRINTL 撕破衣服

A = CFLAG:40
CALL WEARING_CLOTH_ALL
B = CFLAG:40
CFLAG:40 = A

;-------------------------------------------------
;破り取る部位の確認
;-------------------------------------------------
PRINTFORM 现在%SAVESTR:TARGET%的外貌是，
CALL PRINT_CLOTHTYPE
PRINTFORML 。

;引き裂き判定変数をLに保存
CALL COM111_ABLE0L
L:0 = RESULT
CALL COM111_ABLE1L
L:1 = RESULT
CALL COM111_ABLE2L
L:2 = RESULT
CALL COM111_ABLE3L
L:3 = RESULT
CALL COM111_ABLE4L
L:4 = RESULT
CALL COM111_ABLE5L
L:5 = RESULT
CALL COM111_ABLE6L
L:6 = RESULT

;特別コス
IF L:0
	PRINT  [10] - 
	CALL PRINT_CLOTHTYPE_SPECIAL
	PRINTL 剥掉
ENDIF
;ワンピース上半身
IF L:1
	PRINT  [11] - 
	CALL PRINT_CLOTHTYPE_MAIN2
	PRINTL 的上半身撕掉
ENDIF
;ワンピース下半身
IF L:2
	PRINT  [12] - 
	CALL PRINT_CLOTHTYPE_MAIN2
	PRINTL 的下半身撕掉
ENDIF
;ツーピース上
IF L:3
	PRINT  [11] - 
	CALL PRINT_CLOTHTYPE_MAIN2
	PRINTL 的上半撕破
ENDIF
;ツーピース下
IF L:4
	PRINT  [12] - 
	CALL PRINT_CLOTHTYPE_MAIN2
	IF CFLAG:41 >= 1 && CFLAG:41 <= 100
		PRINTL 的裙子撕破
	ELSE
		PRINTL 的下半撕破
	ENDIF
ENDIF
;ブラジャー
SIF L:5
	PRINTL  [13] - 撕碎胸罩
;パンツ
SIF L:6
	PRINTL  [14] - 撕碎内裤
;穿脱衣服
PRINTL  [19] - 返回[穿脱衣服]
PRINTL  [100]- 算了

INPUT

;-------------------------------------------------
;引き裂き処理
;-------------------------------------------------
;剥ぎ取れない特別コス
IF RESULT == 10 && (CFLAG:40 & 64) && (CFLAG:42 == 11 || CFLAG:42 == 79)
	CALL PRINT_CLOTHTYPE_SPECIAL
	PRINTL 被徒手撕破了。
	WAIT
	RETURN 0
;特別コス引き裂き
ELSEIF RESULT == 10 && L:0
	PRINTFORM %SAVESTR:TARGET%的
	CALL PRINT_CLOTHTYPE_SPECIAL
	PRINTL 被强行剥掉了。
	CFLAG:40 -= 64
	CFLAG:47 = -3
;ワンピース上半身引き裂き
ELSEIF RESULT == 11 && L:1
	PRINTFORM %SAVESTR:TARGET%穿着的
	CALL PRINT_CLOTHTYPE_MAIN2
	PRINTL 的上半身被撕坏了。
	CFLAG:40 -= 4
	CFLAG:45 = -3
;ワンピース下半身引き裂き
ELSEIF RESULT == 12 && L:2
	PRINTFORM %SAVESTR:TARGET%穿着的
	CALL PRINT_CLOTHTYPE_MAIN2
	PRINTL 的下半身被撕坏了。
	SIF CFLAG:40 & 8
		CFLAG:40 -= 8
	SIF CFLAG:40 & 16
		CFLAG:40 -= 16
	CFLAG:46 = -3
;ツーピース上引き裂き
ELSEIF RESULT == 11 && L:3
	PRINTFORM %SAVESTR:TARGET%穿着的
	CALL PRINT_CLOTHTYPE_MAIN2
	PRINTL 的上半身被撕破了。
	CFLAG:40 -= 4
	CFLAG:45 = -3
;ツーピース下引き裂き
ELSEIF RESULT == 12 && L:4
	PRINTFORM %SAVESTR:TARGET%穿着的
	CALL PRINT_CLOTHTYPE_MAIN2
	IF CFLAG:41 >= 1 && CFLAG:41 <= 100
		PRINTL 的裙子被撕破了。
	ELSE
		PRINTL 的下半身被撕破了。
	ENDIF
	SIF CFLAG:40 & 8
		CFLAG:40 -= 8
	SIF CFLAG:40 & 16
		CFLAG:40 -= 16
	CFLAG:46 = -3
;ブラジャー引き裂き
ELSEIF RESULT == 13 && L:5
	PRINTFORML %SAVESTR:TARGET%的胸罩被撕碎了。
	CFLAG:40 -= 2
	CFLAG:44 = -3
;パンツ引き裂き
ELSEIF RESULT == 14 && L:6
	PRINTFORML %SAVESTR:TARGET%的内裤被撕碎了。
	CFLAG:40 -= 1
	CFLAG:43 = -3
ELSEIF RESULT == 19
	RETURN 0
ELSEIF RESULT == 100
	RETURN 1
ELSE
	GOTO INPUT_LOOP
ENDIF

IF CFLAG:40 == 0
	PRINTL （已经全裸，撕无可撕）
	PRINTL 
	WAIT
	RETURN 0
ENDIF

PRINTL 

GOTO INPUT_LOOP

;=================================================
;判定用関数群
;=================================================
;特別コス引き裂き判定
@COM111_ABLE0L
;特別コスが設定されていないとダメ
SIF CFLAG:42 == 0
	RETURN 0
;特別コスを付けていないとダメ
SIF (CFLAG:40 & 64) == 0
	RETURN 0
RETURN 1

;ワンピース上引き裂き判定
@COM111_ABLE1L
;設定衣装がワンピースでないとダメ
SIF CFLAG:41 <= 200
	RETURN 0
;上着上が残ってないとダメ
SIF (CFLAG:40 & 4) == 0
	RETURN 0
;邪魔になる特別コスを付けているとダメ
SIF (CFLAG:40 & 64) && CFLAG:42 <= 50
	RETURN 0
RETURN 1

;ワンピース下引き裂き判定
@COM111_ABLE2L
;設定衣装がワンピースでないとダメ
SIF CFLAG:41 <= 200
	RETURN 0
;上着下が残ってないとダメ
SIF (CFLAG:40 & 24) == 0
	RETURN 0
;ズーコの着ぐるみを着ているとダメ
SIF (CFLAG:40 & 64) && CFLAG:42 == 11
	RETURN 0
RETURN 1

;ツーピース上引き裂き判定
@COM111_ABLE3L
;設定衣装がツーピースでないとダメ
SIF CFLAG:41 >= 201
	RETURN 0
;上着上を付けてないとダメ
SIF (CFLAG:40 & 4) == 0
	RETURN 0
;邪魔になる特別コスを付けているとダメ
SIF (CFLAG:40 & 64) && CFLAG:42 <= 50
	RETURN 0
RETURN 1

;ツーピース下引き裂き判定
@COM111_ABLE4L
;設定衣装がツーピースでないとダメ
SIF CFLAG:41 >= 201
	RETURN 0
;上着下を付けてないとダメ
SIF (CFLAG:40 & 24) == 0
	RETURN 0
;ズーコの着ぐるみを着ているとダメ
SIF (CFLAG:40 & 64) && CFLAG:42 == 11
	RETURN 0
RETURN 1

;ブラジャー引き裂き判定
@COM111_ABLE5L
;ブラジャーを付けてないとダメ
SIF (CFLAG:40 & 2) == 0
	RETURN 0
;上着上を着ているとダメ
SIF (CFLAG:40 & 4)
	RETURN 0
;邪魔になる特別コスを付けているとダメ
SIF (CFLAG:40 & 64) && CFLAG:42 <= 50
	RETURN 0
RETURN 1

;パンツ引き裂き判定
@COM111_ABLE6L
;パンツを穿いてないとダメ
SIF (CFLAG:40 & 1) == 0
	RETURN 0
;上着下・ズボンタイプを穿いてるとダメ
SIF (CFLAG:40 & 16) 
	RETURN 0
;ズーコの着ぐるみを着ているとダメ
SIF (CFLAG:40 & 64) && CFLAG:42 == 11
	RETURN 0
;和服の場合は上着下・スカートタイプを着ているとダメ
SIF CFLAG:41 == 202 && (CFLAG:40 & 8)
	RETURN 0
RETURN 1

