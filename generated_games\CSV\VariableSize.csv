﻿
;これはEmueraの変数镰刀指定用ファイルです。
;このファイルをcsvフォルダ内に置くことで配列変数の要素の数を変えることが出来ます。
;VariableSize.CSV中で镰刀を指定していない変数については標準の値が使われます。

;ABLNAMEなどのcsvから読む変数の镰刀も変更できるようになりました。
;ABLとABLNAMEなど対応関係にある変数でも同期はしていませんのでそれぞれを変更する必要があります。
;ただしITEMNAME-ITEMPRICEだけは片方を変更すると他方も変更されます。

;RAND、CHARANUMなどのその都度計算される値については要素数を変更できません。
;NO、NAMEのような配列でない変数も镰刀を指定することはできません。

;変数の镰刀を変更してもセーブデータはそのまま使えますが、セーブされている镰刀より変更後の镰刀の方が小さい場合、はみ出たデータは失われます。
;また、セーブされている镰刀がeramakerの標準の配列镰刀より大きい場合、eramakerでデータをロードすると正常に動作しません。


;ITEMNAMEとITEMPRICEは片方を変更すると他方も同じ値に変更されます
ITEMNAME,10000
;ITEMPRICE,1000
ABLNAME,110
TALENTNAME,10000
EXPNAME,100
MARKNAME,100
PALAMNAME,200
TRAINNAME,1000
BASENAME,100
SOURCENAME,1000
EXNAME,100
EQUIPNAME,100
TEQUIPNAME,100
FLAGNAME,10000
CFLAGNAME,1000
TFLAGNAME,1000
STR,20000

DAY,1000
MONEY,1000
ITEM,10000
FLAG,10000
TFLAG,1000
UP,1000
PALAMLV,1000
EXPLV,1000
EJAC,1000
DOWN,1000
RESULT,1000
COUNT,1000
TARGET,1000
ASSI,1000
MASTER,1000
NOITEM,1000
LOSEBASE,1000
SELECTCOM,1000
ASSIPLAY,1000
PREVCOM,1000

TIME,1000
ITEMSALES,1000
PLAYER,1000
NEXTCOM,1000
PBAND,1000
BOUGHT,1000

A,100000
B,100000
C,100000
D,100000
E,100000
F,100000
G,100000
H,100000
I,100000
J,100000
K,100000
L,100000
M,100000
N,100000
O,100000
P,100000
Q,100000
R,100000
S,100000
T,100000
U,100000
V,100000
W,100000
X,100000
Y,100000
Z,100000

SAVESTR,100
RESULTS,3000

BASE,100
MAXBASE,100
ABL,110
TALENT,10000
EXP,100
MARK,100
PALAM,100
SOURCE,1000
EX,100
CFLAG,1000
JUEL,200
RELATION,300
EQUIP,100
TEQUIP,100
STAIN,100
GOTJUEL,200
NOWEX,100

CSTR,100
CUP,100
CDOWN,100
DOWNBASE,100

TSTR,100

LOCAL,1000
LOCALS,100
ARG,1000000
ARGS,1000000
GLOBAL,1000
GLOBALS,110

;変更不可。乱数システムが使用するため。
;RANDDATA,625

;二次元配列
DITEMTYPE,1000,100
DA,100,100
DB,100,100
DC,100,100
DD,100,100
DE,100,100

CDFLAG, 100, 100

;三次元配列
;全要素数（3つの数を乗算した数）が100万を超えてはならない
TA,100,100,100
TB,100,100,100
