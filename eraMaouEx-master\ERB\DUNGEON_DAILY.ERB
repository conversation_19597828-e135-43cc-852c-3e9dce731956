﻿@DISPLAY_DUNGEON_DAILY
#DIM TEMP
#DIM DYNAMIC STORAGE,1000
#DIM DAILYTARGET
#DIMS DISPLAYCHARA
PRINT 　 
PRINTFORML Space for further docuement
; FLAG <= 20 --> 岌岌可危
; EX_FLAG:99 > 20 && EX_FLAG:99 <= 40
; EX_FLAG:99 > 40 && EX_FLAG:99 <= 60
; EX_FLAG:99 > 60 && EX_FLAG:99 <= 80
; EX_FLAG:99 > 80 && EX_FLAG:99 <= 100
STORAGE = 0

;奴隶日常
REPEAT CHARANUM
	IF CFLAG:COUNT:1 == 0
		IF TALENT:COUNT:76 == 1 || TALENT:COUNT:85 == 1
			STORAGE:0++
			STORAGE:(STORAGE:0) = COUNT
		ENDIF
	ENDIF
REND
;====TESTING PURPOSE====
PRINTL Testing Purpose
PRINTFORML {STORAGE}
;=======================
IF STORAGE != 0
RAND (STORAGE)
RESULT++
DAILYTARGET = STORAGE:RESULT
;====TESTING PURPOSE====
PRINTL Testing Purpose
PRINTFORML {RESULT}
PRINTFORML %SAVESTR:DAILYTARGET%
;=======================
ENDIF

;===DISPLAY===
DISPLAYCHARA = %SAVESTR:DAILYTARGET%

SIF EX_TALENT:DAILYTARGET:2
	LOCALS = 女儿
SIF TALENT:DAILYTARGET:220
	LOCALS = 魔物娘
SIF STRLENS(LOCALS) > 1
	GOTO DAILYTYPE
FOR COUNT, 160, 181
	SIF TALENT:DAILYTARGET:COUNT
		LOCALS = %TALENTNAME:COUNT%
NEXT
SIF STRLENS(LOCALS) > 1
	GOTO DAILYTYPE
FOR COUNT, 101, 200
	SIF EX_TALENT:DAILYTARGET:COUNT
		LOCALS = %EX_TALENTNAME:COUNT%
NEXT
$DAILYTYPE
DISPLAYCHARA += LOCALS
IF TALENT:DAILYTARGET:85
	DISPLAYCHARA += "爱慕日常"
ELSEIF TALENT:DAILYTARGET:76
	DISPLAYCHARA += "淫乱日常"
ENDIF
PRINTFORML %DISPLAYCHARA%
LOCALS = 
;===DISPLAYEND===

;怪物日常
TEMP = RAND(49)
SELECTCASE TEMP
	CASE 0 TO 4
		TEMP += 100
	CASE 5 TO 9
		TEMP += 105
	CASE 10 TO 14
		TEMP += 110
	CASE 15 TO 19
		TEMP += 115
	CASE 20 TO 24
		TEMP += 120
	CASE 25 TO 29
		TEMP += 125
	CASE 30 TO 34
		TEMP += 130
	CASE 35 TO 39
		TEMP += 135
	CASE 40 TO 44
		TEMP += 140
	CASE 45 TO 48
		TEMP += 145
ENDSELECT
IF EX_FLAG:99 >= 0 && EX_FLAG:99 <= 20
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ELSEIF TEMP == 20
PRINTFORML
ELSEIF TEMP == 21
PRINTFORML
ELSEIF TEMP == 22
PRINTFORML
ELSEIF TEMP == 23
PRINTFORML
ELSEIF TEMP == 24
PRINTFORML
ELSEIF TEMP == 25
PRINTFORML
ELSEIF TEMP == 26
PRINTFORML
ELSEIF TEMP == 27
PRINTFORML
ELSEIF TEMP == 28
PRINTFORML
ELSEIF TEMP == 29
PRINTFORML
ELSEIF TEMP == 30
PRINTFORML
ELSEIF TEMP == 31
PRINTFORML
ELSEIF TEMP == 32
PRINTFORML
ELSEIF TEMP == 33
PRINTFORML
ELSEIF TEMP == 34
PRINTFORML
ELSEIF TEMP == 35
PRINTFORML
ELSEIF TEMP == 36
PRINTFORML
ELSEIF TEMP == 37
PRINTFORML
ELSEIF TEMP == 38
PRINTFORML
ELSEIF TEMP == 39
PRINTFORML
ELSEIF TEMP == 40
PRINTFORML
ELSEIF TEMP == 41
PRINTFORML
ELSEIF TEMP == 42
PRINTFORML
ELSEIF TEMP == 43
PRINTFORML
ELSEIF TEMP == 44
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 20 && EX_FLAG:99 <= 40
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ELSEIF TEMP == 20
PRINTFORML
ELSEIF TEMP == 21
PRINTFORML
ELSEIF TEMP == 22
PRINTFORML
ELSEIF TEMP == 23
PRINTFORML
ELSEIF TEMP == 24
PRINTFORML
ELSEIF TEMP == 25
PRINTFORML
ELSEIF TEMP == 26
PRINTFORML
ELSEIF TEMP == 27
PRINTFORML
ELSEIF TEMP == 28
PRINTFORML
ELSEIF TEMP == 29
PRINTFORML
ELSEIF TEMP == 30
PRINTFORML
ELSEIF TEMP == 31
PRINTFORML
ELSEIF TEMP == 32
PRINTFORML
ELSEIF TEMP == 33
PRINTFORML
ELSEIF TEMP == 34
PRINTFORML
ELSEIF TEMP == 35
PRINTFORML
ELSEIF TEMP == 36
PRINTFORML
ELSEIF TEMP == 37
PRINTFORML
ELSEIF TEMP == 38
PRINTFORML
ELSEIF TEMP == 39
PRINTFORML
ELSEIF TEMP == 40
PRINTFORML
ELSEIF TEMP == 41
PRINTFORML
ELSEIF TEMP == 42
PRINTFORML
ELSEIF TEMP == 43
PRINTFORML
ELSEIF TEMP == 44
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 40 && EX_FLAG:99 <= 60
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ELSEIF TEMP == 20
PRINTFORML
ELSEIF TEMP == 21
PRINTFORML
ELSEIF TEMP == 22
PRINTFORML
ELSEIF TEMP == 23
PRINTFORML
ELSEIF TEMP == 24
PRINTFORML
ELSEIF TEMP == 25
PRINTFORML
ELSEIF TEMP == 26
PRINTFORML
ELSEIF TEMP == 27
PRINTFORML
ELSEIF TEMP == 28
PRINTFORML
ELSEIF TEMP == 29
PRINTFORML
ELSEIF TEMP == 30
PRINTFORML
ELSEIF TEMP == 31
PRINTFORML
ELSEIF TEMP == 32
PRINTFORML
ELSEIF TEMP == 33
PRINTFORML
ELSEIF TEMP == 34
PRINTFORML
ELSEIF TEMP == 35
PRINTFORML
ELSEIF TEMP == 36
PRINTFORML
ELSEIF TEMP == 37
PRINTFORML
ELSEIF TEMP == 38
PRINTFORML
ELSEIF TEMP == 39
PRINTFORML
ELSEIF TEMP == 40
PRINTFORML
ELSEIF TEMP == 41
PRINTFORML
ELSEIF TEMP == 42
PRINTFORML
ELSEIF TEMP == 43
PRINTFORML
ELSEIF TEMP == 44
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 60 && EX_FLAG:99 <= 80
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ELSEIF TEMP == 20
PRINTFORML
ELSEIF TEMP == 21
PRINTFORML
ELSEIF TEMP == 22
PRINTFORML
ELSEIF TEMP == 23
PRINTFORML
ELSEIF TEMP == 24
PRINTFORML
ELSEIF TEMP == 25
PRINTFORML
ELSEIF TEMP == 26
PRINTFORML
ELSEIF TEMP == 27
PRINTFORML
ELSEIF TEMP == 28
PRINTFORML
ELSEIF TEMP == 29
PRINTFORML
ELSEIF TEMP == 30
PRINTFORML
ELSEIF TEMP == 31
PRINTFORML
ELSEIF TEMP == 32
PRINTFORML
ELSEIF TEMP == 33
PRINTFORML
ELSEIF TEMP == 34
PRINTFORML
ELSEIF TEMP == 35
PRINTFORML
ELSEIF TEMP == 36
PRINTFORML
ELSEIF TEMP == 37
PRINTFORML
ELSEIF TEMP == 38
PRINTFORML
ELSEIF TEMP == 39
PRINTFORML
ELSEIF TEMP == 40
PRINTFORML
ELSEIF TEMP == 41
PRINTFORML
ELSEIF TEMP == 42
PRINTFORML
ELSEIF TEMP == 43
PRINTFORML
ELSEIF TEMP == 44
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 80 && EX_FLAG:99 <= 100
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ELSEIF TEMP == 20
PRINTFORML
ELSEIF TEMP == 21
PRINTFORML
ELSEIF TEMP == 22
PRINTFORML
ELSEIF TEMP == 23
PRINTFORML
ELSEIF TEMP == 24
PRINTFORML
ELSEIF TEMP == 25
PRINTFORML
ELSEIF TEMP == 26
PRINTFORML
ELSEIF TEMP == 27
PRINTFORML
ELSEIF TEMP == 28
PRINTFORML
ELSEIF TEMP == 29
PRINTFORML
ELSEIF TEMP == 30
PRINTFORML
ELSEIF TEMP == 31
PRINTFORML
ELSEIF TEMP == 32
PRINTFORML
ELSEIF TEMP == 33
PRINTFORML
ELSEIF TEMP == 34
PRINTFORML
ELSEIF TEMP == 35
PRINTFORML
ELSEIF TEMP == 36
PRINTFORML
ELSEIF TEMP == 37
PRINTFORML
ELSEIF TEMP == 38
PRINTFORML
ELSEIF TEMP == 39
PRINTFORML
ELSEIF TEMP == 40
PRINTFORML
ELSEIF TEMP == 41
PRINTFORML
ELSEIF TEMP == 42
PRINTFORML
ELSEIF TEMP == 43
PRINTFORML
ELSEIF TEMP == 44
PRINTFORML
ENDIF
ENDIF
;地城日常
TEMP = RAND(20)
IF EX_FLAG:99 >= 0 && EX_FLAG:99 <= 20
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 20 && EX_FLAG:99 <= 40
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 40 && EX_FLAG:99 <= 60
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 60 && EX_FLAG:99 <= 80
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ENDIF
ELSEIF EX_FLAG:99 > 80 && EX_FLAG:99 <= 100
IF TEMP == 0
PRINTFORML
ELSEIF TEMP == 1
PRINTFORML
ELSEIF TEMP == 2
PRINTFORML
ELSEIF TEMP == 3
PRINTFORML
ELSEIF TEMP == 4
PRINTFORML
ELSEIF TEMP == 5
PRINTFORML
ELSEIF TEMP == 6
PRINTFORML
ELSEIF TEMP == 7
PRINTFORML
ELSEIF TEMP == 8
PRINTFORML
ELSEIF TEMP == 9
PRINTFORML
ELSEIF TEMP == 10
PRINTFORML
ELSEIF TEMP == 11
PRINTFORML
ELSEIF TEMP == 12
PRINTFORML
ELSEIF TEMP == 13
PRINTFORML
ELSEIF TEMP == 14
PRINTFORML
ELSEIF TEMP == 15
PRINTFORML
ELSEIF TEMP == 16
PRINTFORML
ELSEIF TEMP == 17
PRINTFORML
ELSEIF TEMP == 18
PRINTFORML
ELSEIF TEMP == 19
PRINTFORML
ENDIF
ENDIF

@CAL_DUNGEON_DAILY

IF EX_FLAG:99 >= 100
	EX_FLAG:99 = 100
ENDIF
EX_FLAG:99 -= 2
