; _default.config for ERA game

; --- Display Settings ---
; Window width
WINDOW_WIDTH = 1280

; Window height
WINDOW_HEIGHT = 720

; Default font
FONT = "メイリオ"

; Default font size
FONT_SIZE = 20

; Number of lines to display before pausing
LINE_SPLIT = 20

; Number of lines to skip from the top when displaying messages
SKIP_LINE_TOP = 0

; Number of lines to skip from the bottom when displaying messages
SKIP_LINE_BOTTOM = 0

; Whether to draw a line indicating a page break (1=yes, 0=no)
DRAW_SKIP_LINE = 1

; Whether to draw messages line by line (1=yes, 0=no)
DRAW_LINE_BY_LINE = 0

; Number of lines to draw per second if DRAW_LINE_BY_LINE is 1
DRAW_LINES_BY_ONE_SECOND = 0

; Maximum number of messages to display in general scenes
DISPLAY_MESSAGE_MAX = 100

; Maximum number of messages to display in training scenes
DISPLAY_MESSAGE_MAX_TRAIN = 500

; Maximum number of messages to display in shop scenes
DISPLAY_MESSAGE_MAX_SHOP = 500


; --- Engine Configuration Parameters ---
; Maximum length of input string
INPUT_MAX = 256

; Auto-save interval in turns (0 to disable)
AUTO_SAVE_INTERVAL = 10

; Maximum number of lines to store in the in-game log
LOG_MAX = 10000

; Whether to automatically save the log to a file (1=yes, 0=no)
LOG_AUTOSAVE = 1

; Do not wait for input in general scenes (1=no wait, 0=wait)
NO_WAIT_INPUT = 0

; Do not wait for input in COM phase (1=no wait, 0=wait)
NO_WAIT_INPUT_COM = 0

; Do not wait for input in TRAIN phase (1=no wait, 0=wait)
NO_WAIT_INPUT_TRAIN = 0

; Do not wait for input in SHOP phase (1=no wait, 0=wait)
NO_WAIT_INPUT_SHOP = 0

; Maximum number of save slots
SAVE_MAX = 10

; Maximum number of load slots
LOAD_MAX = 10

; Maximum number of data lines for save files
SAVE_SAVE_DATA_MAX = 100000

; Maximum number of data lines for load files
LOAD_SAVE_DATA_MAX = 100000

; Maximum number of text lines for save files
SAVE_SAVE_DATA_TEXT_MAX = 1000

; Maximum number of text lines for save files in SHOP phase
SAVE_SAVE_DATA_TEXT_MAX_SHOP = 2000

; Maximum number of text lines for save files in TRAIN phase
SAVE_SAVE_DATA_TEXT_MAX_TRAIN = 1500

; Maximum number of text lines for save files in BASIC phase
SAVE_SAVE_DATA_TEXT_MAX_BASIC = 1000