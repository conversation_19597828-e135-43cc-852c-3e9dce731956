﻿; AFTERTRAIN
; 训练结束后的处理函数
; 玩家结束训练后，显示训练结果，并返回主菜单或选择界面

FUNCTION AFTERTRAIN
    ; 打印训练结束提示
    PRINTL
    PRINTL "----------------------------------------"
    PRINTL "你结束了本次训练。"
    PRINTL "----------------------------------------"
    PRINTL

    ; 在这里可以添加显示训练效果的代码
    ; 例如，显示提升的属性或技能
    ; PRINTL "你的[技能名]提升了！"
    ; PRINTL "你的[属性名]增加了！"
    ; 根据实际训练逻辑来填充

    ; 提示玩家按键继续
    PRINTL "按任意键返回主菜单..."
    INPUT

    ; 返回主菜单循环
    ; 假设 MAIN_MENU_LOOP 是主菜单的入口函数
    CALL MAIN_MENU_LOOP

    RETURN