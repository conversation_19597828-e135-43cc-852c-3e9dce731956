﻿//exvar

调试变量
--------------------------------------------------

调试变量用于在调试模式中返回调试相关的信息。
通常模式下只会返回空字符串和0。

注意变量名前后都有两个“_”符号。

* __FILE__
	返回当前执行的ERB文件名

* __LINE__
	返回当前执行行的行号。
	行号从1开始。

* __FUNCTION__
	返回当前执行的函数名。
	函数名包括“@”符号以及参数。
	

	
	
数值
--------------------------------------------------

0XE4E4
0xE4E4

0B1010
0b1010


5E3 = 5*10^3
5e3

5P3 = 5*2^3
5p3

赋值运算符 '=

数组 界限 索引


#DIM
	CONST
	REF
	DYNAMIC
	STATIC
	GLOBAL
	SAVEDATA
	

	
	