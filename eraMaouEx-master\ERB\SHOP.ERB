﻿;=================================================
;BEGIN SHOP後最初に呼び出される関数
;=================================================
@EVENTSHOP

;バグ対策
SIF TARGET > CHARANUM - 1
	TARGET = -1

;バグ対策
SIF ASSI > CHARANUM - 1
	ASSI = -1

;一旦購入可能アイテムを空に
REPEAT 100
	ITEMSALES:COUNT = 0
REND


BOUGHT = -1

@SHOW_SHOP

SAVESTR:0 = 你
CALL CLEAR_SHOP
IF BOUGHT >= 0 && BOUGHT < 54
	JUMP ITEM_SHOP
ELSEIF BOUGHT >= 54
	JUMP ITEM_SHOP_TRAP
ENDIF


SIF DAY:1 < 1
	DAY:1 = 1
SIF DAY:2 < 1
	DAY:2 = 1	

CALL DRAW_MAINMENU;メインメニュー描画処理はこっちの関数に丸投げ、本体は_DRAW_MAINMENU.ERB内

@USERSHOP
#DIM TEMP, 300

;店にいる場合は買い物中フラグを外す
IF RESULT == 999 && BOUGHT >= 0
	CALL CLEAR_SHOP
	BOUGHT = -1
ELSEIF RESULT == 998 && BOUGHT >= 0
	BOUGHT = 200
	CALL CLEAR_SHOP
	JUMP ITEM_SHOP_TRAP
ELSEIF RESULT == 997 && BOUGHT >= 0
	BOUGHT = 1
	CALL CLEAR_SHOP
	JUMP ITEM_SHOP
ELSEIF BOUGHT >= 0
	RETURN 0
ENDIF

IF RESULT == 100 && A > 0
;	CALL SELECT_TARGET
;	IF RESULT == 1
;		CALL SELECT_ASSI
;		SIF RESULT == 0
;			ASSI = -1
	IF TARGET <= 0
		CALL SELECT_TARGET
		SIF RESULT == 0
			RETURN 0
	ENDIF
	
	$SELECT_ASSI_LOOP
	IF ASSI <= 0
		TEMP:3 = 0
		REPEAT CHARANUM
			IF CFLAG:COUNT:0 == 2 && COUNT != 0 && CFLAG:COUNT:1 == 0 && TARGET != COUNT
				TEMP:3 += 1
			ENDIF
		REND
		SIF TEMP:3 >= 1
			CALL SELECT_ASSI
		SIF RESULT == 2
			RETURN 0
		SIF ASSI == 0
			ASSI = -1
		IF TARGET == ASSI
			ASSI = -1
			GOTO SELECT_ASSI_LOOP
		ENDIF
	ENDIF

	SIF ASSI >= 1 && TARGET == ASSI
		ASSI = -1
		
	IF CFLAG:MASTER:1 == 10
		PRINTFORMW 育儿室中的%CALLNAME:MASTER%不能进行调教……
		RETURN 0
	ENDIF
	SIF TARGET >= 1 && TARGET != ASSI
		BEGIN TRAIN
;	ENDIF
	RETURN 1
ELSEIF RESULT == 101
	CALL CHARA_INFO
	IF RESULT == 1
		BEGIN TURNEND
		RETURN 0
	ENDIF
ELSEIF RESULT == 102
	CALL DUNGEON_INFO2
ELSEIF RESULT == 103
	CALL EXECUTION
ELSEIF RESULT == 104
	CALL INTERCEPT
ELSEIF RESULT == 105
	CALL ABILITY_UP
ELSEIF RESULT == 106
	CALL CHARA_SALE
ELSEIF RESULT == 107
	BOUGHT = 1
ELSEIF RESULT == 108
	CALL TAILOR_MAIN
	TARGET = FLAG:1
ELSEIF RESULT == 109
	CALL INVASION
	IF RESULT == 1
		BEGIN TURNEND
		RETURN 1
	ENDIF
ELSEIF RESULT == 110 && TALENT:0:325 == 1
	CALL SECRET_LABO
ELSEIF RESULT == 111 && (FLAG:83 || FLAG:84)
	CALL INFRASTRUCTURE
ELSEIF RESULT == 199
	;休憩
	PRINTL 你专心于内政，稍作了休息……（税金+5%）
	FLAG:9 += 5
	BEGIN TURNEND
	RETURN 1
ELSEIF RESULT == 200
	CALL SYSTEM_SAVEGAME
ELSEIF RESULT == 300
	CALL SYSTEM_LOADGAME
ELSEIF RESULT == 777
	CALL CONFIG
ELSEIF RESULT == 888
	CALL MAOUNET
ELSEIF RESULT == 400
	CALL LABO

;ここから追加コマンド
ELSEIF RESULT == 496 && A > 0
	CALL SELECT_TARGET
ELSEIF RESULT == 497 && A > 0
	CALL SELECT_ASSI
ELSEIF RESULT == 498
	CALL CHARA_INFO_INDIVIDUAL_WAPPED, TARGET
ELSEIF RESULT == 499
	CALL CHARA_INFO_INDIVIDUAL_WAPPED, ASSI
ELSEIF RESULT == 500
	FLAG:36 = 0
ELSEIF RESULT == 501
	FLAG:36 = 1
ELSEIF RESULT == 504
	FLAG:36 = 4
ELSEIF RESULT == 505
	FLAG:36 = 5
ELSEIF RESULT > 520 && RESULT <= 530
	; 阶层信息,10层为近卫信息
	RESULT -= 520
	CALL SHOW_FLOOR, RESULT
ELSEIF RESULT == 120
	;人数いっぱいの時は呼べない
	IF CHARANUM < MAX_CHARANUM
		CALL MONSTER_SHOP
	ELSE
		PRINTW 奴隶太多了！
	ENDIF
ELSEIF RESULT == 999
	CALL DEBUG_MENU_U
ENDIF

SIF RESULT == 7788
CALL RELATION_DEBUGPRINT

RETURN 0


;調教するキャラを選ぶ
;----------------------------------------------
@SELECT_TARGET
;----------------------------------------------
#DIM NO_PAGE = 0
#DIM NUM_PAGE = 26
#DIM MAX_PAGE
#DIM T_LCOUNT
#DIM L_LCOUNT

FOR COUNT, 0, CHARANUM
{
	SIF	IS_TRAINABLE(COUNT) != 0
}
		CONTINUE
	MAX_PAGE++
NEXT
IF (MAX_PAGE % NUM_PAGE) > 0
	MAX_PAGE = ( MAX_PAGE / NUM_PAGE ) + 1
ELSE
	MAX_PAGE = MAX_PAGE / NUM_PAGE
ENDIF
MAX_PAGE--

$INPUT_LOOP
;-------------------------------------------------
;缓存、重置列表信息
IF NO_PAGE == 0
	LIST_POS = 0
	PREV_PAGE = 0
	PREV_LIST_POS = 0
ELSEIF NO_PAGE < PREV_PAGE
	SWAP LIST_POS, PREV_LIST_POS
ELSEIF NO_PAGE == PREV_PAGE
	LIST_POS = PREV_LIST_POS
ELSE
	PREV_LIST_POS = LIST_POS
ENDIF
;-------------------------------------------------
CUSTOMDRAWLINE =
PRINTL 请选择调教的奴隶
DRAWLINE
L_LCOUNT = LINECOUNT
CALL SHOW_LIST_TRAINABLE(NO_PAGE,NUM_PAGE,LIST_POS)
;表示人数が返ってくる
IF RESULT < 1
	RETURN 0
ENDIF
L_LCOUNT = LINECOUNT - L_LCOUNT
	IF L_LCOUNT < (NUM_PAGE ) + 1
		REPEAT (NUM_PAGE - L_LCOUNT)
			PRINTL
		REND
	ENDIF
DRAWLINE
PRINTLC [1000] - 上一页
PRINTLC [999] 返回 
PRINTLC [1002] 其它
PRINTLC [1001] - 下一页
PREV_PAGE = NO_PAGE

INPUT
IF RESULT == 999
	;戻る
	RETURN 0
ELSEIF RESULT == 1002
	;その他
	CALL MONSTER_PLAY
	RETURN RESULT
ELSEIF IS_TRAINABLE(RESULT) == 0
	;調教可能な対象
	TARGET = RESULT
	FLAG:1 = TARGET ;前回の調教対象を上書き
	RETURN 1
ELSEIF IS_ASSISTABLE(RESULT) == 0
	;助手可能な対象
	ASSI = RESULT
	FLAG:2 = ASSI ;前回の助手を上書き
	RETURN 1
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF NO_PAGE < MAX_PAGE
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF
GOTO INPUT_LOOP

;助手を選ぶ
;----------------------------------------------
@SELECT_ASSI
;----------------------------------------------
#DIM NO_PAGE = 0
#DIM NUM_PAGE = 13
#DIM MAX_PAGE
#DIM T_LCOUNT
#DIM L_LCOUNT

FOR COUNT, 0, CHARANUM
{
	SIF	IS_ASSISTABLE(COUNT) != 0
}
		CONTINUE
	MAX_PAGE++
NEXT
IF (MAX_PAGE % NUM_PAGE) > 0
	MAX_PAGE = ( MAX_PAGE / NUM_PAGE ) + 1
ELSE
	MAX_PAGE = MAX_PAGE / NUM_PAGE
ENDIF
MAX_PAGE--

$INPUT_LOOP
;-------------------------------------------------
;缓存、重置列表信息
IF NO_PAGE == 0
	LIST_POS = 0
	PREV_PAGE = 0
	PREV_LIST_POS = 0
ELSEIF NO_PAGE < PREV_PAGE
	SWAP LIST_POS, PREV_LIST_POS
ELSEIF NO_PAGE == PREV_PAGE
	LIST_POS = PREV_LIST_POS
ELSE
	PREV_LIST_POS = LIST_POS
ENDIF
;-------------------------------------------------
CUSTOMDRAWLINE =
PRINTL 请选择助手
DRAWLINE
L_LCOUNT = LINECOUNT
CALL SHOW_LIST_ASSISTABLE(NO_PAGE,NUM_PAGE,LIST_POS)
;表示人数が返ってくる
IF RESULT < 1
	RETURN 0
ENDIF
L_LCOUNT = LINECOUNT - L_LCOUNT
	IF L_LCOUNT < (NUM_PAGE * 2 ) + 1
		REPEAT (NUM_PAGE * 2 - L_LCOUNT)
			PRINTL
		REND
	ENDIF
DRAWLINE
PRINTLC [1000] - 上一页
PRINTLC [999] 返回
PRINTLC [1002] 不带助手
PRINTLC [1001] - 下一页
PREV_PAGE = NO_PAGE

INPUT
IF RESULT == 1002
	;助手は無し
	ASSI = -1
	FLAG:2 = ASSI ;前回の助手を上書き
	RETURN 0
ELSEIF RESULT == 999
	;戻る
	RETURN 2
ELSEIF IS_ASSISTABLE(RESULT) == 0
	;助手可能な対象
	ASSI = RESULT
	FLAG:2 = ASSI ;前回の助手を上書き
	RETURN 1
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF NO_PAGE < MAX_PAGE
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF
CLEARLINE 1
GOTO INPUT_LOOP



;----------------------------------------------
@SHOW_FLOOR, ARG
;----------------------------------------------
;显示楼层状态
ARG = LIMIT(ARG,1,10)

DRAWLINE
IF ARG <= 9
	PRINTFORM 第{ARG}阶层
ELSEIF ARG == 10
	PRINTFORM 近卫兵
	PRINTL
	DRAWLINE
	FOR COUNT, 0, CHARANUM
		IF !CFLAG:COUNT:1 && EX_TALENT:COUNT:1
			PRINTFORM [%SAVESTR:COUNT%] —— 
			FOR LOCAL, 200, 212
				SIF TALENT:COUNT:LOCAL
					PRINTFORM %TALENTNAME:LOCAL%
			NEXT
			PRINTL
		ENDIF
	NEXT
	DRAWLINE
	GOTO MONSTERDATA
ENDIF
SELECTCASE FLAG:(ARG+349)
CASE 500
	PRINTFORM  - 商店街　
CASE 501
	PRINTFORM  - 沼泽　　
CASE 502
	PRINTFORM  - 人类牧场
CASE 503
	PRINTFORM  - 冰室　　
CASE 504
	PRINTFORM  - 热砂　　
CASE 505
	PRINTFORM  - 迷宫　　
CASE 506
	PRINTFORM  - 博物馆　
CASE 507
	PRINTFORM  - 娼馆街　
ENDSELECT
PRINTL
DRAWLINE

LOCAL = 0, 0
REPEAT 4
	SIF COUNT == 3
		COUNT = 4
	LOCAL = 300+ARG-1 + COUNT*10
	LOCAL = FLAG:LOCAL
	IF LOCAL > 0 && ITEM:LOCAL > 0
		PRINTFORM [%ITEMNAME:LOCAL%]
		LOCAL:1 ++
	ENDIF
REND
IF LOCAL:1
	PRINTL
	DRAWLINE
ENDIF

CALL ENEMY_EXIST2, ARG
PRINTL

$MONSTERDATA
LOCAL = ((ARG-1)*10+100)
REPEAT 10
	IF ITEM:LOCAL > 0
		PRINTFORML {ITEM:LOCAL,2,LEFT}只%MONSTERNAME(LOCAL)%
	ENDIF
	LOCAL++
REND

PRINTW
