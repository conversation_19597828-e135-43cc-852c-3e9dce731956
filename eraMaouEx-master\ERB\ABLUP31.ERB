﻿;自慰中毒のLvUP処理とその可否判定
;eratohoA ver1,204のスクリプトをベースに処理を簡略化
;eraIm@s_ver.0.17βdのスクリプトを参考に修正
;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;自慰中毒のLvUP
;-------------------------------------------------
@ABLUP31
DRAWLINE
;PRINTL 奴隶的自慰成瘾加深了。
;PRINTL 自慰中毒越高，越容易在自慰中感到满足，
;PRINTL 只有频繁而激烈的自慰，能安抚她那躁动的心。
;CUSTOMDRAWLINE ‥
;自慰中毒はLv5が上限
;[爱慕][淫乱][容易自慰][接受快感][自慰狂][淫乳]が付いている場合はLv10まで开放
IF ABL:31 >= 5 && (TALENT:85 == 0 && TALENT:76 == 0 && TALENT:60 == 0 && TALENT:70 == 0 && TALENT:74 == 0 && TALENT:78 == 0)
	PRINTW 需要特殊素质才能继续提升
	RETURN 0
ELSEIF ABL:31 >= 10
	PRINTW 已达最高级
	RETURN 0
;性交中毒＋自慰中毒は11以上にならない
;でも、珠が沢山あるの場合はレベルアップできる。
ELSEIF ABL:30 + ABL:31 >= 10
	IF JUEL:5 < ABL:31 * ABL:31 * 2550 || JUEL:0 < ABL:31 * ABL:31 * 15000 || JUEL:8 < ABL:31 * ABL:31 * 2000
	PRINTFORML 性交中毒({ABL:30})＋自慰中毒({ABL:31})上限为10
	PRINTFORML 至少达成%PALAMNAME:5%点数{ABL:31 * ABL:31 * 2550}点、%PALAMNAME:0%点数{ABL:31 * ABL:31 * 15000}点或%PALAMNAME:8%点数{ABL:31 * ABL:31 * 2000}点的其中一项
	PRINTFORMW 方可提升当前自慰中毒的等级
	RETURN 0
	ENDIF
ENDIF

;必要な欲情点数
A = 0
;必要な阴核点数
B = 0
;必要な耻情点数
C = 0
;必要な自慰经验回数
D = 0
;必要な调教自慰经验回数
E = 0
;必要な异常经验回数
F = 0

;条件別にＯＫかダメかを記録する
;自慰经验で上げる場合の可否（I=0:可、I&1:点数不足、I&2:経験不足、I&4:能力不足）
I = 0
;调教自慰经验で上げる場合の可否（I=0:可、I&1:点数不足、I&2:経験不足、I&4:能力不足）
J = 0

CALL DECIDE_ABLUP31

;ＬＶ２から３、ＬＶ３から４、４から５に上げるときは异常经验必要（素質：[开放][容易自慰][容易上瘾][淫乱化]なら無視できる）
SIF F > 0
	PRINTFORML %EXPNAME:50%{F}以上(现在{EXP:50})且

;露出癖が必要
PRINTFORML %ABLNAME:17%LV{ABL:31 + 1}以上(现在LV{ABL:17})且

;阴蒂感觉が必要
PRINTFORML %ABLNAME:0%LV{ABL:31 + 1}以上(现在LV{ABL:0})且

;自慰经验で上げる場合
PRINTFORM [0] - %PALAMNAME:5%点数×{JUEL:5}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 
PRINTFORML 　　　%PALAMNAME:0%点数×{JUEL:0}/{B}
PRINTFORML 　　　%PALAMNAME:8%点数×{JUEL:8}/{C}
PRINTFORML 　　　%EXPNAME:10%　{EXP:10}/{D}

;调教自慰经验で上げる場合
PRINTFORM [1] - %PALAMNAME:5%点数×{JUEL:5}/{A} ……
PRINTV GET_ABLUP_STATE(J)
PRINTL 
PRINTFORML 　　　%PALAMNAME:0%点数×{JUEL:0}/{B}
PRINTFORML 　　　%PALAMNAME:8%点数×{JUEL:8}/{C}
PRINTFORML 　　　%EXPNAME:11%　{EXP:11}/{E}

PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 1) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF J != 0 && RESULT == 1
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:31 += 1

IF RESULT == 0 || RESULT == 1
	JUEL:5 -= A
	JUEL:0 -= B
	JUEL:8 -= C
ENDIF

PRINTFORML %ABLNAME:31%变为LV{ABL:31}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP31
;-------------------------------------------------
ABL:31 ++

IF I == 0
	JUEL:5 -= A
	JUEL:0 -= B
	JUEL:8 -= C
ELSEIF J == 0
	JUEL:5 -= A
	JUEL:0 -= B
	JUEL:8 -= C
ENDIF


;-------------------------------------------------
;自慰中毒のLvUP可否判定
;-------------------------------------------------
@DECIDE_ABLUP31
;自慰中毒はLv5が上限
;[爱慕][淫乱][容易自慰][接受快感][自慰狂][淫乳]が付いている場合はLv10まで开放
SIF ABL:31 >= 10
	RETURN 0
SIF ABL:31 >= 5 && (TALENT:85 == 0 && TALENT:76 == 0 && TALENT:60 == 0 && TALENT:70 == 0 && TALENT:74 == 0 && TALENT:78 == 0)
	RETURN 0
;性交中毒LV＋自慰中毒LVは10が上限
SIF ABL:30 + ABL:31 >= 20
	RETURN 0

;判定変数を空に
I = 0
J = 0

A = 0
B = 0
C = 0
D = 0
E = 0
F = 0

IF ABL:31 == 0
	A = 3000
	B = 10000
	C = 1000
	D = 100
	E = 20
ELSEIF ABL:31 == 1
	A = 6000
	B = 25000
	C = 3000
	D = 250
	E = 40
ELSEIF ABL:31 == 2
	A = 12000
	B = 50000
	C = 6000
	D = 500
	E = 60
ELSEIF ABL:31 == 3
	A = 20000
	B = 100000
	C = 15000
	D = 1000
	E = 100
ELSEIF ABL:31 == 4
	A = 32000
	B = 200000
	C = 30000
	D = 1500
	E = 150
ELSEIF ABL:31 == 5
	A = 50000
	B = 250000
	C = 40000
	D = 2000
	E = 200
ELSEIF ABL:31 == 6
	A = 70000
	B = 320000
	C = 50000
	D = 3000
	E = 320
ELSEIF ABL:31 == 7
	A = 100000
	B = 500000
	C = 70000
	D = 4000
	E = 500
ELSEIF ABL:31 == 8
	A = 150000
	B = 800000
	C = 100000
	D = 6000
	E = 800
ELSEIF ABL:31 == 9
	A = 200000
	B = 1000000
	C = 150000
	D = 8000
	E = 1000
ENDIF

;戒备森严
IF TALENT:27
	IF ABL:31 == 3
		TIMES A , 1.50
		TIMES B , 1.50
		TIMES C , 1.50
		TIMES D , 1.50
		TIMES E , 1.50
	ELSEIF ABL:31 == 4
		TIMES A , 2.00
		TIMES B , 2.00
		TIMES C , 2.00
		TIMES D , 2.00
		TIMES E , 2.00
	ELSEIF ABL:31 == 5
		TIMES A , 2.50
		TIMES B , 2.50
		TIMES C , 2.50
		TIMES D , 2.50
		TIMES E , 2.50
	ELSEIF ABL:31 >= 6
		TIMES A , 3.00
		TIMES B , 3.00
		TIMES C , 3.00
		TIMES D , 3.00
		TIMES E , 3.00
	ENDIF
ENDIF

;ＬＶ２から３、ＬＶ３から４、４から５に上げるときは异常经验必要（素質：[开放][容易自慰][淫乱][容易上瘾][疯狂]なら無視できる）
IF ABL:31 == 2 && (TALENT:33 == 0 && TALENT:60 == 0 && TALENT:72 == 0 && TALENT:76 == 0 && TALENT:123 == 0)
	F = ABL:31 - 1
	IF EXP:50 < F
		;异常经验が不足
		I |= 2
		J |= 2
	ENDIF
ENDIF

;容易自慰
IF TALENT:60
	TIMES A , 0.25
	TIMES B , 0.25
	TIMES C , 0.25
	TIMES D , 0.25
ENDIF

;容易上瘾
IF TALENT:72
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
	TIMES D , 0.50
ENDIF

;倒錯的
IF TALENT:80
	TIMES A , 0.75
	TIMES B , 0.75
	TIMES C , 0.75
	TIMES D , 0.75
ENDIF

;淫乱化
IF TALENT:76
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
	TIMES D , 0.50
ENDIF


;异常经验が不足
IF F > EXP:50
	I |= 2
	J |= 2
ENDIF

;露出癖が自慰中毒＋１レベルでないと能力不足
IF ABL:17 < ABL:31 + 1
	I |= 4
	J |= 4
ENDIF

;阴蒂感觉が自慰中毒＋１レベルでないと能力不足
IF ABL:0 < ABL:31 + 1
	I |= 4
	J |= 4
ENDIF

;最低でも1回・1個は必要
SIF A < 1
	A = 1
SIF B < 1
	B = 1
SIF C < 1
	C = 1
SIF D < 1
	D = 1
SIF E < 1
	E = 1

;自慰经验で上げる場合
;欲情点数が不足
SIF JUEL:5 < A
	I |= 1
;阴核点数が不足
SIF JUEL:0 < B
	I |= 1
;耻情点数が不足
SIF JUEL:8 < C
	I |= 1
;自慰经验が不足
SIF EXP:10 < D
	I |= 2

;调教自慰经验で上げる場合
;欲情点数で上げる
SIF JUEL:5 < A
	J |= 1
;阴核点数で上げる
SIF JUEL:0 < B
	J |= 1
;耻情点数で上げる
SIF JUEL:8 < C
	J |= 1
;调教自慰经验が必要
SIF EXP:11 < E
	J |= 2

IF I == 0 || J == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF
;
;
;