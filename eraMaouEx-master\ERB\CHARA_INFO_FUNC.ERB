﻿

@IS_ABLE_TO_ABILITY_UP(ARG)
#FUNCTION
{
RETURNF  (CFLAG:ARG:1 == 0
		|| (CFLAG:0:9 >= 20 && CFLAG:ARG:1 == 2))
	&& BASE:ARG:0 >= 1		
}
 
@IS_ABLE_TO_CLOTH(ARG)
#FUNCTION
RETURNF CFLAG:ARG:1 == 0 && BASE:ARG:0 >= 1


@CHARA_INFO_RESTORE_STATE(ARG)
SIF CFLAG:ARG:1 == 3
	CALL PARTY_DEL,ARG

CFLAG:ARG:1 = 0

@CHARA_INFO_RECOVER_HP(ARG)
LOCAL = (MAXBASE:ARG:0 - BASE:ARG:0)*10/3 + (MAXBASE:ARG:1 - BASE:ARG:1)*5/3
IF MONEY < LOCAL
	PRINTFORMW 需要金钱{LOCAL}G，金钱不够
	RETURN
ENDIF
PRINTFORML 要使用金钱恢复%SAVESTR:ARG%的体力和气力吗？
PRINTFORML 恢复{MAXBASE:ARG:0 - BASE:ARG:0}点体力和{MAXBASE:ARG:1 - BASE:ARG:1}点气力，需花费金钱{LOCAL}G
PRINTFORML [0] 立即恢复
PRINTFORML [1] 还是算了

$INPUT_LOOP1
INPUT
IF RESULT < 0
	GOTO INPUT_LOOP1
ELSEIF RESULT > 1
	GOTO INPUT_LOOP1
ELSEIF RESULT == 1
    RETURN
ELSEIF RESULT == 0
	PRINTFORMW 花费{LOCAL}G，恢复了%SAVESTR:ARG%的体力与气力
	MONEY -= LOCAL
	EX_FLAG:4444 -= LOCAL
	BASE:ARG:0 = MAXBASE:ARG:0
	BASE:ARG:1 = MAXBASE:ARG:1
ENDIF


@CHARA_INFO_UP_LEVEL(ARG)
LOCAL = 0, CFLAG:ARG:9
IF ARG == MASTER
	LOCAL += LOCAL:1 * 100 + 10
ELSEIF TALENT:ARG:220 == 1
	LOCAL += LOCAL:1 * 20 + 10
ELSE
	LOCAL += LOCAL:1 * 10 + 10
ENDIF
LOCAL -= EXP:ARG:80
SIF LOCAL < 0 
    LOCAL = 0

IF MONEY < LOCAL *100
    PRINTFORMW 需要金钱{LOCAL*100}G，金钱不够
	RETURN
ENDIF

PRINTFORML 要使用金钱提升%SAVESTR:ARG%的等级吗？
PRINTFORML 到下一级经验还要{LOCAL}点，需花费金钱{LOCAL*100}G
PRINTFORML [0] 提升等级
PRINTFORML [1] 还是算了

$INPUT_LOOP2
INPUT
IF RESULT < 0
	GOTO INPUT_LOOP2
ELSEIF RESULT > 1
	GOTO INPUT_LOOP2
ELSEIF RESULT == 1
    RETURN
ELSEIF RESULT == 0
	LOCALS = \@ ARG == MASTER ? %NAME:MASTER% # %SAVESTR:ARG% \@
	PRINTFORMW %NAME:MASTER%花费了{LOCAL*100}G，购买了经验{LOCAL}点
	MONEY -= LOCAL *100
	EX_FLAG:4444 -= LOCAL *100
	EXP:ARG:80 += LOCAL
	CALL LVUP, ARG
ENDIF


@CHARA_INFO_CALLBACK(ARG)
IF CFLAG:ARG:9 >= CFLAG:MASTER:9
	PRINTFORMW LV{CFLAG:MASTER:9}的%SAVESTR:MASTER%无法对LV{CFLAG:ARG:9}的%SAVESTR:ARG%发动传送魔法
	RETURN
ENDIF

LOCAL = CFLAG:ARG:9 * 100 / CFLAG:MASTER:9
LOCAL = LOCAL * LOCAL * MAXBASE:MASTER:1 / 10000

IF LOCAL > BASE:MASTER:1
	PRINTFORMW %NAME:MASTER%的气力不足以对%SAVESTR:ARG%发动传送魔法（需要{LOCAL}点，当前{BASE:MASTER:1}点）
	RETURN
ENDIF

PRINTFORML 要召回出击中的%SAVESTR:ARG%吗？
PRINTFORML 发动传送魔法需要消耗%NAME:MASTER%的气力{LOCAL}点
PRINTFORML [0] 立即召回
PRINTFORML [1] 还是算了

$INPUT_LOOP3
INPUT
IF RESULT < 0
	GOTO INPUT_LOOP3
ELSEIF RESULT > 1
	GOTO INPUT_LOOP3
ELSEIF RESULT == 1
    RETURN
ELSEIF RESULT == 0
    BASE:MASTER:1 -= LOCAL
    PRINTFORMW %NAME:MASTER%使用魔法把%SAVESTR:ARG%传送了回来！
    CALL PARTY_DEL,ARG
    CFLAG:ARG:1 = 0
ENDIF

