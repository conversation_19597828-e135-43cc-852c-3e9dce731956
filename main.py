"""
ERA AI Agent System Main Entry Point
"""

import asyncio
import argparse
import logging
import json
import warnings
from pathlib import Path
from typing import Dict, Any

# 設置 asyncio 日誌級別以抑制 aiohttp 未關閉會話的警告
# 這是 LiteLLM 的已知問題，不影響功能
logging.getLogger('asyncio').setLevel(logging.CRITICAL)

from config.settings import initialize_config, get_config, get_config_manager
from core.workflow import ERAAgentWorkflow
from core.models import get_model_manager
from core.rag import initialize_rag
from tools.mcp_tools import initialize_mcp_tools
from data.database import ERADatabase
from core.types import GameConfig

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ERA AI Agent System")
    parser.add_argument("--config", default=".env", help="Configuration file path")
    parser.add_argument("--game-title", default="我的ERA游戏", help="Game title")
    parser.add_argument("--game-description", default="由AI生成的ERA游戏", help="Game description")
    parser.add_argument("--character-count", type=int, default=5, help="Number of characters")
    parser.add_argument("--output-path", default="./generated_games", help="Output directory")
    parser.add_argument("--features", nargs="+", default=["basic", "training", "shop"], help="Game features")
    parser.add_argument("--validate-config", action="store_true", help="Validate configuration and exit")
    parser.add_argument("--init-rag", action="store_true", help="Initialize RAG system and exit")
    parser.add_argument("--interactive", action="store_true", help="Run in interactive mode")
    
    args = parser.parse_args()
    
    # 初始化配置
    if not initialize_config(args.config):
        print("Configuration initialization failed")
        return 1
        
    config = get_config()
    
    # 如果只是验证配置
    if args.validate_config:
        config_manager = get_config_manager()
        validation = config_manager.validate_config()
        summary = config_manager.get_config_summary()
        
        print("Configuration Summary:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))
        
        if validation["valid"]:
            print("\n✓ Configuration is valid")
            return 0
        else:
            print(f"\n✗ Configuration issues: {validation['issues']}")
            return 1
    
    # 初始化系统组件
    print("Initializing ERA AI Agent System...")
    
    # 初始化模型
    model_manager = get_model_manager(config)
    if not model_manager.test_connection():
        print("Model initialization failed")
        return 1
        
    # 初始化数据库
    db = ERADatabase(config.database_path)
    db.initialize_database()
    
    # 初始化RAG系统
    if config.enable_rag:
        if config.era_syntax_data_path:
            rag_initialized = initialize_rag(config.era_syntax_data_path)
            if not rag_initialized:
                print("RAG system initialization failed")
                if not args.init_rag:  # 如果不是专门初始化RAG，则退出
                    return 1
        else:
            print("ERA_SYNTAX_DATA_PATH not configured, RAG disabled")
            
    # 如果只是初始化RAG
    if args.init_rag:
        print("RAG system initialization completed")
        return 0
    
    # 初始化MCP工具
    if config.enable_mcp_tools:
        from core.rag import get_rag_system
        initialize_mcp_tools(db, get_rag_system())
    
    # 交互模式
    if args.interactive:
        return await run_interactive_mode()
    
    # 创建游戏配置
    game_config = GameConfig(
        game_title=args.game_title,
        game_description=args.game_description,
        character_count=args.character_count,
        features=args.features,
        output_path=args.output_path,
        encoding=config.default_encoding
    )
    
    # 运行游戏生成工作流
    print(f"\nGenerating ERA game: {game_config.game_title}")
    print(f"Output path: {game_config.output_path}")
    print(f"Characters: {game_config.character_count}")
    print(f"Features: {', '.join(game_config.features)}")
    
    workflow = ERAAgentWorkflow(config.database_path)
    
    try:
        result = await workflow.run_workflow(game_config)
        
        if result["status"] == "success":
            print(f"\n✓ Game generation completed successfully!")
            print(f"Generated {len(result['generated_files'])} files")
            print(f"Completed steps: {', '.join(result['completed_steps'])}")

            # 显示生成的文件
            if result["generated_files"]:
                print("\nGenerated files:")
                for file_path in result["generated_files"]:
                    print(f"  - {file_path}")

            return 0
        else:
            print(f"\n✗ Game generation failed: {result['error']}")
            return 1

    except Exception as e:
        logging.error(f"Workflow execution failed: {e}")
        print(f"\n✗ System error: {e}")
        return 1
    finally:
        # 清理模型管理器資源
        try:
            await model_manager.cleanup()
        except Exception as e:
            logging.warning(f"Cleanup error: {e}")

        # 額外的清理：關閉所有 aiohttp 會話
        try:
            import aiohttp
            import gc

            # 強制垃圾回收
            gc.collect()

            # 等待一小段時間讓連接關閉
            await asyncio.sleep(0.1)

        except Exception as e:
            logging.warning(f"Additional cleanup error: {e}")

async def run_interactive_mode():
    """交互模式"""
    print("\n=== ERA AI Agent Interactive Mode ===")
    print("Type 'help' for available commands, 'quit' to exit")

    while True:
        try:
            command = input("\nera-agent> ").strip()
            
            if command.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            elif command.lower() == 'help':
                print_help()
            elif command.lower() == 'status':
                await show_system_status()
            elif command.lower().startswith('generate'):
                await handle_generate_command(command)
            elif command.lower() == 'config':
                show_config()
            else:
                print(f"Unknown command: {command}. Type 'help' for available commands.")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
            
    return 0

def print_help():
    """显示帮助信息"""
    print("""
Available commands:
  help                 - Show this help message
  status              - Show system status
  config              - Show current configuration  
  generate <options>  - Generate a new ERA game
    Example: generate --title "My Game" --chars 3
  quit/exit/q         - Exit the program
    """)

async def show_system_status():
    """显示系统状态"""
    config = get_config()
    config_manager = get_config_manager()
    
    print("\n=== System Status ===")

    # 配置验证
    validation = config_manager.validate_config()
    status = "✓ OK" if validation["valid"] else "✗ Issues"
    print(f"Configuration: {status}")
    
    if validation["issues"]:
        for issue in validation["issues"]:
            print(f"  - {issue}")
    
    # 模型连接
    try:
        from .core.models import get_lm_client
        client = get_lm_client()
        model_status = "✓ Connected" if client.test_connection() else "✗ Failed"
        print(f"LM Studio: {model_status}")
    except Exception as e:
        print(f"LM Studio: ✗ Error - {e}")
    
    # RAG系统
    if config.enable_rag:
        try:
            from .core.rag import get_rag_system
            rag = get_rag_system()
            print(f"RAG System: ✓ Enabled")
        except Exception as e:
            print(f"RAG System: ✗ Error - {e}")
    else:
        print("RAG System: - Disabled")
    
    # 数据库
    try:
        db = ERADatabase(config.database_path)
        print(f"Database: ✓ {config.database_path}")
    except Exception as e:
        print(f"Database: ✗ Error - {e}")

def show_config():
    """显示配置信息"""
    config_manager = get_config_manager()
    summary = config_manager.get_config_summary()
    
    print("\n=== Current Configuration ===")
    print(json.dumps(summary, indent=2, ensure_ascii=False))

async def handle_generate_command(command: str):
    """处理生成命令"""
    # 简化的命令解析
    parts = command.split()
    
    # 默认配置
    game_config = GameConfig(
        game_title="交互式ERA游戏",
        game_description="通过交互模式生成的ERA游戏",
        character_count=3,
        features=["basic", "training"],
        output_path="./interactive_game"
    )
    
    # 解析简单参数
    for i, part in enumerate(parts):
        if part == "--title" and i + 1 < len(parts):
            game_config.game_title = parts[i + 1]
        elif part == "--chars" and i + 1 < len(parts):
            try:
                game_config.character_count = int(parts[i + 1])
            except ValueError:
                print("Invalid character count")
                return
    
    print(f"\nGenerating game: {game_config.game_title}")
    print("This may take a few minutes...")

    try:
        config = get_config()
        workflow = ERAAgentWorkflow(config.database_path)
        result = await workflow.run_workflow(game_config)

        if result["status"] == "success":
            print("\n✓ Game generated successfully!")
            print(f"Files saved to: {game_config.output_path}")
        else:
            print(f"\n✗ Generation failed: {result['error']}")

    except Exception as e:
        print(f"\n✗ Error: {e}")

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))