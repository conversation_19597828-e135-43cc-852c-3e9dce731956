#!/usr/bin/env python3
"""
ERA AI Agent System Setup Script
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_dependencies():
    """安装依赖包"""
    print("Installing dependencies...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装requirements.txt
        requirements_path = Path(__file__).parent / "requirements.txt"
        if requirements_path.exists():
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_path)])
        else:
            print("Warning: requirements.txt not found, installing core dependencies")
            core_deps = [
                "langgraph>=0.0.60",
                "litellm>=1.35.0", 
                "aiosqlite>=0.19.0",
                "numpy>=1.24.0",
                "scikit-learn>=1.3.0",
                "faiss-cpu>=1.7.4",
                "requests>=2.31.0",
                "python-dotenv>=1.0.0"
            ]
            for dep in core_deps:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                
        print("✓ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False

def setup_environment():
    """设置环境"""
    print("Setting up environment...")
    
    # 创建.env文件如果不存在
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        env_file.write_text(env_example.read_text())
        print("✓ Created .env file from template")
    
    # 创建必要的目录
    directories = [
        "generated_games",
        "logs",
        "cache"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        
    print("✓ Environment setup completed")
    return True

def check_services():
    """检查外部服务"""
    print("Checking external services...")
    
    # 检查LM Studio
    try:
        import requests
        response = requests.get("http://localhost:1234/v1/models", timeout=5)
        if response.status_code == 200:
            print("✓ LM Studio is running")
        else:
            print("⚠ LM Studio may not be running properly")
    except:
        print("⚠ LM Studio is not running or not accessible")
    
    # 检查Ollama
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama is running")
        else:
            print("⚠ Ollama may not be running properly")
    except:
        print("⚠ Ollama is not running or not accessible")
    
    return True

def initialize_database():
    """初始化数据库"""
    print("Initializing database...")
    
    try:
        from era_ai_agent.data.database import ERADatabase
        
        db = ERADatabase()
        db.initialize_database()
        
        print("✓ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

def load_era_syntax_data():
    """加载ERA语法数据"""
    print("Loading ERA syntax data...")
    
    try:
        from era_ai_agent.core.rag import initialize_rag
        from era_ai_agent.config.settings import get_config
        
        config = get_config()
        
        if not config.era_syntax_data_path:
            print("⚠ ERA_SYNTAX_DATA_PATH not configured in .env")
            return False
            
        if not os.path.exists(config.era_syntax_data_path):
            print(f"⚠ ERA syntax data path not found: {config.era_syntax_data_path}")
            return False
            
        success = initialize_rag(config.era_syntax_data_path)
        
        if success:
            print("✓ ERA syntax data loaded successfully")
        else:
            print("⚠ Failed to load ERA syntax data")
            
        return success
        
    except Exception as e:
        print(f"Error loading ERA syntax data: {e}")
        return False

def run_tests():
    """运行基本测试"""
    print("Running basic tests...")
    
    try:
        # 测试配置
        from era_ai_agent.config.settings import get_config_manager
        
        config_manager = get_config_manager()
        validation = config_manager.validate_config()
        
        if validation["valid"]:
            print("✓ Configuration validation passed")
        else:
            print(f"⚠ Configuration issues: {validation['issues']}")
            
        # 测试模型连接
        from era_ai_agent.core.models import get_lm_client
        
        client = get_lm_client()
        if client.test_connection():
            print("✓ Model connection test passed")
        else:
            print("⚠ Model connection test failed")
            
        return True
        
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

def main():
    """主安装流程"""
    print("=== ERA AI Agent System Setup ===\\n")
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Setting up environment", setup_environment),
        ("Checking external services", check_services),
        ("Initializing database", initialize_database),
        ("Loading ERA syntax data", load_era_syntax_data),
        ("Running tests", run_tests)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\\n{step_name}...")
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"Error in {step_name}: {e}")
            failed_steps.append(step_name)
    
    print("\\n=== Setup Summary ===")
    
    if failed_steps:
        print(f"⚠ Setup completed with issues in: {', '.join(failed_steps)}")
        print("\\nPlease check the error messages above and:")
        print("1. Ensure LM Studio is running on http://localhost:1234")
        print("2. Ensure Ollama is running on http://localhost:11434") 
        print("3. Check that ERA_SYNTAX_DATA_PATH is correctly set in .env")
        print("4. Install any missing dependencies manually")
        return False
    else:
        print("✓ Setup completed successfully!")
        print("\\nThe ERA AI Agent system is ready to use.")
        print("\\nNext steps:")
        print("1. Review and adjust settings in .env file")
        print("2. Start LM Studio and load your preferred model")
        print("3. Start Ollama and pull embedding model: ollama pull nomic-embed-text")
        print("4. Run: python -m era_ai_agent.main --help")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)