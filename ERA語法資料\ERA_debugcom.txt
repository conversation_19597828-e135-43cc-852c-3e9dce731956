﻿
<span style="font-size:small">※在默认情况下，调试命令是被禁用的。
你需要在设置对话框中勾选“启用调试命令（デバッグコマンドを使用する）”并重新启动Emuera。
</span>

调试命令是指在Emuera等待用户输入时键入的以<code>@</code>开始的脚本代码。

调试命令键入后，Emuera会立即执行这些代码，就像在ERB脚本文件中一样。

例如：

 	@MONEY = 10000
 	@PRINTV FLAG:200
 	@PRINTFORM %NAME:MASTER%的CFLAG(1) = {CFLAG:MASTER:1}
 	@ADDCHARA 1

如果只输入变量或表达式，则将返回这些值
 	@ FLAG:200
 	@ @"%NAME:MASTER%的CFLAG(1) = {CFLAG:MASTER:1}"


代码是否区分大小写取决于设置中的“忽略大小写（大文字小文字の違いを無視する）”。

注意，调试命令无法使用控制流的指令，例如IF或CALL。
此外请求输入的指令也无法使用，例如INPUT或WAIT。

下面是一些特殊的调试命令：
;@REBOOT
:重新启动，读取并加载emuera.config、csv、erb。
;@OUTPUT 
:将窗口当前的日志输出到emuera.log中。若文件已经存在则覆盖。
:这与OUTPUTLOG指令的动作相同。
;@EXIT 
:结束并关闭Emuera。与QUIT指令动作相同。
;@CONFIG 
:打开设置对话框。
;@DEBUG 
:打开调试对话框。仅在调试模式下有效。

除了上述的这些命令，也就是直接执行脚本代码的情况下，MASTER的名字与呼名将强制变更为“欺诈师（イカサマ）”。

这是因为调试命令可以很方便的用来作弊，是防止调试命令被滥用的措施。


== 外部链接 ==
* {{Jp}}Emuera Wiki(2015), [https://osdn.jp/projects/emuera/wiki/debugcom デバッグコマンド]

[[分类:EmueraWiki]]
