"""
ERA游戏文件生成模块
基于代理系统的各类游戏文件生成器
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
import asyncio

# 使用相对导入
from core.types import AgentState, GameConfig
from core.era_constants import (
    SYSTEM_FUNCTIONS, BUILTIN_VARIABLES, ERA_COMMANDS,
    CSV_STRUCTURE, DEFAULT_CHARACTER_ATTRIBUTES, ENCODING
)
from core.models import get_model_manager
from core.rag import get_rag_system
from tools.mcp_tools import get_tool_manager

class BaseERAAgent:
    """ERA代理基类"""
    
    def __init__(self, agent_id: str, agent_type: str):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.logger = logging.getLogger(f"{__name__}.{agent_id}")
        self.model_manager = get_model_manager()
        self.rag_system = get_rag_system()
        self.tool_manager = get_tool_manager()
        
    async def generate_content(self, prompt: str, context: Dict[str, Any] = None) -> str:
        """使用模型生成内容"""
        try:
            # 构建系统提示
            system_prompt = self._build_system_prompt()
            
            # 添加上下文信息
            if context:
                context_str = json.dumps(context, ensure_ascii=False, indent=2)
                prompt = f"上下文信息：\n{context_str}\n\n任务：\n{prompt}"
                
            # 構建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            result = await self.model_manager.chat_completion_async(messages)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            return ""
            
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        return f"""你是一个专业的ERA游戏{self.agent_type}代理。
你需要严格遵循ERA语法规范，生成符合Emuera引擎要求的代码和数据。

重要规则：
1. 只输出纯粹的ERA代码/数据，不要包含任何markdown标记（如```erb、```csv等）
2. 不要包含说明文字或解释，只输出文件内容本身
3. 语法必须正确，符合ERA/Emuera引擎要求
4. 编码为UTF-8 with BOM
5. 使用分号(;)作为注释符号
6. 遵循ERA游戏开发最佳实践

输出格式：直接输出文件内容，不要任何包装或说明。"""

class ERBScriptAgent(BaseERAAgent):
    """ERB脚本生成代理"""
    
    def __init__(self):
        super().__init__("erb_script_agent", "erb_script")
        
    async def generate_system_erb(self, game_config: GameConfig) -> str:
        """生成SYSTEM.ERB文件"""
        context = {
            "game_title": game_config.game_title,
            "features": game_config.features
        }
        
        prompt = """参考以下格式生成ERA游戏的SYSTEM.ERB文件：

@EVENTFIRST
#DIM CHARA, 1
#DIM ID_OF_NEWCHARA
#DIM TEMP

TARGET = -1
BOUGHT = -1

DAY:1 = 1

;基础设置
FLAG:5 = 17179934119
FLAG:35 = 0
FLAG:37 = 1

RETURN

@SYSTEM_TITLE
PRINTFORML {game_title}
RETURN

@SYSTEM_LOADEND
RETURN

@SYSTEM_SAVEEND
RETURN

@SYSTEM_TURNEND
RETURN

请按照这个格式生成适合骑士与玩偶主题的系统文件，包含必要的系统函数。
只输出ERB代码，不要任何说明文字。"""
        
        # 暂时跳过RAG和工具验证，直接生成内容
        content = await self.generate_content(prompt, context)

        return content
        
    async def generate_title_erb(self, game_config: GameConfig) -> str:
        """生成TITLE.ERB文件"""
        context = {
            "game_title": game_config.game_title,
            "game_description": game_config.game_description
        }
        
        prompt = """生成ERA游戏的TITLE.ERB文件，这是游戏的标题画面文件。
        
要求：
1. 实现@SYSTEM_TITLE函数
2. 显示游戏标题和基本信息
3. 提供新游戏和加载游戏选项
4. 处理用户输入

请生成完整的TITLE.ERB代码。"""
        
        content = await self.generate_content(prompt, context)
        return content
        
    async def generate_variables_erh(self, game_config: GameConfig) -> str:
        """生成VARIABLES.ERH文件"""
        context = {
            "character_count": game_config.character_count,
            "features": game_config.features
        }

        prompt = """生成ERA游戏的VARIABLES.ERH文件。

重要语法规则：
1. 所有变量声明必须以#开头：#DIM, #DIMS, #DEFINE
2. 注释使用分号(;)开头
3. 字符串数组使用#DIMS声明
4. 数值数组使用#DIM声明
5. 宏定义使用#DEFINE

参考格式：
#DEFINE MAX_CHARANUM 90

;==================================================
;非ユニーク性格のTALENT番号の配列
#DIM ID_OF_GENERAL_CHARASTERISTICS = 160,161,162,163,164,166,172,173,174,175

;头发颜色の配列
#DIMS ARR_HAIRCOLOR = "","金发","栗发","黑发","红发","银发","蓝发","绿发","紫发","白发","暗金发","粉发"

;用于储存保留的TRAINNAEM, 函数@TRAIN_NAME_INIT用于初始化
#DIMS TRAIN_NAME,500

;用于记录最后保存的存档编号
#DIM LASTSAVE_NO,10 = -1

请按照这个格式生成适合骑士与玩偶主题的变量定义，包含必要的宏定义和全局变量。
确保所有变量声明都以#开头！
只输出ERH代码，不要任何说明文字。"""

        guidance = self.rag_system.get_era_guidance("variables", "变量定义和宏")
        prompt += f"\n\nERA语法参考：\n{guidance}"

        content = await self.generate_content(prompt, context)

        # 验证和修复生成的内容
        content = self._fix_erh_syntax(content)
        return content
        
    async def generate_train_main_erb(self, game_config: GameConfig) -> str:
        """生成TRAIN_MAIN.ERB文件"""
        prompt = """生成ERA游戏的TRAIN_MAIN.ERB文件，实现训练/调教系统的主要逻辑。
        
要求：
1. 实现@TRAIN函数
2. 显示角色状态
3. 提供调教选项菜单
4. 处理调教指令的调用

请生成完整的TRAIN_MAIN.ERB代码。"""
        
        guidance = self.rag_system.get_era_guidance("game_flow", "TRAIN流程实现")
        prompt += f"\n\nERA语法参考：\n{guidance}"
        
        content = await self.generate_content(prompt, game_config.__dict__)

        # 验证和修复生成的内容
        content = self._fix_erb_syntax(content)
        return content

    def _fix_erh_syntax(self, content: str) -> str:
        """修复ERH文件语法错误"""
        if not content:
            return content

        lines = content.split('\n')
        fixed_lines = []

        # ERA内置变量列表，避免冲突
        builtin_vars = {'FLAG', 'TFLAG', 'PALAM', 'EXP', 'ABL', 'TALENT', 'MARK', 'EQU', 'TEQUIP'}

        for line in lines:
            stripped = line.strip()

            # 跳过空行和注释
            if not stripped or stripped.startswith(';'):
                fixed_lines.append(line)
                continue

            # 修复变量声明语法
            if stripped.startswith('DIM ') and not stripped.startswith('#DIM'):
                fixed_line = line.replace('DIM ', '#DIM ')

                # 检查是否使用了内置变量名
                var_name = stripped.split()[1].split(',')[0].split('[')[0]
                if var_name in builtin_vars:
                    # 重命名为避免冲突
                    new_var_name = f"GAME_{var_name}"
                    fixed_line = fixed_line.replace(var_name, new_var_name)
                    self.logger.warning(f"Renamed builtin variable: {var_name} -> {new_var_name}")

                fixed_lines.append(fixed_line)
                self.logger.warning(f"Fixed DIM syntax: {stripped} -> {fixed_line.strip()}")
            elif stripped.startswith('DIMS ') and not stripped.startswith('#DIMS'):
                fixed_line = line.replace('DIMS ', '#DIMS ')
                fixed_lines.append(fixed_line)
                self.logger.warning(f"Fixed DIMS syntax: {stripped} -> {fixed_line.strip()}")
            elif stripped.startswith('DEFINE ') and not stripped.startswith('#DEFINE'):
                fixed_line = line.replace('DEFINE ', '#DEFINE ')
                fixed_lines.append(fixed_line)
                self.logger.warning(f"Fixed DEFINE syntax: {stripped} -> {fixed_line.strip()}")
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _fix_erb_syntax(self, content: str) -> str:
        """修复ERB文件语法错误"""
        if not content:
            return content

        lines = content.split('\n')
        fixed_lines = []

        for line in lines:
            stripped = line.strip()

            # 跳过空行和注释
            if not stripped or stripped.startswith(';'):
                fixed_lines.append(line)
                continue

            # 修复函数定义语法
            if stripped.startswith('FUNCTION '):
                # 将FUNCTION替换为@符号
                func_name = stripped.replace('FUNCTION ', '').strip()
                fixed_line = f"@{func_name}"
                fixed_lines.append(fixed_line)
                self.logger.warning(f"Fixed function syntax: {stripped} -> {fixed_line}")
            elif stripped.startswith('@') and stripped.endswith(':'):
                # 移除函数定义末尾的冒号
                fixed_line = line.rstrip(':')
                fixed_lines.append(fixed_line)
                self.logger.warning(f"Removed colon from function: {stripped} -> {fixed_line.strip()}")
            elif 'CLS' in stripped and stripped == 'CLS':
                # 替换CLS为CLEARLINE
                fixed_line = line.replace('CLS', 'CLEARLINE 50')
                fixed_lines.append(fixed_line)
                self.logger.warning(f"Fixed CLS command: {stripped} -> {fixed_line.strip()}")
            elif 'CLEARTEXT' in stripped and stripped == 'CLEARTEXT':
                # 替换CLEARTEXT为CLEARLINE
                fixed_line = line.replace('CLEARTEXT', 'CLEARLINE 50')
                fixed_lines.append(fixed_line)
                self.logger.warning(f"Fixed CLEARTEXT command: {stripped} -> {fixed_line.strip()}")
            elif '{' in stripped or '}' in stripped or '"table_name"' in stripped:
                # 移除JSON格式的内容
                self.logger.warning(f"Removed JSON content: {stripped}")
                continue
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

class CSVDataAgent(BaseERAAgent):
    """CSV数据生成代理"""

    def __init__(self):
        super().__init__("csv_data_agent", "csv_data")

    async def generate_character_csv(self, character_info: Dict[str, Any]) -> str:
        """生成角色CSV文件"""
        try:
            # 使用工具创建角色数据
            char_data_result = await self.tool_manager.call_tool(
                "create_character_data",
                {"character_info": character_info}
            )

            if char_data_result["success"]:
                char_data = char_data_result["result"]

                # 格式化为CSV
                csv_result = await self.tool_manager.call_tool(
                    "format_csv_data",
                    {"data": [char_data], "csv_type": "Chara"}
                )

                if csv_result["success"]:
                    return csv_result["result"]

            # 如果工具调用失败，手动生成
            return self._generate_character_csv_manual(character_info)

        except Exception as e:
            self.logger.error(f"CSV generation failed: {e}")
            return self._generate_character_csv_manual(character_info)

    def _generate_character_csv_manual(self, character_info: Dict[str, Any]) -> str:
        """手动生成角色CSV"""
        lines = [
            ";角色数据文件",
            ";generated by ERA AI Agent",
            "",
            f"{character_info.get('id', 0)},{character_info.get('name', '未命名')},{character_info.get('callname', '')},{character_info.get('nickname', '')}"
        ]
        return "\n".join(lines)

    async def generate_abl_csv(self, abilities: List[Dict[str, Any]]) -> str:
        """生成能力定义CSV"""
        prompt = """参考以下格式生成ERA游戏的Abl.csv文件：

0,阴蒂感觉
1,乳房感觉
2,私处感觉
3,肛门感觉
4,局部感觉
10,顺从
11,欲望
12,技巧
13,侍奉技术
14,性交技术
15,话术

请按照这个格式生成适合骑士与玩偶主题的能力定义。
格式要求：每行必须是"编号,能力名称"，编号必须是整数。
不要包含注释行或分隔线。
只输出CSV数据行，不要任何说明文字。"""

        context = {"abilities": abilities}
        content = await self.generate_content(prompt, context)

        return content

    async def generate_talent_csv(self, talents: List[Dict[str, Any]]) -> str:
        """生成素质定义CSV"""
        prompt = """参考以下格式生成ERA游戏的Talent.csv文件：

0,处女,
1,童贞,
9,崩坏,
10,胆怯,
11,反抗心,
12,刚强,
13,坦率,

请按照这个格式生成适合骑士与玩偶主题的角色素质。
格式要求：每行必须是"编号,素质名称,"，编号必须是整数。
不要包含注释行或分隔线。
只输出CSV数据行，不要任何说明文字。"""

        context = {"talents": talents}
        content = await self.generate_content(prompt, context)

        return content

    async def generate_train_csv(self, train_commands: List[Dict[str, Any]]) -> str:
        """生成训练指令CSV"""
        prompt = """参考以下格式生成ERA游戏的Train.csv文件：

0,爱抚,
1,舔阴,
2,肛门爱抚,
3,自慰,
4,口交(主),
5,胸爱抚,
6,接吻,
10,振动宝石,
11,壶虫,
12,振动杖,

请按照这个格式生成适合骑士与玩偶主题的训练指令。
格式要求：每行必须是"编号,指令名称,"，编号必须是整数。
不要包含注释行或分隔线。
只输出CSV数据行，不要任何说明文字。"""

        context = {"train_commands": train_commands}
        content = await self.generate_content(prompt, context)

        return content

class SystemFlowAgent(BaseERAAgent):
    """系统流程代理"""

    def __init__(self):
        super().__init__("system_flow_agent", "system_flow")

    async def implement_game_flows(self, game_config: GameConfig) -> Dict[str, str]:
        """实现游戏流程"""
        flows = {}

        # 实现各个系统流程
        for flow_name in ["EVENTFIRST", "SHOP", "ABLUP", "AFTERTRAIN", "TURNEND"]:
            flow_content = await self._generate_flow_function(flow_name, game_config)
            flows[flow_name] = flow_content

        return flows

    async def _generate_flow_function(self, flow_name: str, game_config: GameConfig) -> str:
        """生成流程函数"""

        if flow_name == "ABLUP":
            prompt = """生成ERA游戏的ABLUP函数：

@ABLUP
SIF TARGET < 0 || TARGET >= CHARANUM
    RETURN

SELECTCASE ARG
    CASE 0
        ABL:TARGET:0 += 1
        PRINTL 骑士精神提升了！
    CASE 1
        ABL:TARGET:1 += 1
        PRINTL 剑术提升了！
    CASEELSE
        PRINTL 未知的能力类型
ENDSELECT

RETURN

请按照这个格式生成适合骑士与玩偶主题的能力提升脚本。
注意：
1. 使用PRINTL而不是PRINT
2. 不要使用大括号
3. 不要调用不存在的函数如UPDATE_STATUS()或GAME_OVER()
4. 使用正确的ERA语法
只输出ERB代码，不要任何说明文字。"""
        else:
            prompt = f"""生成ERA游戏的{flow_name}流程函数。

请根据ERA游戏引擎的规范，实现{flow_name}函数的完整逻辑。
包含必要的注释和错误处理。
注意：
1. 使用PRINTL而不是PRINT
2. 不要使用大括号
3. 使用正确的ERA语法
只输出ERB代码，不要任何说明文字。"""

        content = await self.generate_content(prompt, game_config.__dict__)
        return content

class CharacterAgent(BaseERAAgent):
    """角色生成代理"""

    def __init__(self):
        super().__init__("character_agent", "character")

    async def create_characters(self, character_count: int, game_theme: str) -> List[Dict[str, Any]]:
        """创建游戏角色"""
        characters = []

        prompt = f"""为ERA游戏创建{character_count}个角色。游戏主题：{game_theme}

要求：
1. 每个角色要有独特的姓名、称呼和昵称
2. 角色要符合游戏主题
3. 提供角色的基本属性和背景
4. 返回JSON格式的角色信息

请生成角色列表。"""

        content = await self.generate_content(prompt)

        try:
            # 嘗試多種方式解析角色信息
            import re
            import json

            # 方法1: 尋找JSON數組
            json_match = re.search(r'\[.*?\]', content, re.DOTALL)
            if json_match:
                try:
                    characters_data = json.loads(json_match.group())
                    if isinstance(characters_data, list):
                        characters = characters_data
                        self.logger.info(f"Successfully parsed {len(characters)} characters from JSON")
                except json.JSONDecodeError:
                    pass

            # 方法2: 如果JSON解析失敗，嘗試解析文本格式
            if not characters:
                # 尋找角色名稱模式
                name_patterns = [
                    r'名[前稱字]?[：:]\s*([^\n\r,，]+)',
                    r'姓名[：:]\s*([^\n\r,，]+)',
                    r'"name"[：:]\s*"([^"]+)"'
                ]

                found_names = []
                for pattern in name_patterns:
                    matches = re.findall(pattern, content)
                    found_names.extend(matches)

                # 創建基於找到名稱的角色
                for i, name in enumerate(found_names[:character_count]):
                    characters.append({
                        "id": i,
                        "name": name.strip(),
                        "callname": name.strip(),
                        "nickname": "",
                        "description": f"基於AI生成內容創建的角色"
                    })

            # 方法3: 如果還是沒有角色，創建默認角色
            if not characters:
                for i in range(character_count):
                    characters.append({
                        "id": i,
                        "name": f"角色{i+1}",
                        "callname": f"角色{i+1}",
                        "nickname": "",
                        "description": f"默認角色{i+1}"
                    })

        except Exception as e:
            self.logger.error(f"Character parsing failed: {e}")
            # 創建默認角色
            for i in range(character_count):
                characters.append({
                    "id": i,
                    "name": f"角色{i+1}",
                    "callname": f"角色{i+1}",
                    "nickname": "",
                    "description": f"默認角色{i+1}"
                })

        return characters

class GameLogicAgent(BaseERAAgent):
    """游戏逻辑代理"""

    def __init__(self):
        super().__init__("game_logic_agent", "game_logic")

    async def integrate_game_logic(self, features: List[str], generated_files: List[str]) -> Dict[str, Any]:
        """整合游戏逻辑"""
        integration_result = {
            "config_files": [],
            "erb_patches": [],
            "validation_results": []
        }

        # 生成配置文件
        config_content = await self._generate_config_files(features)
        integration_result["config_files"] = config_content

        # 验证生成的文件
        for file_path in generated_files:
            if file_path.endswith('.ERB'):
                # 验证ERB文件
                validation = await self._validate_erb_file(file_path)
                integration_result["validation_results"].append(validation)

        return integration_result

    async def _generate_config_files(self, features: List[str]) -> Dict[str, str]:
        """生成配置文件"""
        configs = {}

        # 生成默认配置
        default_config = await self._generate_default_config(features)
        configs["_default.config"] = default_config

        # 生成固定配置
        fixed_config = await self._generate_fixed_config()
        configs["_fixed.config"] = fixed_config

        # 生成替换配置
        replace_config = await self._generate_replace_config()
        configs["_replace.csv"] = replace_config

        return configs

    async def _generate_default_config(self, features: List[str]) -> str:
        """生成默认配置文件"""
        prompt = """生成ERA游戏的_default.config配置文件。

要求：
1. 包含游戏显示设置
2. 包含引擎配置参数
3. 根据游戏特性调整配置

请生成完整的配置内容。"""

        context = {"features": features}
        content = await self.generate_content(prompt, context)
        return content

    async def _generate_fixed_config(self) -> str:
        """生成固定配置文件"""
        return """#固定配置文件
#generated by ERA AI Agent

#基本设置
FONTNAME = MS Gothic
FONTSIZE = 16
LINEHEIGHT = 18

#窗口设置
WIDTH = 800
HEIGHT = 600
"""

    async def _generate_replace_config(self) -> str:
        """生成替换配置文件"""
        return """;替换配置文件
;generated by ERA AI Agent

;基本替换
%%GAME_TITLE%%,ERA游戏标题
%%VERSION%%,1.0.0
"""

    async def _validate_erb_file(self, file_path: str) -> Dict[str, Any]:
        """验证ERB文件"""
        try:
            # 这里应该读取实际文件内容进行验证
            # 为了演示，返回模拟结果
            return {
                "file_path": file_path,
                "valid": True,
                "errors": [],
                "warnings": []
            }
        except Exception as e:
            return {
                "file_path": file_path,
                "valid": False,
                "errors": [str(e)],
                "warnings": []
            }

class ERAGameGenerator:
    """ERA游戏生成器主类"""

    def __init__(self):
        self.agents = {
            "erb_script": ERBScriptAgent(),
            "csv_data": CSVDataAgent(),
            "system_flow": SystemFlowAgent(),
            "character": CharacterAgent(),
            "game_logic": GameLogicAgent()
        }
        self.logger = logging.getLogger(__name__)

    async def generate_complete_game(self, game_config: GameConfig) -> Dict[str, Any]:
        """生成完整的ERA游戏"""
        results = {
            "erb_files": {},
            "csv_files": {},
            "config_files": {},
            "characters": [],
            "validation_results": []
        }

        try:
            # 1. 生成ERB脚本文件
            erb_agent = self.agents["erb_script"]
            results["erb_files"]["SYSTEM.ERB"] = await erb_agent.generate_system_erb(game_config)
            results["erb_files"]["TITLE.ERB"] = await erb_agent.generate_title_erb(game_config)
            results["erb_files"]["VARIABLES.ERH"] = await erb_agent.generate_variables_erh(game_config)
            results["erb_files"]["TRAIN_MAIN.ERB"] = await erb_agent.generate_train_main_erb(game_config)

            # 2. 创建角色
            character_agent = self.agents["character"]
            characters = await character_agent.create_characters(
                game_config.character_count,
                game_config.game_description
            )
            results["characters"] = characters

            # 3. 生成CSV数据文件
            csv_agent = self.agents["csv_data"]

            # 生成角色CSV
            for i, char in enumerate(characters):
                char_csv = await csv_agent.generate_character_csv(char)
                results["csv_files"][f"Chara{i}.csv"] = char_csv

            # 生成其他CSV文件
            results["csv_files"]["Abl.csv"] = await csv_agent.generate_abl_csv([])
            results["csv_files"]["Talent.csv"] = await csv_agent.generate_talent_csv([])
            results["csv_files"]["Train.csv"] = await csv_agent.generate_train_csv([])

            # 4. 实现系统流程
            flow_agent = self.agents["system_flow"]
            flows = await flow_agent.implement_game_flows(game_config)
            for flow_name, flow_content in flows.items():
                results["erb_files"][f"{flow_name}.ERB"] = flow_content

            # 5. 整合游戏逻辑
            logic_agent = self.agents["game_logic"]
            generated_files = list(results["erb_files"].keys()) + list(results["csv_files"].keys())
            integration = await logic_agent.integrate_game_logic(
                game_config.features,
                generated_files
            )
            results["config_files"] = integration["config_files"]
            results["validation_results"] = integration["validation_results"]

            self.logger.info("ERA game generation completed successfully")

        except Exception as e:
            self.logger.error(f"Game generation failed: {e}")
            results["error"] = str(e)

        return results

    async def save_game_files(self, game_config: GameConfig,
                            generation_results: Dict[str, Any]) -> List[str]:
        """保存游戏文件到磁盘"""
        saved_files = []
        output_path = Path(game_config.output_path)

        try:
            # 创建目录结构
            (output_path / "ERB").mkdir(parents=True, exist_ok=True)
            (output_path / "CSV" / "Chara").mkdir(parents=True, exist_ok=True)
            (output_path / "resources").mkdir(parents=True, exist_ok=True)

            # 保存ERB文件
            for filename, content in generation_results["erb_files"].items():
                file_path = output_path / "ERB" / filename
                # ERH文件不使用BOM，其他ERB文件使用BOM
                encoding = 'utf-8' if filename.endswith('.ERH') else 'utf-8-sig'
                with open(file_path, 'w', encoding=encoding) as f:
                    f.write(content)
                saved_files.append(str(file_path))

            # 保存CSV文件
            for filename, content in generation_results["csv_files"].items():
                if filename.startswith("Chara"):
                    file_path = output_path / "CSV" / "Chara" / filename
                else:
                    file_path = output_path / "CSV" / filename

                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    f.write(content)
                saved_files.append(str(file_path))

            # 保存配置文件
            for filename, content in generation_results["config_files"].items():
                file_path = output_path / filename
                encoding = 'utf-8-sig' if filename.endswith('.csv') else 'utf-8'
                with open(file_path, 'w', encoding=encoding) as f:
                    f.write(content)
                saved_files.append(str(file_path))

            # 创建基本的资源文件
            img_csv_path = output_path / "resources" / "img.csv"
            with open(img_csv_path, 'w', encoding='utf-8-sig') as f:
                f.write(";图片资源配置\n;generated by ERA AI Agent\n")
            saved_files.append(str(img_csv_path))

            self.logger.info(f"Saved {len(saved_files)} game files")

        except Exception as e:
            self.logger.error(f"Failed to save game files: {e}")

        return saved_files

# 全局生成器实例
_game_generator = None

def get_game_generator() -> ERAGameGenerator:
    """获取游戏生成器实例"""
    global _game_generator
    if _game_generator is None:
        _game_generator = ERAGameGenerator()
    return _game_generator