﻿;反抗刻印の消去処理とその可否判定
;eratohoA ver1,204のスクリプトを参考に処理を簡略化

;>屈服刻印を消してみるテスト
;>使う場合は↓をABL.ERBの「PRINTL [100] 能力値アップの終了」の前に挿入すること
;>PRINTFORML [99]%MARKNAME:3%-LV{MARK:3}
;>
;>SYSTEM_SOURCE_SUB.ERB内の@MARK_GOT_CHECKも弄ると再度反抗刻印が付いたりしないので吉
;>●改変前
;>IF A >= 500 && A < 1200 && MARK:3 <= 0
;>	MARK:3 = 1
;>●改変後
;>IF A >= 500 && A < 1200 && MARK:4 <= 0
;>	MARK:3 = 1
;>	MARK:4 = 1
;>●以上の内容を3LVの3箇所にすること
;>上記の解らないor解ってもやらない場合に再度屈服刻印付いた場合の弊害は保証しかねます

;-------------------------------------------------
;反抗刻印の消去処理
;-------------------------------------------------
@ABLUP99
DRAWLINE
;PRINTL 奴隶的反抗程度减少了。
;PRINTL 反抗程度越高，越不遵从主人的命令。
;CUSTOMDRAWLINE ‥
;反抗刻印が存在しない場合
IF MARK:3 <= 0
	PRINTL 不存在反抗行为
	WAIT
	RETURN 0
ENDIF

;必要な屈服点数
A = 0
;必要な顺从LV
B = 0

;条件別にＯＫかダメかを記録する
I = 0

CALL DECIDE_ABLUP99

;反抗刻印Lvと同じLvの屈服刻印が必要
PRINTFORML %MARKNAME:2%{MARK:3}以上(现在LV{MARK:2})且

;反抗刻印Lv+2Lvの顺从が必要
PRINTFORML %ABLNAME:10%LV{B}以上(现在LV{ABL:10})必要

PRINTFORM [0] - %PALAMNAME:6%点数×{JUEL:6}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 

PRINTL [100] - 停止

INPUT
IF RESULT != 0 && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

MARK:3 -= 1

JUEL:6 -= A

PRINTFORML %MARKNAME:3%下降为LV{MARK:3}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP99
;-------------------------------------------------
MARK:3 --

IF I == 0
	JUEL:6 -= A
ENDIF


;-------------------------------------------------
;反抗刻印の消去可否判定
;-------------------------------------------------
@DECIDE_ABLUP99
;反抗刻印が存在しない場合
SIF MARK:3 <= 0
	RETURN 0

;判定変数を空に
I = 0

B = 0

IF MARK:3 == 1
	A = 5000
ELSEIF MARK:3 == 2
	A = 10000
ELSEIF MARK:3 == 3
	A = 50000
ENDIF

;刚强
IF TALENT:12
	TIMES A , 3.00
ENDIF

;嚣张
IF TALENT:16
	TIMES A , 1.50
ENDIF

;坦率
IF TALENT:13
	TIMES A , 0.50
ENDIF

;爱慕
IF TALENT:85
	TIMES A , 0.50
ENDIF

;反抗刻印Lvと同じLvの屈服刻印が必要
SIF MARK:3 > MARK:2
	I |= 2

;反抗刻印Lv+2Lvの顺从が必要
B = MARK:3 + 2
SIF B > ABL:10
	I |= 4

;屈服の宝珠が不足
SIF JUEL:6 < A
	I |= 1

IF I == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF
;
;
;