#!/usr/bin/env python3
"""
测试修复后的ERA游戏文件语法
"""

import os
import re
import sys
from pathlib import Path

def check_erh_file_syntax(file_path):
    """检查ERH文件语法"""
    print(f"\n=== 检查 {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    errors = []
    warnings = []
    
    for i, line in enumerate(lines, 1):
        stripped = line.strip()
        
        # 跳过空行和注释
        if not stripped or stripped.startswith(';'):
            continue
            
        # 检查变量声明语法
        if re.match(r'^(DIM|DIMS|DEFINE)\s+', stripped):
            if not stripped.startswith('#'):
                errors.append(f"行{i}: 变量声明必须以#开头: {stripped}")
        
        # 检查是否使用了内置变量
        builtin_vars = ['FLAG', 'TFLAG', 'PALAM', 'EXP', 'ABL', 'TALENT', 'MARK', 'EQU', 'TEQUIP']
        for var in builtin_vars:
            if re.match(rf'^#DIM\s+{var}[\s,]', stripped):
                warnings.append(f"行{i}: 使用了内置变量名 {var}: {stripped}")
    
    # 输出结果
    if errors:
        print("❌ 发现错误:")
        for error in errors:
            print(f"  {error}")
    
    if warnings:
        print("⚠️  发现警告:")
        for warning in warnings:
            print(f"  {warning}")
    
    if not errors and not warnings:
        print("✅ 语法检查通过")
        return True
    elif not errors:
        print("✅ 语法正确，但有警告")
        return True
    else:
        return False

def check_erb_file_syntax(file_path):
    """检查ERB文件语法"""
    print(f"\n=== 检查 {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    errors = []
    warnings = []
    
    for i, line in enumerate(lines, 1):
        stripped = line.strip()
        
        # 跳过空行和注释
        if not stripped or stripped.startswith(';'):
            continue
            
        # 检查函数定义语法
        if stripped.startswith('FUNCTION '):
            errors.append(f"行{i}: 函数定义应使用@符号: {stripped}")
        
        # 检查是否有JSON格式内容
        if '{' in stripped or '}' in stripped or '"table_name"' in stripped:
            errors.append(f"行{i}: 包含非法的JSON格式内容: {stripped}")
        
        # 检查过时的命令
        if stripped == 'CLS':
            warnings.append(f"行{i}: 建议使用CLEARLINE代替CLS: {stripped}")
        elif stripped == 'CLEARTEXT':
            warnings.append(f"行{i}: 建议使用CLEARLINE代替CLEARTEXT: {stripped}")
    
    # 输出结果
    if errors:
        print("❌ 发现错误:")
        for error in errors:
            print(f"  {error}")
    
    if warnings:
        print("⚠️  发现警告:")
        for warning in warnings:
            print(f"  {warning}")
    
    if not errors and not warnings:
        print("✅ 语法检查通过")
        return True
    elif not errors:
        print("✅ 语法正确，但有警告")
        return True
    else:
        return False

def check_all_game_files():
    """检查所有游戏文件"""
    print("开始检查ERA游戏文件语法...")
    
    game_dir = Path("generated_games")
    erb_dir = game_dir / "ERB"
    
    if not erb_dir.exists():
        print(f"❌ 游戏目录不存在: {erb_dir}")
        return False
    
    # 检查ERH文件
    erh_files = list(erb_dir.glob("*.ERH"))
    erb_files = list(erb_dir.glob("*.ERB"))
    
    print(f"找到 {len(erh_files)} 个ERH文件和 {len(erb_files)} 个ERB文件")
    
    all_passed = True
    
    # 检查ERH文件
    for file_path in erh_files:
        if not check_erh_file_syntax(file_path):
            all_passed = False
    
    # 检查ERB文件
    for file_path in erb_files:
        if not check_erb_file_syntax(file_path):
            all_passed = False
    
    return all_passed

def test_specific_fixes():
    """测试特定的修复"""
    print("\n=== 测试特定修复 ===")
    
    # 测试VARIABLES.ERH的修复
    variables_file = "generated_games/ERB/VARIABLES.ERH"
    if os.path.exists(variables_file):
        with open(variables_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有FLAG和TFLAG冲突
        if 'DIM FLAG,' in content and '#DIM GAME_FLAG,' not in content:
            print("❌ VARIABLES.ERH仍然包含FLAG变量冲突")
        elif '#DIM GAME_FLAG,' in content:
            print("✅ VARIABLES.ERH的FLAG变量冲突已修复")
        
        if 'DIM TFLAG,' in content and '#DIM TEMP_FLAG,' not in content:
            print("❌ VARIABLES.ERH仍然包含TFLAG变量冲突")
        elif '#DIM TEMP_FLAG,' in content:
            print("✅ VARIABLES.ERH的TFLAG变量冲突已修复")
    
    # 测试AFTERTRAIN.ERB的修复
    aftertrain_file = "generated_games/ERB/AFTERTRAIN.ERB"
    if os.path.exists(aftertrain_file):
        with open(aftertrain_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'FUNCTION AFTERTRAIN' in content:
            print("❌ AFTERTRAIN.ERB仍然使用FUNCTION语法")
        elif '@AFTERTRAIN' in content:
            print("✅ AFTERTRAIN.ERB的函数定义已修复")
    
    # 测试TITLE.ERB的修复
    title_file = "generated_games/ERB/TITLE.ERB"
    if os.path.exists(title_file):
        with open(title_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '"table_name"' in content or '{' in content:
            print("❌ TITLE.ERB仍然包含JSON格式内容")
        else:
            print("✅ TITLE.ERB的JSON内容已清理")

def main():
    """主函数"""
    print("ERA游戏文件语法检查工具")
    print("=" * 50)
    
    # 检查所有文件
    all_passed = check_all_game_files()
    
    # 测试特定修复
    test_specific_fixes()
    
    # 输出总结
    print("\n" + "=" * 50)
    print("检查结果总结:")
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有文件语法检查通过！")
        print("💡 游戏文件应该可以正常加载运行")
        return 0
    else:
        print("❌ 部分文件存在语法错误")
        print("💡 请根据上述错误信息进行修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
