﻿




;----------------------------------------------
@SHOW_LIST_TRAINABLE(NO_PAGE,NUM_PAGE,LIST_POS)
;----------------------------------------------
#DIM NO_PAGE
#DIM NUM_PAGE
#DIM T_LCOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
;調教可能奴隷の一覧表示
;ついでに調教可能人数を返す
LOCAL = 0
FOR COUNT, LIST_POS, CHARANUM
	IF IS_TRAINABLE(COUNT) == 0
		IF T_LCOUNT < (NO_PAGE + 1)*NUM_PAGE + 1 && T_LCOUNT >= NO_PAGE* NUM_PAGE + 1
		PRINTFORM [{COUNT,2}] %SAVESTR:COUNT,12,LEFT% %GET_JOB_NAME(COUNT),6,LEFT% LV{CFLAG:COUNT:9,4,RIGHT}　HP%BARSTR(BASE:COUNT:0,MAXBASE:COUNT:0,8)% 调教回数:{CFLAG:COUNT:10,3,LEFT}
		;爱、淫乱
		IF TALENT:COUNT:爱慕
			SETCOLOR 255,100,100
			PRINT <爱  慕>
			RESETCOLOR
		ELSEIF TALENT:COUNT:淫乱
			SETCOLOR 255,100,100
			PRINT <淫  乱>
			RESETCOLOR
		ELSE
			SETCOLOR 100,100,100
			PRINT <未沦陷>
			RESETCOLOR
		ENDIF
		;お気に入り
		IF CFLAG:COUNT:700
			PRINT [☆]
		ENDIF
		SIF TALENT:COUNT:73 != 0
		PRINT [陷]
		SIF TALENT:COUNT:72 != 0
		PRINT [瘾]
		SIF TALENT:COUNT:135 != 0
		PRINT [未]
		
		PRINTL
		T_LCOUNT++
		LIST_POS = COUNT
		ENDIF
		LOCAL += 1
	ENDIF
NEXT
RETURN LOCAL

;----------------------------------------------
@SHOW_LIST_ASSISTABLE(NO_PAGE,NUM_PAGE,LIST_POS)
;----------------------------------------------
#DIM NO_PAGE
#DIM NUM_PAGE
#DIM T_LCOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
;助手可能奴隷の一覧表示
;ついでに助手可能人数を返す
LOCAL = 0
FOR COUNT, LIST_POS, CHARANUM
	IF IS_ASSISTABLE(COUNT) == 0
		IF T_LCOUNT < (NO_PAGE + 1)*NUM_PAGE + 1 && T_LCOUNT >= NO_PAGE* NUM_PAGE + 1
		PRINTFORM [{COUNT,2}] %SAVESTR:COUNT,12,LEFT% %GET_JOB_NAME(COUNT),6,LEFT% LV{CFLAG:COUNT:9,4,RIGHT}　HP%BARSTR(BASE:COUNT:0,MAXBASE:COUNT:0,8)%
		;爱、淫乱
		IF TALENT:COUNT:爱慕
			SETCOLOR 255,100,100
			PRINTL <爱  慕>
			RESETCOLOR
		ELSEIF TALENT:COUNT:淫乱
			SETCOLOR 255,100,100
			PRINTL <淫  乱>
			RESETCOLOR
		ELSE
			SETCOLOR 100,100,100
			PRINTL <未沦陷>
			RESETCOLOR
		ENDIF
		SIF TALENT:COUNT:64 != 0
		PRINT [脏]
		SIF TALENT:COUNT:83 != 0
		PRINT [虐]
		SIF TALENT:COUNT:87 != 0
		PRINT [恶]
		SIF TALENT:COUNT:91 != 0
		PRINT [魅]
		SIF TALENT:COUNT:92 != 0
		PRINT [迷]
		SIF TALENT:COUNT:93 != 0
		PRINT [威]
		
		PRINTL
		T_LCOUNT++
		LIST_POS = COUNT
		ENDIF
		LOCAL += 1
	ENDIF
NEXT
RETURN LOCAL

;----------------------------------------------
@IS_TRAINABLE(ARG)
#FUNCTION
;----------------------------------------------
;ARG番のキャラが調教可能なら0、ダメならそれ以外を返す式中関数
SIF ARG < 1 || ARG >= CHARANUM || ARG == MASTER ;キャラ登録範囲外はダメ
	RETURNF 1
SIF CFLAG:ARG:1 != 0 ;調教中でないとダメ
	RETURNF 2
RETURNF 0

;----------------------------------------------
@IS_ASSISTABLE(ARG)
#FUNCTION
;----------------------------------------------
;ARG番のキャラが助手可能なら0、ダメならそれ以外を返す式中関数
SIF ARG < 1 || ARG >= CHARANUM ;キャラ登録範囲外はダメ
	RETURNF 1
SIF CFLAG:ARG:0 != 2 ;助手可能でないとダメ
	RETURNF 2
SIF CFLAG:ARG:1 != 0 ;調教中でないとダメ
	RETURNF 3
SIF TARGET == ARG ;調教対象はダメ
	RETURNF 4
RETURNF 0

;----------------------------------------------
@GET_JOB_NAME(ARG)
#FUNCTIONS
;----------------------------------------------
;ARG番のキャラの職業名の文字列を返す式中関数
;FOR LOCAL, 200, 229
;	SIF TALENT:ARG:LOCAL
;		RETURNF TALENTNAME:LOCAL
;NEXT

IF TALENT:ARG:200
	RETURNF "战士"
ELSEIF TALENT:ARG:201
	RETURNF "魔法师"
ELSEIF TALENT:ARG:202
	RETURNF "神官"
ELSEIF TALENT:ARG:203
	RETURNF "盗贼"
ELSEIF TALENT:ARG:204
	RETURNF "肉便器"
ELSEIF TALENT:ARG:205
	RETURNF "骑士"
ELSEIF TALENT:ARG:206
	RETURNF "巫女"
ELSEIF TALENT:ARG:207
	RETURNF "忍者"
ELSEIF TALENT:ARG:208
	RETURNF "弓手"
ELSEIF TALENT:ARG:209
	RETURNF "苗床"
ELSEIF TALENT:ARG:210
	RETURNF "魔界将军"
ELSEIF TALENT:ARG:211
	RETURNF "魔导神官"
ENDIF

;職業ない場合は空白を返す
;玛奥ちゃんとかがここに来る
RETURNF " "

