#!/usr/bin/env python3
"""
测试修复后的RAG系统
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_ollama_connection():
    """测试Ollama连接"""
    print("=== 测试Ollama连接 ===")
    
    try:
        from core.rag import OllamaEmbedding
        
        # 从环境变量读取配置
        ollama_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        embedding_model = os.getenv("OLLAMA_EMBEDDING_MODEL", "ExpedientFalcon/qwen3-embedding:4b-q4_k_m")
        
        print(f"Ollama URL: {ollama_url}")
        print(f"Embedding Model: {embedding_model}")
        
        client = OllamaEmbedding(ollama_url, embedding_model)
        
        if client.test_connection():
            print("✓ Ollama连接成功")
            
            # 测试嵌入生成
            test_text = "这是一个测试文本"
            embedding = client.get_embedding_sync(test_text)
            
            if embedding:
                print(f"✓ 嵌入生成成功，维度: {len(embedding)}")
                return True
            else:
                print("✗ 嵌入生成失败")
                return False
        else:
            print("✗ Ollama连接失败")
            return False
            
    except Exception as e:
        print(f"✗ Ollama测试出错: {e}")
        return False

def test_knowledge_base():
    """测试知识库功能"""
    print("\n=== 测试知识库功能 ===")
    
    try:
        from core.rag import ERAKnowledgeBase, OllamaEmbedding
        from data.database import ERADatabase
        
        # 创建测试数据库
        db = ERADatabase(":memory:")
        db.initialize_database()
        
        # 创建嵌入客户端
        ollama_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        embedding_model = os.getenv("OLLAMA_EMBEDDING_MODEL", "ExpedientFalcon/qwen3-embedding:4b-q4_k_m")
        embedding_client = OllamaEmbedding(ollama_url, embedding_model)
        
        # 创建知识库
        kb = ERAKnowledgeBase(db, embedding_client)
        
        print("✓ 知识库对象创建成功")
        
        # 测试文件分类
        test_files = [
            "ERA_ERH.txt",
            "ERA_excom.txt", 
            "ERA构文讲座.txt",
            "unknown.txt"
        ]
        
        for filename in test_files:
            category = kb._categorize_file(filename)
            print(f"  {filename} -> {category}")
            
        # 测试文本分块
        test_text = "这是第一行\n这是第二行\n这是第三行\n这是第四行\n这是第五行"
        chunks = kb._chunk_text(test_text, max_length=20)
        print(f"✓ 文本分块测试: {len(chunks)} 块")
        
        return True
        
    except Exception as e:
        print(f"✗ 知识库测试出错: {e}")
        return False

def test_rag_system():
    """测试完整RAG系统"""
    print("\n=== 测试完整RAG系统 ===")
    
    try:
        from core.rag import get_rag_system
        
        # 获取RAG系统实例
        rag = get_rag_system()
        print("✓ RAG系统实例创建成功")
        
        # 检查ERA语法数据路径
        era_syntax_path = os.getenv("ERA_SYNTAX_DATA_PATH", "./ERA語法資料")
        
        if not os.path.exists(era_syntax_path):
            print(f"✗ ERA语法数据路径不存在: {era_syntax_path}")
            return False
            
        print(f"ERA语法数据路径: {era_syntax_path}")
        
        # 初始化RAG系统
        print("正在初始化RAG系统...")
        success = rag.initialize(era_syntax_path)
        
        if success:
            print("✓ RAG系统初始化成功")
            
            # 获取统计信息
            stats = rag.get_statistics()
            print(f"知识库统计:")
            print(f"  总文档数: {stats.get('total_documents', 0)}")
            print(f"  已嵌入文档数: {stats.get('embedded_documents', 0)}")
            print(f"  嵌入覆盖率: {stats.get('embedding_coverage', 0):.2%}")
            print(f"  类别分布: {stats.get('categories', {})}")
            
            # 测试查询
            test_queries = [
                "如何定义ERB函数",
                "CSV文件格式",
                "变量使用方法"
            ]
            
            for query in test_queries:
                print(f"\n查询: {query}")
                result = rag.query(query, top_k=2)
                
                if result.get("error"):
                    print(f"  ✗ 查询失败: {result['error']}")
                else:
                    print(f"  ✓ 找到 {len(result.get('sources', []))} 个相关文档")
                    context = result.get("context", "")
                    if context:
                        print(f"  上下文预览: {context[:100]}...")
                        
            return True
        else:
            print("✗ RAG系统初始化失败")
            return False
            
    except Exception as e:
        print(f"✗ RAG系统测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的RAG系统\n")
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    tests = [
        ("Ollama连接", test_ollama_connection),
        ("知识库功能", test_knowledge_base),
        ("完整RAG系统", test_rag_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！RAG系统修复成功！")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
