﻿CSV文件夹中的_replace.csv中包含了一系列脚本的设置。

== お金の単位 ==

PRINT_SHOPITEM指令显示商品价格时的金钱单位。

默认为<code>$</code>。也可以指定为多个字符组成的文本。

== 単位の位置 ==

PRINT_SHOPITEM指令显示商品价格时金钱单位显示在数字的前后位置。

有效值为<code>前</code>或<code>後</code>。默认值为<code>後</code>。

== 起動時簡略表示 == 

当设置[[设置#加载时显示报告]]为OFF时，Emuera启动加载脚本时显示的文字。

默认为<code>Now Loading...</code>

== 販売アイテム数 ==

贩卖商品数。

== DRAWLINE文字 ==

使用DRAWLINE指令绘制分割线时使用的字符。

默认为<code>-</code>。

== BAR文字1 ==
== BAR文字2 ==

使用BAR或BARL绘制条时使用的字符。

默认分别为<code>*</code>、<code>.</code>。

在这种情况下显示为<code>[****....]</code>。

也可以指定为多个字符组成的文本，但可能会导致显示不正常。

== システムメニュー0 ==
== システムメニュー1 ==

游戏加载后的标题画面显示的两个选项的文字。

默认值分别为<code>最初からはじめる</code>、<code>ロードしてはじめる</code>。

默认标题画面的选项显示为

 [0] 最初からはじめる
 [1] ロードしてはじめる

当使用@SYSTEM_TITLE来实现用户自定义的标题画面时，这里设置的文字将不会被使用。
请在@SYSTEM_TITLE内实现选项的显示。

== COM_ABLE初期値 ==

指定当某个调教指令（TRAIN）不存在对应的@COM_ABLE{X}函数时，是否在调教指令菜单上显示该指令。

@COM_ABLE{X}函数用于确定一个调教指令在当前状态下对当前调教目标是否可用。
若函数返回1，则表示该调教指令可用，并在调教指令菜单中显示该指令。
若函数返回0，则表示该调教指令不可用，将不会显示该指令。

该设置默认值为1，即当某个调教指令没有@COM_ABLE{X}函数时，该调教指令始终可用。

== 汚れの初期値 ==

STAIN初始化时的初始值。

多个数值之间以<code>/</code>分隔。

默认为<code>0, 0, 2, 1, 8 </code>。

== 時間切れ表示 ==

当使用限时输入指令TINPUT输入超时的时候显示的文字。

默认为<code>時間切れ</code>（超过时间）。

== EXPLVの初期値 ==

EXPLV的初始值，即调教经验（EXP）每上升一个等级所需要的数值。

多个数值之间以<code>/</code>分隔。

默认为<code>0, 1, 4, 20, 50, 200</code>。

== PALAMLVの初期値 ==

PALAMLV的初始值，即调教参数（PALAM）每上升一个等级所需要的数值。

多个数值之间以<code>/</code>分隔。

默认为<code>0, 100, 500, 3000, 10000, 30000, 60000, 100000, 150000, 250000</code>。

== PBANDの初期値 ==

PBAND:0的初始值。

默认为4。

== RELATIONの初期値 ==

Chara**.csv中两个角色之间没有指定相性（RELATION）时，这两个角色之间的RELATION的初始值。

默认为0。


== 参见 ==


== 外部链接 ==

*{{jp}} Emuera Wiki (2015), [https://osdn.jp/projects/emuera/wiki/replace _replace.csv]



[[分类:EmueraWiki|_replace.csv]]