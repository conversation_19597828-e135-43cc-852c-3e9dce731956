﻿※以下翻译仅供参考

标题：Emuera
版本：1.821
作者：MinorShift, 妊）|дﾟ)の中の人
发布者：MinorShift
联系方式：<EMAIL>
网站页面：http：//osdn.jp/projects/emuera/
授权：参见[许可协议]。
更新的细节，请参见上述网页。


[关于Emuera]
Emuera是一个eramaker+α的模拟器，
用于代替eramaker来加载执行eramaker所在文件夹中的ERB脚本。
Emuera可以通过鼠标操作，并包含一个简单宏的功能。

[运行环境]
.NET Framework 2.0环境是必需的。

如果出现错误“未能正确初始化应用程序”并无法启动，原因是.NET Framework 2.0的安装不正确。
请从Microsoft下载中心下载安装.NET Framework 2.0后再运行。

[使用方法]
请将EmueraXXXX.exe放入到eramaker.exe所在的文件夹。
（XXXX表示程序的版本号；.exe扩展名可能会因为系统设置而不显示）
若程序未能启动，请参阅[运行环境]一节。
若启动后生成了emuera.config文件，抛出错误并生成了emuera.log，
请查阅 http://OSDN.jp/projects/emuera/wiki/FrontPage 。

[其他产品]
程序源代码、扩展功能的示例已经发布在以下页面。
http://osdn.jp/projects/emuera/releases/
下载源代码和相关文件。

[关于eramaker]
eramaker是由サークル獏の佐藤敏氏制作的程序。
请参考以下网站了解更多信息。
http://www.hakagi.com
※未满18岁请不要阅览

[联系方式]
<EMAIL>

※eramaker的作者并没有参与Emuera的制作。
请不要将Emuera的反馈和错误发送给サークル獏の佐藤敏氏先生。

[许可]（zlib/libpng的许可）
Copyright (C) 2008- MinorShift, 妊）|дﾟ)の中の人

※许可的翻译仅供参考，不具有法律效力

因使用本程序造成的任何损害，作者不承担任何责任。

在符合以下限制的前提下，可以将本程序用于包括商业用途的任何目的，并任意修改和重新发布。

1. 不得歪曲本程序的起源，歪曲原作者信息。若要在你的产品中使用本程序，请包含本文档，尽管这不是强制的。
2. 修改源代码并重新发布时，务必清楚地标识程序，以避免对衍生程序与原始程序的混淆。
3. 修改源代码并重新发布时，不得删除本文，不得修改本文的任何内容。


