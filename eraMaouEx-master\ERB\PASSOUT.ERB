﻿;eraIM@Sから流用

;=============================================================================
;失神処理関連
;=============================================================================
;失神についての色々--------------------------------
;失神判定
;TFLAG:864～882：失神中の状態保存および計算用
;TFLAG:883～894：失神中のUP保存
;TFLAG:895は失神したかどうかの判別用
;TFLAG:896で恐怖、TFLAG:897で絶頂、TFLAG:898で苦痛を管理（少なくとも1つのフラグが2になると失神、失神してる間は2を継続）
;TFLAG:899で失神中のコマンド実行回数管理（失神したコマンドもカウントする、つまり1以上だと失神中）
;--------------------------------------------------
@PASSOUT_CHECK
;システムを許可していない場合は処理しない
SIF FLAG:70 == 1
RETURN 0

TFLAG:895 = 0

Z = 0
Z = NOWEX:0 + NOWEX:1 + NOWEX:2 + NOWEX:3

;2コマンド連続で「2箇所以上強絶頂+αの絶頂」を満たすとランダムで失神
;（失神中はカウントしない、「強絶頂か2箇所以上絶頂」を続けている限りランダムで外れてもフラグは維持）
IF Z >= 16 && TFLAG:897 == 0 && TFLAG:899 < 1 && RAND:10 < 8
	TFLAG:897 = 1
ELSEIF Z >= 16 && TFLAG:897 == 1 && TFLAG:899 < 1 && RAND:10 < 6
	TFLAG:895 = 1
	TFLAG:897 = 2
	EXP:65 += 1
	PRINTL 失神
ELSEIF Z < 16 && TFLAG:897 < 2 && TFLAG:899 < 1
	TFLAG:897 = 0
ENDIF

A = PALAM:9
SIF A >= 15000
	A -= 15000
	
;一度に7500以上の苦痛を受けるか計15000ごとにランダムで失神（既に失神状態の場合はスキップ）
IF (UP:9 >= 7500 || UP:9 + A >= 15000) && TFLAG:899 < 1 && RAND:10 < 5
	IF TFLAG:895 == 0
		TFLAG:895 = 2
		TFLAG:898 = 2
		EXP:65 += 1
		PRINTL 失神
	ELSEIF TFLAG:895 == 1
		TFLAG:895 = 4
		TFLAG:898 = 2
	ENDIF
ENDIF

;一度に5000以上の恐怖を受けるとランダムで失神
IF UP:10 >= 5000 && TFLAG:899 < 1 && RAND:10 < 5
	IF TFLAG:895 == 0
		TFLAG:895 = 3
		TFLAG:896 = 2
		EXP:65 += 1
		PRINTL 失神
	ELSEIF TFLAG:895 == 2
		TFLAG:895 = 6
		TFLAG:896 = 2
	ENDIF
ENDIF

;失神中にコマンド実行
IF TFLAG:896 >= 2 || TFLAG:897 >= 2 || TFLAG:898 >= 2
	IF TFLAG:899 == 0
		TFLAG:899 = 1
	ELSEIF TFLAG:899 >= 1
		TFLAG:899 += 1
	ENDIF
ENDIF

;失神中に（失神した次のコマンドから）
IF TFLAG:899 >= 2
	;強烈な絶頂、一度に5000以上の苦痛を受ける、失神中に3回コマンドを実行のいずれかでランダムで失神から回復
	;一度条件を満たした場合回復するか調教を終えるまでコマンド実行毎に判定
	IF Z >= 16 || (TFLAG:899 >= 2 && UP:9 >= 5000) || TFLAG:899 >= 4
		TFLAG:896 = 3
		TFLAG:897 = 3
		TFLAG:898 = 3
		PRINTL 从失神中恢复了
	ENDIF
ENDIF

;-------------------------------------------------
;失神中のテキスト処理
;-------------------------------------------------
@PASSOUT_TEXT
;失神した時
IF TFLAG:895 > 0
	REPEAT 31
		TFLAG:(864 + COUNT) = 0
	REND
ENDIF

;失神回復時のテキストや口上は以下に羅列した情報を参照します。
;判定に加えたい調教状況やアイテムを追加したい場合はここに書き加えて下さい。
;非挿入中且つ安全套装備中に大量射精するとゴムから溢れる…という個人的設定のため少しややこしくなってます
;余りにも面倒なためテキストは射精系と装備系に分け、どちらかしか表示されないようになってます（優先度は射精＞装備）。
;また、ぶっかけ用に使われているCFLAG:74(eraIm@sPでは现在未使用）もここで一旦リセットします。

;それ以降
IF TFLAG:899 > 1
	IF TFLAG:0 + TFLAG:6 >= 1
		IF SELECTCOM == 80 || CFLAG:74 == 1 || CFLAG:74 == 2
			IF TEQUIP:35 == 0
				TFLAG:868 += TFLAG:0 + TFLAG:6
			ELSE
				TFLAG:868 += (TFLAG:0 + TFLAG:6) - 1
			ENDIF
		ELSEIF SELECTCOM == 29 || CFLAG:74 == 3 || CFLAG:74 == 5
			IF TEQUIP:35 == 0
				TFLAG:869 += TFLAG:0 + TFLAG:6
			ELSE
				TFLAG:869 += (TFLAG:0 + TFLAG:6) - 1
			ENDIF
		ENDIF
	ENDIF
	IF TFLAG:1 + TFLAG:6 >= 1
		IF TEQUIP:35 == 0
			TFLAG:870 += TFLAG:1 + TFLAG:6
		ELSE
			TFLAG:870 += (TFLAG:1 + TFLAG:6) - 1
		ENDIF
	ENDIF
	IF TFLAG:2 + TFLAG:6 >= 1
		IF SELECTCOM == 20 || SELECTCOM == 21 || SELECTCOM == 22 || SELECTCOM == 23 || SELECTCOM == 34 ||SELECTCOM == 64 || SELECTCOM == 120 || SELECTCOM == 121 || SELECTCOM == 128 || SELECTCOM == 129 || SELECTCOM == 130 || SELECTCOM == 131 || SELECTCOM == 132 || SELECTCOM == 133 || SELECTCOM == 134
			SIF TEQUIP:35 == 0
				TFLAG:871 += TFLAG:2 + TFLAG:6
		ELSEIF SELECTCOM == 26 || SELECTCOM == 27 || SELECTCOM == 28 || SELECTCOM == 29 || SELECTCOM == 36
			SIF TEQUIP:35 == 0
				TFLAG:872 += TFLAG:2 + TFLAG:6
		ENDIF
	ENDIF
	TFLAG:873 += TFLAG:3
	IF TFLAG:2 + TFLAG:6 >= 1
		IF SELECTCOM == 33 || CFLAG:74 == 4 || CFLAG:74 == 6 || CFLAG:74 == 7
			IF TEQUIP:35 == 0
				TFLAG:874 += TFLAG:6
			ELSE
				TFLAG:874 += TFLAG:6 - 1
			ENDIF
		ENDIF
	ENDIF
	SIF TFLAG:2 >= 1
		TFLAG:875 += TFLAG:2
	IF TFLAG:15 >= 1
		IF SELECTCOM == 101
			TFLAG:876 += 100
		ELSEIF SELECTCOM == 102
			TFLAG:876 += 1000
		ELSE
			TFLAG:876 += TFLAG:15
		ENDIF
	ENDIF
ENDIF

;失神初回
IF TFLAG:899 == 1
	;バイブ等挿入系アイテム装備判定
	TFLAG:867 = 0
	TFLAG:877 = 0
	SIF TEQUIP:13 == 1 || TEQUIP:19 == 1
		TFLAG:867 = 1
	SIF TEQUIP:11 == 1
		TFLAG:877 = 1
	;取り付け系アイテム装備判定
	TFLAG:866 = 0
	TFLAG:878 = 0
	SIF TEQUIP:14 == 1 || TEQUIP:17 == 1
		TFLAG:866 = 1
	SIF TEQUIP:15 == 1 || TEQUIP:16 == 1
		TFLAG:878 = 1
	;被虐系アイテム装備判定
	TFLAG:864 = 0
	TFLAG:865 = 0
	TFLAG:879 = 0
	SIF TEQUIP:44
		TFLAG:864 = TEQUIP:44
	SIF TEQUIP:45 == 1
		TFLAG:865 = TEQUIP:45
	SIF TEQUIP:46 == 1 || TEQUIP:49 == 1
		TFLAG:879 = 1
	;媚药・利尿剂使用判定
	TFLAG:880 = 0
	IF TEQUIP:21
		TFLAG:880 = 21
	ELSEIF TEQUIP:22
		TFLAG:880 = 22
	ENDIF
	;シチュエーション(特殊コマンド)系の実行中判定
	TFLAG:881 = 0
	IF TEQUIP:53
		TFLAG:881 = 53
	ELSEIF TEQUIP:54
		TFLAG:881 = 54
	ELSEIF TEQUIP:58
		TFLAG:881 = 58
	ENDIF
	;触手系コマンド実行中判定
	TFLAG:882 = TEQUIP:90

	SIF CFLAG:74 != 0
		CFLAG:74 = 0

;２回目以降
ELSE
	;バイブ等挿入系アイテム装備判定
	SIF TEQUIP:11 == 1 && (TFLAG:877 != 1)
		TFLAG:877 = (-1)
	SIF (TEQUIP:13 == 1 || TEQUIP:19 == 1) && (TFLAG:867 != 1)
		TFLAG:867 = (-1)
	;取り付け系アイテム装備判定
	SIF (TEQUIP:14 == 1 || TEQUIP:17 == 1) && (TFLAG:866 != 1)
		TFLAG:866 = (-1)
	SIF (TEQUIP:15 == 1 || TEQUIP:16 == 1) && (TFLAG:878 != 1)
		TFLAG:878 = (-1)
	;被虐系アイテム装備判定
	SIF TEQUIP:44 && TFLAG:864 != TEQUIP:44
		TFLAG:864 = (-1)
	SIF TEQUIP:45 == 1 && TFLAG:865 != TEQUIP:45
		TFLAG:865 = (-1)
	SIF (TEQUIP:46 == 1 || TEQUIP:49 == 1) && (TFLAG:879 != 1)
		TFLAG:879 = (-1)
	;媚药・利尿剂使用判定
	SIF (TEQUIP:21 && TFLAG:880 != 21) || (TEQUIP:22 && TFLAG:880 != 22)
		TFLAG:880 = (-1)
	;シチュエーション(特殊コマンド)系の実行中判定
	SIF (TEQUIP:53 && TFLAG:881 != 53)  || (TEQUIP:54 && TFLAG:881 != 54) || (TEQUIP:58 && TFLAG:881 != 58)
		TFLAG:881 = (-1)
	;触手系コマンド実行中判定
	SIF TEQUIP:90 == 1 && TFLAG:882 != 1
		TFLAG:882 = (-1)
ENDIF

IF TFLAG:899 >= 1
	IF TFLAG:895 == 1
		;口上テンプレいじるの面倒なんでここに口上っぽいもの載せておきますね(^^)
		SIF TEQUIP:45 == 0
		PRINTFORML 「噢哈啊啊啊啊啊啊啊！！…啊啊……哈……喔…♪」
		PRINTFORML 
		PRINTFORML …绝顶的快感令%SAVESTR:TARGET%全身抽搐，当场倒下了，
		PRINTFORML 因为过于强烈的刺激失去了意识。
	ELSEIF TFLAG:895 == 2
		SIF TEQUIP:45 == 0
		PRINTFORML 「不行了～～～～！！！…放、放过……我……吧」
		PRINTFORML 
		PRINTFORML …%SAVESTR:TARGET%当场倒下，因为过于强烈的痛楚失去了意识。
	ELSEIF TFLAG:895 == 3
		SIF TEQUIP:45 == 0
		PRINTFORML 「不行了～～～～！！！…放、放过……我……吧」
		PRINTFORML 
		PRINTFORML …%SAVESTR:TARGET%当场倒下，因为过于强烈的恐惧失去了意识
	ELSEIF TFLAG:895 == 4
		SIF TEQUIP:45 == 0
		PRINTFORML 「噢哈啊啊啊啊啊啊啊！！…放、放过……我……吧」
		PRINTFORML 
		PRINTFORML …%SAVESTR:TARGET%全身抽搐，当场倒下了，
		PRINTFORML 被快感和痛楚同时冲击，失去了意识。
	ELSEIF TFLAG:895 == 6
		SIF TEQUIP:45 == 0
		PRINTFORML 「不行了～～～～！！！…放、放过……我……吧」
		PRINTFORML 
		PRINTFORML …%SAVESTR:TARGET%全身抽搐，当场倒下了，
		PRINTFORML 受不了无法忍耐的痛楚和恐惧，失去了意识。
	ELSEIF TFLAG:896 == 3 && TFLAG:897 == 3 && TFLAG:898 == 3
		PRINTFORML %SAVESTR:TARGET%恢复了意识。
		WAIT
		;地の文章カット
		SIF CFLAG:99 == 0
			CALL PASSOUT_MESSAGE
		;失神回復時口上の呼び出し TFLAG:200が中身違うのでこれもスルーする
		;TFLAG:200 = 12
		;CALL SELF_KOJO
		;PRINTL 
	ELSE
		PRINTFORML 
		PRINTFORML %SAVESTR:TARGET%依然未醒来。
	ENDIF
ENDIF

@PASSOUT_MESSAGE
;自分でもよく分からないくらいメチャクチャな上、下に進むほど大雑把

IF TFLAG:60 == 1
	PRINTFORM 不知不觉间，
	SIF SELECTCOM == 101 || SELECTCOM == 102
		PRINT 触手把
	IF SELECTCOM == 20 || SELECTCOM == 21 || SELECTCOM == 22 || SELECTCOM == 23 || SELECTCOM == 34 || SELECTCOM == 101 || SELECTCOM == 120 || SELECTCOM == 121 || SELECTCOM == 128 || SELECTCOM == 129 || SELECTCOM == 130 || SELECTCOM == 131 || SELECTCOM == 132 || SELECTCOM == 133 || SELECTCOM == 134
		PRINT 膣内
	ELSE
		PRINT 尻穴
	ENDIF
	PRINTFORM 给侵犯了，不顾失去意识的%SAVESTR:TARGET%，粗野地对待她。
	IF SELECTCOM == 101 || SELECTCOM == 102
		PRINT 触手
	ELSE
		PRINTFORM %SAVESTR:PLAYER%
	ENDIF
	PRINTW 的抽插在持续中……
ENDIF

IF TFLAG:873 >= 1
	IF TFLAG:60 == 1
		IF SELECTCOM == 20 || SELECTCOM == 21 || SELECTCOM == 22 || SELECTCOM == 23 || SELECTCOM == 34 || SELECTCOM == 120 || SELECTCOM == 121 || SELECTCOM == 128 || SELECTCOM == 129 || SELECTCOM == 130 || SELECTCOM == 131 || SELECTCOM == 132 || SELECTCOM == 133 || SELECTCOM == 134
			PRINT 被阴茎强行塞满，
		ELSEIF SELECTCOM == 101
			PRINT 被触手强行塞满，
		ENDIF
	ENDIF
	PRINT 从私处里流出了血，
	IF TFLAG:871 == 1
		PRINT 混合着精液，
	ELSEIF TFLAG:871 >= 2
		PRINT 混合着大量的精液，
	ELSEIF TFLAG:876 >= 100 && TFLAG:876 < 200
		PRINT 混合着污液，
	ELSEIF TFLAG:876 >= 200
		PRINT 混合着大量的污液，
	ENDIF
	PRINTL 终于察觉了，
	PRINTFORMW 不知不觉中，处女被夺走，茫然地呆了…
ELSEIF TFLAG:871 >= 1
	IF TFLAG:60 == 1
		IF SELECTCOM == 20 || SELECTCOM == 21 || SELECTCOM == 22 || SELECTCOM == 23 || SELECTCOM == 34 || SELECTCOM == 120 || SELECTCOM == 121 || SELECTCOM == 128 || SELECTCOM == 129 || SELECTCOM == 130 || SELECTCOM == 131 || SELECTCOM == 132 || SELECTCOM == 133 || SELECTCOM == 134
			PRINT 被阴茎强行塞满的
		ELSEIF SELECTCOM == 101
			PRINT 被触手强行塞满的
		ENDIF
	ENDIF
	PRINT 私处
	IF TFLAG:872 >= 1
		PRINT 和
		IF SELECTCOM == 26 || SELECTCOM == 27 || SELECTCOM == 28 || SELECTCOM == 29
			PRINT 被阴茎强行塞满的
		ELSEIF SELECTCOM == 102
			PRINT 被触手强行塞满的
		ENDIF
		PRINT 尻穴
	ENDIF
	PRINT 里面，
	IF TFLAG:871 >= 2 || TFLAG:872 >= 2
		PRINT 流出大量的
	ELSE
		PRINT 滴下
	ENDIF
	PRINT 精液，终于被察觉了，
	IF TALENT:85 == 1
		PRINTW 茫然地不知如何是好…
	ELSEIF TALENT:10 == 1
		PRINTW 马上哭了起来…
	ELSE
		PRINTFORMW 紧紧地盯着%SAVESTR:PLAYER%…
	ENDIF
ELSEIF TFLAG:872 >= 1
	IF TFLAG:60 == 1
		IF SELECTCOM == 26 || SELECTCOM == 27 || SELECTCOM == 28 || SELECTCOM == 29
			PRINT 被阴茎强行塞满的
		ELSEIF SELECTCOM == 102
			PRINT 被触手强行塞满的
		ENDIF
	ENDIF
	PRINT 尻穴里
		IF TFLAG:871 >= 2 || TFLAG:872 >= 2
			PRINT 流出大量的
		ELSE 
			PRINT 滴下
		ENDIF
		PRINT 精液，终于被察觉了，
	IF TALENT:85 == 1
		PRINTW 茫然地不知如何是好…
	ELSEIF TALENT:10 == 1
		PRINTW 马上哭了起来…
	ELSE
		PRINTFORMW 紧紧地盯着%SAVESTR:PLAYER%…
	ENDIF
ELSEIF TFLAG:876 >= 1
	PRINT 全身沾满了
	SIF TFLAG:868 + TFLAG:869 + TFLAG:870 + TFLAG:871 + TFLAG:872 + TFLAG:873 + TFLAG:874 + TFLAG:875 >= 1
		PRINT 精液，
	PRINTW 触手吐出的污液，和无法隐藏的困惑…
ELSEIF TFLAG:868 + TFLAG:869 + TFLAG:870 + TFLAG:874 + TFLAG:875 >= 1
	PRINT 不省人事前，没有感觉到身体
	SIF TFLAG:868 + TFLAG:869 + TFLAG:870 + TFLAG:874 + TFLAG:875 >= 3
		PRINT 里面
	PRINT 被洒满的精液，困惑地看着，
	IF TALENT:85 == 1
		PRINTW 流出来了…
	ELSE
		PRINTW 并害怕着…
	ENDIF
ELSEIF TFLAG:867 < 0 || TFLAG:877 < 0
	PRINTFORM  不知什么时候，
	IF TEQUIP:11 == 1
		IF TEQUIP:13 == 1
			PRINT 两穴都被蠕虫，
		ELSEIF TEQUIP:19 == 1
			PRINT 私处被蠕虫，肛门被肛珠，
		ELSE
			PRINT 私处被蠕虫，
		ENDIF
	ELSEIF TEQUIP:13 == 1
		PRINT 肛门被蠕虫，
	ELSEIF TEQUIP:19 == 1
		PRINT 肛门被肛珠，
	ENDIF	
	PRINT 插入了。
	SIF TFLAG:878 < 0 || TFLAG:866 < 0
		PRINT 被装上了异样的器具，
	SIF TFLAG:879 < 0 || TFLAG:864 < 0 || TFLAG:865 < 0
		PRINT 被装上了器具，
	SIF TEQUIP:53
		PRINT 这个姿态被拍下来了，
	PRINTW 对这样的事感到不知所措…
ELSEIF TFLAG:878 < 0 || TFLAG:866 < 0
	PRINT 对不经意间被装上了器具感到不知所措…
ELSEIF TFLAG:879 < 0 || TFLAG:864 < 0 || TFLAG:865 < 0
	PRINTFORM 不知什么时候，
	IF TEQUIP:44
		PRINT 被绑起来了，
		SIF TFLAG:878 < 0 || TFLAG:866 < 0
			PRINT 之后，被装上了异样的器具，
		PRINTFORM 怎么会这样……
	ELSEIF TEQUIP:45 == 1 || TEQUIP:49 == 1 || TEQUIP:46 == 1
		PRINT 被装上了器具，
	ENDIF
	PRINTFORMW 发现后开始感到困惑和恐惧了…	
ELSEIF TFLAG:880 < 0
	PRINTFORMW 发现自己身体的异样，掩饰不住地困惑着，
ELSEIF TFLAG:881 < 0
	IF TEQUIP:53
		PRINT 映照出自己的摄影机，
	ELSEIF TEQUIP:54
		PRINT 不知不觉间被带到屋外去了，
	ELSEIF TEQUIP:58
		PRINT 不知不觉间被带到浴室去了，
	ENDIF
	PRINTFORMW 发现后开始感到困惑和恐惧了…
ELSEIF TFLAG:882 < 0
	PRINT 不知不觉间…身体被触手缠绕了，开始感到困惑和恐惧了…
ENDIF
IF (TFLAG:867 + TFLAG:877 + TFLAG:878 + TFLAG:866 + TFLAG:879 + TFLAG:864 + TFLAG:865 + TFLAG:881 + TFLAG:882) < 0 || TFLAG:872 >= 1
	WAIT
ENDIF

G = TFLAG:868 + TFLAG:869 + TFLAG:870 + TFLAG:874 + TFLAG:875 + TFLAG:876
X = TFLAG:867 + TFLAG:877 + TFLAG:878 + TFLAG:866 + TFLAG:879 + TFLAG:864 + TFLAG:865 + TFLAG:880 + TFLAG:881
Y = TFLAG:871 + TFLAG:872
TIMES X , -1

;-------------------------------------------------
;失神中のパラメータ処理
;-------------------------------------------------
@PASSOUT_PALAM_CHECK
IF TFLAG:895 > 0
	TFLAG:883 += UP:6
	TFLAG:884 += UP:8
	TFLAG:885 += UP:10
	TFLAG:886 += UP:11
	TFLAG:887 += UP:12
	TFLAG:888 += UP:13
ELSE
	TFLAG:889 += UP:6
	TFLAG:890 += UP:8
	TFLAG:891 += UP:10
	TFLAG:892 += UP:11
	TFLAG:893 += UP:12
	TFLAG:894 += UP:13
ENDIF

UP:7 = 0
UP:4 = 0
UP:6 = 0
UP:8 = 0
UP:9 = 0
UP:10 = 0
UP:11 = 0
UP:12 = 0
UP:13 = 0

;-------------------------------------------------
;失神回復時のパラメータ処理
;-------------------------------------------------
@PASSOUT_PALAM_UP
A = TFLAG:883 * (12 - TFLAG:899)
B = TFLAG:884 * (12 - TFLAG:899)
C = TFLAG:885 * (12 - TFLAG:899)
D = TFLAG:886 * (12 - TFLAG:899)
E = TFLAG:887 * (12 - TFLAG:899)
F = TFLAG:888 * (12 - TFLAG:899)
IF TFLAG:899 > 2
	A += TFLAG:889 * (TFLAG:899 - 2)
	B += TFLAG:890 * (TFLAG:899 - 2)
	C += TFLAG:891 * (TFLAG:899 - 2)
	D += TFLAG:892 * (TFLAG:899 - 2)
	E += TFLAG:893 * (TFLAG:899 - 2)
	F += TFLAG:894 * (TFLAG:899 - 2)
ENDIF
A /= 600
B /= 240
C /= 60
D /= 10
E /= 10
F /= 10
IF G >= 1
	A += A * G
	B += B * G
	C += C * G
	D += D * G
	E += E * G
	F += F * G
	IF ABL:32 == 3
		UP:5 += 1000
	ELSEIF ABL:32 == 4
		UP:5 += 1500
	ELSEIF ABL:32 >= 5
		UP:5 += 2000
	ENDIF
ENDIF

IF X >= 1
	A += A * X
	B += B * X
	C += C * X
	D += D * X
	E += E * X
	F += F * X
ENDIF

IF Y >= 1
	A += A * Y
	B += B * Y
	C += C * Y
	D += D * Y
	E += E * Y
	F += F * Y
	IF ABL:32 == 3
		UP:5 += 1000
	ELSEIF ABL:32 == 4
		UP:5 += 1500
	ELSEIF ABL:32 >= 5
		UP:5 += 2000
	ENDIF
ENDIF

;IF TFLAG:60
	;TIMES A , 1.50
	;TIMES B , 1.50 
	;TIMES C , 1.50
	;TIMES D , 1.50
	;TIMES E , 1.50
	;TIMES F , 1.50
;ENDIF

IF TFLAG:873 >= 1
	A *= 2
	B *= 2
	C *= 2
	D *= 2
	E *= 2
	F *= 2
ENDIF

Z = 100
Z -= MARK:2 * 10
Z -= ABL:10 * 5

SIF TALENT:85
	Z /= 2

UP:7 += A * (100 - Z) / 100
UP:8 += B * (100 - Z) / 100
UP:10 += C * (100 - Z) / 100
UP:11 += D * Z / 100
UP:12 += E * Z / 100
UP:13 += F * Z / 100

IF TFLAG:896 == 3 || TFLAG:897 == 3 || TFLAG:898 == 3
	TFLAG:896 = 0
	TFLAG:897 = 0
	TFLAG:898 = 0
	TFLAG:899 = 0
ENDIF

;-------------------------------------------------
;野外PLAY中の失神処理
;-------------------------------------------------
@PASSOUT_OUTDOOR
;野外PLAY解除
TEQUIP:54 = 0
PRINTFORMW %SAVESTR:TARGET%失神了，所以带回了房间…

;調教者の体力・気力が若干減少
BASE:MASTER:0 -= 20
BASE:MASTER:1 -= 10
SIF BASE:MASTER:0 < 0
	BASE:MASTER:0  = 0
SIF BASE:MASTER:1 < 0
	BASE:MASTER:1  = 0
