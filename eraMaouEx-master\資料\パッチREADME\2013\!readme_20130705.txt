﻿eramaou_0270patch_20130705

※(eramaou　ver.0.270推奨)

加筆・改変・再アップロードはご自由にどうぞ。

■使い方
全てのERBファイルをERBフォルダに上書きコピーしてください。

■仕様
・メインメニューの表記を修正。
・能力の表示であなたを選んだとき「魔王になる前」「魔王になったきっかけ」と表示（ただしフレーバー的な意味合いしか無い）。
・前回の調教コマンドを表示。
・調教中の現在の調教者に色をつけました。
・調教中の状況の表示に色をつけました。
・調教中の使用中の表示の道具に色をつけて「挿入中」を追加しました。
・膣内射精のチェックで３Ｐ時と助手の逆レイプ時を追加。
・３Ｐの膣内射精チェックを追加。
・他細かいバグ修正。
・瀕死時パッチ入り。

_DRAW_MAINMENU.ERB
仕切り線とフォントの指定を前後に入れ替えて仕切り線が改行されて表示されないようにしました。
所持罠一覧の表示罠の文字数を20から18にしました。
代入演算子の修正。

TRAIN_MAIN.ERB
調教者を色で表示。
TSTR:90で前回の調教コマンドを覚えるようにしました。
関数P_Cを追加してTSTR:90に前回のコマンド名を覚えさせます。

USERCOM.ERB
前回の調教コマンドの表示部分を追加。

SYSTEM_SOURCE.ERB
膣内射精のチェック内に
;3P助手が膣内射精
;3P主人が膣内射精
;調教対象が助手に膣内射精（逆レイプ）
を追加。

INFO.ERB
「状況の表示」に色を追加。
「使用中の道具を表示」に色と[挿入中]を追加。

LOOK.ERB
709、759行目
IF NO:TARGET != 0　で勇者と魔王の分岐

COMF64.ERB
主人と助手の膣内射精フラグを追加。

SHOP_LOBO.ERB
再生処女膜の代入演算子を修正。
再生処女膜のメッセージを修正。

EVENT_K4.ERB
893行目の識別子を修正。

COMF24.ERB
85行目の代入演算子を修正。

DUNGEON.ERB
29行目の代入演算子を修正。

DUNGEON_RYOUZYOKU.ERB
82行目の代入演算子を修正。

LABO_DUNGEON_MAP.ERB
8行目の代入演算子を修正。

NAEDOKO.ERB
18行目の代入演算子を修正。

USE_EX_ITEM.ERB
15行目の代入演算子を修正。

