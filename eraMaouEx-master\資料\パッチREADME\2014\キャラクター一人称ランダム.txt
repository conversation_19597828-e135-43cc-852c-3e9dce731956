﻿2014/11/10

人柱　二版

・気弱
・高貴
・冷徹
・悪女
対応口上追加
前述のとおりマオちんは保留

文字列関係の命令の仕様で勘違いしてた部分を修正

一人称が名前で音が長い場合、略称になったりします
フランソワーズ　→　フランなど
まだあまりパターンは多くないので増やしていきたいところ



2014/11/01

各口上の一人称をランダムに決定してみるのです
人柱版なのです
人柱なので慈愛と自信家にしか対応してないのです



一人称のバリエーションとしては以下になります

	私				僕	俺	自分の名前
	わたし	わたくしあたし	あたい	ぼく	おれ
	ワタシ	ワタクシアタシ	アタイ	ボク	オレ

これらを、独断と偏見で口上ごとにあったりなかったりを設定します
（高貴が「あたい」とか冷徹が「ぼく」だと違和感があるため）

		わたし	わたくしあたし	あたい	ぼく	おれ	自分の名前
	慈愛	○	○					
	自信家	○		○		○		○
	気弱	○		○		○		
	高貴	○	○					○
	冷徹	○		○			○	
	マオ				○
	悪女	○		○	○			○
	ハート		○					
	スペード				○		
	ダイヤ	○						
	クラブ	○						

また、各口上のデフォルト一人称や「私」の頻度を上げたり
逆にカタコト感が強そうな「ワタクシ」あたりを低くしています
ユニークキャラはランダムにしていませんっていうか対応する必要ないんじゃないかな？という気もします
今回対応した慈愛口上と自信家口上では、「私」と「わたし」の表記ゆれがあったため統一してしまいました
このパッチの弊害として、途中で導入した対応口上のキャラでは全て「私」になってしまうため、
とりあえずの措置として能力の表示でランダムに再設定できるようにしておきました



開発者向け
一人称の記憶用にCFLAG:450を使用しています
また、口上から一人称を呼び出す際はPRINTFORMで

%SELF_CALL(キャラクターID（, 語尾の長さ）)%

としてください
「語尾の長さ」は、例えば口上で
「わ、私ぃい…」としたい場合の「ぃい」の部分です
不要ならば省略できます
語頭の「わ」も

%SELF_CALL_FIRST(キャラクターID)%

でできます。