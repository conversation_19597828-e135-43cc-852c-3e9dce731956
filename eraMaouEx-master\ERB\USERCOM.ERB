﻿;SHOW_USERCOM
;FLAG:25 & 1 爱抚系フィルタ
;FLAG:25 & 2 器具系フィルタ
;FLAG:25 & 4 肛门性交系フィルタ
;FLAG:25 & 8 私处性交系フィルタ 
;FLAG:25 & 16 ＳＭ系フィルタ
@SHOW_USERCOM

IF GETBIT(FLAG:5,34)
	;绘制自定义COM菜单
	CALL CLEAR_TO_POINT
	CALL SHOW_COMMENU
ENDIF
PRINTL
DRAWLINE
RESETCOLOR
PRINTC 能力表示[100]
PRINTC 污秽表示[101]
LOCAL = 2
IF ASSI > 0 && ASSI:1 > 0
	PRINTC 交代助手[102]
	LOCAL ++
ENDIF
IF LOCAL == PRINTCPERLINE()
	LOCAL = 0
	PRINTL
ENDIF
IF (TARGET == MASTER || CFLAG:0 >= 2) && ASSI:1 > 0
	PRINTC 对换调教[112]
	LOCAL ++
ENDIF
IF LOCAL == PRINTCPERLINE()
	LOCAL = 0
	PRINTL
ENDIF
PRINTC 避孕套设定[103]
PRINTL
IF FLAG:25 & 1
	SETCOLOR 100,100,100
	PRINTC 爱抚系过滤[104]
	RESETCOLOR
ELSE
	PRINTC 爱抚系过滤[104]
ENDIF
IF FLAG:25 & 2
	SETCOLOR 100,100,100
	PRINTC 器具系过滤[105]
	RESETCOLOR
ELSE
	SETCOLOR 100,149,237
	PRINTC 器具系过滤[105]
	RESETCOLOR
ENDIF
IF FLAG:25 & 4
	SETCOLOR 100,100,100
	PRINTC 私处性交系过滤[106]
	RESETCOLOR
ELSE
	SETCOLOR 255,165,0
	PRINTC 私处性交系过滤[106]
	RESETCOLOR
ENDIF
SIF 3 == PRINTCPERLINE()
	PRINTL
IF FLAG:25 & 8
	SETCOLOR 100,100,100
	PRINTC 肛门性交系过滤[107]
	RESETCOLOR
ELSE
	SETCOLOR 219,112,147
	PRINTC 肛门性交系过滤[107]
	RESETCOLOR
ENDIF
SIF 4 == PRINTCPERLINE()
	PRINTL
IF FLAG:25 & 16
	SETCOLOR 100,100,100
	PRINTC ＳＭ系过滤[108]
	RESETCOLOR
ELSE
	SETCOLOR 255,99,71
	PRINTC ＳＭ系过滤[108]
	RESETCOLOR
ENDIF
	PRINTC 调教菜单登录[990]
	PRINTL
	IF FLAG:550 > 0
		PRINTC 调教菜单表示[991]
		PRINTC 调教菜单实行[992]
	ENDIF
PRINTC 调教结束[999]
PRINTL
IF PREVCOM > -1
	CALL P_C
	PRINT ＜上次的调教指令：
	SETCOLOR 0xDDA0DD
	PRINTFORM %TSTR:90%
	RESETCOLOR
	PRINTL ＞
ENDIF

@USERCOM
REDRAW 1
IF RESULT == 100
	CALL SHOW_CHARA_INFO(TARGET)
	RETURN 1
ELSEIF RESULT == 101
	CALL STAIN_INFO
	RETURN 1
ELSEIF RESULT == 102 && ASSI > 0 && ASSI:1 > 0
	IF TARGET == MASTER
		PLAYER = PLAYER == TARGET:1 ? ASSI:1 # TARGET:1
		ASSI = PLAYER
	ELSEIF TARGET == TARGET:1
		PLAYER = PLAYER == MASTER ? ASSI:1 # MASTER
		ASSI = ASSI:1
	ELSE
		PLAYER = PLAYER == MASTER ? TARGET:1 # MASTER
		ASSI = TARGET:1
	ENDIF
	ASSIPLAY = PLAYER != MASTER ? 1 # 0
	RETURN 1
ELSEIF RESULT == 112 && (TARGET == MASTER || CFLAG:0 >= 2) && ASSI:1 > 0
	SWAP TARGET, PLAYER
	SIF PLAYER == ASSI:1 || PLAYER == TARGET:1
		ASSI = PLAYER
	ASSIPLAY = PLAYER != MASTER ? 1 # 0
	RETURN 1
ELSEIF RESULT == 103
	CALL CONDOM_SETTINGS
	RETURN 1
ELSEIF RESULT == 104
	IF FLAG:25 & 1
		FLAG:25 &= 30
	ELSE
		FLAG:25 |= 1
	ENDIF
ELSEIF RESULT == 105
	IF FLAG:25 & 2
		FLAG:25 &= 29
	ELSE
		FLAG:25 |= 2
	ENDIF
ELSEIF RESULT == 106
	IF FLAG:25 & 4
		FLAG:25 &= 27
	ELSE
		FLAG:25 |= 4
	ENDIF
ELSEIF RESULT == 107
	IF FLAG:25 & 8
		FLAG:25 &= 23
	ELSE
		FLAG:25 |= 8
	ENDIF
ELSEIF RESULT == 108
	IF FLAG:25 & 16
		FLAG:25 &= 15
	ELSE
		FLAG:25 |= 16
	ENDIF
ELSEIF RESULT == 990
	CALL COMSEQ_REGISTER
	RETURN 1
ELSEIF RESULT == 991 && FLAG:550 > 0 
	DRAWLINE
	CALL COMSEQ_SHOW
	DRAWLINE
	WAIT
	RETURN 1
ELSEIF RESULT == 992 && FLAG:550 > 0 
	CALL COMSEQ_TRAIN
ELSEIF RESULT == 999
	BEGIN AFTERTRAIN
	RETURN 1
ENDIF
RETURN 0

@SET_CLEAR_POINT
TFLAG:999 = LINECOUNT

@CLEAR_TO_POINT
SIF LINECOUNT - TFLAG:999 <= 0
	RETURN
CLEARLINE LINECOUNT - TFLAG:999
TFLAG:999 = LINECOUNT

@SHOW_COMMENU
#DIM L_I
#DIM L_IDX
#DIM L_CUN
L_IDX = -1
L_CUN = -1

FOR L_I,0,300
	SIF STRLENS(TRAINNAME:L_I) <= 0
		CONTINUE
	L_IDX++
	
	RESULT = 1
	TRYCALLFORM COM_ABLE{L_I}
	SIF RESULT == 0
		CONTINUE
	
	L_CUN = (L_CUN+1)%PRINTCPERLINE()
	SIF L_CUN == 0
		PRINTL
	
	CALL GET_ADV_COM, L_I
	IF RESULT == 64 && L_I != 64
		PRINTFORMC %TRAINNAME:64%・%TRAINNAME:L_I%[{L_IDX,3}]
	ELSE
		PRINTFORMC %TRAIN_NAME:RESULT%[{L_IDX,3}]
	ENDIF
	
NEXT
PRINTL
