#DEFINE MAX_CHARANUM 90

;==================================================
; 游戏全局状态标志
#DIM FLAG, 100
; 临时游戏状态标志，通常在调教开始时重置
#DIM TFLAG, 100
; 剧情事件标志
#DIM EVENT_FLAG, 50
; 当前游戏状态 (0:主菜单, 1:训练, 2:商店, 3:事件, 4:休息, 5:地图)
#DIM GAME_STATE
; 当前所在地点ID
#DIM CURRENT_LOCATION

; 商店相关变量
#DIM SHOP_ITEM_PRICE, 100
#DIM SHOP_ITEM_STOCK, 100
#DIM SHOP_LAST_SELECTED_ITEM
#DIMS SHOP_ITEM_NAME, 100

; 调教相关变量
#DIM PALAM_LV_BOUNDARY, 20
#DIM EXP_LV_BOUNDARY, 20
; 用于储存保留的TRAINNAEM, 函数@TRAIN_NAME_INIT用于初始化
#DIMS TRAIN_NAME, 500

; 角色生成相关变量
; 非ユニーク性格的TALENT番号的配列
#DIM ID_OF_GENERAL_CHARASTERISTICS = 160,161,162,163,164,166,172,173,174,175
; 头发颜色的配列
#DIMS ARR_HAIRCOLOR = "","金发","栗发","黑发","红发","银发","蓝发","绿发","紫发","白发","暗金发","粉发"
; 眼睛颜色的配列
#DIMS ARR_EYECOLOR = "","黑色","棕色","蓝色","绿色","灰色","紫色","红色","金色","银色"
; 肤色的配列
#DIMS ARR_SKINCOLOR = "","白皙","健康","小麦色","古铜色"
; 种族名称的配列
#DIMS ARR_RACE_NAME = "","人类","精灵","矮人","兽人","龙族","天使","恶魔","妖精","不死族"

; 游戏世界相关变量
#DIMS ARR_LOCATION_NAME = "","骑士团本部","训练场","武器库","宿舍","图书馆","工坊","市场","酒馆","城墙","地牢"

; 骑士与玩偶主题特有变量
#DIM KNIGHT_RANK ; 骑士等级
#DIM DOLL_COMPLETION ; 玩偶完成度或制作进度
#DIM KNIGHT_HONOR ; 骑士荣誉值
#DIM DOLL_MATERIALS, 20 ; 制作玩偶的材料库存
#DIMS KNIGHT_TITLE = "","见习骑士","正式骑士","精英骑士","骑士队长","圣骑士"