﻿#DEFINE MAX_CHARANUM 3

;==================================================
;骑士与玩偶主题变量定义

;角色类型
#DEFINE CHARA_TYPE_KNIGHT 1
#DEFINE CHARA_TYPE_DOLL 2

;角色属性
#DIM CHARACTER_NAME$[MAX_CHARANUM]
#DIM CHARACTER_TYPE[MAX_CHARANUM]
#DIM CHARACTER_HP[MAX_CHARANUM]
#DIM CHARACTER_STR[MAX_CHARANUM]
#DIM CHARACTER_DEX[MAX_CHARANUM]
#DIM CHARACTER_INT[MAX_CHARANUM]
#DIM CHARACTER_LUCK[MAX_CHARANUM]
#DIM CHARACTER_GOLD[MAX_CHARANUM]

;骑士专用属性
#DIM KNIGHT_SKILL_LEVEL[MAX_CHARANUM] ;剑术等级
#DIM KNIGHT_ARMOR_TYPE[MAX_CHARANUM] ;盔甲类型
#DIM KNIGHT_MOUNT[MAX_CHARANUM] ;坐骑

;玩偶专用属性
#DIM DOLL_RARITY[MAX_CHARANUM] ;稀有度
#DIM DOLL_APPEARANCE[MAX_CHARANUM] ;外观
#DIM DOLL_ABILITY[MAX_CHARANUM] ;特殊能力


;商店物品
#DIMS SHOP_ITEMS$ = "剑","盾牌","盔甲","玩偶","药水","宝石"
#DIM SHOP_ITEM_PRICE[6]

;训练项目
#DIMS TRAIN_NAME$ = "剑术训练","体能训练","魔法训练","魅力训练"
#DIM TRAIN_COST[4]
#DIM TRAIN_EFFECT[4]


;全局变量
#DIM CURRENT_CHARACTER ;当前角色索引
#DIM GAME_GOLD ;游戏内金币数量
#DIM GAME_FLAG ;游戏标志


;头发颜色(示例)
#DIMS ARR_HAIRCOLOR$ = "","金发","黑发","棕发","银发"

;眼睛颜色(示例)
#DIMS ARR_EYECOLOR$ = "","蓝色","绿色","棕色","黑色"


;其他变量根据需要添加