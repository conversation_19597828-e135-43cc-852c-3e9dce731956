﻿
;eraIM@Sから流用（eramaou）

;=================================================
;アイテム購入・売却処理画面 
;=================================================
@ITEM_SHOP_TRAP
#DIM ICOUNT_A
#DIM ICOUNT_B
CUSTOMDRAWLINE =
PRINTL 《可以购买在地下城里布置的陷阱》
DRAWLINE
PRINTV DAY+1
PRINT 日
IF TIME == 0
	PRINTL  午前
ELSE
	PRINTL  午后
ENDIF

PRINTFORML [所持金:{MONEY}点]

SETCOLORBYNAME LightSalmon
PRINTFORML [陷阱Lv:{FLAG:85}]
PRINTFORML [陷阱]
RESETCOLOR
ICOUNT_B = 0
FOR ICOUNT_A,60,92
	SIF ITEM:ICOUNT_A == 0
		CONTINUE
	PRINTFORM [%ITEMNAME:ICOUNT_A + @"(x{ITEM:ICOUNT_A})",20,LEFT%]
	ICOUNT_B += 1
	IF ICOUNT_B % 5 == 0
		PRINTL 
	ENDIF
NEXT
SIF ICOUNT_B % 5 > 0
	PRINTL 
SETCOLORBYNAME LightSalmon
PRINTFORML [戒指]
RESETCOLOR
ICOUNT_B = 0
FOR ICOUNT_A,300,321
	SIF ITEM:ICOUNT_A == 0
		CONTINUE
	PRINTFORM [%ITEMNAME:ICOUNT_A + @"(x{ITEM:ICOUNT_A})",20,LEFT%]
	ICOUNT_B += 1
	IF ICOUNT_B % 5 == 0
		PRINTL 
	ENDIF
NEXT
SIF ICOUNT_B % 5 > 0
	PRINTL 

DRAWLINE

CALL SALEITEM_CHECK_TRAP
;所持点を一時保存
TFLAG:15 = MONEY

$INPUT_LOOP
;以下に自動的に売り物が表示される
PRINT_SHOPITEM

PRINTL 《请输入要购买陷阱的编号》

DRAWLINE
PRINTLC [997] - 普通物品　
PRINTLC [999] - 返回
PRINTL

;-------------------------------------------------
;陷阱出現条件
;-------------------------------------------------
@SALEITEM_CHECK_TRAP

ITEMSALES:60 = 1
ITEMSALES:61 = 1
ITEMSALES:62 = 1
ITEMSALES:63 = 1
ITEMSALES:69 = 1
ITEMSALES:72 = 1
ITEMSALES:73 = 1
ITEMSALES:74 = 1
ITEMSALES:75 = 1
ITEMSALES:76 = 1
ITEMSALES:77 = 1
ITEMSALES:78 = 1
ITEMSALES:81 = 1
ITEMSALES:82 = 1
ITEMSALES:83 = 1
ITEMSALES:84 = 1
ITEMSALES:85 = 1
ITEMSALES:86 = 1

IF TALENT:0:327 == 1
	;えっちな陷阱
	ITEMSALES:64 = 1
	ITEMSALES:65 = 1
	ITEMSALES:66 = 1
	ITEMSALES:67 = 1
	ITEMSALES:68 = 1
	ITEMSALES:70 = 1
	ITEMSALES:71 = 1
	ITEMSALES:79 = 1
ELSE
	;淫魔知识
	ITEMSALES:54 = 1
ENDIF

;魔虫知识
SIF TALENT:MASTER:328 == 0
	ITEMSALES:56 = 1

;魔蟲知識でも手に入る罠
IF TALENT:MASTER:328 == 1
	ITEMSALES:65 = 1
	ITEMSALES:79 = 1
	ITEMSALES:80 = 1
ENDIF

;指輪
ITEMSALES:91 = 1
;陷阱Lv
SIF FLAG:85 < CFLAG:0:9
	ITEMSALES:55 = 1

RETURN 0


;
;