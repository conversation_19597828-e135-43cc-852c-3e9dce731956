﻿;-----------------------------------------------
;魔法関係
;-----------------------------------------------
;TARGET_TYPEが1のとき勇者からモンスター、2のときモンスターから勇者、3のとき勇者から奴隷、4のとき奴隷から勇者
@MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
;-----------------------------------------------

SIF TARGET_TYPE == 1 && E:(B + 3) == 0
	RETURN 999

CALL MAGIC_SELECT,TARGET_TYPE
CALL MAGIC_USE,RESULT,TARGET_TYPE

SIF RESULT == 999
	RETURN 999

;呪術チェック

CALL SHAMAN_SELECT,TARGET_TYPE
CALL MAGIC_USE,RESULT,TARGET_TYPE

SIF RESULT == 999
	RETURN 999

	
;法術チェック

IF (TARGET_TYPE == 1 || TARGET_TYPE == 4) && TALENT:A:242 == 0
	RETURN 0
ELSEIF TARGET_TYPE == 2
	RETURN 0
ELSEIF TARGET_TYPE == 3 && TALENT:B:242 == 0
	RETURN 0
ENDIF


IF RAND:2 == 0
	CALL SHIELD_MAGIC
ELSE
	CALL HEAL_MAGIC
ENDIF

RETURN 0

;--------------------------------------
@MAGIC_USE,ARG,TARGET_TYPE
#DIM TARGET_TYPE
;--------------------------------------

IF ARG == 1
	CALL TELEPORT_MAGIC,TARGET_TYPE
ELSEIF ARG == 2
	CALL SLEEP_MAGIC,TARGET_TYPE
ELSEIF ARG == 3
	CALL ENERGY_BOLT_MAGIC,TARGET_TYPE
ELSEIF ARG == 4
	CALL ENERGY_DRAIN_MAGIC,TARGET_TYPE
ELSEIF ARG == 5
	CALL FIREBALL_MAGIC,TARGET_TYPE
ELSEIF ARG == 6
	CALL HEAL_MAGIC,TARGET_TYPE
ELSEIF ARG == 7
	CALL CURSE_MAGIC,TARGET_TYPE
ELSEIF ARG == 8
	CALL MIND_DRAIN_MAGIC,TARGET_TYPE
ELSEIF ARG == 9
	CALL LV_DRAIN_MAGIC,TARGET_TYPE
ENDIF

SIF RESULT == 999
	RETURN 999


;-------------------------------------
@MAGIC_SELECT,TARGET_TYPE
#DIM TARGET_TYPE
#DIM ANTI_MAGIC_DMG,2
#DIM MAGIC_LV
;-------------------------------------

;魔法を使えるかチェックと使用魔法
IF (TARGET_TYPE == 1 || TARGET_TYPE == 4) && TALENT:A:241 == 0
	RETURN 0
ELSEIF TARGET_TYPE == 3 && TALENT:B:241 == 0
	RETURN 0
ENDIF

;魔力拡散ダメージ
ANTI_MAGIC_DMG:0 = CFLAG:A:9 * 10 + 200
SIF ANTI_MAGIC_DMG:0 > 600
	ANTI_MAGIC_DMG:0 = 600
ANTI_MAGIC_DMG:1 = CFLAG:A:9 * 20 + 100
SIF ANTI_MAGIC_DMG:1 > 600
	ANTI_MAGIC_DMG:1 = 600

;魔力拡散フラグ
IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	IF CFLAG:A:503 & 2
		SIF FLAG:5 & 32
			PRINTW *魔力暴走了！*
		BASE:A:0 -= ANTI_MAGIC_DMG:0
		BASE:A:1 -= ANTI_MAGIC_DMG:1
	ENDIF
ELSEIF TARGET_TYPE == 3
	IF CFLAG:B:503 & 2
		SIF FLAG:5 & 32
			PRINTW *魔力暴走了！*
		BASE:B:0 -= ANTI_MAGIC_DMG:0
		BASE:B:1 -= ANTI_MAGIC_DMG:1
	ENDIF
ENDIF


IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	MAGIC_LV = CFLAG:A:9
ELSEIF TARGET_TYPE == 2
	RETURN E:(B + 6)
ELSEIF TARGET_TYPE == 3
	MAGIC_LV = CFLAG:B:9
ENDIF

MAGIC_LV = (MAGIC_LV / 3) + 2
SIF MAGIC_LV > 7
	MAGIC_LV = 7
MAGIC_LV = RAND:MAGIC_LV

IF MAGIC_LV == 0
	RETURN 0
ELSEIF MAGIC_LV == 1
	RETURN 3
ELSEIF MAGIC_LV == 2
	RETURN 2
ELSEIF MAGIC_LV == 3
	RETURN 1
ELSEIF MAGIC_LV == 4
	RETURN 4
ELSE
	RETURN 5
ENDIF
RETURN 0

;-------------------------------------
@SHAMAN_SELECT,TARGET_TYPE
#DIM TARGET_TYPE
#DIM ANTI_MAGIC_DMG,2
#DIM MAGIC_LV
;-------------------------------------

;咒术を使えるかチェックと使用魔法(Y)
IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	SIF TALENT:A:250 == 0
		RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF TALENT:B:250 == 0
		RETURN 0
ENDIF


;魔力拡散ダメージ
ANTI_MAGIC_DMG:0 = CFLAG:A:9 * 10 + 200
SIF ANTI_MAGIC_DMG:0 > 600
	ANTI_MAGIC_DMG:0 = 600
ANTI_MAGIC_DMG:1 = CFLAG:A:9 * 20 + 100
SIF ANTI_MAGIC_DMG:1 > 600
	ANTI_MAGIC_DMG:1 = 600

;魔力拡散フラグ
IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	IF CFLAG:A:503 & 2
		SIF FLAG:5 & 32
			PRINTW *魔力暴走了！*
		BASE:A:0 -= ANTI_MAGIC_DMG:0
		BASE:A:1 -= ANTI_MAGIC_DMG:1
	ENDIF
ELSEIF TARGET_TYPE == 3
	IF CFLAG:B:503 & 2
		SIF FLAG:5 & 32
			PRINTW *魔力暴走了！*
		BASE:B:0 -= ANTI_MAGIC_DMG:0
		BASE:B:1 -= ANTI_MAGIC_DMG:1
	ENDIF
ENDIF


IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	MAGIC_LV = CFLAG:A:9
ELSEIF TARGET_TYPE == 2
	RETURN 0
ELSEIF TARGET_TYPE == 3
	MAGIC_LV = CFLAG:B:9
ENDIF

MAGIC_LV = (MAGIC_LV / 3) + 2
SIF MAGIC_LV > 7
	MAGIC_LV = 7
MAGIC_LV = RAND:MAGIC_LV

IF MAGIC_LV == 0
	RETURN 0
ELSEIF MAGIC_LV == 1
	RETURN 3
ELSEIF MAGIC_LV == 2
	RETURN 7
ELSEIF MAGIC_LV == 3
	RETURN 1
ELSEIF MAGIC_LV == 4
	RETURN 8
ELSE
	RETURN 9
ENDIF
RETURN 0

;-------------------------------
@MAGIC_DAMAGE_CAP,CHARA_LV,ENEMY_LV,DAMAGE,DMG_CAP
#DIM CHARA_LV
#DIM DAMAGE
#DIM ENEMY_LV
#DIM DMG_CAP
#DIM CAP_BONUS
;-------------------------------

;キャップはレベル差で増減
CAP_BONUS = CHARA_LV - ENEMY_LV
SIF CAP_BONUS <= -100
	CAP_BONUS = -99
DMG_CAP *= 100 + CAP_BONUS
DMG_CAP /= 100 
SIF DAMAGE > DMG_CAP
	DAMAGE = DMG_CAP

SIF DAMAGE <= 0
	DAMAGE = 1

RETURN DAMAGE

;-------------------------------
@MAGIC_BONUS_C_TO_M,CHARA,DAMAGE
#DIM CHARA
#DIM DAMAGE
;-------------------------------
;キャラからモンスターに対する効果補正

;エルフ特性。1.5倍
SIF TALENT:CHARA:314 == 1 || TALENT:CHARA:314 == 7
	DAMAGE += DAMAGE / 2

;青肌特性。1.33倍
SIF TALENT:CHARA:244
	DAMAGE += DAMAGE / 3

;額の目特性。1.2倍
SIF TALENT:CHARA:260
	DAMAGE += DAMAGE / 5

;畏怖・隷属処理
IF CFLAG:CHARA:130 == E:B && CFLAG:CHARA:131 > 5
	PRINTFORM 凌辱的记忆历历在目……无法反抗！
	DAMAGE /= 10
ELSEIF CFLAG:CHARA:130 == E:B && CFLAG:CHARA:131 >= 0
	PRINTFORM 凌辱的记忆历历在目……
	DAMAGE *= 6 - CFLAG:CHARA:131
	DAMAGE /= 10
ENDIF

;ボスの魔法抵抗。1/10
IF E:(B + 8) == 1
	DAMAGE /= 10
	SIF FLAG:5 & 32
		PRINT BOSS成功抵抗了魔法攻击！
ENDIF

RETURN DAMAGE

;-------------------------------
@MAGIC_BONUS_M_TO_C,CHARA,DAMAGE
#DIM CHARA
#DIM DAMAGE
;-------------------------------
;モンスターからキャラに対する魔法効果補正

;ボスの魔法特効。1.5倍
IF E:(B + 8) == 1
	DAMAGE += DAMAGE / 2
	SIF FLAG:5 & 32
		PRINT BOSS的强力魔法！
ENDIF

IF CHARA >= 0
	;魔法耐性。0.77倍
	IF TALENT:CHARA:257
		DAMAGE -= DAMAGE / 3
		SIF FLAG:5 & 32
			PRINT 魔法耐性！　
	ENDIF
	
	;褐色肌。0.8倍
	;魔法に耐性があるとダークエルフっぽい？
	SIF TALENT:CHARA:253
		DAMAGE -= DAMAGE / 5
	
	;魔法デバフ
	IF CFLAG:CHARA:682 > 50
		;最大50%増加
		DAMAGE += DAMAGE / 2
		;10%程度低下
		CFLAG:CHARA:682 -= (CFLAG:CHARA:682 / 10) + 1
	ELSEIF CFLAG:CHARA:682 > 0
		;%増加
		DAMAGE *= 100 + CFLAG:CHARA:682
		DAMAGE /= 100
		;10%程度低下
		CFLAG:CHARA:682 -= (CFLAG:CHARA:682 / 10) + 1
	ENDIF
	
	;畏怖・隷属処理
	IF CFLAG:CHARA:130 == E:B && CFLAG:CHARA:131 > 5
		PRINTFORM 凌辱的记忆历历在目……无法反抗！
		DAMAGE *= 2
	ELSEIF CFLAG:CHARA:130 == E:B && CFLAG:CHARA:131 >= 0
		PRINTFORM 凌辱的记忆历历在目……
		DAMAGE *= 6 + CFLAG:CHARA:131
		DAMAGE /= 5
	ENDIF
	
ENDIF


RETURN DAMAGE

;-------------------------------
@MAGIC_BONUS_C_TO_C,CHARA,DAMAGE,DEF_CHARA
#DIM CHARA
#DIM DAMAGE
#DIM DEF_CHARA
;-------------------------------

;エルフ特性。1.5倍
SIF TALENT:CHARA:314 == 1 || TALENT:CHARA:314 == 7
	DAMAGE += DAMAGE / 2

;青肌特性。1.33倍
SIF TALENT:CHARA:244
	DAMAGE += DAMAGE / 3

;額の目特性。1.2倍
SIF TALENT:CHARA:260
	DAMAGE += DAMAGE / 5

IF DEF_CHARA >= 0
	;魔法耐性。0.77倍
	IF TALENT:DEF_CHARA:257
		DAMAGE -= DAMAGE / 3
		SIF FLAG:5 & 32
			PRINT 魔法耐性！　
	ENDIF
	
	;褐色肌。0.8倍
	;魔法に耐性があるとダークエルフっぽい？
	SIF TALENT:CHARA:253
		DAMAGE -= DAMAGE / 5
	
	;魔法デバフ
	IF CFLAG:CHARA:682 > 50
		;最大50%増加
		DAMAGE += DAMAGE / 2
		;10%程度低下
		CFLAG:CHARA:682 -= (CFLAG:CHARA:682 / 10) + 1
	ELSEIF CFLAG:CHARA:682 > 0
		;%増加
		DAMAGE *= 100 + CFLAG:CHARA:682
		DAMAGE /= 100
		;10%程度低下
		CFLAG:CHARA:682 -= (CFLAG:CHARA:682 / 10) + 1
	ENDIF
	
ENDIF

RETURN DAMAGE

;-------------------------------
@TELEPORT_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
;-------------------------------
;テレポート
IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	
	;パーティの重症度によって判定する
	;リーダーの場合全員を見れる
	;仲間が使用した場合自分とリーダーだけを見る
	
	LOCAL:0 = 0
	;自分が重症か
	SIF BASE:A:0 <= 600
		LOCAL:0 += 1
	;仲間Aが重症か
	LOCAL:1 = CFLAG:A:531
	SIF BASE:(LOCAL:1):0 <= 600 && LOCAL:1 > 0
		LOCAL:0 += 1
	
	;仲間Bが重症か
	LOCAL:1 = CFLAG:A:532
	SIF BASE:(LOCAL:1):0 <= 600 && LOCAL:1 > 0
		LOCAL:0 += 1
	
	;リーダーが重症か
	LOCAL:1 = CFLAG:A:533
	SIF BASE:(LOCAL:1):0 <= 600 && LOCAL:1 > 0
		LOCAL:0 += 1
	
	SIF LOCAL:0 <= 0
		RETURN 0

	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%在危机关头使出传送术脱离了！
	BASE:A:1 -= 10
	D:20 = RAND:100
	RETURN 999
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW 怪物用传送把%SAVESTR:A%送走了。
	D:20 = RAND:100
	CFLAG:A:509 = 1
	RETURN 999
ELSEIF TARGET_TYPE == 3
	SIF BASE:B:0 > 600
		RETURN 0
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%在危机关头使出传送术脱离了！
	BASE:B:1 -= 10
	CFLAG:B:3 = RAND:100
	RETURN 999
ENDIF
RETURN 0

;-------------------------------
@SLEEP_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
;-------------------------------
;スリープ

IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了睡眠咒语！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;モンスターレベルによる効果軽減
	DAMAGE -= E:(B + 1) / 5
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,300
	DAMAGE = RESULT
	

	C = B + 2
	E:C -= RAND:DAMAGE
	
	IF E:C < 0
		SIF FLAG:5 & 32
			PRINTW 怪物完全睡着了…
		E:C = 0
	ELSEIF Y <= 0
		SIF FLAG:5 & 32
			PRINTW 咒语的效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 怪物还在沉睡着…
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了睡眠咒语！
		
	DAMAGE = E:(B + 1) + CFLAG:0:9
	
	CALL MAGIC_BONUS_M_TO_C,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,E:(B + 1),CFLAG:A:9,DAMAGE,300
	DAMAGE = RESULT
	
	CFLAG:A:11 -= RAND:DAMAGE
	IF CFLAG:A:11 < 0
		SIF FLAG:5 & 32
			PRINTW 勇者完全睡着了…
		CFLAG:A:11 = 0
	ELSEIF DAMAGE <= 0
		SIF FLAG:5 & 32
			PRINTW 咒语效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 勇者还在沉睡…
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了睡眠咒语！！
	BASE:B:1 -= 30
	DAMAGE = CFLAG:B:9
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT

	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,300
	DAMAGE = RESULT

	CFLAG:A:11 -= RAND:DAMAGE
	IF CFLAG:A:11 < 0
		SIF FLAG:5 & 32
			PRINTW 奴隶完全睡着了…
		CFLAG:A:11 = 0
	ELSEIF DAMAGE <= 0
		SIF FLAG:5 & 32
			PRINTW 咒语的效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 奴隶还在沉睡…
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了睡眠咒语！！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,300
	DAMAGE = RESULT
	
	CFLAG:B:11 -= RAND:DAMAGE
	IF CFLAG:B:11 < 0
		SIF FLAG:5 & 32
			PRINTW 勇者完全睡着了…
		CFLAG:B:11 = 0
	ELSEIF Y <= 0
		SIF FLAG:5 & 32
			PRINTW 咒语效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 勇者还在沉睡…
	ENDIF
	RETURN 0
ENDIF
RETURN 0


;------------------------------------
@ENERGY_BOLT_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
#DIM KILL_MONS
;------------------------------------
;エナジーボルト
IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了魔法箭！
	BASE:A:1 -= 15
	DAMAGE = CFLAG:A:9 * 5
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;モンスターレベルによる効果軽減
	DAMAGE -= E:(B + 1) / 20
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,600
	DAMAGE = RESULT

	KILL_MONS = DAMAGE / E:(B + 3)
	
	IF KILL_MONS <= 0
		SIF FLAG:5 & 32
			PRINTW 魔法箭好像完全没有效果
	ELSEIF KILL_MONS > 0
		SIF DAMAGE > E:(B + 99)
			DAMAGE = E:(B + 99)
		SIF FLAG:5 & 32
			PRINTFORMW 魔法箭贯穿了{KILL_MONS}只怪物！
		E:(B + 99) -= KILL_MONS
		EXP:A:80 += E:(B + 1) * KILL_MONS
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了魔法箭！
	DAMAGE = E:(B + 1) + CFLAG:0:9
	DAMAGE *= 5
	
	CALL MAGIC_BONUS_M_TO_C,A,DAMAGE
	DAMAGE = RESULT
	
	BASE:A:0 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 魔法箭造成了{DAMAGE}伤害！
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了魔法箭！
	BASE:B:1 -= 15
	DAMAGE = CFLAG:B:9 * 5
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,600
	DAMAGE = RESULT
	
	BASE:A:0 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 魔法箭造成了{DAMAGE}伤害！
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了魔法箭！
	BASE:A:1 -= 15
	DAMAGE = CFLAG:A:9 * 5
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,600
	DAMAGE = RESULT
	
	BASE:B:0 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 魔法箭造成了{DAMAGE}伤害！
	RETURN 0
ENDIF
RETURN 0

;----------------------------------
@ENERGY_DRAIN_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
#DIM KILL_MONS
;----------------------------------
;エナジードレイン
IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了魔法吸收！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9 * 5
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,500
	DAMAGE = RESULT
	
	KILL_MONS = DAMAGE / E:(B + 3)
	IF KILL_MONS <= 0
		SIF FLAG:5 & 32
			PRINTW 魔法吸收好像完全没有效果
	ELSEIF KILL_MONS > 0
		SIF KILL_MONS > E:(B + 99)
			KILL_MONS = E:(B + 99)
		SIF FLAG:5 & 32
			PRINTFORMW 魔法吸取贯穿了{KILL_MONS}只怪物！
		E:(B + 99) -= KILL_MONS
		EXP:A:80 += E:(B + 1) * KILL_MONS
	ENDIF
	BASE:A:0 += DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 恢复了HP{DAMAGE}点！
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了魔法吸取！
	
	DAMAGE = (E:(B + 1) + CFLAG:0:9) * 5
	
	CALL MAGIC_BONUS_M_TO_C,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,E:(B + 1),CFLAG:A:9,DAMAGE,500
	DAMAGE = RESULT
	
	BASE:A:1 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 魔法吸取了{DAMAGE}气力！
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了魔法吸取！
	BASE:B:1 -= 30
	DAMAGE = CFLAG:B:9 * 5
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,500
	DAMAGE = RESULT
	
	BASE:A:1 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 魔法吸取了{DAMAGE}气力！
	BASE:B:0 += DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 恢复了HP{DAMAGE}点！
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了魔法吸取！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9 * 5
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,500
	DAMAGE = RESULT
	
	BASE:B:1 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 魔法吸取了{DAMAGE}气力！
	BASE:A:0 += DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 恢复了HP{DAMAGE}点！
	RETURN 0
ENDIF
RETURN 0

;------------------------------
@FIREBALL_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
#DIM KILL_MONS
;------------------------------
;ファイアボール

IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了火球术！
	BASE:A:1 -= 40
	DAMAGE = CFLAG:A:9 * E:(B + 99) / 2
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,800
	DAMAGE = RESULT
	
	KILL_MONS = DAMAGE / E:(B + 3)
	IF KILL_MONS <= 0
		SIF FLAG:5 & 32
			PRINTW 火球术好像完全没有效果
	ELSEIF KILL_MONS > 0
		SIF KILL_MONS > E:(B + 99)
			KILL_MONS = E:(B + 99)
		SIF FLAG:5 & 32
			PRINTFORMW 火球术烧尽了{KILL_MONS}只怪物！
		E:(B + 99) -= KILL_MONS
		EXP:A:80 += E:(B + 1) * KILL_MONS
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了火球术！
	DAMAGE = (E:(B + 1) + CFLAG:0:9) * 10
	
	CALL MAGIC_BONUS_M_TO_C,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,E:(B + 1),CFLAG:A:9,DAMAGE,800
	DAMAGE = RESULT
	
	BASE:A:0 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 火球术对勇者造成了{DAMAGE}伤害！
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了火球术！
	BASE:B:1 -= 40
	DAMAGE = CFLAG:B:9 * 10
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,800
	DAMAGE = RESULT
	
	BASE:A:0 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 火球术对奴隶造成了{DAMAGE}伤害！
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了火球术！
	BASE:A:1 -= 40
	DAMAGE = CFLAG:A:9 * 10
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,800
	DAMAGE = RESULT
	
	BASE:B:0 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 火球术对勇者造成了{DAMAGE}伤害！
	RETURN 0
ENDIF
RETURN 0

;--------------------------
@HEAL_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
;--------------------------
;ヒール

IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	
	;パーティの重症度によって判定する
	;リーダーの場合全員を見れる
	;仲間が使用した場合自分とリーダーだけを見る
	
	LOCAL:0 = 0
	;自分が重症か
	SIF BASE:A:0 * 100 / MAXBASE:A:0 < 60
		LOCAL:0 = A
	;仲間Aが重症か
	LOCAL:1 = CFLAG:A:531
	SIF BASE:(LOCAL:1):0 * 100 / MAXBASE:(LOCAL:1):0 < 60 && LOCAL:1 > 0
		LOCAL:0 = LOCAL:1
	
	;仲間Bが重症か
	LOCAL:1 = CFLAG:A:532
	SIF BASE:(LOCAL:1):0 * 100 / MAXBASE:(LOCAL:1):0 < 60 && LOCAL:1 > 0
		LOCAL:0 = LOCAL:1
	
	;リーダーが重症か
	LOCAL:1 = CFLAG:A:533
	SIF BASE:(LOCAL:1):0 * 100 / MAXBASE:(LOCAL:1):0 < 60 && LOCAL:1 > 0
		LOCAL:0 = LOCAL:1
	
	SIF LOCAL:0 <= 0
		RETURN 0
	IF FLAG:5 & 32
		PRINTFORM %SAVESTR:A%咏唱了治疗术！
		IF LOCAL:0 == A
			PRINT 自己
		ELSE
			PRINTS SAVESTR:(LOCAL:0)
		ENDIF
		PRINTW 得到了治疗！
	ENDIF
	BASE:A:1 -= 5
	DAMAGE = CFLAG:A:9 * 5
	
	;対象が存在しないので
	;キャラtoキャラの対象存在なしを使用する
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,-1
	DAMAGE = RESULT
	
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:(LOCAL:0)%的HP恢复了{DAMAGE}点！
	BASE:(LOCAL:0):0 += DAMAGE
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了治疗术！
	DAMAGE = E:(B + 1) + CFLAG:0:9
	
	;対象が存在しないので
	;モンスターtoキャラの対象存在なしを使用する
	CALL MAGIC_BONUS_M_TO_C,-1,DAMAGE
	DAMAGE = RESULT
	
	E:(B + 3) += 1 + (DAMAGE / 60)
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF BASE:B:0 * 100 / MAXBASE:B:0 < 60
		RETURN 0
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了治疗术！
	BASE:B:1 -= 5
	DAMAGE = CFLAG:B:9 * 5
	
	;対象が存在しないので
	;キャラtoキャラの対象存在なしを使用する
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,-1
	DAMAGE = RESULT
	
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%的HP恢复了{DAMAGE}点！
	BASE:B:0 += DAMAGE
	RETURN 0
ENDIF
RETURN 0

;--------------------------
@SHIELD_MAGIC
#DIM TARGET_TYPE
#DIM DAMAGE
;--------------------------
;盾の護法

IF TARGET_TYPE == 1 || TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了护盾术！
	BASE:A:1 -= 5
	DAMAGE = CFLAG:A:9 * 2
	
	;対象が存在しないので
	;キャラtoキャラの対象存在なしを使用する
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,-1
	DAMAGE = RESULT
	
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%防御值上升了{DAMAGE}点！
	CFLAG:A:12 += DAMAGE
	RETURN 0
ELSEIF TARGET_TYPE == 2
	;怪物使用の場合回復魔法と効果は変わらず
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了护盾术！
	DAMAGE = E:(B + 1) + CFLAG:0:9
	
	;対象が存在しないので
	;モンスターtoキャラの対象存在なしを使用する
	CALL MAGIC_BONUS_M_TO_C,-1,DAMAGE
	DAMAGE = RESULT
	
	E:(B + 3) += 1 + (DAMAGE / 60)
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了护盾术！
	BASE:B:1 -= 5
	DAMAGE = CFLAG:B:9 * 2
	
	;対象が存在しないので
	;キャラtoキャラの対象存在なしを使用する
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,-1
	DAMAGE = RESULT
	
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%防御值上升了{DAMAGE}点！
	CFLAG:B:12 += DAMAGE
	RETURN 0
ENDIF
RETURN 0


;-----------------------------------
@CURSE_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
#DIM KILL_MONS
;-----------------------------------
;カース

IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了诅咒术！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,200
	DAMAGE = RESULT
	
	E:(B + 3) -= RAND:DAMAGE / 2
	IF E:(B + 3) <= 0
		SIF FLAG:5 & 32
			PRINTW 怪物完全被诅咒了…
		E:C = 1
	ELSEIF DAMAGE == 0
		SIF FLAG:5 & 32
			PRINTW 诅咒效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 怪物仍然被诅咒着…
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 2
	PRINTFORMW %ITEMNAME:(E:B)%咏唱了诅咒术！
	DAMAGE = E:(B + 1) + CFLAG:0:9
	
	CALL MAGIC_BONUS_M_TO_C,A,DAMAGE
	DAMAGE = RESULT

	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,E:(B + 1),CFLAG:A:9,DAMAGE,200
	DAMAGE = RESULT

	CFLAG:A:12 -= RAND:DAMAGE / 2
	IF CFLAG:A:12 < 0
		SIF FLAG:5 & 32
			PRINTW 勇者完全被诅咒了…
		CFLAG:A:12 = 0
	ELSEIF DAMAGE == 0
		SIF FLAG:5 & 32
			PRINTW 诅咒效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 勇者仍然被诅咒着…
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了诅咒术！
	BASE:B:1 -= 30
	DAMAGE = CFLAG:B:9
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,200
	DAMAGE = RESULT
	
	CFLAG:A:12 -= RAND:RESULT / 2
	IF CFLAG:A:12 < 0
		SIF FLAG:5 & 32
			PRINTW 奴隶完全被诅咒了…
		CFLAG:A:12 = 0
	ELSEIF DAMAGE == 0
		SIF FLAG:5 & 32
			PRINTW 诅咒效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 奴隶仍然被诅咒着…
	ENDIF
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了诅咒术！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,200
	DAMAGE = RESULT
	
	CFLAG:B:12 -= RAND:RESULT / 2
	IF CFLAG:B:12 < 0
		SIF FLAG:5 & 32
			PRINTW 勇者完全被诅咒了…
		CFLAG:B:12 = 0
	ELSEIF DAMAGE == 0
		SIF FLAG:5 & 32
			PRINTW 诅咒效果消失了
	ELSE
		SIF FLAG:5 & 32
			PRINTW 勇者仍然被诅咒着…
	ENDIF
	RETURN 0
ENDIF
RETURN 0

;----------------------------------
@MIND_DRAIN_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
#DIM KILL_MONS
;----------------------------------
;マインドドレイン
IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了精神吸收！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9 * 5
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,500
	DAMAGE = RESULT
	
	KILL_MONS = DAMAGE / E:(B + 3)
	IF KILL_MONS <= 0
		SIF FLAG:5 & 32
			PRINTW 精神吸收好像完全没有效果
	ELSEIF KILL_MONS > 0
		SIF KILL_MONS > E:(B + 99)
			KILL_MONS = E:(B + 99)
		SIF FLAG:5 & 32
			PRINTFORMW 精神吸收贯穿了{KILL_MONS}只怪物！
		E:(B + 99) -= KILL_MONS
		EXP:A:80 += E:(B + 1) * KILL_MONS
	ENDIF
	BASE:A:1 += DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 气力恢复{DAMAGE}点！
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了精神吸收！
	DAMAGE = (E:(B + 1) + CFLAG:0:9) * 5
	
	CALL MAGIC_BONUS_M_TO_C,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,E:(B + 1),CFLAG:A:9,DAMAGE,500
	DAMAGE = RESULT
	
	BASE:A:1 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 精神吸收造成了{DAMAGE}点气力伤害！
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了精神吸收！
	BASE:B:1 -= 30
	DAMAGE = CFLAG:B:9 * 5
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,500
	DAMAGE = RESULT
	
	BASE:A:1 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 精神吸收了{DAMAGE}点气力！
	BASE:B:1 += DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 气力恢复{DAMAGE}点！
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了魔法吸取！
	BASE:A:1 -= 30
	DAMAGE = CFLAG:A:9 * 5
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,500
	DAMAGE = RESULT
	
	BASE:B:1 -= DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 精神吸收了{DAMAGE}点气力！
	BASE:A:1 += DAMAGE
	SIF FLAG:5 & 32
		PRINTFORMW 气力恢复{DAMAGE}点！
	RETURN 0
ENDIF
RETURN 0

;----------------------------------
@LV_DRAIN_MAGIC,TARGET_TYPE
#DIM TARGET_TYPE
#DIM DAMAGE
#DIM KILL_MONS
;----------------------------------
;レベルドレイン
IF TARGET_TYPE == 1
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了经验吸取！
	BASE:A:1 -= 50
	DAMAGE = CFLAG:A:9 * 10
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,E:(B + 1),DAMAGE,400
	DAMAGE = RESULT
	
	KILL_MONS = DAMAGE / E:(B + 3)
	IF KILL_MONS <= 0
		SIF FLAG:5 & 32
			PRINTW 但好像完全没有效果。
	ELSEIF KILL_MONS > 0
		SIF KILL_MONS > E:(B + 99)
			KILL_MONS = E:(B + 99)
		SIF FLAG:5 & 32
			PRINTFORMW 经验吸取贯穿了{KILL_MONS}只怪物！
		E:(B + 99) -= KILL_MONS
		EXP:A:80 += E:(B + 1) * KILL_MONS
	ENDIF
	EXP:A:80 += DAMAGE / 20
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%得到了{DAMAGE/20}点经验！
	RETURN 0
ELSEIF TARGET_TYPE == 2
	SIF FLAG:5 & 32
		PRINTFORMW %ITEMNAME:(E:B)%咏唱了经验吸取！
	DAMAGE = E:(B + 1) + CFLAG:0:9
	
	CALL MAGIC_BONUS_C_TO_M,A,DAMAGE
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,E:(B + 1),CFLAG:A:9,DAMAGE,400
	DAMAGE = RESULT
	
	EXP:A:80 -= DAMAGE
	BASE:A:0 -= DAMAGE*10
	SIF FLAG:5 & 32
		PRINTFORMW 吸取%SAVESTR:A%{DAMAGE}点经验值并造成了{DAMAGE*10}点HP的伤害！
	IF EXP:A:80 < 0
		CFLAG:A:9 -= 1
		EXP:A:80 = CFLAG:A:9 * 10
		;ステータスも減少
		CFLAG:A:11 -= 1
		CFLAG:A:12 -= 1
		CFLAG:A:13 -= 1
		CFLAG:A:14 -= 1
		SIF FLAG:5 & 32
			PRINTFORMW %SAVESTR:A%的等级下降了1级。
	ENDIF
	;レベルチェック
	CALL CHARA_LV_CHECK,A
	SIF FLAG:5 & 32
		PRINTFORMW あなたは{DAMAGE/2}の経験値を得た！
	EXP:0:80 += DAMAGE / 2
	RETURN 0
ELSEIF TARGET_TYPE == 3
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%咏唱了经验吸收！
	BASE:B:1 -= 50
	DAMAGE = CFLAG:B:9
	
	CALL MAGIC_BONUS_C_TO_C,B,DAMAGE,A
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:B:9,CFLAG:A:9,DAMAGE,400
	DAMAGE = RESULT
	
	EXP:A:80 -= DAMAGE
	BASE:A:0 -= DAMAGE*10
	SIF FLAG:5 & 32
		PRINTFORMW 吸取%SAVESTR:A%{DAMAGE}点经验值并造成了{DAMAGE*10}点HP的伤害！
	IF EXP:A:80 < 0
		CFLAG:A:9 -= 1
		EXP:A:80 = CFLAG:A:9 * 10
		;ステータスも減少
		CFLAG:A:11 -= 1
		CFLAG:A:12 -= 1
		CFLAG:A:13 -= 1
		CFLAG:A:14 -= 1
		SIF FLAG:5 & 32
			PRINTFORMW %SAVESTR:A%的等级下降了1级。
	ENDIF
	;レベルチェック
	CALL CHARA_LV_CHECK,A
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:B%得到{DAMAGE/2}经验值！
	EXP:B:80 += DAMAGE / 2
	RETURN 0
ELSEIF TARGET_TYPE == 4
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%咏唱了经验吸收！
	BASE:A:1 -= 50
	DAMAGE = CFLAG:A:9
	
	CALL MAGIC_BONUS_C_TO_C,A,DAMAGE,B
	DAMAGE = RESULT
	
	;効果量キャップ
	CALL MAGIC_DAMAGE_CAP,CFLAG:A:9,CFLAG:B:9,DAMAGE,400
	DAMAGE = RESULT
	
	EXP:B:80 -= DAMAGE
	BASE:B:0 -= DAMAGE*10
	SIF FLAG:5 & 32
		PRINTFORMW 吸取%SAVESTR:B%{DAMAGE}点经验值并造成了{DAMAGE*10}点HP的伤害！
;	IF EXP:B:80 < 0
;		CFLAG:B:9 -= 1
;		EXP:B:80 = CFLAG:B:9 * 10
;		;ステータスも減少
;		CFLAG:B:11 -= 1
;		CFLAG:B:12 -= 1
;		CFLAG:B:13 -= 1
;		CFLAG:B:14 -= 1
;		SIF FLAG:5 & 32
;			PRINTFORMW %SAVESTR:B%的等级下降了1级。
;	ENDIF
	;レベルチェック
	CALL CHARA_LV_CHECK,B
	SIF FLAG:5 & 32
		PRINTFORMW %SAVESTR:A%得到{DAMAGE/2}经验值！
	EXP:A:80 += DAMAGE / 2
	RETURN 0
ENDIF
RETURN 0

