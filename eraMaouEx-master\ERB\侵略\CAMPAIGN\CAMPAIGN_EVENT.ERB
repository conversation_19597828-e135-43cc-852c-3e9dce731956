﻿
;大型イベント専用処理
;----------------------------------
;メニュー
;----------------------------------
@CAMPAIGN_MENU
#DIM CHARA
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 24
$INPUT_LOOP
DRAWLINE
PRINT 当前選擇的行動: 

IF FLAG:400 > 0
	TRYCALLFORM CAMPAIGN_NAME_{FLAG:400}
ELSE
	PRINT 无
ENDIF

PRINTL  
DRAWLINE

IF FLAG:400 == 0
	PRINTL [0] 行動選擇
ELSE
	PRINTL [1] 奴隷選召（気力-100）
	PRINTL [2] 派遣奴隷
	;PRINTL [3] 確認奴隷的強化状態
ENDIF
DRAWLINE
PRINTL [999] 返回

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 0 && FLAG:400 != 0
	PRINTL *无法在行動進行時進行變更*
	GOTO INPUT_LOOP
ELSEIF RESULT == 0
	CALL SELECT_CAMPAIGN
ELSEIF FLAG:400 == 0
	PRINTL *請選擇行動*
	GOTO INPUT_LOOP
ELSEIF RESULT == 1
	;奴隷選別
	;キャラメイクできる
	PRINTL 消耗了100点気力……
	IF BASE:MASTER:1 < 100
		PRINTW *気力不足！*
		GOTO INPUT_LOOP
	ENDIF
	
	CALL RAND_CHARA_MAKE
	SIF RESULT == 0
		GOTO INPUT_LOOP
	CHARA = RESULT
	;成功した場合気力100消費
	BASE:MASTER:1 -= 100
	;素質ON
	LOCAL = FLAG:400 + 360
	TALENT:CHARA:LOCAL = 1
	
ELSEIF RESULT == 2
	;奴隷派遣
	$INPUT_LOOP_2
	DRAWLINE
	PRINTL 選擇要派往敵地的奴隷
	PRINTL 要派遣誰呢？
	DRAWLINE
	CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
	PRINTLC [1000] - 上一页
	PRINTLC [999] - 返  回
	PRINTLC [1001] - 下一页
	
	INPUT
	
	;イベント素質
	LOCAL = FLAG:400 + 360
	IF RESULT == 999
		GOTO INPUT_LOOP
	ELSEIF RESULT == 1000		;上一页
		IF NO_PAGE > 0
			NO_PAGE --
			CLEARLINE LINECOUNT-L_LCOUNT		
		ENDIF
		GOTO INPUT_LOOP_2
	ELSEIF RESULT == 1001		;下一页
		IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
			NO_PAGE ++
			CLEARLINE LINECOUNT-L_LCOUNT
		ENDIF
		GOTO INPUT_LOOP_2
	ELSEIF RESULT < 0 || RESULT >= CHARANUM
		GOTO INPUT_LOOP_2
	;臨死中のキャラは排除
	ELSEIF BASE:RESULT:0 < 1
		GOTO INPUT_LOOP_2
	ELSEIF TALENT:RESULT:LOCAL == 0
		PRINTFORMW 无法派遣没有【%TALENTNAME:LOCAL%】的奴隷
		GOTO INPUT_LOOP_2
	ELSEIF TALENT:RESULT:292
		PRINTFORMW 由于%SAVESTR:RESULT%是魔王之影而无法派遣
		GOTO INPUT_LOOP_2
	ELSEIF CFLAG:RESULT:1 == 12
		PRINTFORMW %SAVESTR:RESULT%已經被派遣了
		GOTO INPUT_LOOP_2
	ELSEIF CFLAG:RESULT:1 != 0
		PRINTFORMW %SAVESTR:RESULT%当前无法被派遣
		GOTO INPUT_LOOP_2
	ENDIF
	CHARA = RESULT
	CFLAG:CHARA:1 = 12
	;現在階層と目標階層をリセット
	CFLAG:CHARA:501 = 1
	CFLAG:CHARA:507 = 0
	CFLAG:CHARA:520 = 0
	CFLAG:CHARA:521 = 1
	PRINTFORMW 派遣了%SAVESTR:CHARA%
	GOTO INPUT_LOOP_2
ENDIF

GOTO INPUT_LOOP

;----------------------------------
@SELECT_CAMPAIGN
;----------------------------------
;キャンペーン選び放題
;最大20個だけど、20個もイベント作らないだろう…的な
DRAWLINE
FOR LOCAL, 1, 21
	;イベントERBがあればメニューが表示される
	TRYCALLFORM CAMPAIGN_EXIST_{LOCAL}
NEXT
DRAWLINE
PRINTL [999] 返回

INPUT

SIF RESULT == 999
	RETURN 0
;イベントERBがあれば初期設定をしてくれる
TRYCALLFORM CAMPAIGN_SET_{RESULT}
;共通初期設定
;深度リセット
FLAG:401 = 0

RETURN 0

;----------------------------------
@CAMPAIGN_ROOM,ARG:0
;----------------------------------
;敵ダンジョンの施設をロードする
;ARG:0=階層

SIF FLAG:400 < 1
	RETURN 0

RESULT = 0
TRYCALLFORM CAMPAIGN_ROOM_{FLAG:400},ARG:0

RETURN RESULT

;----------------------------------
@CAMPAIGN_ROOM_EXTRA,ARG:0
;----------------------------------
;敵ダンジョンの施設拡張をロードする

SIF FLAG:400 < 1
	RETURN 0

RESULT = 0
TRYCALLFORM CAMPAIGN_ROOM_EXTRA_{FLAG:400},ARG:0

RETURN RESULT

;----------------------------------
@CAMPAIGN_QUEST,ARG:0
;----------------------------------
;階層踏破時に発生するイベント
;ARG:0はリーダー

SIF FLAG:400 < 1
	RETURN 0
;階層を見て進行度と比較
LOCAL = CFLAG:(ARG:0):501
IF LOCAL > FLAG:401
	PRINTL ―STORY―
	FORCEWAIT
	TRYCALLFORM CAMPAIGN_STORY_{FLAG:400}
	;ストーリーが進む
	FLAG:401 += 1
	FORCEWAIT
ENDIF
RESULT = 0
TRYCALLFORM CAMPAIGN_QUEST_{FLAG:400},ARG:0
RETURN RESULT

;----------------------------------
@CAMPAIGN_TRAP,ARG:0
;----------------------------------
;階層ごとに発生する罠
;ARG:0はTRAP_NUM（@DUNGEON_TRAP参照）

SIF FLAG:400 < 1
	RETURN 0
RESULT = 0
TRYCALLFORM CAMPAIGN_TRAP_{FLAG:400},ARG:0
RETURN RESULT

;----------------------------------
@CAMPAIGN_EQUIP_SELECT,ARG:0
;----------------------------------
;敵ダンジョンの指輪宝箱をロードする
;ARG:0=階層
SIF FLAG:400 < 1
	RETURN 0
RESULT = 0
TRYCALLFORM CAMPAIGN_EQUIP_SELECT_{FLAG:400},ARG:0
RETURN RESULT

;----------------------------------
@CAMPAIGN_MONSTER_LIST,ARG:0
;----------------------------------
;敵ダンジョンの出現敵をロードする
;ARG:0=階層
;イベント未発の場合、スケルトン
SIF FLAG:400 < 1
	RETURN 190
RESULT = 190
TRYCALLFORM CAMPAIGN_MONSTER_LIST_{FLAG:400},ARG:0
RETURN RESULT

;----------------------------------
@CAMPAIGN_MONSTER_EXTRA,ARG:0
;----------------------------------
;敵ダンジョンの出現敵拡張をロードする
;ARG:0=INUM
;イベント未発の場合、スケルトン
SIF FLAG:400 < 1
	RETURN 190
RESULT = 190
TRYCALLFORM CAMPAIGN_MONSTER_EXTRA_{FLAG:400},ARG:0
RETURN RESULT

;----------------------------------
@CAMPAIGN_DUNGEON_LV
;----------------------------------
;ダンジョンLVをロードする
SIF FLAG:400 < 1
	RETURN 0
RESULT = 0
TRYCALLFORM CAMPAIGN_DUNGEON_LV_{FLAG:400}
RETURN RESULT

;----------------------------------
@CAMPAIGN_GAMEOVER
;----------------------------------
;ゲームオーバー判定
SIF FLAG:400 < 1
	RETURN 0

IF BASE:MASTER:1 <= 0
	PRINTFORMW ***%SAVESTR:MASTER%的体力耗尽了***
	PRINTFORMW 戰役結束了
	FLAG:400 = 0
	BASE:MASTER:1 = 1
	FOR LOCAL, 0, CHARANUM
		;ダンジョン攻略キャンセル
		SIF CFLAG:LOCAL:1 == 12
			CFLAG:LOCAL:1 = 0
	NEXT
ENDIF

RETURN 0

;----------------------------------
@CAMPAIGN_EXP_PILLORY,ARG:0
;----------------------------------
;晒し台による経験値取得
SIF FLAG:400 < 1
	RETURN 0

LOCAL:1 = (CFLAG:(ARG:0):661+CFLAG:(ARG:0):662+CFLAG:(ARG:0):663+CFLAG:(ARG:0):664+CFLAG:(ARG:0):665) / 5 + 1

FOR LOCAL, 0, CHARANUM
	;ダンジョン攻略支援。経験値取得
	SIF CFLAG:LOCAL:1 == 12
		EXP:LOCAL:80 += LOCAL:1
NEXT

PRINTFORMW 通過榨取攻略中的奴隷的能量獲得了{LOCAL:1}点経験値

RETURN 0

;----------------------------------
@CAMPAIGN_ENDING,ARG:0
;----------------------------------
;エンディング
SIF FLAG:400 < 1
	RETURN 0
RESULT = 0
TRYCALLFORM CAMPAIGN_ENDING_{FLAG:400}
FLAG:400 = 0
RETURN RESULT

