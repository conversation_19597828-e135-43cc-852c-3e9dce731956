﻿;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;绳子
;SM系コマンド
;-------------------------------------------------
@COM44

IF TEQUIP:90
	PRINTL 触手紧缚
	;SAVESTR:22 = 触手紧缚
ELSE
	PRINTL 绳
	;SAVESTR:22 = 绳子
ENDIF
CALL TRAIN_MESSAGE_B

;紧缚经验が高いと消費減少
IF EXP:51 < EXPLV:3 / 2
	LOSEBASE:0 += 100
	LOSEBASE:1 += 150
ELSEIF EXP:51 < EXPLV:4 / 2
	LOSEBASE:0 += 80
	LOSEBASE:1 += 120
ELSE
	LOSEBASE:0 += 60
	LOSEBASE:1 += 90
ENDIF

;-------------------------------------------------
;ソースの計算
;-------------------------------------------------
SOURCE:6 = 800
SOURCE:10 = 800
SOURCE:13 = 500
SOURCE:14 = 500

;PALAM:欲情をみる
IF PALAM:5 < PALAMLV:1
	TIMES SOURCE:10 , 0.80
ELSEIF PALAM:5 < PALAMLV:2
	TIMES SOURCE:10 , 0.90
ELSEIF PALAM:5 < PALAMLV:3
	TIMES SOURCE:10 , 1.00
ELSEIF PALAM:5 < PALAMLV:4
	TIMES SOURCE:10 , 1.10
ELSEIF PALAM:5 >= PALAMLV:4
	TIMES SOURCE:10 , 1.20
ENDIF

;ABL:顺从をみる
IF ABL:10 == 0
	TIMES SOURCE:10 , 0.40
ELSEIF ABL:10 == 1
	TIMES SOURCE:10 , 0.60
ELSEIF ABL:10 == 2
	TIMES SOURCE:10 , 0.80
ELSEIF ABL:10 == 3
	TIMES SOURCE:10 , 1.00
ELSEIF ABL:10 == 4
	TIMES SOURCE:10 , 1.10
ELSE
	TIMES SOURCE:10 , 1.20
ENDIF

;ABL:抖M气质をみる
IF ABL:21 == 0
	TIMES SOURCE:10 , 0.80
ELSEIF ABL:21 == 1
	TIMES SOURCE:10 , 1.00
ELSEIF ABL:21 == 2
	TIMES SOURCE:10 , 1.30
ELSEIF ABL:21 == 3
	TIMES SOURCE:10 , 1.60
ELSEIF ABL:21 == 4
	TIMES SOURCE:10 , 2.00
ELSE
	TIMES SOURCE:10 , 3.00
ENDIF

;倒錯的
SIF TALENT:80
	TIMES SOURCE:10 , 2.00

;-------------------------------------------------
;経験上昇
;-------------------------------------------------
EXP:51 += 5
PRINTL 紧缚经验＋５

;绳子の着脱
TEQUIP:44 = 1 - TEQUIP:44
SIF TEQUIP:90
	T = 0

;爱情经验
E = 1
IF CFLAG:2 >= 1000 && (ABL:21 >= 3 || TALENT:88) && ASSIPLAY == 0
	PRINTFORML %EXPNAME:23%+{E}
	EXP:23 += E
ENDIF
E = 0

RETURN 1

;-------------------------------------------------
;-------------------------------------------------
;绳子で緊縛中
;-------------------------------------------------
@EQUIP_COM44
IF TEQUIP:90
	PRINTL ＜触手紧缚中＞
ELSE
	PRINTL ＜紧缚中＞
ENDIF

;-------------------------------------------------
;ソースの計算
;-------------------------------------------------
;紧缚经验が高いと消費減少
IF EXP:51 < EXPLV:3 / 2
	LOSEBASE:0 += 50
	LOSEBASE:1 += 100
ELSEIF EXP:51 < EXPLV:4 / 2
	LOSEBASE:0 += 40
	LOSEBASE:1 += 80
ELSE
	LOSEBASE:0 += 30
	LOSEBASE:1 += 60
ENDIF

;ABL:抖M气质をみる
IF ABL:21 == 0
	A = 60
ELSEIF ABL:21 == 1
	A = 180
ELSEIF ABL:21 == 2
	A = 300
ELSEIF ABL:21 == 3
	A = 480
ELSEIF ABL:21 == 4
	A = 700
ELSE
	A = 850
ENDIF

;倒錯的
SIF TALENT:80
	TIMES A , 2.00

;PALAM:欲情をみる
IF PALAM:5 < PALAMLV:1
	TIMES A , 0.80
ELSEIF PALAM:5 < PALAMLV:2
	TIMES A , 0.90
ELSEIF PALAM:5 < PALAMLV:3
	TIMES A , 1.00
ELSEIF PALAM:5 < PALAMLV:4
	TIMES A , 1.10
ELSEIF PALAM:5 >= PALAMLV:4
	TIMES A , 1.20
ENDIF

SOURCE:6 += A
SOURCE:12 += A
SOURCE:13 += A
SOURCE:14 += A

;-------------------------------------------------
;経験上昇
;-------------------------------------------------
IF TALENT:122 == 0 && TALENT:PLAYER:122 == 0
	PRINTS EXPNAME:40
	PRINTL +1
	EXP:40 += 1
ELSEIF TALENT:122 == 1 && TALENT:PLAYER:122 == 1
	PRINTS EXPNAME:41
	PRINTL +1
	EXP:41 += 1
ENDIF

SIF ASSIPLAY == 0 && ABL:21 >= 2
	TFLAG:30 += 1

SIF TEQUIP:90
	T += 1

EXP:51 += 2
PRINTL 紧缚经验＋２
;
