﻿
= ERA游戏的实现 =

一个ERA游戏是由三个组成部分实现的：【用户定义的数据与数据结构】、【Emuera内置的框架与流程】、【用户实现的过程】。

（“用户”是指游戏制作者）


== 用户定义的数据与数据结构 ==

【用户定义的数据与数据结构】主要是指对变量的定义，包括整个CSV文件夹以及所有ERH文件。
这可以进一步细分为三部分：

=== 对Emuera内置变量结构的定义 ===

Emuera预定义了一些变量，例如FLAG变量，是个一元数值数组变量，用来记录游戏当前的进度与状态，也就是顾名思义的FLAG。

FLAG变量在脚本中有两种引用方式：
　　通过数值索引来引用，例如“FLAG:81”；
　　通过文本索引来引用，例如“FLAG:人間界の侵攻度”。

而要通过文本索引来引用FLAG变量，则必须要预先在FLAG.csv中声明。（“81, 人間界の侵攻度,”）
通过这种声明，游戏制作者同时也规定了该FLAG在游戏中的用途。

此外，游戏制作者同时也可以通过VariableSize.csv来声明FLAG变量的数组大小。

这种声明我们认为是对FLAG这个变量的结构的定义。

除了FLAG，类似的内置变量包括ABL, CFALG, MARK, TALENT, TFLAG等。


=== 对角色数据的定义 ===

在CSV文件夹中一般可以看到很多CharaXXX.csv。
这些文件每一个代表一个角色，其中定义了这个角色的名字（NAME, CALLNAME）、素质（TALENT）、
基础值（BASE）、能力（ABL）、状态（CFLAG）等

这些定义我们认为是对角色数据的定义。

=== 用户自定义变量 ===

用户可以通过.erh文件声明用于所有ERB脚本中的变量（广域变量）。

在声明的同时也可以包含赋值。

这实际上是对以FLAG、TALENT为代表的Emuera内置变量的补足。


== 用户实现的过程 ==

ERA游戏的过程实际上包含两部分：一部分由用户显式的定义和实现，另一部分则是“埋入”在Emuera程序内部的过程。
前者就是在ERB脚本中用户定义的函数。
而后者就是接下来要介绍的【Emuera内置的框架与流程】。



== Emuera内置的框架与流程 ==

【Emuera内置的框架与流程】也可以进一步细分成三个部分，一个是前面提到的【Emuera内置变量】，一个是【Emuera指令和内置方法】。
第三个是经常不被人了解的，但作为游戏制作者必须深入了解的部分——【Emuera执行流程】（Flow）。


=== Emuera执行流程 ===

简单来说“流程”（Flow）是指“当发生某个事件时，Emuera执行某个特定的函数（系统函数），然后再接着执行某个函数（系统函数）”。

==== 标题流程（Flow TITLE） ====

一般用户进入游戏看到的第一个画面被称为标题画面（Title）。 

当游戏要进入标题画面发生时（事件），Emuera会在所有脚本文件中查找用户是否定义了函数@SYSTEM_TITLE（系统函数）。
这个函数应当是用户定义的，用来绘制标题画面以及实现相关逻辑。
若有，则Emuera执行这个函数。
若无，则Emuera通过一个内部的实现来绘制标题画面。

具体为：
　　在屏幕上绘制一条横线；
　　绘制游戏名、制作人员、制作年等信息；
　　绘制一条分割线；
　　绘制两个选项“[0] 新的游戏”和“[1] 加载游戏”。
　　当用户输入“0”时，Emuera开始“首次游戏流程”（Flow FIRST）。
　　当用户输入“1”时，Emuera开始“加载游戏流程”（Flow LOADGAME）。

==== 其他流程 ====

除了标题流程（Flow TITLE）外，其他流程包括：

* “首次游戏流程”（Flow FIRST）：开始新的游戏的流程，包括相关数据的初始化。
* “商店/主页流程”（Flow SHOP）：进入商店并购买东西的流程。
　　　　商店页面是进入游戏后首先进入的页面，也是“每日结束”后返回的页面。
　　　　因此很多游戏将其作为“主页面”而非单纯的“商店页面”。
* “调教流程”（Flow TRAIN）：对目标进行调教的流程，包括绘制调教界面、调教菜单以及调用调教指令。
* “能力提升流程”（Flow ABLUP）：对角色的能力（ABL）进行修改的流程。通常在调教结束后会执行。
* “调教后流程”（Flow AFTERTRAIN）：在要退出调教状态时的执行的流程。
* “日期变更流程”（Flow TURNEND）：当玩家所有操作结束，要切换到下一天（或者下个半天）时执行的流程。
　　　　通常游戏会在这个流程中对玩家本日的行为进行“结算”。
* “加载游戏流程”（Flow LOADGAME）：加载游戏存档（saveN.sav）的流程，包括绘制加载存档的界面。
* “保存游戏流程”（Flow SAVEGAME）：保存游戏存档（saveN.sav）的流程，包括绘制保存存档的界面，以及生成存档标题。
* “加载存档结束流程”（Flow LOADDATAEND）：在加载存档完毕，游戏即将开始时执行的流程，包括相关数据的初始化。
