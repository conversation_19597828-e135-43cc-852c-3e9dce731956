﻿;欲望のLvUP処理とその可否判定
;eratohoA ver1,204のスクリプトをベースに処理を簡略化
;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;欲望のLvUP
;-------------------------------------------------
@ABLUP11
DRAWLINE
;PRINTL 奴隶的性欲提升了。
;PRINTL 欲望越高，被调教时越容易取得快感，也会因愉悦而更加恭顺。
;CUSTOMDRAWLINE ‥
;欲望はLv5が上限
;[容易陷落][淫乱]が付いている場合は10まで上昇可能
IF ABL:11 >= 5 && (TALENT:73 == 0 && TALENT:76 == 0)
	PRINTW 需要特殊素质才能继续提升
	RETURN 0
ELSEIF ABL:11 >= 10
	PRINTW 已达最高级
	RETURN 0
ENDIF

;必要な欲情点数
A = 0

;条件別にＯＫかダメかを記録する
;欲情点数による可否（I=0:可、I&1:点数不足、I&2:経験不足）
I = 0

;异常经验が必要か
E = 0

CALL DECIDE_ABLUP11

;异常经验が必要か
SIF E > 0
	PRINTFORML %EXPNAME:50%{E}以上(现在{EXP:50})且

;欲情点数で上げる
PRINTFORM [0] - %PALAMNAME:5%点数×{JUEL:5}/{A} ……
IF I == 0
	PRINT ＯＫ
ELSE
	SIF I & 1
		PRINT 点数不足 
	SIF I & 2
		PRINT 经验不足
ENDIF
PRINTL 

PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 0) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:11 += 1

IF RESULT == 0
	JUEL:5 -= A
ENDIF

PRINTFORML %ABLNAME:11%变为LV{ABL:11}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP11
;-------------------------------------------------
ABL:11 ++

IF I == 0
	JUEL:5 -= A
ENDIF


;-------------------------------------------------
;欲望のLvUP可否判定
;-------------------------------------------------
@DECIDE_ABLUP11
;欲望はLv5が上限、
;[容易陷落][淫乱]が付いている場合は10まで上昇可能
SIF ABL:11 >= 5 && (TALENT:73 == 0 && TALENT:76 == 0)
	RETURN 0
SIF ABL:11 >= 10
	RETURN 0

;判定変数を空に
A = 0
E = 0
I = 0

IF ABL:11 == 0
	A = 5
ELSEIF ABL:11 == 1
	A = 50
ELSEIF ABL:11 == 2
	A = 1000
ELSEIF ABL:11 == 3
	A = 5000
ELSEIF ABL:11 == 4
	A = 12000
ELSEIF ABL:11 == 5
	A = 20000
ELSEIF ABL:11 == 6
	A = 30000
ELSEIF ABL:11 == 7
	A = 50000
ELSEIF ABL:11 == 8
	A = 80000
ELSEIF ABL:11 == 9
	A = 150000
ENDIF

;戒备森严
IF TALENT:27
	SIF ABL:11 == 3
		TIMES A , 1.50
	SIF ABL:11 == 4
		TIMES A , 2.00
	SIF ABL:11 == 5
		TIMES A , 2.50
	SIF ABL:11 >= 6
		TIMES A , 3.00
ENDIF

;克制
IF TALENT:20
	TIMES A , 1.20
ENDIF

;保守的
IF TALENT:24
	TIMES A , 1.10
ENDIF

;看重贞操
IF TALENT:30
	TIMES A , 1.50
;看轻贞操
ELSEIF TALENT:31
	TIMES A , 0.95
ENDIF

;压抑
IF TALENT:32
	TIMES A , 1.50
;开放
ELSEIF TALENT:33
	TIMES A , 0.90
ENDIF

;抵抗
SIF TALENT:34
	TIMES A , 1.50

;害羞
IF TALENT:35
	TIMES A , 1.10
;不知羞耻
ELSEIF TALENT:36
	TIMES A , 0.95
ENDIF

;接受快感
IF TALENT:70
	TIMES A , 0.80
;否定快感
ELSEIF TALENT:71
	TIMES A , 1.50
ENDIF

;容易上瘾
SIF TALENT:72
	TIMES A , 0.95
;容易陷落
SIF TALENT:73
	TIMES A , 0.50
;淫乱
SIF TALENT:76
	TIMES A , 0.70
;妓女
SIF TALENT:180
	TIMES A , 0.90
;倾城
SIF TALENT:181
	TIMES A , 0.80
;人妻
SIF TALENT:157
	TIMES A , 0.80

;ＬＶ４から５に上げるときは异常经验必要（素質：[开放][接受快感][容易陷落][淫乱][疯狂]なら無視できる）
IF ABL:11 == 4 && (TALENT:33 == 0 && TALENT:70 == 0 && TALENT:73 == 0 && TALENT:76 == 0 && TALENT:123 == 0)
	E = 1
;ＬＶ７から８に上げるときは异常经验が３以上必要（素質：[开放][接受快感][容易陷落][淫乱][疯狂]なら無視できる）
ELSEIF ABL:11 == 7 && (TALENT:33 == 0 && TALENT:70 == 0 && TALENT:73 == 0 && TALENT:76 == 0 && TALENT:123 == 0)
	E = 3
ENDIF

;最低でも1個は必要
SIF A < 1
	A = 1

;欲情点数は足りている？
SIF JUEL:5 < A
	I |= 1

;异常经验は足りている？
SIF E > EXP:50
	I |= 2

IF I == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF
;
;
;