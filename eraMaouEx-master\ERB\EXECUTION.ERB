﻿;------------------------------------------
@EXECUTION
#DIM LV
#DIM LAST_MEMBER
#DIM NO_CHECK
#DIM FAMILY,3

;████修改点1████
#DIM batchSelection,256
#DIM temp
#DIM isOnExecution
;████修改点1████

;ZはTARGETを保存してるため使用禁止
;------------------------------------------
;処刑コマンド
;LAST_MEMBER = 残り処刑待ち人数
;NO_CHECK    = お気に入りされてない処刑待ち

;FLAG:83  肉便器の数

$EXCUTION_MAIN

CUSTOMDRAWLINE =
PRINT 请选择处刑对象
IF FLAG:84 < 20 && DAY < 60
	PRINTFORML <{60 - DAY}天以内再展出{20 - FLAG:84}名勇者到博物館将解锁实绩！>
ELSE
	PRINTL
ENDIF

DRAWLINE

LAST_MEMBER = 0
NO_CHECK = 0

REPEAT CHARANUM
	IF (CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 7) && COUNT != 0 && (!EX_TALENT:COUNT:1 || (EX_TALENT:COUNT:2 && GETBIT(EX_FLAG:9000,1)))
		PRINTFORM [{LAST_MEMBER,2}] %SAVESTR:COUNT,12,LEFT% %GET_JOB_NAME(COUNT),6,LEFT% LV{CFLAG:COUNT:9,3,RIGHT} 
		LAST_MEMBER += 1
		IF CFLAG:COUNT:700
			PRINT [☆]
		ELSE
			NO_CHECK += 1
		ENDIF
		SIF CFLAG:COUNT:0 > 0 && COUNT != 0
			PRINT [可卖掉] 
		PRINTL 
	ENDIF
REND


SIF LAST_MEMBER == 0
	RETURN 0

DRAWLINE
PRINTL [100] 返回

;████修改点2████
PRINTS "\n" + "[121] 批量选择"
;████修改点2████

$INPUT_LOOP
INPUT

;████修改点3████
IF RESULT == 121	
	$BatchSelectionRefresh
	CUSTOMDRAWLINE =
	PRINTS "请进行处刑对象的批量选择" + "\n"
	DRAWLINE
	temp = 1
	WHILE temp < CHARANUM
		IF batchSelection:temp == 1
			SETCOLOR 214,64,64
		ENDIF 
		IF (CFLAG:temp:1 == 0 || CFLAG:temp:1 == 7) && (!EX_TALENT:temp:1 || (EX_TALENT:temp:2 && GETBIT(EX_FLAG:9000,1)))
			PRINTFORM [{temp,3,RIGHT}] %SAVESTR:temp,12,LEFT% %GET_JOB_NAME(temp),6,LEFT% LV{CFLAG:temp:9,3,RIGHT} 
			IF CFLAG:temp:700
				PRINTS "[☆]"
			ENDIF
			IF CFLAG:temp:0 > 0 && temp != 0
				PRINTS "[可卖掉]" 
			ENDIF 
			IF batchSelection:temp == 1
				PRINTS " 【処刑認可】 "
			ENDIF 
			PRINTS "\n"
		ENDIF
		RESETCOLOR 
		temp++
	WEND
	DRAWLINE
	PRINTS "[100] 返回" + "\n"*2
	PRINTS "[121] 选择处刑方式" + "\n"
	INPUT
	SELECTCASE RESULT
	CASE 100
		RESTART
	CASE 121
		temp = 1
		WHILE temp < CHARANUM
			IF batchSelection:temp == 1
				BREAK 
			ENDIF
			temp++
			IF temp >= CHARANUM
				GOTO BatchSelectionRefresh
			ENDIF 
		WEND
		PRINTS "\n" + "[0] 流放出地下城"
		PRINTS "\n" + "[1] 公开处刑"
		PRINTS "\n" + "[2] 博物馆展品"
		PRINTS "\n" + "[3] 施行猎奇向处刑"
		PRINTS "\n" + "[4] 做成肉便器"
		PRINTS "\n" + "[5] 士兵化"
		PRINTS "\n" + "[6] 固定示众"
		PRINTS "\n" + "[7] 消除记忆后释放"
		PRINTS "\n"*2 + "[100] 停止"
		PRINTS "\n"*2 + "[101] 水晶球记录"
		INPUT
		SELECTCASE RESULT
		CASE 0 TO 7
			temp = 1
			WHILE temp < CHARANUM
				IF batchSelection:temp == 1
					IF CFLAG:temp:700 
						PRINTS "有 [☆]收藏 的目标被选中，请检查处刑名单"
						WAIT
						GOTO BatchSelectionRefresh
					ENDIF
				ENDIF
				temp++
			WEND
			RESULT:10 = RESULT:0
			isOnExecution = 1
			temp = CHARANUM
			WHILE temp > 0
				IF batchSelection:temp == 1
					A = temp
					RESULT:0 = RESULT:10
					PRINTS "\n"
					GOTO BatchSelectionExecution 
					$BatchSelectionSetTartget
					batchSelection:temp = 0
				ENDIF
				temp--
			WEND
			isOnExecution = 0
			GOTO BatchSelectionRefresh
		CASE 100
			GOTO BatchSelectionRefresh
		CASE 101
			INVERTBIT EX_FLAG:9000,2
			GOTO BatchSelectionRefresh
		CASEELSE 
			GOTO BatchSelectionRefresh
		ENDSELECT
	CASEELSE
		IF RESULT > 0 && RESULT <= CHARANUM
			IF batchSelection:RESULT == 1
				batchSelection:RESULT = 0
			ELSE 
				batchSelection:RESULT = 1
			ENDIF 
		ENDIF 
		GOTO BatchSelectionRefresh
	ENDSELECT
ENDIF 
;████修改点3████
	

IF RESULT < 0
	GOTO INPUT_LOOP
ELSEIF RESULT >= LAST_MEMBER && RESULT != 100
	GOTO INPUT_LOOP
ENDIF

SIF RESULT == 100
	RETURN 0

X = RESULT
A = 0
REPEAT CHARANUM
	IF (CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 7) && COUNT != 0 && (!EX_TALENT:COUNT:1 || (EX_TALENT:COUNT:2 && GETBIT(EX_FLAG:9000,1))) && A == X
		A = COUNT
		BREAK
	ENDIF
	SIF (CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 7) && COUNT != 0 && (!EX_TALENT:COUNT:1 || (EX_TALENT:COUNT:2 && GETBIT(EX_FLAG:9000,1)))
		A += 1
REND

$INPUT_LOOP_1
SIF CFLAG:A:700
	SETCOLOR (GETDEFCOLOR() - 0x444444)

PRINTL [0] 流放出地下城
PRINTL [1] 公开处刑
PRINTL [2] 博物馆展品
PRINTL [3] 施行猎奇向处刑
PRINTL [4] 做成肉便器
SIF CFLAG:A:700
		RESETCOLOR
PRINTL [5] 士兵化
PRINTL [6] 固定示众
PRINTL [7] 消除记忆后释放
PRINTL  
PRINT [100] 停止		
SETCOLOR GETBIT(EX_FLAG:9000,2) ?  0xffffff # 0x646464
PRINTL [101] 水晶球记录
RESETCOLOR
$INPUT_LOOP_2
INPUT
IF RESULT < 0
	GOTO INPUT_LOOP_2
ELSEIF RESULT >= 8 && RESULT != 100 && RESULT != 101
	GOTO INPUT_LOOP_2
ENDIF

SELECTCASE RESULT
	CASE 0, 1, 2, 3, 4
		IF CFLAG:A:700
			PRINTFORMW %SAVESTR:A%在收藏列表之中，不能被处刑。
			GOTO INPUT_LOOP_2
		ENDIF
	CASE 5
        IF INRANGE(NO:A,17,40)
	    PRINTFORMW %SAVESTR:A%拥有【不受洗脑】，无法进行士兵化洗脑。
		GOTO INPUT_LOOP_2
		ENDIF
ENDSELECT

IF RESULT == 100
	TARGET = FLAG:1
	RETURN 0
ELSEIF RESULT == 101
	INVERTBIT EX_FLAG:9000,2
	GOTO INPUT_LOOP_1
ENDIF

;████修改点4████
$BatchSelectionExecution
;████修改点4████

TARGET = A
TFLAG:16 = RESULT

CALL EXUCUTION_KOUJO


IF TFLAG:16 == 0
	PRINTFORML %NAME:MASTER%把%SAVESTR:A%从地下城里永久驱逐了。
	;████修改点5████
	;JUMP BANISHMENT
	CALL BANISHMENT
	GOTO BatchSelectionSetTartget
	;████修改点5████

ELSEIF TFLAG:16 == 1
	PRINTFORML %NAME:MASTER%把%SAVESTR:A%公开处刑了。
	;████修改点6████
	;JUMP PUBLIC_EXECUTION
	CALL PUBLIC_EXECUTION
	GOTO BatchSelectionSetTartget
	;████修改点6████

ELSEIF TFLAG:16 == 2
	PRINTFORMW %SAVESTR:A%被带到了工作室……
	;████修改点7████
	;JUMP MUSEUM
	CALL MUSEUM
	GOTO BatchSelectionSetTartget
	;████修改点7████

ELSEIF TFLAG:16 == 3
	PRINTFORML %NAME:MASTER%决定让%SAVESTR:A%品尝真正的痛苦…
	PRINTW 　　　　　　　　　　< ※ 注 意 ！ ※ >
	PRINTW （之后将发生非常黄暴的事！！）
	;████修改点8████
	;JUMP GROTESQUE
	CALL GROTESQUE
	GOTO BatchSelectionSetTartget
	;████修改点8████


ELSEIF TFLAG:16 == 4
	FLAG:83 += 1
	IF TALENT:A:220 != 1 && EX_TALENT:A:1 != 1 
	EX_FLAG:99 += 2
	PRINTFORML 威望值增加
	ELSE
	EX_FLAG:99 -= 10
	PRINTFORML 威望值减少
	ENDIF
	FAMILY = CFLAG:A:605
	FAMILY:1 = FAMILY % 10
	CALL SEARCH_FAMILY, A
	FAMILY:2 = RESULT
	M = FAMILY:2
	SIF TALENT:A:85
		PRINTFORM 深爱着你的%SAVESTR:A%不知道自己为什么要被做成肉便器，不停地高叫着你的名字，请求饶恕。
	PRINTFORML 但%SAVESTR:PLAYER%依然给%SAVESTR:A%烙上了封锁所有力量的封印，
	PRINTL 被吸收了全部力量的她，身体变成淫靡的肉块了。
	PRINTL 作为地下城里怪物的慰问品被使用着，
	PRINTW 今后别说重新当勇者，就连看一眼阳光也不可能了吧。
	;エルフ
	SIF TALENT:A:原种族 == 1
		PRINTFORMW 精灵族的%SAVESTR:A%在丑陋的兽人中广受好评。
	;精液中毒
	SIF ABL:A:32 > 0 || TALENT:A:擅用舌头
		PRINTFORMW %SAVESTR:A%作为精液便器，每天都心怀欢喜地把精液喝光了。
	;百合中毒・百合气质
	SIF ABL:A:33 > 0 | ABL:A:22 > 2
		PRINTFORMW 作为女子便器的%SAVESTR:A%获得了很高的评价。阴部、肛门有污垢也毫不在意，老老实实地把她们舔高潮了。
	;抖M气质
	SIF ABL:A:21 > 0
		PRINTFORMW 沐浴在骂声中的%SAVESTR:A%腿间开始湿润，脸上浮现起恍惚的笑容。
	;露出癖
	SIF ABL:A:17 > 0
		PRINTFORMW %SAVESTR:A%在地下城的大街上展露痴态。不管是不是怪物，对所有路过的客人，均热情献媚。
	;侍奉精神
	SIF ABL:A:16 > 0
		PRINTFORMW 完全崩坏了的%SAVESTR:A%连自我都失去了，只有在侍奉肉棒时能感觉到喜悦。
	;V感覚
	SIF ABL:A:2 > 3
		PRINTFORMW 被数之不尽的阴茎抽插，%SAVESTR:A%的私处完全扩张，失去弹性了。最近的对象，全是巨魔和马这样有巨根的。
	;A感覚
	SIF ABL:A:3 > 3
		PRINTFORMW 已经算不上是性器官，%SAVESTR:A%的肛门，被极限扩张，关都关不上了。可是哪怕这样，只要有肉棒在直肠射精，她还是兴奋得快疯了似得。
	;爱慕
	SIF TALENT:A:85
		PRINTFORMW %SAVESTR:A%把所有的阴茎都幻想成深爱的你的阴茎的模样。
	;V敏感・淫壺
	SIF TALENT:A:104 || TALENT:A:232
		PRINTFORMW %SAVESTR:A%对被什么东西插入并不在意，用卑微的话语哀求着打种。
	;A敏感・淫肛
	SIF TALENT:A:106 || TALENT:A:233
		PRINTFORMW %SAVESTR:A%的肛门特别有感觉，恶魔们特意把她的肛门改造成能怀孕的样子。
	;C敏感・淫核
	SIF TALENT:A:102 || TALENT:A:230
		PRINTFORMW %SAVESTR:A%的阴蒂又大又肿，一鞭子下去，她就爱液四射，口水横流地绝顶了。
	;B敏感・淫乳
	SIF TALENT:A:108 || TALENT:A:231
		PRINTFORMW %SAVESTR:A%的乳头被恶魔们改造过，现在可以插入乳头里性交了。
	;魔术・咒术
	SIF TALENT:A:241 || TALENT:A:250
		PRINTFORMW 有魔力的%SAVESTR:A%，头部完全被头罩包裹，强制地被削弱了魔力。
	;法术
	SIF TALENT:A:242
		PRINTFORMW 有神圣之力的%SAVESTR:A%，被恶魔们强制肛交了无数次，完全堕落为恶魔的力量了。
	
	;扶她＆二人以上の肉便器
	IF TALENT:A:121 && FLAG:83 >= 2
		IF TALENT:A:318 == 1
			;巨根
			PRINTFORML 扶她巨根的%SAVESTR:A%被改造成更大的阴茎，
			PRINTFORMW 奉命去侵犯其它的肉便器了。
		ELSEIF TALENT:A:318 == 2
			;短小包茎
			PRINTFORML 扶她短小包茎的%SAVESTR:A%为了发泄性欲压在其它肉便器身上。
			PRINTFORMW 但即使激烈地摆动腰身，也无法令对方满足。
		ELSEIF TALENT:A:318 == 3
			;包茎
			PRINTFORML 扶她包茎的%SAVESTR:A%哪怕洗澡，也不剥开包皮清洗里面的污垢。
			PRINTFORMW 而是让其它肉便器用口服侍清洁。
		ELSE
			;普通
			PRINTFORML 扶她的%SAVESTR:A%积极地侵犯着其它肉便器，
			PRINTFORMW 巨大的阴囊生产了大量的精液，射到周围都是。
		ENDIF
	ENDIF
	
	;魅力点・美乳
	SIF TALENT:A:312 == 12
		PRINTFORMW 作为魅力点的美乳，现在变成一堆丑陋膨胀的肉块了。
	;魅力点・ヒップライン
	SIF TALENT:A:312 == 14
		PRINTFORMW 作为魅力点的臀部曲线，现在变成一团下流膨胀的肉块了。
	;魅力点・性器
	SIF TALENT:A:312 == 21
		PRINTFORMW 作为魅力点的性器，现在正变成奇怪的肉块。
	;魅力点・髪の光沢
	SIF TALENT:A:312 == 22
		PRINTFORMW 作为魅力点的光泽的头发，现在完全褪色了。
	;魅力点・大きな尻
	SIF TALENT:A:312 == 23
		PRINTFORMW 作为魅力点的大屁股，现在变成了充满下流气息的成熟桃子。
	
	;故郷の恋人・憧れの人が好き
	SIF TALENT:A:317 == 4 || TALENT:317 == 11
		PRINTFORMW %SAVESTR:A%双眼空虚，在重复着谁的名字。也许正在妄想和爱人拥抱吧。
	PRINTFORMW 现在的肉便器数量：{FLAG:83}

	SIF FAMILY:2 >= 0
		CSTR:(FAMILY:2):5 = 肉便器%SAVESTR:A%
	TSTR:30 = 肉便器%SAVESTR:A%
	CALL VIDEO_MATURO
	
ELSEIF TFLAG:16 == 5

	TALENT:A:254 = 1
	PRINTFORML 在%SAVESTR:A%的身体上刻上了服从的刻印，
	PRINTW 只残留一点点的意识和记忆，
	PRINTW 命令其现在就去把勇者杀光。
	CFLAG:A:13 /= 2
	CFLAG:A:14 /= 2
	PRINTW *法术的副作用导致其战斗力下降了*
	CSTR:30 = 魔王傀儡%SAVESTR:A%
	TSTR:30 = 魔王傀儡%SAVESTR:A%
	CALL VIDEO_MATURO2
	GOTO LABEL_EXIT

ELSEIF TFLAG:16 == 6
	CFLAG:A:1 = 8
	PRINTFORML 把%SAVESTR:A%的屁股抬高，扣在固定的枷锁上，
	PRINTW 任由怪物们发泄性欲，
	IF TALENT:A:9 == 1
		PRINTW 被玩坏了的奴隶，什么都意识不到，在痴笑着。
	ELSEIF  TALENT:A:76 == 1
		PRINTW 淫乱的奴隶，不如说，正在享受现在的样子。
	ELSE
		PRINTW 悲哀的奴隶，对今后将发生的制裁害怕极了。
	ENDIF
	CSTR:30 = 魔族公廁%SAVESTR:A%
	TSTR:30 = 魔族公廁%SAVESTR:A%
	CALL VIDEO_MATURO2
	GOTO LABEL_EXIT
	
ELSEIF TFLAG:16 == 7
	
	PRINTFORMW %SAVESTR:A%被清除了关于地下城的所有记忆，被丢到地下城外了。
	PRINTFORMW %SAVESTR:A%再次开始了冒险……
	
	;侵入階層・侵攻度・侵攻中・再起点設定
	CFLAG:A:501 = 1
	CFLAG:A:502 = 0
	CFLAG:A:1 = 2
	CFLAG:A:508 = 3
	;善恶值がいくらか回復
	SIF CFLAG:A:151 < -50
		CFLAG:A:151 = -50
	;好感度も低く
	CFLAG:A:2 = 20
	
	GOTO LABEL_EXIT
ENDIF


A = TARGET

;武器解除
W:0 = CFLAG:A:550
CALL EQUIP_GET
CFLAG:A:550 = -1
;指輪解除
W:0 = CFLAG:A:551
CALL EQUIP_GET
CFLAG:A:551 = -1
;指輪解除
W:0 = CFLAG:A:552
CALL EQUIP_GET
CFLAG:A:552 = -1

LV = CFLAG:A:9

X = NO:A + 199
FLAG:X = 1

;前回の助手・調教対象だった場合はフラグを空に
SIF FLAG:1 == TARGET
	FLAG:1 = -1
SIF FLAG:2 == TARGET
	FLAG:2 = -1

;前回の助手・調教対象より前だった場合はフラグを減算
SIF FLAG:1 > TARGET
	FLAG:1 -= 1
SIF FLAG:2 > TARGET
	FLAG:2 -= 1

TARGET = FLAG:1
ASSI = FLAG:2

CALL PARTY_CHAR_DEL, A

DELCHARA A

CALL NAME_RESET


FLAG:80 += 1

LV += 1
LV *= 50

EXP:0:80 += LV
PRINTFORMW 《封印吸收了力量，使你获得了{LV}的经验值！》

;████修改点9████
GOTO BatchSelectionSetTartget
;████修改点9████

NO_CHECK -= 1

;処刑残りがいる場合、最初に戻る
SIF NO_CHECK > 0
	GOTO EXCUTION_MAIN

;调教对象复位
$LABEL_EXIT
TARGET = FLAG:1
ASSI = FLAG:2

;████修改点10████
IF isOnExecution == 1
	GOTO BatchSelectionSetTartget
ENDIF 
;████修改点10████

RETURN 0

;------------------------------------------
@EXECUTION_MINI(ARGS = "")
#DIM LV
;------------------------------------------
;簡易処刑コマンド。TARGETがキャラNo

;武器解除
W:0 = CFLAG:550
CALL EQUIP_GET
CFLAG:550 = -1
;指輪解除
W:0 = CFLAG:551
CALL EQUIP_GET
CFLAG:551 = -1
;指輪解除
W:0 = CFLAG:552
CALL EQUIP_GET
CFLAG:552 = -1

LV = CFLAG:9

DRAWLINE
IF ARGS == ""
	PRINTFORML 给%SAVESTR:TARGET%刻下了封印所有力量的烙印。
	PRINTW 继续处刑
ENDIF
X = NO:TARGET + 199
FLAG:X = 1

;前回の助手・調教対象だった場合はフラグを空に
SIF FLAG:1 == TARGET
	FLAG:1 = -1
SIF FLAG:2 == TARGET
	FLAG:2 = -1

;前回の助手・調教対象より前だった場合はフラグを減算
SIF FLAG:1 > TARGET
	FLAG:1 -= 1
SIF FLAG:2 > TARGET
	FLAG:2 -= 1

TARGET = FLAG:1
ASSI = FLAG:2

CALL PARTY_CHAR_DEL, TARGET

IF ARGS == ""
	IF TALENT:TARGET:220 != 1 && EX_TALENT:TARGET:1 != 1 
		EX_FLAG:99 += 2
		PRINTFORML 威望值增加
	ELSE
		EX_FLAG:99 -= 10
		PRINTFORML 威望值减少
	ENDIF
ENDIF

DELCHARA TARGET

;DELCHARA A
	
FLAG:80 += 1

LV += 1
LV *= 50

EXP:0:80 += LV
PRINTL  
PRINTL 得到了用勇者力量形成的勋章
PRINTL 勋章经验+1
EXP:0:81 += 1
PRINTFORMW 《封印把勇者的力量吸收了，你获得了{LV}的经验值！》

DRAWLINE

RETURN 0

