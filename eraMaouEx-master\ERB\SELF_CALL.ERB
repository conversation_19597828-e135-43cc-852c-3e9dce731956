﻿;一人称の決定
@RANDOM_SELF_CALL, ARG, MODE = 0
;ARG	キャラクターNo.
;MODE	模式选择：0为随机，1为自定义
#DIM MODE
LOCAL = CFLAG:ARG:450
SIF MODE == 0
	GOTO RANDOM
$INPUT_LOOP
DRAWLINE
PRINTL 请输入想设定的第一人称，若不输入择随机设定
DRAWLINE
INPUTS
LOCALS '= RESULTS
IF LOCALS == ""
	GOTO RANDOM
ELSE
	IF STRLENS(LOCALS) > 0
		CSTR:ARG:60 = %LOCALS%
		CFLAG:ARG:450 = 0
		RETURN 0
	ENDIF
ENDIF
$RANDOM
SIF LOCAL >= 200
	LOCAL = -1

IF LOCAL < 0
	LOCALS = %CSVCSTR(NO:ARG,60)%
	
	IF STRLENS(LOCALS) > 0
		CSTR:ARG:60 = %LOCALS%
		CFLAG:ARG:450 = 0
		RETURN 0
	ENDIF
ENDIF
	
IF LOCAL < 9
	CSTR:ARG:60 = 我
	CFLAG:ARG:450 = 9
	RETURN 9
ENDIF

IF LOCAL < 100
	LOCAL -= 10
	CALL SET_SUIT_SELFCALL, ARG, LOCAL
	IF RESULT >= 0
		 CFLAG:ARG:450 = RESULT + 10
		 RETURN RESULT + 10
	ENDIF
	LOCAL = 99
ENDIF

IF LOCAL < 200
	LOCAL -= 100
	CALL SET_NICK_SELFCALL, ARG, LOCAL
	IF RESULT >= 0
		CFLAG:ARG:450 = RESULT + 100
		RETURN RESULT
	ENDIF
ENDIF

CFLAG:ARG:450 = -1
;RESTART
CALL RANDOM_SELF_CALL, ARG

; ;ユニークキャラは一人称固定
; ;玛奥
; SIF TALENT:ARG:165
	; CFLAG:ARG:450 = 31

; ;金红桃
; SIF TALENT:ARG:167
	; CFLAG:ARG:450 = 11

; ;银黑桃
; SIF TALENT:ARG:168
	; CFLAG:ARG:450 = 42

; ;黑方片
; SIF TALENT:ARG:169
	; CFLAG:ARG:450 = 0

; ;クラブ
; SIF TALENT:ARG:170
	; CFLAG:ARG:450 = 0

; ;莉莉
; SIF TALENT:ARG:171
	; CFLAG:ARG:450 = 0

@SET_SUIT_SELFCALL, ARG, ARG:1 = -1
#DIM L_教育 = 0
#DIM L_姿态 = 0
#DIM L_开放 = 0

LOCAL = ARG:1
CALL CALC_SELFCALL_FACTOR, ARG
L_教育 = RESULT:0
L_姿态 = RESULT:1
L_开放 = RESULT:2

$SELECT_LOOP
LOCAL ++

SELECTCASE LOCAL
CASE 0
	IF L_开放 < -2 && L_教育 > 0
		IF L_开放 <= -5
			CSTR:ARG:60 = \@ RAND:2 ? 吾辈 # 老身 \@
		ELSE
			CSTR:ARG:60 = \@ L_姿态 < -3 ? 奴家 # 妾身 \@
			SIF TALENT:ARG:122
				CSTR:ARG:60 = \@ L_姿态 < -3 ? 在下 # 鄙人 \@
		ENDIF
	ELSE
		GOTO SELECT_LOOP
	ENDIF
CASE 1
	IF L_教育 < -2
		CSTR:ARG:60 = 俺
		SIF L_姿态 >= 5
			CSTR:ARG:60 = 老娘
		SIF L_姿态 >= 5 && TALENT:ARG:122
			CSTR:ARG:60 = 老子
	ELSE
		GOTO SELECT_LOOP
	ENDIF
CASE 2
	IF L_教育 > 2
		IF L_姿态 >= 5 
			CSTR:ARG:60 = 本宫
		ELSEIF L_姿态 > 2
			CSTR:ARG:60 = 本小姐
			SIF TALENT:ARG:122
				CSTR:ARG:60 = 本少爷
		ELSEIF L_姿态 < -2
			CSTR:ARG:60 = 小女子
			SIF TALENT:ARG:122
				CSTR:ARG:60 = 小人
		ELSEIF L_姿态 <= -5
			CSTR:ARG:60 = 在下
		ELSE
			GOTO SELECT_LOOP
		ENDIF
	ELSE
		GOTO SELECT_LOOP
	ENDIF
CASE 3
	IF INRANGE(L_教育,-2,2) && INRANGE(L_姿态, -2,2) && INRANGE(L_开放, -2,2)
		CSTR:ARG:60 = 人家
		SIF TALENT:ARG:122
			CSTR:ARG:60 = 鄙人
	ELSE
		GOTO SELECT_LOOP
	ENDIF
CASEELSE
	LOCAL = -1
	RETURN -1
ENDSELECT
RETURN LOCAL

@SET_NICK_SELFCALL, ARG, ARG:1 = -1
LOCAL = ARG:1

$SELECT_LOOP
LOCALS '= SAVESTR:ARG
LOCAL:1 = STRLENSU(LOCALS)
LOCAL ++

IF STRLENSU(LOCALS) *2 != STRLENS(LOCALS)
	CSTR:ARG:60 '= LOCALS
	LOCAL = -1
	RETURN -1
ENDIF 

IF NID_GET_TYPE(CFLAG:ARG:6) == 0
	;和名
	;REF CHARA_NAME.ERB
	SELECTCASE LOCAL
	CASE 0
		;皐月 -> 皐月
		SIF LOCAL:1 > 2
			GOTO SELECT_LOOP
		CSTR:ARG:60 '= LOCALS
	CASE 1
		;佳奈美 -> 佳奈 || 佳美
		;纪美子 -> 纪美
		SIF LOCAL:1 <= 2
			GOTO SELECT_LOOP
		SIF SUBSTRINGU(LOCALS,LOCAL:1 -1) == "子"
			LOCALS '= SUBSTRINGU(LOCALS,0,LOCAL:1 -1)
		CSTR:ARG:60 '= SUBSTRINGU(LOCALS,0,1) + SUBSTRINGU(LOCALS, RAND:(STRLENSU(LOCALS)-1)+1,1)
	CASE 2
		;樱 -> 小樱
		SIF LOCAL:1 > 1
			LOCALS '= SUBSTRINGU(LOCALS,0,1)
		CSTR:ARG:60 '= "小" + LOCALS
	CASE 3
		;樱 -> 樱子
		;佳奈美 -> 佳子
		SIF LOCAL:1 > 1
			LOCALS '= SUBSTRINGU(LOCALS,0,1)
		CSTR:ARG:60 '= LOCALS + "子"
	CASE 4
		;樱 -> 樱酱
		SIF LOCAL:1 > 1
			LOCALS '= SUBSTRINGU(LOCALS,0,1)
		CSTR:ARG:60 '= LOCALS + "酱"
	CASE 5
		;樱 -> 樱子樱子
		;菊枝 -> 菊枝菊枝
		;佳奈美 -> 佳奈佳奈 || 佳美佳美
		IF LOCAL:1 <= 1
			LOCALS += "子"
		ELSEIF LOCAL:1 > 2
			SIF SUBSTRINGU(LOCALS,LOCAL:1 -1) == "子"
			LOCALS '= SUBSTRINGU(LOCALS,0,LOCAL:1 -1)
			LOCALS '= SUBSTRINGU(LOCALS,0,1) + SUBSTRINGU(LOCALS, RAND:(STRLENSU(LOCALS)-1)+1,1)
		ENDIF
		CSTR:ARG:60 '= LOCALS *2
	CASEELSE
		LOCAL = -1
		RETURN -1
	ENDSELECT

ELSE
	;洋名
	SELECTCASE LOCAL
	CASE 0
		;艾莉 -> 艾莉
		SIF LOCAL:1 > 3
			GOTO SELECT_LOOP
		CSTR:ARG:60 '= LOCALS
	CASE 1
		;索菲亚 -> 索菲 || 索亚
		SIF LOCAL:1 <= 2
			GOTO SELECT_LOOP
		CSTR:ARG:60 '= SUBSTRINGU(LOCALS,0,1) + SUBSTRINGU(LOCALS, RAND:(LOCAL:1-1)+1,1)
	CASE 2
		SIF LOCAL:1 > 1
			LOCALS '= SUBSTRINGU(LOCALS,0,1)
		CSTR:ARG:60 '= "小" + LOCALS
	CASE 3
		;艾提卡 -> 艾儿
		SIF LOCAL:1 < 2
			GOTO SELECT_LOOP
		SIF LOCAL:1 == 2 && SUBSTRINGU(LOCALS,1,1) == "儿"
			GOTO SELECT_LOOP
		SELECTCASE SUBSTRINGU(LOCALS,0,1)
		CASE "爱","艾","安","薇","夏","菲","伊","珍","若","索","佩","洛","露","莎"
		CASEELSE
			GOTO SELECT_LOOP
		ENDSELECT
		CSTR:ARG:60 '= SUBSTRINGU(LOCALS,0,1) + "儿"
	CASE 4
		;艾提卡 -> 艾卡儿
		SIF LOCAL:1 < 2
			GOTO SELECT_LOOP
		SIF SUBSTRINGU(LOCALS,LOCAL:1 -1) == "儿"
			GOTO SELECT_LOOP
		SELECTCASE SUBSTRINGU(LOCALS,0,1)
		CASE "爱","艾","安","薇","夏","菲","伊","珍","若","索","佩","洛","露","莎"
		CASEELSE
			GOTO SELECT_LOOP
		ENDSELECT
		CSTR:ARG:60 '= SUBSTRINGU(LOCALS,0,1) + SUBSTRINGU(LOCALS,LOCAL:1 -1) + "儿"
	CASEELSE
		LOCAL = -1
		RETURN -1
	ENDSELECT
ENDIF

RETURN LOCAL

@CALC_SELFCALL_FACTOR, ARG
#DIM L_教育 = 0
#DIM L_姿态 = 0
#DIM L_开放 = 0
; 　　	负　	正
; 教育	俺咱	小女子
; 姿态	在下	本小姐
; 开放	妾身


; 种族 
; REF LOOK.ERB> GET_LOOK_INFO
SELECTCASE GET_LOOK_INFO(ARG,"种族")
CASE "精灵", "暗精灵"
	L_教育+=2
	L_姿态+=2
	L_开放+=2
CASE "天使", "堕天使"
	L_开放+=2
CASE "吸血鬼"
	L_教育+=2
	L_姿态++
CASE "龙族"
	L_教育+=2
	L_姿态+=1
	L_开放-=2
CASE "魔族"
	GOTO CASE_魔族
CASE "矮人"
	L_教育-=2
	L_姿态+=1
	L_开放--
ENDSELECT


; 种族2（魔族）
IF 0
$CASE_魔族
SELECTCASE GET_LOOK_INFO(ARG,"种族2")
CASE "植物"
	L_姿态--
CASE "妖精","史莱姆"
	L_教育++
CASE "魔兽","魔兽"
	L_教育-=2
ENDSELECT
ENDIF

; 成为勇者前的生活
SELECTCASE GET_LOOK_INFO(ARG,"成为勇者前的生活")
CASE "学生"
	L_教育++
CASE "修女"
	L_教育++
	L_姿态--
CASE "巫女","预言家","占卜师","隐士"
	L_教育++
	L_开放-=2
CASE "小偷","乞丐","贫民"
	L_教育-=2
CASE "商人"
	L_教育++
	L_姿态-=2
CASE "军人"
	L_姿态-=2
CASE "贵族"
	L_姿态+=2
ENDSELECT

SWAP ARG,TARGET

; 高贵
IF TALENT:163
	L_教育+=2
	L_姿态+=2
ENDIF
; 智慧
IF TALENT:172
	L_教育+=2
	L_姿态--
ENDIF
; 懦弱
IF TALENT:162
	L_姿态-=2
ENDIF
; 恶女
IF TALENT:166
	L_教育+=1
	L_姿态+=2
	SIF RAND:4 == 0
		L_姿态++
ENDIF
; 贵公子
IF TALENT:174
	L_教育+=2
	L_姿态++
	SIF RAND:4 == 0
		L_姿态 += 2
	SIF RAND:3 == 0
		L_教育++
ENDIF
; 好奇的
SIF TALENT:23
	L_开放+=5
; 保守的
SIF TALENT:24
	L_开放 = -10

; 嚣张、傲娇
SIF TALENT:16 || TALENT:18
	L_姿态 += 5
; 高姿态
SIF TALENT:15 
	L_姿态 = 10
; 低姿态
SIF TALENT:17 
	L_姿态 = -10

SWAP ARG,TARGET

RETURN L_教育,L_姿态,L_开放


;返回人物的第一人称
@SELF_CALL, ARG:0, ARG:1
;ARG:0	キャラクターNo.
;ARG:1	废弃
#FUNCTIONS

ARG = ARG < 0 ? TARGET # ARG
LOCALS = \@ STRLENS(CSTR:ARG:60) ? %CSTR:ARG:60% # 我 \@

RETURNF LOCALS


;返回人物的第一人称的首字
@SELF_CALL_FIRST, ARG = -1
;ARG:0	キャラクターNo.
#FUNCTIONS

ARG = ARG < 0 ? TARGET # ARG
LOCALS = \@ STRLENS(CSTR:ARG:60) ? %CSTR:ARG:60% # 我 \@

RETURNF SUBSTRINGU(LOCALS,0,1)


@SELF_CALLNAME, ARGS
THROW 函数@SELF_CALLNAME 已被废弃，请调用函数@RANDOM_SELF_CALL
