﻿; -----------------------------------------------------------------------------
; EVENTFIRST.ERB - 游戏启动时首次执行的事件函数
; -----------------------------------------------------------------------------

@EVENTFIRST
; 游戏启动时首次执行的事件。
; 通常用于显示游戏标题、版本信息，并调用初始化函数。

; 清屏
CLEARTEXT

; 显示游戏标题
PRINTL
PRINTL %GAME_TITLE%
PRINTL

; 显示游戏描述
PRINTL %GAME_DESCRIPTION%
PRINTL

; 显示版本信息 (如果需要，可在此处添加版本号)
PRINTL Version: 0.1.0 (Alpha)
PRINTL

; 调用初始化函数
; 这个函数通常用于初始化所有变量、数组、角色数据等。
CALLFORM INIT

; 调用系统标题画面函数
; 这个函数通常用于显示主菜单、新游戏/加载游戏选项等。
CALLFORM SYSTEM_TITLE

; 调用系统调试函数 (可选，用于开发阶段)
; CALLFORM SYSTEM_DEBUG

; 调用第一次进入游戏时的函数 (例如，新游戏开始时的序章或教程)
CALLFORM SYSTEM_FIRST

; 返回0表示正常结束EVENTFIRST，游戏将进入主循环
RETURN 0