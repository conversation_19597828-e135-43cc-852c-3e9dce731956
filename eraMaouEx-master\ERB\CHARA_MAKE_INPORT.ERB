﻿;---------------------------------------------------------
@CHARA_MAKE_INPORT
#DIM CHARA, 1
#DIM LIST, 100
#DIMS NUMS, 10
#LOCALSIZE 4
;---------------------------------------------------------
;他の勇者の登場設定
SIF FLAG:76 <= 0
	RETURN 0

VARSET LIST, -100
LOCAL:1 = 0

FOR LOCAL, 0, 100
	SIF GLOBALS:LOCAL == ""
		CONTINUE
	SPLIT GLOBALS:(LOCAL), "_", RESULTS
	SIF TOINT(RESULTS:2) > FLAG:76
		CONTINUE
	FOR LOCAL:2, 0, CHARANUM
		LOCAL:3 = 0
		IF CFLAG:(LOCAL:2):190 == TOINT(RESULTS)
			LOCAL:3 = 1
			BREAK
		ENDIF
	NEXT
	SIF LOCAL:3
		CONTINUE
	;同一のキャラ番号を持つキャラがいる場合ははじく
	SIF GETCHARA(TOINT(RESULTS:1), 0) >= 0
		CONTINUE
	LIST:(LOCAL:1) = LOCAL
	LOCAL:1++
NEXT

;作成可能な勇者がいない
SIF !LOCAL:1
	RETURN 0

;キャラ確定
SPLIT GLOBALS:(LIST:(RAND:(LOCAL:1))), "_", LOCALS
;空白キャラを作成
ADDVOIDCHARA
CHARA = CHARANUM - 1
;キャラ番号
NO:CHARA = TOINT(LOCALS:1)
;名前の設定
SAVESTR:CHARA = %LOCALS:3%
CALLNAME:CHARA = CSVCALLNAME(NO:CHARA, 0)
NAME:CHARA = CALLNAME(NO:CHARA, 0)
;ABL
SPLIT LOCALS:4, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	ABL:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:5, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	BASE:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:6, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	MAXBASE:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:7, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	CFLAG:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:8, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	EXP:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:9, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	EQUIP:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:10, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	JUEL:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:11, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	TALENT:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:12, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	MARK:CHARA:TOINT(NUMS) = TOINT(NUMS:1)
NEXT
SPLIT LOCALS:13, "/", RESULTS
FOR LOCAL, 0, RESULT-1
	SPLIT RESULTS:LOCAL, ",", NUMS
	CSTR:CHARA:TOINT(NUMS) = %NUMS:1%
NEXT

;侵入階層?侵攻度?侵攻中設定
CFLAG:CHARA:501 = 1
CFLAG:CHARA:502 = 0
CFLAG:CHARA:1 = 2
IF FLAG:77
	;レベル?经验值設定
	CFLAG:CHARA:9 = 1
	EXP:CHARA:80 = 0
	IF FLAG:60 > 0
		REPEAT FLAG:60
			CALL ST_UP, CHARA
		REND
	ENDIF
ENDIF
BASE:CHARA:0 = MAXBASE:CHARA:0
BASE:CHARA:1 = MAXBASE:CHARA:1
;一応

SIF CFLAG:(CHARA):451 == 0
	CALL CHAR_BODY_GENERATE_WAPPED, CHARA

RETURN CHARA


