;---------------------------------------------------------
@CHAR_BUST_REGENERATE_WAPPED, ARG
;---------------------------------------------------------
SIF !GETBIT(FLAG:5,15)
	RETURN 

IF !CFLAG:ARG:451 || !CFLAG:ARG:453
	CALL CHAR_BODY_GENERATE_WAPPED,ARG
ELSE
	SWAP TARGET, ARG
	CALL CHAR_BUST_GENERATE(CFLAG:451, CFLAG:453 *100)
	CFLAG:455 = (RESULT:0)/100
	SWAP TARGET, ARG
ENDIF

;---------------------------------------------------------
@CHAR_HWEI<PERSON>HT_GENERATE, CHAR_AGE 
;---------------------------------------------------------
; 身高与体重的生成
; （将对象预设为TARGET）
#DIM CHAR_AGE
#DIM CHAR_HEIGHT,10
#DIM CHAR_WEIGHT,10
#DIM CONST BASE_HEIGHT = 109000
#DIM CONST BASE_WEIGHT = 17000
SIF TALENT:122 == 0
	CALL STATISTICS_WOMAN(CHAR_AGE)
SIF TALENT:122
	CALL STATISTICS_MAN(CHAR_AGE)

IF TALENT:100
;娇小
{
	CHAR_HEIGHT = RESULT:0 *2 - (RESULT:0 + RESULT:1)/2,
			RESULT:0, 
			(RESULT:0 + RESULT:1)/2
}
{
	CHAR_WEIGHT = RESULT:3 *2 - (RESULT:3 + RESULT:4)/2,
			RESULT:3, 
			(RESULT:3 + RESULT:4)/2
}
ELSEIF TALENT:99
;魁梧
{
	CHAR_HEIGHT = (RESULT:1 + RESULT:2)/2,
			RESULT:2,
			RESULT:2 *2 - (RESULT:1 + RESULT:2)/2
}
{
	CHAR_WEIGHT = (RESULT:4 + RESULT:5)/2,
			RESULT:5,
			RESULT:5 *2 - (RESULT:4 + RESULT:5)/2
}
ELSE
;一般
{
	CHAR_HEIGHT = (RESULT:0 + RESULT:1)/2, 
			RESULT:1, 
			(RESULT:1 + RESULT:2)/2
}
{
	CHAR_WEIGHT = (RESULT:3 + RESULT:4)/2, 
			RESULT:4, 
			(RESULT:4 + RESULT:5)/2
}
ENDIF

CALL NORMAL_RANGE_PICKUP(CHAR_HEIGHT:0,CHAR_HEIGHT:1,CHAR_HEIGHT:2)
CHAR_HEIGHT = RESULT

CALL NORMAL_RANGE_PICKUP(CHAR_WEIGHT:0,CHAR_WEIGHT:1,CHAR_WEIGHT:2,RESULT:1)
CHAR_WEIGHT = RESULT

; 人族	0		144 	155 	165 	176 
; 精灵	0.15	149 	162 	173 	186 
IF TALENT:314 == 1 || TALENT:314 == 7
	CHAR_HEIGHT += (CHAR_HEIGHT - BASE_HEIGHT)*3/20
	CHAR_WEIGHT += (CHAR_WEIGHT - BASE_WEIGHT)*3/20
; 龙族	0.25	153 	167 	179 	193 
ELSEIF TALENT:314 == 5
	CHAR_HEIGHT += (CHAR_HEIGHT - BASE_HEIGHT)/4
	CHAR_WEIGHT += (CHAR_WEIGHT - BASE_WEIGHT)*4/5
; 霍比特-0.15	139 	148 	157 	166 
ELSEIF TALENT:314 == 10
	CHAR_HEIGHT -= (CHAR_HEIGHT - BASE_HEIGHT)*15/20
	CHAR_WEIGHT -= (CHAR_WEIGHT - BASE_WEIGHT)*12/20
; 矮人	-0.15	139 	148 	157 	166 
ELSEIF TALENT:314 == 11
	CHAR_HEIGHT -= (CHAR_HEIGHT - BASE_HEIGHT)*9/20
	CHAR_WEIGHT -= (CHAR_WEIGHT - BASE_WEIGHT)*7/20
; 精英部下
ELSEIF TALENT:220
	SELECTCASE TALENT:319
	; 妖精 50~60
	CASE 6
		CHAR_HEIGHT /= 3
		CHAR_WEIGHT /= 3
	; 巨人
	CASE 7
		CHAR_HEIGHT *= 2
		CHAR_WEIGHT *= 2
	ENDSELECT
ENDIF

;肌肉型
SIF TALENT:248
	CHAR_WEIGHT = CHAR_WEIGHT * 108 /100
SIF TALENT:248 && TALENT:122
	CHAR_WEIGHT = CHAR_WEIGHT * 108 /100
;虚弱
SIF TALENT:256
	CHAR_WEIGHT = CHAR_WEIGHT * 92 /100
SIF TALENT:256 && TALENT:122
	CHAR_WEIGHT = CHAR_WEIGHT * 103 /100
;肥胖
SIF TALENT:115
	CHAR_WEIGHT = CHAR_WEIGHT * 115 /100
RETURN CHAR_HEIGHT,CHAR_WEIGHT


;---------------------------------------------------------
@CHAR_BUST_GENERATE, L_AGE, CHAR_HEIGHT
;---------------------------------------------------------
; 身高与体重的生成
; （将对象预设为TARGET）
; RETURN 上胸围，下胸围，胸围差
#DIM L_AGE
#DIM CHAR_HEIGHT
#DIM BUST_D,10
#DIM BUST_U
#DIM AGE_COUNT
LOCAL:1 = A,B,C

;绝壁
A:4 = 2500
A:3 = 5000
;贫乳
A:2 = 7500
A = 10000
;普乳
B = 12500
C = 15000
D = 17500
;巨乳
E = 20000
; F = 22500
; 爆乳
G = 25000
H = 27500
; I = 30000
J = 32500
; 超乳
K = 35000
;L = 37500
M = 40000

; ===胸围差===
; 绝壁
IF TALENT:116 && L_AGE >= 14

	;14~~绝壁计算[-]~AA（不含AA）
	IF L_AGE >= 16
		;16岁，AAA以上
		BUST_D = A:3 + RAND:25 *100
	ELSE
		;14岁，[-]以上
		BUST_D = A:4 + RAND:50 *100
	ENDIF	
	
; 贫乳
ELSEIF TALENT:109 && L_AGE >= 14

	;14~~贫乳计算AA~B（不含B）
	IF L_AGE >= 16
		;16岁以上，A以上
		BUST_D = A + RAND:25 *100
	ELSE
		;14岁以上，AA以上
		BUST_D = A:2 + RAND:50 *100
	ENDIF	
	
; 未发育
ELSEIF L_AGE <= 11 && TALENT:135

	;10~11一般计算（基本不发育）
	BUST_D = RAND:25 * 100	
	
; 未发育
ELSEIF L_AGE <= 14 && TALENT:135

	;10~11一般计算（基本不发育）
	BUST_D = RAND:50 * 100
	
; 普乳 巨乳 爆乳 超乳
ELSEIF L_AGE >= 16 || TALENT:110 || TALENT:114 || TALENT:119 || (L_AGE < 16 && TALENT:135 == 0 && !TALENT:109 && !TALENT:116)

	AGE_COUNT = 16 - L_AGE
	;17~24岁普乳计算B~E（不含E）
	
	L_AGE = LIMIT(L_AGE,16,24)
	;下边界，16岁，基本成型，B
	BUST_D = B
	;上边界，24岁，不再发育，E（不含）
	BUST_D:2 = E
	;标准中值，B+E/2
	BUST_D:3 = (BUST_D + BUST_D:2)/2
	;中值随年龄偏移，16->24，BC/2->DE/2
	BUST_D:1 = (L_AGE-16)*(D+E-B-C)/2/(24-16) + (B+C)/2

	;重新调整边界值
	IF BUST_D:1 < BUST_D:3
		;位于左侧
		BUST_D:2 = BUST_D:1 *2 - BUST_D
	ELSE
		;位于右侧
		BUST_D = BUST_D:1 *2 - BUST_D:2
	ENDIF

	;模拟正态分布
	CALL NORMAL_RANGE_PICKUP(BUST_D, BUST_D:1, BUST_D:2)

	;上下胸围差
	BUST_D = RESULT
	
	IF TALENT:119 && L_AGE >= 16
		;17~24岁超乳计算K~M（不含M）
		BUST_D += (K - B)
		LOCAL = M - K
		LOCAL = RAND(LOCAL)
		BUST_D += LOCAL
	ELSEIF (TALENT:119 && L_AGE >= 12) || (TALENT:114 && L_AGE >= 16)
		;17~24岁爆乳计算G~J（不含J）
		BUST_D += (G - B)
		LOCAL = J - G
		LOCAL = RAND(LOCAL)
		BUST_D += LOCAL
	ELSEIF (TALENT:114 && L_AGE >= 12) || (TALENT:110 && L_AGE >= 12)
		;17~24岁巨乳计算E~H（不含H）
		BUST_D += E - B
		LOCAL = H - E
		LOCAL = RAND(LOCAL)
		BUST_D += LOCAL
	ENDIF
;超乳の場合ランダムでさらに巨きく
	IF TALENT:119 && (RAND:2 == 0 || TALENT:130)
		BUST_D += 18000 + RAND:2000 + RAND:2000 + RAND:3000
	ENDIF
;歳が未熟だが、未熟ではないの場合
	IF AGE_COUNT < 0
		AGE_COUNT = 20 + AGE_COUNT
		BUST_D = BUST_D * AGE_COUNT / 20 
	ENDIF
;男性
ELSEIF TALENT:122
	BUST_D = A:4 + RAND:30 *100
	;虚弱
	SIF TALENT:256
		BUST_D -= RAND:15 *100
	;肌肉型
	SIF TALENT:248
		BUST_D += RAND:65 *200
	;肥胖
	SIF TALENT:115
		BUST_D += RAND:50 *200
; 发育中
ELSE
	;12~16一般计算[-]~C（不含C）（快速发育）
	;（这里只用直线来拟合）
	L_AGE = LIMIT(L_AGE,12,16)
	BUST_D = A:4
	BUST_D:2 = C
	BUST_D:3 = (BUST_D + BUST_D:2)/2
	;中值随年龄偏移，12->16，([-]+AAA)/2->BC/2
	BUST_D:1 = (L_AGE-12)*(B+C-A:4-A:3)/2/(16-12) + (A:3+A:4)/2

	IF BUST_D:1 < BUST_D:3
		BUST_D:2 = BUST_D:1 *2 - BUST_D
	ELSE
		BUST_D = BUST_D:1 *2 - BUST_D:2
	ENDIF
	CALL NORMAL_RANGE_PICKUP(BUST_D, BUST_D:1, BUST_D:2)
	BUST_D = RESULT
	
	; [IF_DEBUG]
	; WAIT
	; WAIT
	; WAIT
	; [ENDIF]
ENDIF

A = LOCAL:1
B =	LOCAL:2
C = LOCAL:3

; ===下胸围===

CALL UNDER_BUST, TARGET, CHAR_HEIGHT / 100
BUST_U = RESULT * 100

RETURN BUST_U + BUST_D, BUST_U, BUST_D



;---------------------------------------------------------
@NORMAL_POINT_PICKUP(ARG)
;---------------------------------------------------------
;从模拟正态分布概率为1,3,9,3,1的5个连续的点中取点
;ARG: 中值
SELECTCASE RAND:17 + 1
	CASE 1
		RETURN ARG-2
	CASE 2 TO 4
		RETURN ARG-1
	CASE 5 TO 13
		RETURN ARG
	CASE 14 TO 16
		RETURN ARG+1
	CASE 17
		RETURN ARG+2
	CASEELSE
		RETURN ARG
ENDSELECT

;---------------------------------------------------------
@NORMAL_RANGE_PICKUP(ARG, ARG:1, ARG:2, ARG:3=-1)
;---------------------------------------------------------
;从模拟正态分布概率为1,3,9,3,1的5个区块中取点
;ARG: 下边界,中值,上边界,刻度
;RETURN: 取值,刻度
IF ARG:3 < 0
	SELECTCASE RAND:34 + 1
	CASE 1 TO 2
		LOCAL = RAND:20
	CASE 3 TO 8
		LOCAL = RAND:20 + 20
	CASE 9 TO 17
		LOCAL = RAND:10 + 40
	CASE 18 TO 26
		LOCAL = RAND:10 + 50
	CASE 27 TO 32
		LOCAL = RAND:20 + 60
	CASE 33 TO 34
		LOCAL = RAND:20 + 80
	CASEELSE
		LOCAL = 50
	ENDSELECT
ELSE
	LOCAL = ARG:3 > 100 ? 100 # ARG:3
ENDIF

IF LOCAL <= 50
	LOCAL:1 = ARG + (ARG:1 - ARG)*LOCAL/50
ELSE
	LOCAL:1 = ARG:1 + (ARG:2 - ARG:1)*(LOCAL-50)/50
ENDIF

RETURN LOCAL:1, LOCAL

;---------------------------------------------------------
@STATISTICS_WOMAN(ARG = 18)
;---------------------------------------------------------
;2005年九省市儿童体格发育调查数据
;潘嘉严(2010) 中国2～18岁女童身高、体重标准差单位曲线图 [Online] Available from: http://www.haodf.com/zhuanjiaguandian/doctorpan_150362.htm (Accessed at 2015/7/8)
; RETURN 身高P3, 身高P50, 身高P97, 体重P3, 体重P50, 体重P97
IF LOCAL <= 0 
	LOCAL:0 = 128200,134200,140200,145000,147900,149500,149800,149900,150000,150100
	LOCAL:15 = 140100,146600,152400,156300,158600,159800,160100,160200,160300,160400
	LOCAL:30 = 152700,159200,154500,167600,169300,170100,170300,170400,170500,170600
	LOCAL:50 = 21800,24500,28000,32000,35500,37800,39000,39900,40000,40100,40200,40200,40200
	LOCAL:65 = 31100,35400,40000,44000,47100,49000,50100,50700,51100,51400,51700,51900,52000
	LOCAL:80 = 46500,54000,60500,64500,66100,67000,67200,67500,68000,68400,68700,68900,69000
ENDIF

SIF ARG < 10
	ARG = 10
SIF ARG > 22
	ARG = 22

ARG -= 10
IF ARG < 9
	RETURN LOCAL:ARG,LOCAL:(ARG+15),LOCAL:(ARG+30),LOCAL:(ARG+50),LOCAL:(ARG+65),LOCAL:(ARG+80)
ELSE
	RETURN LOCAL:9,LOCAL:(9+15),LOCAL:(9+30),LOCAL:(ARG+50),LOCAL:(ARG+65),LOCAL:(ARG+80)
ENDIF

;---------------------------------------------------------
@STATISTICS_MAN(ARG = 18)
;---------------------------------------------------------
;2005年九省市儿童体格发育调查数据
;潘嘉严(2010) 中国2～18男童身高、体重标准差单位曲线图 [Online] Available from: http://www.haodf.com/zhuanjiaguandian/doctorpan_150361.htm 
; RETURN 身高P3, 身高P50, 身高P97, 体重P3, 体重P50, 体重P97
IF LOCAL <= 0 
	LOCAL:0 = 127900,132200,137200,144000,151900,158500,158800,160300,160500,160900
	LOCAL:15 = 140200,145300,151900,159500,165900,169800,171600,172300,172700,173100
	LOCAL:30 = 152000,158900,166900,175100,180200,182800,184000,184500,184800,185000
	LOCAL:50 = 23600,24800,28400,32000,36500,40600,43500,45300,46300,46500,46700,46800,47000
	LOCAL:65 = 33700,37700,42500,48100,53400,57100,59400,60700,61400,61600,61800,61900,62000
	LOCAL:80 = 51400,57600,64700,72600,79100,82500,83900,84500,84700,84900,85100,85200,85200
ENDIF

SIF ARG < 10
	ARG = 10
SIF ARG > 22
	ARG = 22

ARG -= 10
IF ARG < 9
	RETURN LOCAL:ARG,LOCAL:(ARG+15),LOCAL:(ARG+30),LOCAL:(ARG+50),LOCAL:(ARG+65),LOCAL:(ARG+80)
ELSE
	RETURN LOCAL:9,LOCAL:(9+15),LOCAL:(9+30),LOCAL:(ARG+50),LOCAL:(ARG+65),LOCAL:(ARG+80)
ENDIF