﻿
@DUNGEON_INFO2
#DIM DISPLAY_FLAG = 0
#DIM SELECT_FLAG = 0, 0, 0
#DIM COMPARE_BIT
#DIM LCOUNT, 2
#DIM LINE_COUNT, 2
#DIM DISPLAY_LINE
#DIM DIALOGUE, 2


REDRA<PERSON> 0

;コマンド入力の表示
CUSTOMDRAWLINE =

WHILE RESULT != 999
	DISPLAY_LINE = 17

;ブランク表示のため陷阱類の総行数を計算
	LINE_COUNT:0 = 4
	LINE_COUNT:1 = 0
	FOR LCOUNT:0, 60, 89
		SIF ITEM:(LCOUNT:0) > 0
			LINE_COUNT:1 += 1
	NEXT
	IF LINE_COUNT:1 % 3
		LINE_COUNT:1 = LINE_COUNT:1 / 3 + 2
	ELSE
		LINE_COUNT:1 = LINE_COUNT:1 / 3 + 1
	ENDIF
	SIF LINE_COUNT:1 > 4
		LINE_COUNT:0 = LINE_COUNT:1

	LINE_COUNT:1 = 0
	FOR LCOUNT:0, 300, 321
		SIF ITEM:(LCOUNT:0) > 0
			LINE_COUNT:1 += 1
	NEXT
	IF LINE_COUNT:1 % 3
		LINE_COUNT:1 = LINE_COUNT:1 / 3 + 2
	ELSE
		LINE_COUNT:1 = LINE_COUNT:1 / 3 + 1
	ENDIF
	SIF LINE_COUNT:1 > LINE_COUNT:0
		LINE_COUNT:0 = LINE_COUNT:1

	FONTBOLD
	CALL MENU_BUTTON, (DISPLAY_FLAG != 0), @"%UNICODE(0x258c)% 陷 阱　", 900
	CALL MENU_BUTTON, (DISPLAY_FLAG != 1), @"%UNICODE(0x258c)% 设 施　", 901
	CALL MENU_BUTTON, (DISPLAY_FLAG != 2), @"%UNICODE(0x258c)% 戒 指　", 902
	FONTREGULAR
	PRINTL 
	COMPARE_BIT = 1
	FOR LCOUNT:0, 0, 9
		PRINTFORM [{(LCOUNT:0 + 1) * 10 + 100}] 第{LCOUNT:0 + 1}阶层　
;陷阱の表示
		IF DISPLAY_FLAG == 0
			FOR LCOUNT:1, 0, 3
				SIF LCOUNT:1 != 0
					PRINT  / 
				X = LCOUNT:0 + (LCOUNT:1 * 10) + 300
				Y = FLAG:X
				IF SELECT_FLAG:(LCOUNT:1) & COMPARE_BIT
					SETCOLOR 128, 255, 0
				ELSE
					SETCOLORBYNAME RoyalBlue
				ENDIF
				PRINTFORM [{(LCOUNT:0 + 1) * 10 + LCOUNT:1 + 101}]
				IF Y <= 0
					PRINTFORM 陷阱：%"无",18,LEFT%
				ELSEIF ITEM:Y > 0
					PRINTFORM 陷阱：%ITEMNAME:Y,14,LEFT%({ITEM:Y, 2})
				ELSE
					PRINTFORM 陷阱：%"无",18,LEFT%
					FLAG:X = -1
				ENDIF
				RESETCOLOR
			NEXT
;设施の表示
		ELSEIF DISPLAY_FLAG == 1
			X = LCOUNT:0 + 350
			Y = FLAG:X
			LOCAL:0 = 1
			LOCAL:0 = LOCAL:0 << LCOUNT:0
			IF SELECT_FLAG:0 & LOCAL:0
				SETCOLOR 128, 255, 0
			ELSE
				SETCOLORBYNAME RoyalBlue
			ENDIF
			IF Y <= 0
				PRINTFORM 设施：通路
			ELSEIF Y >= 500 && Y <= 507
				PRINTFORM 设施：%ITEMNAME:Y%
			ELSE
				PRINTFORM 设施：通路
				FLAG:X = 0
			ENDIF
			RESETCOLOR
;指輪の表示
		ELSE
			X = LCOUNT:0 + 340
			Y = FLAG:X
			LOCAL:0 = 1
			LOCAL:0 = LOCAL:0 << LCOUNT:0
			IF SELECT_FLAG:0 & LOCAL:0
				SETCOLOR 128, 255, 0
			ELSE
				SETCOLORBYNAME RoyalBlue
			ENDIF
			IF Y <= 0
				PRINTFORM 宝箱：无
			ELSEIF ITEM:Y > 0
				PRINTFORM 宝箱：%ITEMNAME:Y%({ITEM:Y})
			ELSE
				PRINTFORM 宝箱：无
				FLAG:X = -1
			ENDIF
			RESETCOLOR
		ENDIF
		PRINTL 
		COMPARE_BIT *= 2
	NEXT
	IF DISPLAY_FLAG == 0
		PRINTFORML [200] 全部陷阱 [201]陷阱%"　Ａ",21,LEFT%  [202]陷阱%"　Ｂ",21,LEFT%  [203]陷阱　Ｃ　　　　　　　　
	ELSE
		PRINTFORML [200] 全部阶层
	ENDIF
	PRINTL 

	SIF SELECT_FLAG:0 == 0 && SELECT_FLAG:1 == 0 && SELECT_FLAG:2 == 0
		SETCOLOR 128, 128, 128

;陷阱の表示
	IF DISPLAY_FLAG == 0
		Y = 0
		LINE_COUNT:1 = 0
		IF DIALOGUE:1 == 0
			PRINTL [  0] 解除陷阱
			DISPLAY_LINE += 1
			FOR LCOUNT:0, 60, 89
				IF ITEM:(LCOUNT:0) > 0
					IF Y > 2
						Y = 0
						DISPLAY_LINE += 1
						LINE_COUNT:1 += 1
						PRINTL 
					ENDIF
					PRINTFORM [{LCOUNT:0, 3}] %ITEMNAME:(LCOUNT:0), 16, LEFT%（{ITEM:(LCOUNT:0), 2}）
					Y += 1
				ENDIF
			NEXT
		ELSEIF DIALOGUE:1 == -1
			RESETCOLOR
			PRINTL 
			PRINTL 　　* 还没有选择对象！！ *
			PRINTL 
			DISPLAY_LINE += 2
			LINE_COUNT:1 += 2
		ENDIF
		IF LINE_COUNT:0 > LINE_COUNT:1
			FOR LCOUNT:0, LINE_COUNT:1, LINE_COUNT:0
				PRINTFORML 
				DISPLAY_LINE += 1
			NEXT
		ENDIF
;设施
	ELSEIF DISPLAY_FLAG == 1
		DISPLAY_LINE += 5
		LINE_COUNT:1 = 4
		IF DIALOGUE:1 > 0
			IF DIALOGUE:0
				PRINTFORML 　在 {DIALOGUE:1} 个阶层修建 %ITEMNAME:(DIALOGUE:0)%
				PRINTFORML 　　合计花费 {10000 * DIALOGUE:1}p，确认执行吗？
			ELSE
				PRINTFORML 　在 {DIALOGUE:1} 个阶层修建 通路
				PRINTFORML 　　合计花费　    0p ，确认执行吗？
			ENDIF
			PRINTL 
			PRINT 　　　　[0] - 好的　　[1] - 不要
		ELSEIF DIALOGUE:1 == -1
			RESETCOLOR
			PRINTL 
			PRINTL 　　* 还没有选择对象！！ *
			PRINTL 
			DISPLAY_LINE -= 1
		ELSEIF DIALOGUE:1 == -2
			DISPLAY_LINE -= 1
			RESETCOLOR
			PRINTL 
			PRINTL 　　* 钱不够！！ *
			PRINTL 
		ELSE
			Y = 0
			PRINTL [  0] 通路
			FOR LCOUNT:0, 500, 508
				IF Y >= 3
					Y = 0
					PRINTL 
				ENDIF
				PRINTFORM [{LCOUNT:0}] %ITEMNAME:(LCOUNT:0), 8, LEFT%  
				Y += 1
			NEXT
		ENDIF
		PRINTL 
		PRINTL 
		IF LINE_COUNT:0 > LINE_COUNT:1
			FOR LCOUNT:0, LINE_COUNT:1, LINE_COUNT:0
				PRINTFORML 
				DISPLAY_LINE += 1
			NEXT
		ENDIF
;指輪
	ELSE
		Y = 0
		LINE_COUNT:1 = 0
		IF DIALOGUE:1 == 0
			PRINTL [  0] 取下宝物
			DISPLAY_LINE += 1
			FOR LCOUNT:0, 300, 321
				IF ITEM:(LCOUNT:0) > 0
					IF Y > 2
						Y = 0
						DISPLAY_LINE += 1
						LINE_COUNT:1 += 1
						PRINTL 
					ENDIF
					PRINTFORM [{LCOUNT:0, 3}] %ITEMNAME:(LCOUNT:0), 16, LEFT%（{ITEM:(LCOUNT:0), 2}）
					Y += 1
				ENDIF
			NEXT
		ELSEIF DIALOGUE:1 == -1
			RESETCOLOR
			PRINTL 
			PRINTL 　　* 还没有选择对象！！ *
			PRINTL 
			DISPLAY_LINE += 2
			LINE_COUNT:1 += 2
		ENDIF
		IF LINE_COUNT:0 > LINE_COUNT:1
			FOR LCOUNT:0, LINE_COUNT:1, LINE_COUNT:0
				PRINTFORML 
				DISPLAY_LINE += 1
			NEXT
		ENDIF
	ENDIF
	RESETCOLOR

	PRINTFORML [10] 部下状态总览
	PRINTFORM [11]1～3层 [12]4～6层 [13]7～9层 [14]近卫兵
	PRINTPLAIN  显示部下　　　　　　　　　　　　　　　
	PRINTFORML [100]禁止怪物迎击　现在：\@(FLAG:5 & 16) ?ON#OFF\@
	DRAWLINE
	PRINTL [999] - 返回
	;注意書き表示時は入力を無視
	IF DIALOGUE:1 < 0
		WAITANYKEY
		DIALOGUE:1 = 0
		CLEARLINE DISPLAY_LINE
		CONTINUE
	ELSE
	ENDIF
	INPUT
	;陷阱設定時の入力判定
	IF DISPLAY_FLAG == 0
		;陷阱の選択範囲
		IF RESULT > 100 && RESULT < 204
			COMPARE_BIT = 1 << RESULT / 10 - 11
			;全指定
			IF RESULT == 200
				IF SELECT_FLAG:0 == 511 && SELECT_FLAG:1 == 511 && SELECT_FLAG:2 == 511
					SELECT_FLAG:0 = 0
					SELECT_FLAG:1 = 0
					SELECT_FLAG:2 = 0
				ELSE
					SELECT_FLAG:0 = 511
					SELECT_FLAG:1 = 511
					SELECT_FLAG:2 = 511
				ENDIF
			;行指定
			ELSEIF RESULT % 10 == 0
				COMPARE_BIT = 1 << RESULT / 10 - 11
				IF SELECT_FLAG:0 & COMPARE_BIT && SELECT_FLAG:1 & COMPARE_BIT && SELECT_FLAG:2 & COMPARE_BIT
					SELECT_FLAG:0 ^= COMPARE_BIT
					SELECT_FLAG:1 ^= COMPARE_BIT
					SELECT_FLAG:2 ^= COMPARE_BIT
				ELSE
					SELECT_FLAG:0 |= COMPARE_BIT
					SELECT_FLAG:1 |= COMPARE_BIT
					SELECT_FLAG:2 |= COMPARE_BIT
				ENDIF
			;列指定
			ELSEIF RESULT > 200
				IF SELECT_FLAG:(RESULT - 201) == 511
					SELECT_FLAG:(RESULT - 201) = 0
				ELSE
					SELECT_FLAG:(RESULT - 201) = 511
				ENDIF
			;単独指定
			ELSEIF RESULT % 10 < 4
				SELECT_FLAG:(RESULT % 10 - 1) ^= COMPARE_BIT
			ENDIF
		;陷阱の指定
		ELSEIF (RESULT == 0) || (RESULT >= 60 && RESULT < 89 && ITEM:RESULT)
			SIF SELECT_FLAG:0 == 0 && SELECT_FLAG:1 == 0 && SELECT_FLAG:2 == 0
				DIALOGUE:1 = -1
			COMPARE_BIT = 1
			FOR LCOUNT:0, 0, 9
				FOR LCOUNT:1, 0, 3
					IF SELECT_FLAG:(LCOUNT:1) & COMPARE_BIT
						IF RESULT == 0
							FLAG:(LCOUNT:0 + LCOUNT:1 * 10 + 300) = -1
						ELSE
							FLAG:(LCOUNT:0 + LCOUNT:1 * 10 + 300) = RESULT
						ENDIF
					ENDIF
				NEXT
				COMPARE_BIT *= 2
			NEXT
		ENDIF
	;设设施定時の入力判定
	ELSEIF DISPLAY_FLAG == 1
		IF DIALOGUE:1 > 0
			IF RESULT == 0
				IF (MONEY >= 10000 * DIALOGUE:1 && DIALOGUE:0 != 0) || (DIALOGUE:0 == 0)
					COMPARE_BIT = 1
					FOR LCOUNT:0, 350, 359
						IF SELECT_FLAG:0 & COMPARE_BIT
							FLAG:(LCOUNT:0) = DIALOGUE:0
						ENDIF
						COMPARE_BIT *= 2
					NEXT
					IF DIALOGUE:0 != 0
                       MONEY -= 10000 * DIALOGUE:1
                       EX_FLAG:4444 -= 10000 * DIALOGUE:1
                    ENDIF
					SELECT_FLAG:0 = 0
					DIALOGUE:0 = 0
					DIALOGUE:1 = 0
				ELSE
					DIALOGUE:1 = -2
				ENDIF
			ELSEIF RESULT == 1
				DIALOGUE:0 = 0
				DIALOGUE:1 = 0
			ENDIF
			CLEARLINE DISPLAY_LINE
			CONTINUE
		ENDIF
		;设施の選択範囲
		;単独指定
		IF RESULT > 100 && RESULT < 200
			SELECT_FLAG:0 ^= 1 << RESULT / 10 - 11
		;全指定
		ELSEIF RESULT == 200
			IF SELECT_FLAG:0 == 511
				SELECT_FLAG:0 = 0
			ELSE
				SELECT_FLAG:0 = 511
			ENDIF
		;设施の指定
		ELSEIF (RESULT == 0) || (RESULT >= 500 && RESULT < 508)
			IF SELECT_FLAG:0 == 0
				DIALOGUE:1 = -1
			ELSE
				DIALOGUE:0 = RESULT
				DIALOGUE:1 = 0
				COMPARE_BIT = 1
				FOR LCOUNT:0, 0, 9
					IF SELECT_FLAG & COMPARE_BIT
						DIALOGUE:1 += 1
					ENDIF
					COMPARE_BIT *= 2
				NEXT
			ENDIF
		ENDIF
	;宝物設定時の入力判定
	ELSE
		;宝物の選択範囲
		;単独指定
		IF RESULT > 100 && RESULT < 200
			SELECT_FLAG:0 ^= 1 << RESULT / 10 - 11
		;全指定
		ELSEIF RESULT == 200
			IF SELECT_FLAG:0 == 511
				SELECT_FLAG:0 = 0
			ELSE
				SELECT_FLAG:0 = 511
			ENDIF
		;宝物の指定
		ELSEIF (RESULT == 0) || (RESULT >= 300 && RESULT < 340 && ITEM:RESULT)
			IF SELECT_FLAG:0 == 0
				DIALOGUE:1 = -1
			ELSE
				COMPARE_BIT = 1
				FOR LCOUNT:0, 0, 9
					IF SELECT_FLAG & COMPARE_BIT
						FLAG:(LCOUNT:0 + 340) = RESULT
					ENDIF
					COMPARE_BIT *= 2
				NEXT
			ENDIF
		ENDIF
	ENDIF

	;全設定時の入力判定
	IF RESULT >= 900 && RESULT <= 902
		DISPLAY_FLAG = RESULT % 10
		SELECT_FLAG:0 = 0
		SELECT_FLAG:1 = 0
		SELECT_FLAG:2 = 0
	ENDIF

	;コンフィグ「怪物迎撃禁止」
	IF RESULT == 100
		FLAG:5 ^= 16
	ENDIF

	;部下の表示
	IF RESULT >= 10 && RESULT <= 14
		$PRINT
		PRINTL ******************
		PRINTL 地下城内的部下
		PRINTL ******************

		Z = 0
		R = 100
		IF RESULT >= 11 && RESULT <= 20
				R = 30
			IF RESULT == 12
				Z = 30
			ELSEIF RESULT == 13
				Z = 60
			ELSEIF RESULT == 14
				R = 10
				Z = 90
			ENDIF
		ENDIF
		KAI_LIST = RESULT
		REPEAT 100
			SIF Z >= 100 || R <= 0
				BREAK
			Y = Z % 10
			IF Y == 0
				X = Z / 10
				X += 1
				WAIT
				DRAWLINE
				IF X != 10
					PRINTFORML 第{X}阶层
				ELSEIF X == 10
					PRINTFORML 近卫兵
				ENDIF
				CALL ENEMY_EXIST2(X)
			ENDIF
			A = Z + 100
			B = ITEM:A
			IF B > 0
				
				PRINTFORML [{A}] {B}只%MONSTERNAME(A)% 
			ENDIF
			Z += 1
			R -= 1

		REND
		
		DRAWLINE
		PRINTL [999] 返回
		INPUT
		
		IF RESULT < 200 && RESULT >= 100
			CALL MONSTER_SETUP,RESULT
			GOTO PRINT
		ENDIF
		
	CONTINUE
	ENDIF

	SIF RESULT != 999
		CLEARLINE DISPLAY_LINE
WEND

DISPLAY_FLAG = 0
SELECT_FLAG:0 = 0
SELECT_FLAG:1 = 0
SELECT_FLAG:2 = 0
DIALOGUE:0 = 0
DIALOGUE:1 = 0


REDRAW 1



;-----------------------------------
@ENEMY_COMPARE(ARG, ARG:1)
; 比较排序
;-----------------------------------
#FUNCTION

SIF ARG == ARG:1
	RETURNF 0

;比较阶层
SIF CFLAG:ARG:501 != CFLAG:(ARG:1):501
	RETURNF CFLAG:ARG:501 < CFLAG:(ARG:1):501 ? -1 # 1

LOCAL = 0,0,0,0,0
;2=侵攻中　3=迎撃中　2=潜入中
LOCAL:3 = CFLAG:ARG:1 == 3 && CFLAG:ARG:500 == 4 ? 2 # CFLAG:ARG:1
LOCAL:4 = CFLAG:(ARG:1):1 == 3 && CFLAG:(ARG:1):500 == 4 ? 2 # CFLAG:(ARG:1):1

;勇者奴隶的比较
IF LOCAL:3 != LOCAL:4
	;两支队伍
	LOCAL = LOCAL:3 == 2 ? CFLAG:ARG:533 # ARG
	LOCAL:1 = LOCAL:4 == 2 ? CFLAG:(ARG:1):533 # ARG:1
;两个奴隶的比较
ELSEIF LOCAL:3 == 3
	;两支队伍
	LOCAL = ARG
	LOCAL:1 = ARG:1
;两个勇者的比较
ELSE
	;两支队伍
	LOCAL = CFLAG:ARG:533
	LOCAL:1 = CFLAG:(ARG:1):533
ENDIF

;比较两支队伍的攻略度
SIF LOCAL != LOCAL:1
	RETURNF CFLAG:LOCAL:502 < CFLAG:(LOCAL:1):502 ? -1 # 1
	
;队长
SIF LOCAL == ARG
	RETURNF -1
SIF LOCAL == ARG:1
	RETURNF 1

;两个队员
RETURNF CFLAG:LOCAL:531 == ARG ? -1 # 1


;-----------------------------------
@ENEMY_EXIST2(ARG)
;-----------------------------------
#DIM L_CHAR
#DIM L_INDX
#DIM L_LEN
#DIM L_LAST
#DIM MAX_NAME_LEN = 0

VARSET LOCAL
L_LEN = 0

;筛选并排序
FOR L_CHAR, 1, CHARANUM

	SIF ARG == 10
		CONTINUE
	;不在当前阶层
	SIF CFLAG:L_CHAR:501 != ARG
		CONTINUE
	;不在侵攻或迎击
	SIF ( CFLAG:L_CHAR:1 != 2 && CFLAG:L_CHAR:1 != 3 && TALENT:L_CHAR:221 == 0 ) || (((CFLAG:L_CHAR:1 != 0 && ARG == 10) || (CFLAG:L_CHAR:1 != 3 && CFLAG:L_CHAR:1 != 2)) && TALENT:L_CHAR:221 )
		CONTINUE
	
	L_LEN ++
	MAX_NAME_LEN = MAX(STRLENS(SAVESTR:L_CHAR), MAX_NAME_LEN)
	
	FOR L_INDX, 0, L_LEN
		IF LOCAL:L_INDX <= 0
			LOCAL:L_INDX = L_CHAR
			BREAK
		ELSEIF ENEMY_COMPARE(L_CHAR,LOCAL:L_INDX) < 0
			ARRAYSHIFT LOCAL, 1, L_CHAR, L_INDX, L_LEN
			BREAK
		ENDIF
	NEXT
NEXT
L_CHAR = 0
L_LAST = 0
FOR L_INDX, 0, L_LEN
	L_LAST = CFLAG:L_CHAR:533
	L_CHAR = LOCAL:L_INDX
	
	SIF L_CHAR <= 0
		CONTINUE
	
	;同一只队伍中
	IF L_LAST > 0 && CFLAG:L_LAST:533 == CFLAG:L_CHAR:533
	;新的队伍
	ELSE
		;上一行结尾换行
		PRINTL
		; IF L_INDX > 0 && CFLAG:L_LAST:507 == 1
			; SETCOLOR 200,200,100
			; PRINTL *逃走中*			
		; ENDIF
		
		;新的一行开头
		IF CFLAG:L_CHAR:507 == 1
			SETCOLOR 200,200,100
			;#;PRINTFORM <{CFLAG:L_CHAR:533,2}>　{CFLAG:L_CHAR:502,2}\%　
			PRINTFORM [逃走中]　
		ELSEIF CFLAG:L_CHAR:1 == 3
			SETCOLOR 100,255,255
			;#;PRINTFORM <{CFLAG:L_CHAR:533,2}>　{CFLAG:L_CHAR:502,2}\%　
			PRINTFORM [迎击中]　
		ELSEIF CFLAG:L_CHAR:1 == 2
			SETCOLOR 255,100,100
			;#;PRINTFORM <{CFLAG:L_CHAR:533,2}>　{CFLAG:L_CHAR:502,2}\%　
			IF CFLAG:L_CHAR:520 > 0
				PRINTFORM [{CFLAG:L_CHAR:520+1}F侵攻]　
			ELSE
				PRINTFORM [侵攻中]　
			ENDIF
		ENDIF
	ENDIF
	;更换颜色
	IF CFLAG:L_CHAR:1 == 2
		SETCOLOR 255,100,100
	ELSEIF CFLAG:L_CHAR:1 == 3
		SETCOLOR 100,255,255
	ENDIF
	;#;PRINTFORM [{L_CHAR,2}]
	PRINTFORM %SAVESTR:L_CHAR,MAX_NAME_LEN,LEFT%　
NEXT
;最后一支队伍的结尾换行
PRINTL
RESETCOLOR
IF X == 10
	FOR COUNT, 0, CHARANUM
		IF !CFLAG:COUNT:1 && EX_TALENT:COUNT:1
			PRINTFORM [护卫中]　
			PRINTFORM [{COUNT,2}]
			PRINTFORM %SAVESTR:COUNT,MAX_NAME_LEN,LEFT%
			FOR LOCAL, 200, 212
				SIF TALENT:COUNT:LOCAL
					PRINTFORM %TALENTNAME:LOCAL%
			NEXT
			PRINTL
		ENDIF
	NEXT
ENDIF
