﻿;FileName_SYSTEM_DEBUG_Universal.ERB -------------------------------- Ver.191025
;启动调试模式: @DEBUG_MENU_U
;使用例子:
;ELSEIF RESULT == 9988
;	CALL DEBUG_MENU_U
;原作者：Tsubasa (2015/5/28 4th Version)
;源文件: SYSTEM_DEBUG_Universal_eraTW.ERB
;魔改:star
;==============================================================================================
;--------------------------------------------主菜单--------------------------------------------
;==============================================================================================
@DEBUG_MENU_U
DRAWLINE
PRINTFORML 已经第{DAY}天
PRINTFORML (現有{MONEY}圓)
DRAWLINE
PRINTL 你要準備做什麼？
PRINTL 
PRINTLC [ 0] - 角色編輯
PRINTLC [ 1] - 修改金錢
PRINTLC [ 2] - FLAG操作
PRINTLC [ 3] - 角色全回復
PRINTLC [ 4] - ITEM編輯
PRINTLC [ 5] - TFLAG編輯
PRINTLC [ 6] - 日期編輯（慎用！）
PRINTLC [ 7] - TIME編輯
PRINTLC [99] - 跳至下一天（慎用！）
PRINTL 
DRAWLINE
PRINTLC [100] - 返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 0
	LOCAL = MASTER
	LOCAL:1 = TARGET
	LOCAL:2 = ASSI
	TARGET=0
	CALL EDIT_CHARA
	MASTER = LOCAL
	TARGET = LOCAL:1
	ASSI = LOCAL:2
ELSEIF RESULT == 1
	CALL EDIT_MONEY
ELSEIF RESULT == 2
	CALL EDIT_FLAG
ELSEIF RESULT == 3
	CALL EDIT_HEAL_ALL
ELSEIF RESULT == 4
	CALL EDIT_ITEM
ELSEIF RESULT == 5
	CALL EDIT_TFLAG
ELSEIF RESULT == 6
	CALL EDIT_DAY
ELSEIF RESULT == 7
	CALL EDIT_TIME
ELSEIF RESULT == 99
	CALL NEXTDAY
ELSEIF RESULT == 100
	RETURN 0
ELSE
	GOTO INPUT_LOOP
ENDIF
RESTART
;====================================================================================================
;--------------------------------------------角色相关菜单--------------------------------------------
;====================================================================================================

;--------------------------------------------------
;キャラ書き換え
;--------------------------------------------------
@EDIT_CHARA
;判定能否显示前后的角色
LOCAL:1 = -1
LOCAL:2 = -1
FOR LOCAL, 0, CHARANUM
	SIF LOCAL == TARGET
		CONTINUE
	IF LOCAL < TARGET
		LOCAL:1 = LOCAL
	ELSE
		LOCAL:2 = LOCAL
		BREAK
	ENDIF
NEXT

DRAWLINE
PRINTFORML \@(TARGET < 0) ? 請選擇對象 # 現在「%NAME%/%CALLNAME%」選擇中\@
DRAWLINE
PRINTFORMLC [  0] - \@(TARGET >= 0) ? 対象変更 # 対象選擇\@
IF TARGET >= 0
	;PRINTLC [  1] - 対象情報
	PRINTLC [  2] - ABL編輯
	PRINTLC [  3] - EXP編輯
	PRINTLC [  4] - MARK編輯
	PRINTLC [  5] - TALENT編輯
	PRINTLC [  6] - JUEL編輯
	PRINTLC [  7] - CFLAG操作
	PRINTLC [  8] - PALAM編輯
	PRINTLC [  9] - BASE編輯
	PRINTLC [ 10] - MAXBASE編輯
	PRINTLC [ 11] - EX編輯
	PRINTLC [ 12] - SOURCE編輯
ENDIF
PRINTL 
DRAWLINE
PRINTFORMLC \@(LOCAL:1 < 0) ? %" " * 16% # [1011]%CALLNAME:(LOCAL:1)% 編輯\@
PRINTFORMLC [1000]返回
PRINTFORMLC \@(LOCAL:2 < 0) ? %" " * 16% # [1019]%CALLNAME:(LOCAL:2)% 編輯\@
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 1000
	RETURN 0
ELSEIF RESULT == 1011 && LOCAL:1 >= 0
	TARGET = LOCAL:1
ELSEIF RESULT == 1019 && LOCAL:2 >= 0
	TARGET = LOCAL:2
ELSEIF RESULT == 0
	CALL SELECT_TARGET_DEBUG
ELSEIF TARGET < 0
	PRINTW 對象未選擇。
;ELSEIF RESULT == 1
	;CALL SINGLE_CHARA_STATE(TARGET,1)
ELSEIF RESULT == 2
	CALL EDIT_ABL
ELSEIF RESULT == 3
	CALL EDIT_EXP
ELSEIF RESULT == 4
	CALL EDIT_MARK
ELSEIF RESULT == 5
	CALL EDIT_TALENT
ELSEIF RESULT == 6
	CALL EDIT_JUEL
ELSEIF RESULT == 7
	CALL EDIT_CFLAG
ELSEIF RESULT == 8
	CALL EDIT_PALAM
ELSEIF RESULT == 9
	CALL EDIT_BASE
ELSEIF RESULT == 10
	CALL EDIT_MAX_BASE
ELSEIF RESULT == 11
	CALL EDIT_EX
ELSEIF RESULT == 12
	CALL EDIT_SOURCE
ENDIF
RESTART

@SELECT_TARGET_DEBUG
;表示させるキャラを抽出（LOCAL:2に人数）
VARSET LOCAL, 0
LOCAL:1 = CHARANUM / 20

$PRINT_LIST
DRAWLINE
SIF TARGET >= 0
	PRINTFORML 現在選擇角色是:%CALLNAME:TARGET%
PRINTFORML 你要選擇哪個對象？ ＜page.{LOCAL+1}＞
DRAWLINE
REPEAT CHARANUM
	IF COUNT >= LOCAL * 20 && COUNT < (LOCAL+1) * 20
		PRINTFORMLC [{COUNT,2}]%NAME:COUNT%
		PRINTFORMC %CALLNAME:COUNT%[{COUNT,2}]
		PRINTL 
	ENDIF
REND
DRAWLINE
PRINTFORMLC \@(LOCAL <= 0) ? %" " * 16% # [1001]前頁\@
PRINTLC [1000]返回
PRINTFORMLC \@(LOCAL >= LOCAL:1) ? %" " * 16% # [1009]次頁\@
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 1000
	RETURN 0
ELSEIF RESULT == 1001 && LOCAL > 0
	LOCAL -= 1
	GOTO PRINT_LIST
ELSEIF RESULT == 1009 && LOCAL < LOCAL:1
	LOCAL += 1
	GOTO PRINT_LIST
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
ENDIF

TARGET = RESULT
PRINTFORML %CALLNAME:TARGET% 已選擇

;--------------------------------------------------
; PALAM操作
;--------------------------------------------------
@EDIT_PALAM
DRAWLINE
FOR LOCAL, 0, 200
	SIF STRLENS(PALAMNAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%PALAMNAME:LOCAL% ({PALAM:LOCAL}) 
NEXT
PRINTL 
DRAWLINE
PRINTLC [999]返回
PRINTLC [888]PALAM 重置
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 888
	VARSET PALAM, 0
	PRINTW PALAM已經全部重置
	RESTART
ELSEIF RESULT < 0 || RESULT > 199
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	SIF STRLENS(PALAMNAME:LOCAL) == 0
		GOTO INPUT_LOOP
	DRAWLINE
	PRINTFORML %PALAMNAME:LOCAL%修改為：(現在{PALAM:LOCAL})
	PRINTFORML 請輸入数値[0-9999999]（無効数値則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < 0 || RESULT > 9999999
		RESTART
	PALAM:LOCAL = RESULT
ENDIF
RESTART

;--------------------------------------------------
; BASE操作
;--------------------------------------------------
@EDIT_BASE
DRAWLINE
FOR LOCAL, 0, 100
	PRINTFORMLC [{LOCAL, 3}] \@ (STRLENS(BASENAME:LOCAL) == 0) ? BASE:{LOCAL, 3} # %BASENAME:LOCAL%\@ = {BASE:LOCAL}
NEXT
PRINTL 
DRAWLINE
;PRINTLC [199]BASE重置为0
PRINTLC [200]返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT == 1995
	VARSET BASE, 0
	PRINTW BASE已經全部重置
	RESTART
ELSEIF RESULT < 0 || RESULT > 99
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
;	SIF STRLENS(BASENAME:LOCAL) == 0
;		GOTO INPUT_LOOP
	DRAWLINE
	PRINTFORML \@ (STRLENS(BASENAME:LOCAL) == 0) ? BASE:{LOCAL} # %BASENAME:LOCAL%\@ 修改為：(現在{BASE:LOCAL})
;	PRINTFORML %BASENAME:LOCAL%の変更(現在{BASE:LOCAL})
	PRINTFORML 請輸入数値[0-9999999]（無効数値則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < 0 || RESULT > 9999999
		RESTART
	BASE:LOCAL = RESULT
ENDIF
RESTART


;--------------------------------------------------
; ABL操作
;--------------------------------------------------
;ABL変更
@EDIT_ABL
VARSIZE ABL
LOCAL:9=RESULT
PRINTFORMLC (MAX = {LOCAL:9-1})
PRINTL
DRAWLINE
FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(ABLNAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%ABLNAME:LOCAL%LV ({ABL:LOCAL})  
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTLC [201]全ABL修改為99
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT == 201
	FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(ABLNAME:LOCAL) == 0
		CONTINUE
	ABL:LOCAL = 99
	NEXT
	PRINTW ABL 全部修改為99了
	RESTART
SIF RESULT < 0 || RESULT >= LOCAL:9
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML %ABLNAME:LOCAL%LV修改為(現在{ABL:LOCAL})
	PRINTFORML 請輸入数値[-999-999]（無効数値則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < -999|| RESULT > 999
		RESTART
	ABL:LOCAL = RESULT
ENDIF
RESTART

;--------------------------------------------------
;素質編集
;--------------------------------------------------
@EDIT_TALENT
LOCAL = 0 ;当前页码
LOCAL:1 = 9 ;最大页码数
VARSIZE TALENT
LOCAL:9=RESULT
$PRINT_LIST
PRINTFORMLC (MAX = {LOCAL:9-1})
PRINTL
DRAWLINE
PRINTFORML 哪個素質要修改呢？ ＜page.{LOCAL+1}＞
DRAWLINE
REPEAT LOCAL:9
	SIF COUNT < LOCAL * 100 || COUNT >= (LOCAL + 1) * 100 || STRLENS(TALENTNAME:(COUNT)) == 0
		CONTINUE
	IF TALENT:(COUNT) == 0
		PRINTFORMLC [{COUNT, 3}]×【%TALENTNAME:(COUNT)%】
	ELSEIF TALENT:(COUNT) == 1
		PRINTFORMLC [{COUNT, 3}]◎【%TALENTNAME:(COUNT)%】
	ELSEIF TALENT:(COUNT) > 1
		PRINTFORMLC [{COUNT, 3}]○【%TALENTNAME:(COUNT)%】
	ENDIF
REND
PRINTL 
DRAWLINE
PRINTFORMLC \@(LOCAL <= 0) ? %" " * 16% # [1001]前頁\@
PRINTLC [99999]返回
PRINTFORMLC \@(LOCAL >= LOCAL:1) ? %" " * 16% # [1009]次頁\@
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 99999
	RETURN 0
ELSEIF RESULT == 1001 && LOCAL > 0
	LOCAL -= 1
	GOTO PRINT_LIST
ELSEIF RESULT == 1009 && LOCAL < LOCAL:1
	LOCAL += 1
	GOTO PRINT_LIST
ELSEIF RESULT < 0 || RESULT > 999
	GOTO INPUT_LOOP
ENDIF
SIF STRLENS(TALENTNAME:RESULT) == 0
	GOTO INPUT_LOOP
LOCAL:1 = RESULT
DRAWLINE
PRINTFORML %TALENTNAME:(RESULT)%修改為
PRINTFORML 請輸入数値（-999 - 999, 請參照CSV文件進行）
PRINTLC [-99999]返回
INPUT
SIF RESULT < -999 || RESULT > 999
	RESTART
TALENT:(LOCAL:1) = RESULT
GOTO PRINT_LIST


;--------------------------------------------------
; MARK操作
;--------------------------------------------------
;MARK変更
@EDIT_MARK
DRAWLINE
FOR LOCAL, 0, 100
	SIF STRLENS(MARKNAME:LOCAL) == 0
		CONTINUE
	PRINTFORML [{LOCAL, 3}]%MARKNAME:LOCAL% ({MARK:LOCAL})
NEXT
PRINTL [100]返回
$INPUT_LOOP
INPUT
IF RESULT == 100
	RETURN 0
ELSEIF RESULT < 0 || RESULT > 99
	GOTO INPUT_LOOP
ENDIF
LOCAL = RESULT
SIF STRLENS(MARKNAME:LOCAL) == 0
	GOTO INPUT_LOOP
DRAWLINE
PRINTFORML %MARKNAME:LOCAL%LV修改為(現在{MARK:LOCAL})
PRINTFORML 請輸入数値[0-3]（無効数値則取消）
PRINTLC [-99999]返回
INPUT
SIF RESULT < 0 || RESULT >3
	RESTART
MARK:LOCAL = RESULT

RESTART

;--------------------------------------------------
; EXP操作
;--------------------------------------------------
;EXP変更
@EDIT_EXP
VARSIZE EXP
LOCAL:9=RESULT
PRINTFORMLC (MAX = {LOCAL:9-1})
PRINTL
DRAWLINE
FOR LOCAL:1, 0, LOCAL:9
	LOCAL = LOCAL:1
	SIF STRLENS(EXPNAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%EXPNAME:LOCAL% ({EXP:LOCAL})　
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTLC [201]EXP全部為999
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT == 201
	FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(EXPNAME:LOCAL) == 0
		CONTINUE
	EXP:LOCAL = 999
	NEXT
	PRINTW EXP 全部最大化處理
	RESTART
ELSEIF RESULT < 0 || RESULT > LOCAL:9
	GOTO INPUT_LOOP
ELSE
LOCAL = RESULT
SIF STRLENS(EXPNAME:LOCAL) == 0
	GOTO INPUT_LOOP
DRAWLINE
PRINTFORML %EXPNAME:LOCAL%修改為(現在{EXP:LOCAL})
PRINTFORML 請輸入数値（負数則取消）
PRINTLC [-99999]返回
INPUT
SIF RESULT < 0
	RESTART
EXP:LOCAL = RESULT
ENDIF
RESTART

;--------------------------------------------------
; JUEL操作
;--------------------------------------------------
;JUEL変更
@EDIT_JUEL
DRAWLINE
FOR LOCAL, 0, 200
	SIF STRLENS(PALAMNAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%PALAMNAME:LOCAL%の珠({JUEL:LOCAL}) 
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTLC [201]JUEL全部9999999
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT == 201
	FOR LOCAL, 0, 200
	SIF STRLENS(PALAMNAME:LOCAL) == 0
		CONTINUE
	JUEL:LOCAL = 9999999
	NEXT
	PRINTW JUEL最大化處理完畢
	RESTART
ELSEIF RESULT < 0 || RESULT > 199
	GOTO INPUT_LOOP
ELSE
LOCAL = RESULT
SIF STRLENS(PALAMNAME:LOCAL) == 0
	GOTO INPUT_LOOP
DRAWLINE
PRINTFORML %PALAMNAME:LOCAL%の珠修改為：(現在{JUEL:LOCAL})
PRINTFORML 請輸入数値（負数則取消）
PRINTLC [-99999]返回
INPUT
SIF RESULT < 0
	RESTART
JUEL:LOCAL = RESULT
ENDIF
RESTART


;--------------------------------------------------
; CFLAG操作
;--------------------------------------------------
@EDIT_CFLAG
LOCAL = 0
VARSIZE CFLAG
LOCAL:9=RESULT
$PRINT_LIST
DRAWLINE
PRINTFORML 現在目前的CFLAG (MAX = {LOCAL:9-1})
PRINTFORML 哪些需要修改？
DRAWLINE
REPEAT LOCAL:9
	SIF STRLENS(CFLAGNAME:COUNT) == 0
		CONTINUE
	PRINTFORMLC [{COUNT, 3}] \@ (STRLENS(CFLAGNAME:COUNT) == 0) ? CFLAG:{COUNT, 3} # %CFLAGNAME:COUNT%\@ = {CFLAG:COUNT}
REND
PRINTL 
DRAWLINE
PRINTLC [10001]返回
$INPUT_LOOP
INPUT
IF RESULT == 10001
	RETURN 0

ELSEIF RESULT < 0 || RESULT >= LOCAL:9
	GOTO INPUT_LOOP
ENDIF
LOCAL:1 = RESULT
DRAWLINE
PRINTFORML \@ (STRLENS(CFLAGNAME:RESULT) == 0) ? CFLAG:{RESULT} # %CFLAGNAME:RESULT%\@ 修改為(現在{CFLAG:RESULT})
PRINTFORML 請輸入数値
PRINTLC [-99999]返回
INPUT
SIF RESULT == -999
	RESTART
CFLAG:(LOCAL:1) = RESULT
GOTO PRINT_LIST

;--------------------------------------------------
; MAXBASE操作
;--------------------------------------------------
@EDIT_MAX_BASE
DRAWLINE
FOR LOCAL, 0, 100
	PRINTFORMLC [{LOCAL, 3}] \@ (STRLENS(BASENAME:LOCAL) == 0) ? MAXBASE:{LOCAL, 3} # %BASENAME:LOCAL%\@ = {MAXBASE:LOCAL}
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT < 0 || RESULT > 99
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML \@ (STRLENS(BASENAME:LOCAL) == 0) ? MAXBASE:{LOCAL} # %BASENAME:LOCAL%\@ の上限修改(現在{MAXBASE:LOCAL})
	PRINTFORML 請輸入数値[0-9999999]（無効数値則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < 0 || RESULT > 9999999
		RESTART
	MAXBASE:LOCAL = RESULT
ENDIF
RESTART
;--------------------------------------------------
; EX操作
;--------------------------------------------------
;EX変更
@EDIT_EX
VARSIZE EX
LOCAL:9=RESULT
PRINTFORML (MAX = {LOCAL:9-1})
PRINTL 
DRAWLINE
FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(EXNAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%EXNAME:LOCAL%={EX:LOCAL}  
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTLC [201]全EX修改為999
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT == 201
	FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(EXNAME:LOCAL) == 0
		CONTINUE
	EX:LOCAL = 999
	NEXT
	PRINTW EX 全部修改為999了
	RESTART
SIF RESULT < 0 || RESULT >= 99
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML %EXNAME:LOCAL%修改為(現在{EX:LOCAL})
	PRINTFORML 請輸入数値[-9999-9999]（無効数値則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < -9999|| RESULT > 9999
		RESTART
	EX:LOCAL = RESULT
ENDIF
RESTART
;--------------------------------------------------
; SOURCE操作
;--------------------------------------------------
;SOURCE変更
@EDIT_SOURCE
VARSIZE SOURCE
LOCAL:9=RESULT
PRINTFORML (MAX = {LOCAL:9-1})
PRINTL 
DRAWLINE
FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(SOURCENAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%SOURCENAME:LOCAL%={SOURCE:LOCAL}  
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
SIF RESULT < 0 || RESULT >= LOCAL:9
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML %SOURCENAME:LOCAL%修改為(現在{SOURCE:LOCAL})
	PRINTFORML 請輸入数値[0-9999]（無効数値則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < 0|| RESULT > 9999
		RESTART
	SOURCE:LOCAL = RESULT
ENDIF
RESTART
;======================================================================================================
;--------------------------------------------角色无关的菜单--------------------------------------------
;======================================================================================================
;--------------------------------------------------
;所持金変更処理
;--------------------------------------------------
@EDIT_MONEY
PRINTFORML (持有{MONEY:0}圓 or 持有{MONEY:1}圓 or 持有{MONEY:2}圓)
DRAWLINE
FOR LOCAL, 0, 3
	PRINTFORMLC [{LOCAL, 3}]MONEY:{LOCAL, 3}  = {MONEY:LOCAL}
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT < 0 || RESULT > 3
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML MONEY:{LOCAL} 修改為：(現在{MONEY:LOCAL})
	PRINTFORML 請輸入数値[0-99999999]（輸入無效數值則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < 0 || RESULT > 99999999
		RESTART
	MONEY:LOCAL = RESULT
ENDIF
RESTART

;--------------------------------------------------
;全角色全回復
;--------------------------------------------------
@EDIT_HEAL_ALL
REPEAT CHARANUM
	BASE:COUNT:0 = MAXBASE:COUNT:0
	BASE:COUNT:1 = MAXBASE:COUNT:1
REND
PRINTW 全員体力与气力已經回復
;--------------------------------------------------
; ITEM操作
;--------------------------------------------------
;ITEMの有無をCSVから取得
;@DISP_ITEM(ARG)
;#FUNCTION
;RETURNF (STRLENS(ITEMNAME:ARG)) ? 1 # 0

;ITEM変更
@EDIT_ITEM
VARSIZE ITEM
LOCAL:9=RESULT
PRINTFORMLC (MAX = {LOCAL:9-1})
PRINTL
DRAWLINE
FOR LOCAL, 0, LOCAL:9
	SIF STRLENS(ITEMNAME:LOCAL) == 0
		CONTINUE
	PRINTFORMLC [{LOCAL, 3}]%ITEMNAME:LOCAL% ({ITEM:LOCAL})　
NEXT
PRINTL 
DRAWLINE
PRINTLC [9999]返回
PRINTL 
$INPUT_LOOP
INPUT
SIF RESULT == 9999
	RETURN 0
SIF RESULT < 0 || RESULT >= LOCAL:9
	GOTO INPUT_LOOP
LOCAL = RESULT
SIF STRLENS(ITEMNAME:LOCAL) == 0
	GOTO INPUT_LOOP
DRAWLINE
PRINTFORML %ITEMNAME:LOCAL%修改為(現在{ITEM:LOCAL})
PRINTFORML 請輸入数値（負数則取消）
PRINTLC [-99999]返回
INPUT
SIF RESULT < 0
	RESTART
ITEM:LOCAL = RESULT
RESTART
;--------------------------------------------------
;FLAG編集
;--------------------------------------------------
;説明付与
;@DEFINE_FLAG, ARG
;IF STRLENS(FLAGNAME:ARG) > 0
;	PRINTS FLAGNAME:ARG
;ELSE
;	PRINT ――
;ENDIF

@EDIT_FLAG
VARSET LOCAL, 0
VARSIZE FLAG
LOCAL:9=RESULT
LOCAL:1 = 199
LOCAL:3 = 0
$PRINT_LIST

IF LOCAL:3
	PRINTFORMLC (MAX = {LOCAL:9-1})
	PRINTL
	DRAWLINE
	PRINTFORML 現在目前的FLAG
	PRINTFORML 哪些需要修改？
	DRAWLINE
	REPEAT LOCAL:9
		SIF STRLENS(FLAGNAME:COUNT) == 0
			CONTINUE
		PRINTFORMLC [{COUNT, 4}] \@ (STRLENS(FLAGNAME:COUNT) == 0) ? FLAG:{COUNT, 4} # %FLAGNAME:COUNT%\@ = {FLAG:COUNT}
	REND
	PRINTL 
	DRAWLINE
	PRINTLC [10000]返回
	PRINTLC [10020]切换显示模式（显示全部项/显示有效项）
	PRINTL 
ELSE
	PRINTFORMLC (MAX = {LOCAL:9-1})
	PRINTL
	DRAWLINE
	PRINTFORML 現在目前的FLAG
	PRINTFORML 哪些需要修改？ ＜page.{LOCAL+1}＞
	DRAWLINE
	REPEAT LOCAL:9
		SIF COUNT < LOCAL * 100 || COUNT >= (LOCAL + 1) * 100 
			CONTINUE
		PRINTFORMLC [{COUNT,3}] \@ (STRLENS(FLAGNAME:COUNT) == 0) ? FLAG:{COUNT} # %FLAGNAME:COUNT%\@= {FLAG:COUNT}
		;PRINTFORMLC [{COUNT, 4}] FLAG:{COUNT, 4} = {FLAG:COUNT}
	REND
	PRINTL 
	DRAWLINE
	PRINTFORMLC \@(LOCAL <= 0) ? %" " * 17% # [10001]前頁\@
	PRINTLC [10000]返回
	PRINTFORMLC \@(LOCAL >= LOCAL:1) ? %" " * 17% # [10009]次頁\@
	PRINTL 
	PRINTFORMLC \@(LOCAL/10 <= 0) ? %" " * 17% # [10011]10頁以前\@
	PRINTFORMLC %" " * 17%
	PRINTFORMLC \@(LOCAL/10 >= LOCAL:1/10) ? %" " * 17% # [10019]10頁以後\@
	PRINTLC [10020]切换显示模式（显示全部项/显示有效项）
	PRINTL 

ENDIF

$INPUT_LOOP
INPUT
IF RESULT == 10000
	RETURN 0
ELSEIF RESULT == 10001 && LOCAL > 0
	LOCAL -= 1
	GOTO PRINT_LIST
ELSEIF RESULT == 10009 && LOCAL < LOCAL:1
	LOCAL += 1
	GOTO PRINT_LIST
ELSEIF RESULT == 10011 && LOCAL/10 > 0
	LOCAL -= 10
	GOTO PRINT_LIST
ELSEIF RESULT == 10019 && LOCAL/10 < LOCAL:1/10
	LOCAL += 10
	GOTO PRINT_LIST
ELSEIF RESULT == 10020
	LOCAL:3 = !LOCAL:3
	GOTO PRINT_LIST
ELSEIF RESULT < 0 || RESULT >= LOCAL:9
	GOTO INPUT_LOOP

ENDIF
LOCAL:2 = RESULT
DRAWLINE
PRINTFORML FLAG:{LOCAL:2}(\@ (STRLENS(FLAGNAME:(LOCAL:2)) == 0) ? ―― # %FLAGNAME:(LOCAL:2)%\@)修改為(現在{FLAG:(LOCAL:2)})
PRINTFORML 請輸入数値（負数則取消）
PRINTLC [-99999]返回
INPUT
SIF RESULT < 0
	RESTART
FLAG:(LOCAL:2) = RESULT
GOTO PRINT_LIST

;--------------------------------------------------
;TFLAG編集
;--------------------------------------------------
@EDIT_TFLAG
LOCAL = 0
VARSIZE TFLAG
LOCAL:9=RESULT
$PRINT_LIST
PRINTFORMLC (MAX = {LOCAL:9-1})
PRINTL
DRAWLINE
PRINTFORML 現在目前的TFLAG
PRINTFORML 哪些需要修改？
DRAWLINE
REPEAT LOCAL:9
	SIF STRLENS(TFLAGNAME:COUNT) == 0
		CONTINUE
	PRINTFORMLC [{COUNT, 3}] \@ (STRLENS(TFLAGNAME:COUNT) == 0) ? TFLAG:{COUNT, 3} # %TFLAGNAME:COUNT%\@ = {TFLAG:COUNT}
REND
PRINTL 
DRAWLINE
PRINTLC [1000]返回
PRINTL [1010]調教FLAG全部重置(要注意)
$INPUT_LOOP
INPUT
IF RESULT == 1000
	RETURN 0
ELSEIF RESULT == 1010
	VARSET TFLAG, 0
	PRINTW 調教FLAG已經全部重置
	GOTO PRINT_LIST
ELSEIF RESULT < 0 || RESULT >= LOCAL:9
	GOTO INPUT_LOOP
ENDIF
LOCAL:1 = RESULT
DRAWLINE
PRINTFORML \@ (STRLENS(TFLAGNAME:RESULT) == 0) ? TFLAG:{RESULT} # %TFLAGNAME:RESULT%\@ 修改為(現在{TFLAG:RESULT})
PRINTFORML 請輸入数値（負数為取消）
PRINTLC [-99999]返回
INPUT
SIF RESULT < 0
	RESTART
TFLAG:(LOCAL:1) = RESULT
GOTO PRINT_LIST


;--------------------------------------------------
;EDIT_DAY
;--------------------------------------------------
@EDIT_DAY
PRINTFORML 输入你想要设定的天数（負数則取消），现在是第{DAY}天
PRINTFORML 注意！退出到行动菜单后生效
PRINTLC [-99999]返回
$INPUT_LOOP
INPUT
IF RESULT >= 0
	DAY = RESULT
	PRINTFORMW 设定为第{DAY}天
	BEGIN AFTERTRAIN
ELSE
	RETURN 0
ENDIF

;--------------------------------------------------
;EDIT_TIME
;--------------------------------------------------
@EDIT_TIME
PRINTFORML 
DRAWLINE
FOR LOCAL, 0, 3
	PRINTFORMLC [{LOCAL, 3}]TIME:{LOCAL, 3}  = {TIME:LOCAL}
NEXT
PRINTL 
DRAWLINE
PRINTLC [200]返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT < 0 || RESULT > 3
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML TIME:{LOCAL} 修改為：(現在{TIME:LOCAL})
	PRINTFORML 請輸入数値[0-9999]（輸入無效數值則取消）
	PRINTLC [-99999]返回
	INPUT
	SIF RESULT < 0 || RESULT > 99999
		RESTART
	TIME:LOCAL = RESULT
ENDIF
RESTART

;--------------------------------------------------
;NEXTDAY
;--------------------------------------------------
@NEXTDAY
PRINTL 确定？
PRINTLC [0]NO
PRINTC [1]YES
INPUT
IF RESULT
DAY += 1
PRINTFORMW 注意！退出DEBUG菜单后生效
BEGIN AFTERTRAIN
ELSE
RETURN 0
ENDIF
