﻿;顺从のLvUP処理とその可否判定
;eratohoA ver1,204のスクリプトをベースに処理を簡略化
;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;顺从のLvUP
;-------------------------------------------------
@ABLUP10
DRAWLINE
;PRINTL 奴隶的顺从提升了。
;PRINTL 顺从越高，越容易遵从命令。
;CUSTOMDRAWLINE ‥
;顺从はLv5が上限
;[爱慕][盲从]が付いている場合は10まで上昇可能
IF ABL:10 >= 5 && (TALENT:85 == 0 && TALENT:86 == 0)
	PRINTW 需要特殊素质才能继续提升
	RETURN 0
ELSEIF ABL:10 >= 10
	PRINTW 已达最高级
	RETURN 0
ENDIF

;必要な恐怖点数
A = 0
;必要な恭顺点数
B = 0
;必要な欲情点数
C = 0
;必要な屈服点数
D = 0
;必要な异常经验回数
E = 0

;条件別にＯＫかダメかを記録する
;恐怖点数による可否（I=0:可、I&1:点数不足、I&2:経験不足）
I = 0
;恭顺点数による可否（J=0:可、J&1:点数不足、J&2:経験不足）
J = 0
;欲情点数による可否（K=0:可、K&1:点数不足、K&2:経験不足、K=256:そのレベルでは自動不可）
K = 0
;屈服点数による可否（L=0:可、L&1:点数不足、L&2:経験不足、L=256:そのレベルでは自動不可）
L = 0

CALL DECIDE_ABLUP10

;异常经验が必要か
SIF E > 0
	PRINTFORML %EXPNAME:50%{E}以上(现在{EXP:50})且

IF A > 0
	;恐怖点数で上げる
	PRINTFORM [0] - %PALAMNAME:10%点数×{JUEL:10}/{A} ……
	PRINTV GET_ABLUP_STATE(I)
	PRINTL 
ENDIF

;恭顺点数で上げる
PRINTFORM [1] - %PALAMNAME:4%点数×{JUEL:4}/{B} ……
PRINTV GET_ABLUP_STATE(J)
PRINTL 

IF C > 0
	;欲情点数で上げる
	PRINTFORM [2] - %PALAMNAME:5%点数×{JUEL:5}/{C} ……
	PRINTV GET_ABLUP_STATE(K)
	PRINTL 
ENDIF

IF D > 0
	;屈服点数で上げる
	PRINTFORM [3] - %PALAMNAME:6%点数×{JUEL:6}/{D} ……
	PRINTV GET_ABLUP_STATE(L)
	PRINTL 
ENDIF

PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 3) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF J != 0 && RESULT == 1
	PRINTL 未满足条件
	RESTART
ELSEIF K == 256 && RESULT == 2
	RESTART
ELSEIF K != 0 && RESULT == 2
	PRINTL 未满足条件
	RESTART
ELSEIF L == 256 && RESULT == 3
	RESTART
ELSEIF L != 0 && RESULT == 3
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:10 += 1

IF RESULT == 0
	JUEL:10 -= A
ELSEIF RESULT == 1
	JUEL:4 -= B
ELSEIF RESULT == 2
	JUEL:5 -= C
ELSEIF RESULT == 3
	JUEL:6 -= D
ENDIF

PRINTFORML %ABLNAME:10%变为LV{ABL:10}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP10
;-------------------------------------------------
ABL:10 ++

IF I == 0
	JUEL:10 -= A
ELSEIF J == 0
	JUEL:4 -= B
ELSEIF K == 0
	JUEL:5 -= C
ELSEIF L == 0
	JUEL:6 -= D
ENDIF


;-------------------------------------------------
;顺从のLvUP可否判定
;-------------------------------------------------
@DECIDE_ABLUP10
;顺从はLv5が上限、[爱慕][盲从]が付いている場合はLv10まで开放
SIF ABL:10 >= 5 && (TALENT:85 == 0 && TALENT:86 == 0)
	RETURN 0
SIF ABL:10 >= 10
	RETURN 0

;判定変数を空に
I = 0
J = 0
K = 0
L = 0

A = 0
B = 0
C = 0
D = 0
E = 0

IF ABL:10 == 0
	A = 10
	B = 10
	C = 300
	D = 200
ELSEIF ABL:10 == 1
	A = 150
	B = 100
	C = 1000
	D = 1200
ELSEIF ABL:10 == 2
	A = 1000
	B = 800
	C = 2000
	D = 3000
ELSEIF ABL:10 == 3
	A = 3000
	B = 3000
	C = 0
	D = 12000
ELSEIF ABL:10 == 4
	A = 8000
	B = 5000
	C = 0
	D = 0
ELSEIF ABL:10 == 5
	A = 12000
	B = 10000
	C = 0
	D = 0
ELSEIF ABL:10 == 6
	A = 25000
	B = 20000
	C = 0
	D = 0
ELSEIF ABL:10 == 7
	A = 0
	B = 40000
	C = 0
	D = 0
ELSEIF ABL:10 == 8
	A = 0
	B = 80000
	C = 0
	D = 0
ELSEIF ABL:10 == 9
	A = 0
	B = 150000
	C = 0
	D = 0
ENDIF

;ＬＶ４から５に上げるときは异常经验必要
;（素質：[胆怯][坦率][容易陷落][淫乱][爱慕][盲从]なら無視できる）
IF ABL:10 == 4 && (TALENT:10 == 0 && TALENT:13 == 0 && TALENT:76 == 0 && TALENT:73 == 0 && TALENT:85 == 0 && TALENT:86 == 0)
	E = 1
;ＬＶ７から８に上げるときは异常经验が２以上必要
;（素質：[胆怯][坦率][容易陷落][淫乱][爱慕][盲从]なら無視できる）
ELSEIF ABL:10 == 7 && (TALENT:10 == 0 && TALENT:13 == 0 && TALENT:76 == 0 && TALENT:73 == 0 && TALENT:85 == 0 && TALENT:86 == 0)
	E = 2
ENDIF

;胆怯
IF TALENT:10
	TIMES A , 0.50
	TIMES B , 0.90
	TIMES D , 0.90
ENDIF
;反抗心
IF TALENT:11
	TIMES A , 2.00
	TIMES B , 1.50
	TIMES C , 1.20
	TIMES D , 1.50
ENDIF
;刚强
IF TALENT:12
	TIMES A , 3.00
	TIMES B , 1.50
	TIMES C , 1.20
	TIMES D , 1.50
ENDIF
;坦率
IF TALENT:13
	TIMES B , 0.80
	TIMES D , 0.90
ENDIF
;嚣张
IF TALENT:16
	TIMES A , 1.20
	TIMES B , 1.50
	TIMES D , 1.20
ENDIF
;高姿态
IF TALENT:15
	TIMES A , 1.20
	TIMES B , 1.50
	TIMES D , 2.00
;低姿态
ELSEIF TALENT:17
	TIMES B , 0.80
	TIMES D , 0.80
ENDIF

;压抑
IF TALENT:32
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES C , 2.00
	TIMES D , 1.20
;开放
ELSEIF TALENT:33
	TIMES C , 0.50
ENDIF
;抵抗
IF TALENT:34
	TIMES A , 1.50
	TIMES B , 1.50
	TIMES C , 2.00
	TIMES D , 2.00
ENDIF

;淫乱
SIF TALENT:76
	TIMES C , 0.50
;爱慕
SIF TALENT:85
	TIMES B , 0.75
;盲从
SIF TALENT:86
	TIMES B , 0.20

;嫉妒
IF TALENT:84
	TIMES B , 5.00
	TIMES C , 0.80
	TIMES D , 2.00
ENDIF

;最低でも1個は必要
SIF B < 1
	B = 1

;恐怖点数は必要か？
IF A > 0
	;恐怖点数は足りている？
	SIF JUEL:10 < A
	I |= 1
ELSE
	I = 256
ENDIF

;恭顺点数は足りている？
SIF JUEL:4 < B
	J |= 1

;欲情点数は必要か？
IF C > 0
	;欲情点数は足りている？
	SIF JUEL:5 < C
		K |= 1
ELSE
	K = 256
ENDIF

;屈服点数は足りている？
IF D > 0
	;屈服点数で上げる
	SIF JUEL:6 < D
		L |= 1
ELSE
	L = 256
ENDIF

;异常经验は足りている？
IF E > EXP:50
	I |= 2
	J |= 2
	K |= 2
	L |= 2
ENDIF

IF I == 0 || J == 0 || K == 0 || L ==0
	RETURN 1
ELSE
	RETURN 0
ENDIF

;
;
;