﻿@ENDCHECKDRAGONSIS
;普林希斯END判断
SIF TALENT:GETCHARA(32):76 == 1
		EX_FLAG:2809 = -10
;就是锁上了，淫龙公主剧情？不存在的

SIF GETCHARA(32) > 0 && EX_FLAG:2809 == 0 && CFLAG:GETCHARA(32):10 == 1
	EX_FLAG:2809 = 10
	;初次

;通过实验室爱慕淫乱互换将导致剧情线重置
IF TALENT:GETCHARA(32):85 == 1 && EX_FLAG:2809 == -10
	EX_FLAG:2809 = 20
	;恋慕线起始2
	;由于龙公主不可直接获得恋慕
	TALENT:GETCHARA(32):85 = 0
	CFLAG:GETCHARA(32):515 = 0
ELSEIF TALENT:GETCHARA(32):76 == 1 && EX_FLAG:2809 >= 20
	EX_FLAG:2809 = -10
	;无淫乱线
	CFLAG:GETCHARA(32):515 = 0
ENDIF

IF EX_FLAG:2809 >= 10 && EX_FLAG:2809 < 20
	IF CFLAG:GETCHARA(32):515 % 1000 >= 30
	;末三位为剧情事件判断符，在这的作用就是，S01"初次tj"后作为剧情起点开始计时，累计到30后进行S02“记忆封印”剧情
		EX_FLAG:2809 = 20
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 20 && EX_FLAG:2809 < 30
	IF CFLAG:GETCHARA(32):515 % 1000 >= 45
		EX_FLAG:2809 = 30
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 30 && EX_FLAG:2809 < 40
	IF CFLAG:GETCHARA(32):515 % 1000 >= 50
		EX_FLAG:2809 = 40
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 30 && EX_FLAG:2809 < 40
	IF CFLAG:GETCHARA(32):515 % 1000 >= 60
		EX_FLAG:2809 = 40
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 40 && EX_FLAG:2809 < 50
	IF CFLAG:GETCHARA(32):515 % 1000 >= 70
		EX_FLAG:2809 = 50
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 50 && EX_FLAG:2809 < 60
	IF CFLAG:GETCHARA(32):515 % 1000 >= 70
		EX_FLAG:2809 = 60
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 60 && EX_FLAG:2809 < 70
	IF CFLAG:GETCHARA(32):515 % 1000 >= 80
		EX_FLAG:2809 = 70
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 70 && EX_FLAG:2809 < 80
	IF CFLAG:GETCHARA(32):515 % 1000 >= 100
		EX_FLAG:2809 = 80
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF
ELSEIF	EX_FLAG:2809 >= 80 && EX_FLAG:2809 < 90
	IF CFLAG:GETCHARA(32):515 % 1000 >= 110
		EX_FLAG:2809 = 90
	ELSE
		CFLAG:GETCHARA(32):515 += 1
	ENDIF	
ENDIF

@DRAGONSIS_NPC_FLAG(ARGS,ARG = 1)
#FUNCTION
SELECTCASE ARGS
	CASE "蓝塞尔"
		LOCAL = 10000000000 * ARG
	CASE "艾丽珊"
		LOCAL = 1000000000 * ARG
	CASE "首席女巫"
		LOCAL = 100000000 * ARG
	CASE "夺心魔顾问"
		LOCAL = 10000000 * ARG
	CASE "兽人将军"
		LOCAL = 1000000 * ARG
	CASE "魔界大公"
		LOCAL = 100000 * ARG
	CASE "大公长女"
		LOCAL = 10000 * ARG
	CASE "红龙长老"
		LOCAL = 1000 * ARG
ENDSELECT
RETURNF LOCAL

@DE_DRAGONSIS_NPC_FLAG(ARGS,ARG = 1)
#FUNCTION
SELECTCASE ARGS
	CASE "蓝塞尔"
		LOCAL = 10000000000 * ARG
	CASE "艾丽珊"
		LOCAL = 1000000000 * ARG
	CASE "首席女巫"
		LOCAL = 100000000 * ARG
	CASE "夺心魔顾问"
		LOCAL = 10000000 * ARG
	CASE "兽人将军"
		LOCAL = 1000000 * ARG
	CASE "魔界大公"
		LOCAL = 100000 * ARG
	CASE "大公长女"
		LOCAL = 10000 * ARG
	CASE "红龙长老"
		LOCAL = 1000 * ARG
ENDSELECT
RETURNF LOCAL
;上面这两组是FLAG的转换
;用法： 
;CFLAG:GETCHARA(32):515 += DRAGONSIS_NPC_FLAG("蓝塞尔",N) 
;给蓝塞尔的FLAG加上N的数值，空着不填就是1，不过最终值别超过10就是了，
;DE_DRAGONSIS_NPC_FLAG("蓝塞尔")