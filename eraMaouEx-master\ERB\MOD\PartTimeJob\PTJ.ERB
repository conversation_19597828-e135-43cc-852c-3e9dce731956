﻿@PTJ_BUTTON, ARG
IF GETBIT(EX_FLAG:9000,2)
	CALL SHOW_PTJ_BUTTON_LEVEL(18,ARG)
ELSE
	CALL SHOW_BUTTON_BICH_LEVEL(18,ARG)
ENDIF

@SHOW_PTJ_BUTTON_LEVEL(NUM, ARG)
#DIM NUM
#DIM PTJ_COUNT = 0
#DIM PTJ_POS = 0
FOR LOCAL, 400, 450
	IF EX_CFLAG:ARG:LOCAL % 10 > 0
		PTJ_COUNT++
		SIF EX_CFLAG:ARG:LOCAL % 10 > PTJ_POS
			PTJ_POS = LOCAL
	ENDIF
NEXT
IF CFLAG:ARG:120 == 0 && PTJ_COUNT
	PRINTFORM [{NUM}] 打工	
;ELSEIF !PTJ_POS
;	CALL SHOW_BUTTON_BITCH_LEVEL(NUM,ARG)
ELSE
	PRINTFORM [{NUM}] %PTJ_NAME_SWITCH(PTJ_POS)%积极性 - 
	IF EX_CFLAG:ARG:PTJ_POS == 0
		PRINT 没有
	ELSEIF EX_CFLAG:ARG:PTJ_POS == 1
		PRINT 普通
	ELSE
		PRINTFORM {EX_CFLAG:ARG:PTJ_POS}等级
	ENDIF
ENDIF

@PTJ_NAME_SWITCH, PTJ_POS
#FUNCTIONS
#DIM PTJ_POS
SELECTCASE PTJ_POS
	CASE 400
		LOCALS = 風俗
	CASE 401
		LOCALS = 鬥姬
	CASE 402
		LOCALS = 演藝
	CASE 403
		LOCALS = 女僕
	CASE 404
		LOCALS = 教師
	CASE 405
		LOCALS = 馴獣師
	CASE 406
		LOCALS = 獄卒
	CASE 407
		LOCALS = 圖書管理員
	CASE 408
		LOCALS = 宗教
	CASE 409
		LOCALS = 研究
ENDSELECT
RETURNF LOCALS

@SET_PTJ_LEVEL(ARG)
#DIM NO_PAGE = 0
#DIM L_LCOUNT = 9
#DIM PTJ_POS_LV 
#DIM MAX_PAGE = 10
#DIM BUTTTON_NUM
DRAWLINE
PRINTFORML 当前风评：
PRINTL 请设定等级
IF NO_PAGE > 0
	PRINTBUTTON "<<上一页			",1001
ELSE
	PRINTC         			
ENDIF
PRINTBUTTON "清空等级			",1000
;PRINTBUTTON "淫情展開			",1002
IF NO_PAGE < 5
	PRINTBUTTON "下一页>>",1003
ELSE
	PRINTC         			           
ENDIF
PRINTL
;DRAWLINE
PRINTL ██▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀█▀▀▀▀▀▀▀▀▀▀▀██
FOR PTJ_POS_LV, 0, 5
	PRINT ██
	SIF PTJ_POS_LV == 0
		LOCALS = %"  ██████   ",11%
	SIF PTJ_POS_LV == 1
		LOCALS = %"   ████    ",11%
	SIF PTJ_POS_LV == 2
		LOCALS = %"    ██▋    ",11%
	SIF PTJ_POS_LV == 3
		LOCALS = %"     █▍    ",11%
	SIF	PTJ_POS_LV == 4
		LOCALS = %"     █     ",11%
FOR	LOCAL, 400 + (NO_PAGE * 10), 400 + ((NO_PAGE + 1) * 10)
	BUTTTON_NUM = LOCAL*10+5-PTJ_POS_LV
	IF EX_CFLAG:ARG:LOCAL >= (5 - PTJ_POS_LV)
		PRINTBUTTON LOCALS, BUTTTON_NUM
	ELSE
		PRINTBUTTON "           ", BUTTTON_NUM
	ENDIF
	IF LOCAL == (400 + (NO_PAGE * 10) + 9)
		PRINTL ██
	ELSE
		PRINT █
	ENDIF
NEXT
NEXT
;CUSTOMDRAWLINE ━
PRINTL ██▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀▉▀▀▀▀▀█▀▀▀▀▀██
PRINT ██
FOR	LOCAL, 400 + (NO_PAGE * 10), 400 + ((NO_PAGE + 1) * 10)
	LOCALS = %PTJ_NAME_SWITCH(LOCAL)%
	IF LOCALS != ""
		CALL BUTTON_ALIGN_CENTER (LOCALS,11,(LOCAL*10 + 6))
	ELSE
		PRINTFORM %"",11%
	ENDIF
	IF LOCAL == (400 + (NO_PAGE * 10) + 9)
		PRINTL ██
	ELSE
		PRINT █
	ENDIF
NEXT

INPUT
;IF 
;CLEARLINE LINECOUNT-L_LCOUNT




@BUTTON_ALIGN_CENTER, ARGS, MAXBIT, NUM
#DIM MAXBIT
#DIM NUM
#DIM BIT_COUNT,2
#DIMS SPACE
UNICODE 0xe5e5
SPACE = -
STRLENFORM %ARGS%
BIT_COUNT:0 = RESULT
BIT_COUNT:1 = (MAXBIT - BIT_COUNT:0) / 2
IF BIT_COUNT:1 < 1 && (MAXBIT - BIT_COUNT:0) > 0
	LOCALS = %SPACE%
	LOCALS += ARGS
ELSE
	LOCALS =
	IF (MAXBIT - BIT_COUNT:0) % 2 > 0
		REPEAT (BIT_COUNT:1 + 1)
			LOCALS += SPACE
		REND
	ELSE
		REPEAT BIT_COUNT:1
			LOCALS += SPACE
		REND
	ENDIF
	LOCALS += ARGS
	REPEAT BIT_COUNT:1
		LOCALS += SPACE
	REND
ENDIF
PRINTBUTTON LOCALS,NUM