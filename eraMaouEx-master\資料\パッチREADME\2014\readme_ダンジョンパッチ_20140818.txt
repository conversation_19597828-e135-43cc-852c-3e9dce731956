﻿eramaou_侵略コロシアムパッチ_20140818

※(eramaou　ver.0.304推奨)

加筆・改変・再アップロード・まとめ収録はご自由にどうぞ。

■使い方
　全てのERBファイルをERBフォルダに上書きコピーしてください。

■仕様
・勇者がダンジョンでパーティを組みやすくなりました。
・ダンジョン内で勇者が陥落した場合、ちゃんとパーティから抜けるようになります。
・ダンジョン内の潜入奴隷同士で工作活動をしなくなります。
・１人パーティの時は１度だけ「○○はパーティを組みました！」と表示するようにしました。

■追加・修正箇所
　■DUNGEON.ERB
　359行目を以下の文に修正
　SIF SIDEA > 0 && CFLAG:SIDEA:1 == 2
　361行目を以下の文に修正
　SIF SIDEB > 0 && CFLAG:SIDEB:1 == 2
　潜入工作員が引っかかる場合があるので修正。

　399行目を以下の文に修正。
　IF CFLAG:(ARG:0):151 <= -150 && CFLAG:SIDEA:1 == 2 
　↓
　IF CFLAG:(ARG:0):151 <= -150 && CFLAG:(ARG:0):1 == 2
　ケアレスミスっぽいので。

　409行目を以下の文に修正
　SIF SIDEA > 0 && CFLAG:SIDEA:1 == 2
　411行目を以下の文に修正
　SIF SIDEB > 0 && CFLAG:SIDEB:1 == 2
　潜入工作員が引っかかる場合があるので修正。

　477行目に以下の文を追加
　CALL PARTY_DEL, B
　491行目に以下の文を追加
　CALL PARTY_DEL, ARG:0
　パーティ脱退処理が入ってなかったので追加。

　■DUNGEON_BATTLE2.ERB
　315～324行目と335～344行目に以下の文を追記。
	CALL DEATH_CHECK2, ATKER, DEFER
	IF RESULT == 2
		SIF FLAG:5 & 1
			CALL PC_RYOU
		BREAK
	ELSEIF RESULT == 1
		BREAK
	ENDIF
　デスチェックを入れておかないと死んだ後に殴ってくる事があったので。

　422行目の戦闘結果の文を以下に修正。
	IF CFLAG:(ARG:0):1 == 0
		PRINTFORML %SAVESTR:(ARG:0)%は勇者に敗北し、魔王の元へと帰っていった。
		RETURN 1
	ELSEIF CFLAG:(ARG:0):1 == 9
		PRINTFORML %SAVESTR:(ARG:0)%は勇者に敗北し、狂王の物になってしまった。
		RETURN 1
	ENDIF
　奴隷敗北時のみ文章が出るように（勝利時引き分け時は別の関数からメッセージが出ます）
　
　1310、1316行目を以下に修正
	SIF ENEMY > 0 && ENEMY != ARG:0 && CFLAG:ENEMY:500 != 4
　この条件文を入れておかないと潜入奴隷同士で毒を盛ったりする愉快なことになるので。

　■DUNGEON_PARTY.ERB
　120行目と144行目を修正。
　BREAK
　↓
　CONTINUE
　CONTINUEにして戻さないとキャラ全員をチェックするパーティ加入処理がそこで止まってしまうので。
　
　123行目と147行目に追加。
　SIF CFLAG:CHARID:533 == 0 && CHARID == NEW
　	PRINTFORMW %SAVESTR:CHARID%はパーティのリーダーになった！
　勇者がパーティリーダーになった旨は一度だけ表示されます。

　132行目と156行目を以下に修正。
　SIF CHARID != NEW
　	PRINTFORMW %SAVESTR:NEW%は%SAVESTR:CHARID%のパーティに加わった！
　上の処理に合わせて修正。

　208行目@PARTY_DEL関数をまるっと書き換え。
　;リーダーが抜ける場合
　;CFLAG:531 CFLAG:532 CFLAG:533 をクリア
　;仲間Ａと仲間ＢのCFLAG:533をクリア

　;仲間Ａが抜ける場合
　;仲間ＡのCFLAG:533をクリア、リーダーのCFLAG:531をクリア。

　;仲間Ｂが抜ける場合
　;仲間ＢのCFLAG:533をクリア、リーダーのCFLAG:532をクリア。
　以上のことをしないとパーティリーダーが調教可能になってもダンジョンにいる奴隷がパーティリーダーに潜入工作等をします。

