﻿
----------------------------------------------------
税収による収入を基本にして大雑把な収入デフレを行いました
----------------------------------------------------

◆税収を追加
TAX.ERBを追加
EVENT_NEXTDAY.ERB
159～160に呼び出しを追加

◆施設拡張を追加
DUNGEON_ROOM.ERB
商店街を調整

◆奴隷迎撃の仕事に施設拡張を追加
SHOP_2.ERB
DUNGEON_ROOM.ERB

◆行商人の収入を減少
DUNGEON_TRAP.ERB
738行、効果を1/10に
1021行、価格を70に
ITEM.csv
行商人の価格を70に

◆罠の価格を低く
DUNGEON_TRAP.ERB

◆撃破報酬を減額
DUNGEON.ERB
撃破報酬を1/10に

◆一部のアイテムを値引き
ITEM.csv

-----------------------------------
戦闘バランスの調整
-----------------------------------

◆処刑時に得られるレベルを100の経験値に変更
EXECUTION.ERB

◆収入が減ったので勇者の回復量を少なく調整
SYSTEM.ERB
238行目
あと変数を定義

◆スケルトンが複数出現するように
MONSTER_DATA.ERB
DUNGEON_BATTLE.ERB
1～10体出現
そのかわりダンジョン階層によって
スケルトンが強化されるように

◆モンスターのステータスが見えるように
MONSTER_DATA.ERB
DUNGEON_BATTLE.ERB
いままではモンスターのステータスは固定で、魔王レベルによってダメージを強化していましたが
これからは魔王レベルによって直接モンスターのステータスを強化するように

◆法術を追加
MAGIC.ERB
シールドを張って防御力を強化します

◆ダンジョン外に戻った勇者がアイテムを購入するように
DUNGEON.ERB

◆勇者の侵攻を少し改良
DUNGEON.ERB

◆戦利品漁りと死体蹴りのカルマ減少を緩和
DUNGEON_BATTLE.ERB
DUNGEON_RYOUZYOKU.ERB

----------------------------------
細かい便利になりそうな変更・追加
----------------------------------

◆INVASION.ERB
95行あたり、あなたの体力表示
征服地の名前が変わるように

◆ダンジョン内売春の強化
DUNGEON_BICH.ERB
代金を徴収するように
内職を減額
地の文を追加

◆ダンジョン売春の条件に元職業とカルマによるボーナスを追加
DUNGEON_BITCH.ERB
娼婦キャラには売春！
あと売春条件を緩和

◆ダンジョン外に帰還した勇者が冒険資金捻出のために売春するように
DUNGEON_BITCH.ERB
DUNGEON.ERB
実際には経験値を得る
カルマが低いとき限定
よっぽどカルマが低いか、調教済みか、元娼婦とか物乞いとかじゃないとやらないはず

◆武器をメイン画面に表示しないように
_DRAW_MAINMENU.ERB

◆処刑時に武器と指輪を回収するように
EXECUTION.ERB
EQUIP.ERB
@EQUIP_GETに例外処理を追加

