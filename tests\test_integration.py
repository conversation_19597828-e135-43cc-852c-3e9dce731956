"""
ERA AI Agent System Test Suite
"""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
import json
import os

from era_ai_agent.core.types import GameConfig
from era_ai_agent.config.settings import initialize_config, get_config
from era_ai_agent.data.database import ERADatabase
from era_ai_agent.agents.era_generators import get_game_generator

class TestERASystemIntegration:
    """ERA系统集成测试"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录fixture"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
        
    @pytest.fixture
    def test_config(self, temp_dir):
        """测试配置fixture"""
        config_file = Path(temp_dir) / ".env"
        config_content = f"""
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_MODEL=test-model
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
DATABASE_PATH={temp_dir}/test.db
ERA_SYNTAX_DATA_PATH={Path(__file__).parent / "test_data"}
DEFAULT_OUTPUT_PATH={temp_dir}/output
LOG_LEVEL=DEBUG
ENABLE_RAG=false
ENABLE_MCP_TOOLS=true
ENABLE_VALIDATION=true
"""
        config_file.write_text(config_content)
        
        # 初始化配置
        initialize_config(str(config_file))
        
        return str(config_file)
        
    @pytest.fixture
    def test_database(self, temp_dir):
        """测试数据库fixture"""
        db_path = Path(temp_dir) / "test.db"
        db = ERADatabase(str(db_path))
        db.initialize_database()
        return db
        
    @pytest.fixture
    def sample_game_config(self, temp_dir):
        """示例游戏配置"""
        return GameConfig(
            game_title="测试ERA游戏",
            game_description="这是一个用于测试的ERA游戏",
            character_count=3,
            features=["basic", "training", "shop"],
            output_path=str(Path(temp_dir) / "test_game"),
            encoding="utf-8-sig"
        )
        
    def test_config_initialization(self, test_config):
        """测试配置初始化"""
        config = get_config()
        
        assert config.lm_studio_base_url == "http://localhost:1234/v1"
        assert config.lm_studio_model == "test-model"
        assert config.enable_mcp_tools == True
        assert config.enable_validation == True
        
    def test_database_operations(self, test_database, sample_game_config):
        """测试数据库操作"""
        # 创建项目
        project_id = test_database.create_project(
            name="测试项目",
            description="测试项目描述", 
            game_config=sample_game_config
        )
        
        assert project_id > 0
        
        # 更新代理状态
        test_database.update_agent_state(
            project_id, 
            "test_agent", 
            "running",
            {"progress": 50}
        )
        
        # 获取项目状态
        status = test_database.get_project_status(project_id)
        
        assert status is not None
        assert status["project"]["name"] == "测试项目"
        assert len(status["agents"]) == 1
        assert status["agents"][0]["status"] == "running"
        
    def test_erb_script_generation(self, sample_game_config):
        """测试ERB脚本生成"""
        from era_ai_agent.agents.era_generators import ERBScriptAgent
        
        agent = ERBScriptAgent()
        
        # 由于需要模型连接，这里只测试初始化
        assert agent.agent_id == "erb_script_agent"
        assert agent.agent_type == "erb_script"
        
    def test_csv_data_generation(self, sample_game_config):
        """测试CSV数据生成"""
        from era_ai_agent.agents.era_generators import CSVDataAgent
        
        agent = CSVDataAgent()
        
        # 测试手动CSV生成
        character_info = {
            "id": 0,
            "name": "测试角色",
            "callname": "测试",
            "nickname": "小测"
        }
        
        csv_content = agent._generate_character_csv_manual(character_info)
        
        assert "测试角色" in csv_content
        assert "0,测试角色,测试,小测" in csv_content
        
    def test_game_config_validation(self, sample_game_config):
        """测试游戏配置验证"""
        assert sample_game_config.game_title == "测试ERA游戏"
        assert sample_game_config.character_count == 3
        assert "basic" in sample_game_config.features
        assert sample_game_config.encoding == "utf-8-sig"
        
    @pytest.mark.asyncio
    async def test_complete_generation_workflow(self, sample_game_config, test_database):
        """测试完整生成工作流（模拟）"""
        # 由于需要实际的模型连接，这里只测试工作流结构
        from era_ai_agent.core.workflow import ERAAgentWorkflow
        
        workflow = ERAAgentWorkflow(test_database.db_path)
        
        # 测试工作流构建
        graph = workflow.build_workflow()
        assert graph is not None
        
        # 测试状态初始化
        from era_ai_agent.core.workflow import GraphState
        from era_ai_agent.core.types import AgentState, AgentMessage
        
        initial_state = {
            "agent_state": AgentState(),
            "messages": [],
            "current_step": "init",
            "error": None,
            "completed_steps": []
        }
        
        initial_state["agent_state"].game_config = sample_game_config
        
        # 验证初始状态
        assert initial_state["agent_state"].game_config.game_title == "测试ERA游戏"
        assert initial_state["current_step"] == "init"
        assert len(initial_state["completed_steps"]) == 0

class TestMCPTools:
    """MCP工具测试"""
    
    def test_tool_manager_initialization(self):
        """测试工具管理器初始化"""
        from era_ai_agent.tools.mcp_tools import MCPToolManager, ToolParameter, ToolType
        
        manager = MCPToolManager()
        
        # 注册测试工具
        def test_tool(param1: str) -> dict:
            return {"result": f"processed: {param1}"}
            
        manager.register_tool(
            name="test_tool",
            description="测试工具",
            category=ToolType.VALIDATION,
            parameters=[
                ToolParameter("param1", "string", "测试参数")
            ],
            function=test_tool
        )
        
        assert "test_tool" in manager.tools
        
        # 测试工具schema
        schema = manager.get_tool_schema("test_tool")
        assert schema["name"] == "test_tool"
        assert "param1" in schema["parameters"]["properties"]
        
    @pytest.mark.asyncio
    async def test_tool_execution(self):
        """测试工具执行"""
        from era_ai_agent.tools.mcp_tools import MCPToolManager, ToolParameter, ToolType
        
        manager = MCPToolManager()
        
        def echo_tool(message: str) -> str:
            return f"Echo: {message}"
            
        manager.register_tool(
            name="echo_tool",
            description="回声工具",
            category=ToolType.VALIDATION,
            parameters=[
                ToolParameter("message", "string", "要回声的消息")
            ],
            function=echo_tool
        )
        
        # 执行工具
        result = await manager.call_tool("echo_tool", {"message": "Hello"})
        
        assert result["success"] == True
        assert result["result"] == "Echo: Hello"
        
        # 测试参数验证
        result = await manager.call_tool("echo_tool", {})
        assert result["success"] == False
        assert "missing" in result["error"].lower()

class TestRAGSystem:
    """RAG系统测试"""
    
    def test_knowledge_base_structure(self):
        """测试知识库结构"""
        from era_ai_agent.core.rag import ERAKnowledgeBase
        from era_ai_agent.data.database import ERADatabase
        
        # 由于需要实际的嵌入服务，这里只测试结构
        db = ERADatabase(":memory:")
        db.initialize_database()
        
        # 模拟嵌入客户端
        class MockEmbeddingClient:
            def get_embedding_sync(self, text):
                return [0.1] * 768  # 模拟768维向量
                
        embedding_client = MockEmbeddingClient()
        kb = ERAKnowledgeBase(db, embedding_client)
        
        assert kb.db == db
        assert kb.embedding_client == embedding_client
        
    def test_file_categorization(self):
        """测试文件分类"""
        from era_ai_agent.core.rag import ERAKnowledgeBase
        
        # 创建模拟实例
        kb = ERAKnowledgeBase(None, None)
        
        # 测试文件分类
        assert kb._categorize_file("ERA_ERH.txt") == "variables"
        assert kb._categorize_file("ERA_excom.txt") == "commands"
        assert kb._categorize_file("ERA构文讲座.txt") == "syntax"
        assert kb._categorize_file("ERA游戏的实现.txt") == "game_design"
        assert kb._categorize_file("unknown.txt") == "general"
        
    def test_text_chunking(self):
        """测试文本分块"""
        from era_ai_agent.core.rag import ERAKnowledgeBase
        
        kb = ERAKnowledgeBase(None, None)
        
        # 测试短文本
        short_text = "这是一个短文本"
        chunks = kb._chunk_text(short_text, max_length=100)
        assert len(chunks) == 1
        assert chunks[0] == short_text
        
        # 测试长文本
        long_text = "这是第一行\\n" + "这是第二行\\n" * 100
        chunks = kb._chunk_text(long_text, max_length=50)
        assert len(chunks) > 1

class TestFileGeneration:
    """文件生成测试"""
    
    def test_erb_file_structure(self):
        """测试ERB文件结构"""
        # 测试基本ERB结构
        erb_content = '''@SYSTEM_TITLE
#FUNCTION
DRAWLINE
PRINTL %GAME_TITLE%
DRAWLINE
PRINTL [0] 新的游戏
PRINTL [1] 加载游戏
INPUT
SIF RESULT == 0
\tCALL EVENTFIRST
ELSEIF RESULT == 1
\tCALL LOADGAME
ENDIF
'''
        
        # 验证基本结构
        lines = erb_content.strip().split('\\n')
        assert lines[0].startswith('@')  # 函数定义
        assert '#FUNCTION' in lines[1]   # 函数声明
        assert any('DRAWLINE' in line for line in lines)  # ERA指令
        assert any('INPUT' in line for line in lines)     # 输入处理
        
    def test_csv_file_structure(self):
        """测试CSV文件结构"""
        csv_content = ''';角色数据文件
;generated by ERA AI Agent

0,测试角色,测试,小测
1,角色2,角色2,
'''
        
        lines = csv_content.strip().split('\\n')
        assert lines[0].startswith(';')  # 注释行
        assert '测试角色' in lines[3]     # 数据行
        assert lines[3].count(',') >= 3  # 至少4个字段
        
    def test_config_file_structure(self):
        """测试配置文件结构"""
        config_content = '''#固定配置文件
#generated by ERA AI Agent

#基本设置
FONTNAME = MS Gothic
FONTSIZE = 16

#窗口设置
WIDTH = 800
HEIGHT = 600
'''
        
        lines = config_content.strip().split('\\n')
        assert any(line.startswith('#') and '设置' in line for line in lines)
        assert any('FONTNAME' in line for line in lines)
        assert any('WIDTH' in line for line in lines)

def run_integration_tests():
    """运行集成测试"""
    print("Running ERA AI Agent integration tests...")
    
    # 运行pytest
    test_dir = Path(__file__).parent
    exit_code = pytest.main([
        str(test_dir),
        "-v",
        "--tb=short",
        "-x"  # 遇到第一个失败就停止
    ])
    
    return exit_code == 0

if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)