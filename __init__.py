"""
ERA AI Agent System Package
"""

__version__ = "1.0.0"
__author__ = "ERA AI Agent Team"
__description__ = "基于多代理系统的ERA游戏生成AI系统"

from .config.settings import get_config, initialize_config
from .core.workflow import ERAAgentWorkflow
from .core.types import GameConfig, AgentState
from .agents.era_generators import get_game_generator

__all__ = [
    "get_config",
    "initialize_config", 
    "ERAAgentWorkflow",
    "GameConfig",
    "AgentState",
    "get_game_generator"
]