﻿;-------------------------------------------------------------------------------
;    メインメニュー
;-------------------------------------------------------------------------------
;===============================================================================
@DRAW_MAINMENU
#DIM TEMP, 300

;バグ対策
SIF TARGET > CHARANUM - 1
	TARGET = -1

;バグ対策
SIF ASSI > CHARANUM - 1
	ASSI = -1

;バグ対策
SIF ASSI == TARGET
	ASSI = -1

IF TARGET >= 1
	SIF CFLAG:TARGET:1 != 0
		TARGET = -1
ENDIF

IF ASSI >= 1
	SIF CFLAG:ASSI:1 != 0
		ASSI = -1
ENDIF

REDRAW 0

;初めの二重線
;SETFONT "ARIEL BLACK"
DRAWLINEFORM %UNICODE(0x2550)%

;FONTBOLD
;PRINTFORML   %UNICODE(0x258c)%Main Menu
;FONTREGULAR
;SETFONT

;一番上の段
FONTBOLD
ALIGNMENT RIGHT
PRINT 　　
PRINTFORM 第{DAY/365}年　
PRINTFORM {DAY:1}月{DAY:2}日（第
PRINTV DAY+1
PRINT 日）
SETCOLORBYNAME Yellow
SIF DAY:2 == 15
	PRINT 《满月》
RESETCOLOR
IF TIME == 0
	PRINT  上午
ELSE
	PRINT  下午
ENDIF
PRINT 　　
PRINTFORM (所持金：{MONEY} pts.)
PRINTL 　　
ALIGNMENT LEFT
FONTREGULAR
;SETFONT

;仕切り線 & 調教対象/助手 ボタン、押すと選択画面に
DRAWLINEFORM %UNICODE(0x2500)%
SETFONT "ARIEL BLACK"
FONTBOLD
PRINT 　
IF Target >= 1
	CALL MENU_BUTTON, 0, @"%UNICODE(0x258c)%调教目标", 496
ELSE
	CALL MENU_BUTTON, 1, @"%UNICODE(0x258c)%调教目标", 496
ENDIF
;PRINT 　　　　　　　　　　　　　　　　　
;下記PRINTは仮調整用
PRINT 　　　　　　　　　　　　　　　　　　　　　
IF ASSI >= 1
	CALL MENU_BUTTON, 0, @"%UNICODE(0x258c)%助手", 497
ELSE
	CALL MENU_BUTTON, 1, @"%UNICODE(0x258c)%助手", 497
ENDIF
PRINTL 
FONTREGULAR
SETFONT

;調教対象/助手名 ボタン、押すとステータス画面に
SETFONT "ＭＳ ゴシック";等幅フォントに
FONTBOLD

PRINT 　
IF TARGET >= 1 && TARGET < CHARANUM
	PRINT  
	PRINTBUTTON @"%SAVESTR:TARGET%", 498
	LOCALS = %SAVESTR:TARGET%
	STRLENS LOCALS
	TEMP:2 = 18 - RESULT
;	PRINTFORM {TEMP:2}
	PRINTFORM %"",TEMP:2,LEFT%
ENDIF
SIF TARGET < 0
PRINTFORM %"",36%
SIF TARGET > 0
PRINT 　　　　　　　　
;下記PRINTは仮調整用
PRINT 　　　　　　　　　
IF ASSI >= 1 && ASSI < CHARANUM
	PRINTBUTTON @"%SAVESTR:ASSI%", 499
	LOCALS = %SAVESTR:ASSI%
	STRLENS LOCALS
	TEMP:2 = 18 - RESULT
	PRINTFORM %"",TEMP:2,LEFT%
	
ENDIF
PRINTFORML 

IF TARGET >= 1
	PRINTS "　 "
	CALL LIFE_BAR,TARGET, -1
	PRINTS "　 "
	SIF ASSI >= 1 && ASSI < CHARANUM
		CALL LIFE_BAR,ASSI, -1
ENDIF
FONTREGULAR
SETFONT
PRINTL

;仕切り線 & 各情報画面用ボタン 押すとその情報が描画される、切り替えはFLAG:36に代入して
DRAWLINEFORM %UNICODE(0x2500)%
SETFONT "ARIEL BLACK"
FONTBOLD
PRINT 　
IF FLAG:36 == 0
	CALL MENU_BUTTON, 0, @"%UNICODE(0x258c)%物品/技能", 500
ELSE
	CALL MENU_BUTTON, 1, @"%UNICODE(0x258c)%物品/技能", 500
ENDIF
PRINT 　　
IF FLAG:36 == 1
	CALL MENU_BUTTON, 0, @"%UNICODE(0x258c)%持有陷阱", 501
ELSE
	CALL MENU_BUTTON, 1, @"%UNICODE(0x258c)%持有陷阱", 501
ENDIF
PRINT 　　
;将来拡張用あれこれ
;PRINTFORM %UNICODE(0x258c)%Slaves
;PRINT 　　
;PRINTFORM %UNICODE(0x258c)%Monsters
;PRINT 　　
IF FLAG:36 == 4
	CALL MENU_BUTTON, 0, @"%UNICODE(0x258c)%地城概况", 504
ELSE
	CALL MENU_BUTTON, 1, @"%UNICODE(0x258c)%地城概况", 504
ENDIF
PRINT 　　
;PRINTFORM %UNICODE(0x258c)%Enemys
;PRINT 　　
;PRINTFORM %UNICODE(0x258c)%Invasion
IF FLAG:36 == 5
	CALL MENU_BUTTON, 0, @"%UNICODE(0x258c)%地城日常", 505
ELSE
	CALL MENU_BUTTON, 1, @"%UNICODE(0x258c)%地城日常", 505
ENDIF
PRINT 　　
PRINTL 
;RESETCOLOR
FONTREGULAR
SETFONT

;各情報画面表示、処理が入り組むのが予想されるのでそれぞれ専用の関数に飛ばす、関数本体は现在は全てこのERB末に記載
IF FLAG:36 == 0
	CALL DRAW_HAVEITEMS
ELSEIF FLAG:36 == 1
	CALL DRAW_HAVETRAPS
ELSEIF FLAG:36 == 4
	CALL DRAW_DUNGEON_OVERVIEW
ELSEIF FLAG:36 == 5
	CALL DRAW_DUNGEON_DAILY
ELSE
	CALL DRAW_HAVEITEMS
ENDIF

;仕切り線 & コマンドパネルタイトル
DRAWLINEFORM %UNICODE(0x2500)%
SETFONT "ARIEL BLACK"
FONTBOLD
PRINT 　
PRINTFORML %UNICODE(0x258c)%Commands
FONTREGULAR
SETFONT

;奴隷がいるか？調教された奴隷はいるか？
A = 0
B = 0
REPEAT CHARANUM
	SIF CFLAG:COUNT:1 == 0 && COUNT != 0
		A += 1
	SIF CFLAG:COUNT:0 > 0 && COUNT != 0
		B += 1
REND

;コマンドパネル
SETFONT "ＭＳ ゴシック";等幅フォントに
FONTBOLD
SETCOLORBYNAME Gray

PRINT 　
IF A > 0
	PRINTLCD [100] 调教
ELSE
	 PRINTLC [---] 　　　　
ENDIF
PRINT 　
IF CHARANUM >= 1
	PRINTLCD [101] 能力显示
ELSE
	 PRINTLC [---] 　　　　　
ENDIF
PRINT 　
IF FLAG:502 == 0
	PRINTLCD [102] 地下城
	PRINTL
ELSE
	PRINTLCD [102] 场子
	PRINTL
ENDIF
PRINT 　
IF A > 0
	PRINTLCD [103] 处刑
ELSE
	 PRINTLC [---] 　　
ENDIF
PRINT 　
IF A > 0
	PRINTLCD [104] 迎击
ELSE
	 PRINTLC [---] 　　
ENDIF
PRINT 　
; IF A > 0
	PRINTLCD [105] 能力值提升
	PRINTL
; ELSE
	 ; PRINTLC [---] 　　　　　　
	; PRINTL
; ENDIF
PRINT 　
IF B > 0
	PRINTLCD [106] 贩卖奴隶
ELSE
	 PRINTLC [---] 　　　　
ENDIF
PRINT 　
	PRINTLCD [107] 购物
PRINT 　
IF A > 0 && FLAG:37 == 1
	PRINTLCD [108] 换装
	PRINTL
ELSE
	 PRINTLC [---] 　　　　　
	PRINTL
ENDIF
PRINT 　
	PRINTLCD [109] 侵略
PRINT 　
IF TALENT:0:325 == 1
	PRINTLCD [110] 实验室
ELSE
	 PRINTLC [---] 　　　　　
ENDIF
PRINT 　
IF FLAG:83 || FLAG:84
	PRINTLCD [111] 设施·设备
	PRINTL
ELSE
	 PRINTLC [---] 　　　　　
	PRINTL
ENDIF
PRINT 　
	PRINTLCD [120] 召唤
PRINT 　
	PRINTLCD [199] 休息
PRINT 　
	PRINTLCD [200] 保存
	PRINTL
PRINT 　
	PRINTLCD [300] 读取
PRINT 　
	PRINTLCD [777] 设定
PRINT 　
	PRINTLCD [888] 通信
	PRINTL


FONTREGULAR
SETFONT
RESETCOLOR

;下二重仕切り線
;SETFONT "ARIEL BLACK"
DRAWLINEFORM %UNICODE(0x2550)%
;SETFONT

REDRAW 1

RETURN 0

;-------------------------------------------------------------------------------
;    所持アイテム/スキル一覧
;-------------------------------------------------------------------------------
;===============================================================================
@DRAW_HAVEITEMS(ARG:0 = 0, ARG:98, ARG:99)
SETFONT "ＭＳ ゴシック";等幅フォントに
IF ARG:0 == 0
	PRINT 　 
	PRINTFORM 技巧Lv： Lv{ABL:MASTER:12}

	PRINT 　　
	PRINT 所持知识： 
	SIF TALENT:MASTER:55 == 1
		PRINT 【调合知识】　

	SIF TALENT:MASTER:325 == 1
		PRINT 【魔界知识】　

	SIF TALENT:MASTER:327 == 1
		PRINT 【淫魔知识】

	SIF TALENT:MASTER:328 == 1
		PRINT 【魔虫知识】
	PRINTL 
ENDIF

;REPEAT 100
;	SIF ITEM:COUNT > 0
;		PRINTFORM %ITEMNAME:COUNT%({ITEM:COUNT})
;REND
SETFONT "ＭＳ ゴシック";等幅フォントに
ARG:98 = 0
REPEAT 59
	ARG:99 = COUNT
	IF ARG:98 >= 5
		PRINTFORML 　
		ARG:98 = 0
	ENDIF
	IF ITEM:(ARG:99) > 0
		SIF ARG:98 == 0
			PRINT  
		PRINT 　
		PRINTFORM %ITEMNAME:(ARG:99) + @"({ITEM:(ARG:99)})",18,LEFT%
		ARG:98 += 1
	ENDIF
REND
PRINTL  
ARG:98 = 0
	SIF ITEM:91 > 0
		PRINTFORM %ITEMNAME:91%({ITEM:91}) 
REPEAT 40
	ARG:99 = COUNT + 300
	IF ARG:98 >= 5
		PRINTFORML 　
		ARG:98 = 0
	ENDIF
	IF ITEM:(ARG:99) > 0
		SIF ARG:98 == 0
			PRINT  
		PRINT 　
		PRINTFORM %ITEMNAME:(ARG:99) + @"({ITEM:(ARG:99)})",18,LEFT%
		ARG:98 += 1
	ENDIF
REND
PRINTL  
SETFONT
RETURN


;-------------------------------------------------------------------------------
;    所持陷阱一覧
;-------------------------------------------------------------------------------
;===============================================================================
@DRAW_HAVETRAPS(ARG:0, ARG:98, ARG:99)

SETFONT "ＭＳ ゴシック";等幅フォントに
ARG:98 = 0
REPEAT 31
	ARG:99 = COUNT + 59
	IF ARG:98 >= 5
		PRINTFORML 　
		ARG:98 = 0
	ENDIF
	IF ITEM:(ARG:99) > 0
		SIF ARG:98 == 0
			PRINT  
		PRINT 　
		PRINTFORM %ITEMNAME:(ARG:99) + @"({ITEM:(ARG:99)})",18,LEFT%
		ARG:98 += 1
	ENDIF
REND
PRINTL  
SETFONT

RETURN

;-------------------------------------------------------------------------------
;    ダンジョンの状況概要
;-------------------------------------------------------------------------------
;===============================================================================
@DRAW_DUNGEON_OVERVIEW
#DIM DYNAMIC TEMP, 500
#DIM DYNAMIC TEMP1, 100
#DIM L_近卫
#DIM L_奴隶
PRINT 　 
PRINTFORML 迷宫Lv： Lv{CFLAG:0:9} (经验值： {EXP:0:80})　　陷阱Lv：Lv{FLAG:85}　　现在的勇者初期Lv： Lv{FLAG:60 + 1}
REPEAT 99
	TEMP:COUNT = 0
REND
L_奴隶 = 0
L_近卫 = 0

SETFONT "ＭＳ ゴシック";等幅フォントに
IF CHARANUM >= 1
	REPEAT CHARANUM
		;主人公は排除
		SIF COUNT == 0
			CONTINUE
			;RETURN 0
		;ダンジョン内に居ない子は排除
		IF CFLAG:COUNT:1 == 2 || CFLAG:COUNT:1 == 3
			IF CFLAG:COUNT:1 == 2
				IF CFLAG:COUNT:501 <= 1 && CFLAG:COUNT:502 == 0
					TEMP:10 += 1
				ELSE
					TEMP:(CFLAG:COUNT:501) += 1
				ENDIF
				TEMP:97 += 1
			ENDIF
			
			IF CFLAG:COUNT:1 == 3
				D = (CFLAG:COUNT:501) + 10
				TEMP:D += 1
				TEMP:96 += 1
			ENDIF
		ENDIF
		
		L_近卫 += EX_TALENT:COUNT:1 > 0
		L_奴隶 += CFLAG:COUNT:1 != 2 && CFLAG:COUNT:1 != 9
	REND
ENDIF	
	
	
	B = 0
	C = 0
	D = 0
	Z = 0
	R = 100

		REPEAT 100
		SIF Z >= 100 || R <= 0
			BREAK
			
		Y = Z % 10
		IF Y == 0
			X = Z / 10
			X += 1
			D = X + 10
			TEMP1:4 += 1
			FONTBOLD
			IF X != 10 && TEMP1:4 == 1
				PRINT 　 
				LOCALS = 第{X}阶层：　　　　　　　　　　　　　　　　　　　　　 
				PRINTBUTTON LOCALS, X+520
				IF X+1 != 10
					LOCALS = 第{X+1}阶层：
					PRINTBUTTON LOCALS, X+1+520
				ELSEIF X+1 == 10
					LOCALS = 近卫兵：
					PRINTBUTTON LOCALS, X+1+520
				ENDIF
				PRINTFORML 
			ELSEIF X == 10
;				PRINT 　 
;				PRINTFORM 近卫兵：
			ELSE
				TEMP1:4 = 0
			ENDIF
			FONTREGULAR
		ENDIF
		
		A = Z + 100
		B += ITEM:A
		C += ITEM:A
		Z += 1
		R -= 1

		Y = Z % 10
		IF Y == 0
			TEMP1:2 = X + 349
			TEMP1:3 = FLAG:(TEMP1:2)
			
			IF X != 10
				SIF TEMP1:4 == 1
					PRINT 　  
				PRINTFORM 部下{B, 4}只, 
				SIF TEMP:X >= 1
					SETCOLOR 0xFFFF00
				PRINTFORM 勇者：{TEMP:X, 2}人
				RESETCOLOR
				PRINTFORM , 
				SIF TEMP:D >= 1
					SETCOLOR 0x64A0FF
				PRINTFORM 迎击：{TEMP:D, 2}人　
				RESETCOLOR
				PRINTFORM 设施：
				IF TEMP1:3 == 500
					PRINTFORM 商店街　
				ELSEIF TEMP1:3 == 501
					PRINTFORM 沼泽　　
				ELSEIF TEMP1:3 == 502
					PRINTFORM 人类牧场
				ELSEIF TEMP1:3 == 503
					PRINTFORM 冰室　　
				ELSEIF TEMP1:3 == 504
					PRINTFORM 热砂　　
				ELSEIF TEMP1:3 == 505
					PRINTFORM 迷宫　　
				ELSEIF TEMP1:3 == 506
					PRINTFORM 博物馆　
				ELSEIF TEMP1:3 == 507
					PRINTFORM 娼馆街　
				ELSE
					PRINTFORM 无　　　
				ENDIF
				SIF TEMP1:4 == 1
					PRINT 　

			ELSEIF X == 10
				PRINTFORM 近卫兵：
				PRINTFORML {B + L_近卫, 4}体		迷宫外的勇者：{TEMP:10, 2}人　
			ENDIF
			B = 0
			TEMP:98 += 1

			IF TEMP:98 >= 2 && TEMP1:5 != 4
				PRINTFORML 
				TEMP:98 = 0
				TEMP1:5 += 1
			ENDIF
		ENDIF
	REND
PRINT 　
PRINTFORM  部下统计：{C, 5}只, 奴隶：{L_奴隶, 3}人, 勇者：{TEMP:97, 2}人, 迎击：{TEMP:96, 2}人, 肉便器：{FLAG:83, 5}个, 展品：{FLAG:84, 5}个
;CALL DUNGEON_FINAL_BATTLE, 1
;SIF RESULT >= 1
;	PRINTFORM 魔王房间可以迎击
PRINTL  
SETFONT
RETURN 0

;-------------------------------------------------------------------------------
;    地城日常
;-------------------------------------------------------------------------------
;===============================================================================
@DRAW_DUNGEON_DAILY
IF EX_FLAG:99 >= 100
	EX_FLAG:99 = 100
ENDIF
PRINT 　 
PRINTFORM 威望值：{EX_FLAG:99} 
IF EX_FLAG:99 <= 20 && EX_FLAG:99 >= 0
PRINT 【岌岌可危】
ELSEIF EX_FLAG:99 <= 40 && EX_FLAG:99 > 20
PRINT 【动荡不安】
ELSEIF EX_FLAG:99 <= 60 && EX_FLAG:99 > 40
PRINT 【略受质疑】
ELSEIF EX_FLAG:99 <= 80 && EX_FLAG:99 > 60
PRINT 【相安无事】
ELSEIF EX_FLAG:99 <= 100 && EX_FLAG:99 > 80
PRINT 【广受爱戴】
ENDIF
PRINTL
CALL DISPLAY_DUNGEON_DAILY