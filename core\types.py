import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import logging

class AgentType(Enum):
    MAIN = "main"
    ERB_SCRIPT = "erb_script"
    CSV_DATA = "csv_data"
    SYSTEM_FLOW = "system_flow"
    CHARACTER = "character"
    GAME_LOGIC = "game_logic"

@dataclass
class AgentConfig:
    """代理配置"""
    agent_id: str
    agent_type: AgentType
    model_config: Dict[str, Any]
    tools: List[str]
    dependencies: List[str]
    
@dataclass
class GameConfig:
    """游戏配置"""
    game_title: str
    game_description: str
    character_count: int
    features: List[str]
    output_path: str
    encoding: str = "utf-8-sig"  # ERA files use UTF-8 with BOM
    
@dataclass
class AgentMessage:
    """代理间消息"""
    sender: str
    receiver: str
    message_type: str
    content: Dict[str, Any]
    timestamp: float
    
class AgentState:
    """代理状态管理"""
    def __init__(self):
        self.agents: Dict[str, Any] = {}
        self.game_config: Optional[GameConfig] = None
        self.current_step: str = "init"
        self.generated_files: List[str] = []
        self.errors: List[str] = []
        
    def update_agent_status(self, agent_id: str, status: str, data: Dict[str, Any] = None):
        """更新代理状态"""
        if agent_id not in self.agents:
            self.agents[agent_id] = {}
        self.agents[agent_id]['status'] = status
        if data:
            self.agents[agent_id]['data'] = data
            
    def add_generated_file(self, file_path: str):
        """记录生成的文件"""
        self.generated_files.append(file_path)
        
    def add_error(self, error: str):
        """记录错误"""
        self.errors.append(error)
        logging.error(error)