﻿;侍奉精神のLvUP処理とその可否判定
;eratohoA ver1,204のスクリプトをベースに処理を簡略化

;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;侍奉精神のLvUP
;-------------------------------------------------
@ABLUP16
DRAWLINE
;PRINTL 奴隶的侍奉精神提升了。
;PRINTL 侍奉精神越高，越容易在侍奉中获得快感。
;CUSTOMDRAWLINE ‥
;侍奉精神はLv5が上限
;[爱慕][盲从][献身的]が付いている場合はLv10まで开放
IF ABL:16 >= 5 && (TALENT:63 == 0 && TALENT:85 == 0 && TALENT:86 == 0)
	PRINTW 需要特殊素质才能继续提升
	RETURN 0
ELSEIF ABL:16 >= 10
	PRINTW 已达最高级
	RETURN 0
ENDIF

;必要な屈服点数
A = 0
;必要な恭顺点数
B = 0
;必要な习得点数
C = 0
;必要な侍奉快乐经验回数
D = 0
;必要な绝顶经验＆精液经验回数
E = 0
;必要な异常经验回数
F = 0

;条件別にＯＫかダメかを記録する
;屈服点数による可否（I=0:可、I&1:点数不足、I&2:経験不足、I&4:能力不足）
I = 0
;恭顺点数による可否（J=0:可、J&1:点数不足、J&2:経験不足、J&4:能力不足、J=256:そのレベルでは自動不可）
J = 0
;习得点数による可否（K=0:可、K&1:点数不足、K&2:経験不足、K&4:能力不足、K=256:そのレベルでは自動不可）
K = 0

CALL DECIDE_ABLUP16

;顺从が侍奉精神＋１レベルでないといけない
PRINTFORML %ABLNAME:10%LV{ABL:16+1}以上(现在LV{ABL:10})且

;ＬＶ３から４、４から５に上げるときは异常经验必要（素質：[盲从]なら無視できる）
SIF F > 0
	PRINTFORML %EXPNAME:50%{F}以上(现在{EXP:50})且

;屈服点数で上げる
PRINTFORM [0] - %PALAMNAME:6%点数×{JUEL:6}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 
IF E > 0
	PRINTFORML 　　　%EXPNAME:2%　{EXP:2}/{E}
	PRINTFORML 　　　%EXPNAME:20%　{EXP:20}/{E}
ENDIF

IF B > 0
	;恭顺点数で上げる
	PRINTFORM [1] - %PALAMNAME:4%点数×{JUEL:4}/{B} ……
	PRINTV GET_ABLUP_STATE(J)
	PRINTL 
	SIF D > 0
		PRINTFORML 　　　%EXPNAME:21%　{EXP:21}/{D}
ENDIF

IF C > 0
	;习得点数で上げる
	PRINTFORM [2] - %PALAMNAME:7%点数×{JUEL:7}/{C} ……
	PRINTV GET_ABLUP_STATE(K)
	PRINTL 
	PRINTFORML 　　　%EXPNAME:2%　{EXP:2}/1
ENDIF

PRINTL [100] - 停止

$INPUT_LOOP
INPUT
IF (RESULT < 0 || RESULT > 2) && RESULT != 100
	GOTO INPUT_LOOP
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	GOTO INPUT_LOOP
ELSEIF J == 256 && RESULT == 1
	GOTO INPUT_LOOP
ELSEIF J != 0 && RESULT == 1
	PRINTL 未满足条件
	GOTO INPUT_LOOP
ELSEIF K == 256 && RESULT == 2
	GOTO INPUT_LOOP
ELSEIF K != 0 && RESULT == 2
	PRINTL 未满足条件
	GOTO INPUT_LOOP
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:16 += 1

IF RESULT == 0
	JUEL:6 -= A
ELSEIF RESULT == 1
	JUEL:4 -= B
ELSEIF RESULT == 2
	JUEL:7 -= C
ENDIF

PRINTFORML %ABLNAME:16%变为LV{ABL:16}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP16
;-------------------------------------------------
ABL:16 ++

IF I == 0
	JUEL:6 -= A
ELSEIF J == 0
	JUEL:4 -= B
ELSEIF K == 0
	JUEL:7 -= C
ENDIF



;-------------------------------------------------
;侍奉精神のLvUP可否判定
;-------------------------------------------------
@DECIDE_ABLUP16
SIF ABL:16 >= 10
	RETURN 0
SIF ABL:16 >= 5 && (TALENT:63 == 0 && TALENT:85 == 0 && TALENT:86 == 0)
	RETURN 0

;判定変数を空に
I = 0
J = 0
K = 0

A = 0
B = 0
C = 0
D = 0
E = 0
F = 0



IF ABL:16 == 0
	A = 100
	B = 20
	C = 100
	D = 1
	E = 1
ELSEIF ABL:16 == 1
	A = 1200
	B = 400
	C = 2000
	D = 1
	E = 3
ELSEIF ABL:16 == 2
	A = 5000
	B = 2000
	C = 10000
	D = 20
	E = 6
ELSEIF ABL:16 == 3
	A = 10000
	B = 3000
	C = 0
	D = 20
	E = 10
ELSEIF ABL:16 == 4
	A = 30000
	B = 10000
	C = 0
	D = 100
	E = 20
ELSEIF ABL:16 == 5
	A = 50000
	B = 20000
	C = 0
	D = 200
	E = 80
ELSEIF ABL:16 == 6
	A = 70000
	B = 30000
	C = 0
	D = 350
	E = 150
ELSEIF ABL:16 == 7
	A = 100000
	B = 50000
	C = 0
	D = 500
	E = 200
ELSEIF ABL:16 == 8
	A = 150000
	B = 80000
	C = 0
	D = 700
	E = 400
ELSEIF ABL:16 == 9
	A = 200000
	B = 150000
	C = 0
	D = 1000
	E = 800
ENDIF

;戒备森严
IF TALENT:27
	IF ABL:16 == 3
		TIMES A , 1.50
		TIMES B , 1.50
		TIMES D , 1.50
		TIMES E , 1.50
	ELSEIF ABL:16 == 4
		TIMES A , 2.00
		TIMES B , 2.00
		TIMES D , 2.00
		TIMES E , 2.00
	ELSEIF ABL:16 == 5
		TIMES A , 2.50
		TIMES B , 2.50
		TIMES D , 2.50
		TIMES E , 2.50
	ELSEIF ABL:16 >= 6
		TIMES A , 3.00
		TIMES B , 3.00
		TIMES D , 3.00
		TIMES E , 3.00
	ENDIF
ENDIF

;ＬＶ３以上に上げるときは异常经验必要（素質：[献身的][爱慕][盲从]なら無視できる）
IF ABL:16 >= 3 && (TALENT:63 == 0 && TALENT:85 == 0 && TALENT:86 == 0)
	F = ABL:16 - 2
ENDIF

;反抗心
IF TALENT:11
	TIMES A , 1.20
	TIMES B , 1.40
	TIMES D , 1.20
	TIMES E , 1.20
ENDIF

;刚强
IF TALENT:12
	TIMES A , 1.20
	TIMES D , 1.20
ENDIF

;坦率
IF TALENT:13
	TIMES A , 0.90
	TIMES B , 0.80
	TIMES C , 0.80
	TIMES D , 0.70
ENDIF

;嚣张
IF TALENT:16
	TIMES A , 1.10
	TIMES B , 1.10
ENDIF

;高姿态
IF TALENT:15
	TIMES A , 1.40
	TIMES B , 1.30
	TIMES D , 1.20
	TIMES E , 1.20
;低姿态
ELSEIF TALENT:17
	TIMES A , 0.90
	TIMES B , 0.80
	TIMES D , 0.80
	TIMES E , 0.80
ENDIF

;克制
IF TALENT:20
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES D , 1.20
	TIMES E , 1.20
ENDIF

;冷漠
IF TALENT:21
	TIMES A , 1.10
	TIMES B , 1.10
	TIMES D , 1.20
	TIMES E , 1.10
ENDIF

;感情淡薄
IF TALENT:22
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES D , 1.40
	TIMES E , 1.20
ENDIF

;保守的
IF TALENT:24
	TIMES A , 1.30
	TIMES B , 1.30
	TIMES C , 1.30
	TIMES D , 1.20
	TIMES E , 1.20
ENDIF

;乐观的
IF TALENT:25
	TIMES A , 0.90
	TIMES B , 0.80
	TIMES D , 0.70
	TIMES E , 0.75
;悲观的
ELSEIF TALENT:26
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES D , 0.80
	TIMES E , 0.80
ENDIF

;爱表现
IF TALENT:28
	TIMES A , 0.90
	TIMES B , 0.90
	TIMES C , 0.90
	TIMES D , 0.90
ENDIF

;压抑
IF TALENT:32
	TIMES A , 1.10
	TIMES B , 1.10
	TIMES D , 2.50
	TIMES E , 3.00
;开放
ELSEIF TALENT:33
	TIMES A , 0.90
	TIMES B , 0.90
	TIMES D , 0.70
	TIMES E , 0.60
ENDIF

;抵抗
IF TALENT:34
	TIMES A , 1.50
	TIMES B , 1.50
	TIMES C , 1.50
	TIMES D , 2.00
	TIMES E , 2.00
ENDIF

;把柄
IF TALENT:37
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES C , 0.80
	TIMES D , 0.50
	TIMES E , 0.50
ENDIF

;快速学习
SIF TALENT:50
	TIMES C , 0.80

;学习缓慢
SIF TALENT:51
	TIMES C , 1.60

;擅用舌头
SIF TALENT:52
	TIMES C , 0.80

;献身的
IF TALENT:63
	TIMES A , 0.80
	TIMES B , 0.70
	TIMES D , 0.60
	TIMES E , 0.80
ENDIF

;接受快感
IF TALENT:70
	TIMES E , 0.70
;否定快感
ELSEIF TALENT:71
	TIMES E , 3.00
ENDIF

;容易陷落
IF TALENT:73
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES D , 0.50
	TIMES E , 0.50
ENDIF

;倒錯的
IF TALENT:80
	TIMES A , 0.75
	TIMES B , 0.75
	TIMES C , 0.75
	TIMES D , 0.75
	TIMES E , 0.75
ENDIF

;施虐狂
IF TALENT:83
	TIMES A , 1.20
	TIMES B , 1.20
	TIMES D , 1.50
ENDIF

;爱慕
IF TALENT:85
	TIMES A , 0.75
	TIMES B , 0.75
	TIMES C , 0.75
	TIMES D , 0.75
ENDIF

;盲从
IF TALENT:86
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
	TIMES D , 0.50
	TIMES E , 0.50
ENDIF

;小恶魔
IF TALENT:87
	TIMES A , 1.10
	TIMES B , 1.10
	TIMES D , 1.20
	TIMES E , 0.80
ENDIF

;疯狂
IF TALENT:123
	TIMES A , 2.50
	TIMES B , 3.00
	TIMES C , 1.50
	TIMES D , 0.80
	TIMES E , 0.50
ENDIF

;崩坏
IF TALENT:9
	TIMES A , 2.50
	TIMES B , 2.50
	TIMES C , 2.50
	TIMES D , 0.50
	TIMES E , 0.50
ENDIF

;最低でも1回・1個は必要
SIF A < 1
	A = 1
SIF B < 1
	B = 1
SIF D < 1
	D = 1
SIF E < 1
	E = 1

;屈服点数は足りている？
SIF JUEL:6 < A
	I |= 1

;恭顺点数は足りている？
IF B > 0
	SIF JUEL:4 < B
		J |= 1
	;この場合侍奉快乐经验が必要なことあり
	SIF EXP:21 < D
		J |= 2
ELSE
	J = 256
ENDIF

;习得点数は足りている？
IF C > 0
	SIF JUEL:7 < C
		K |= 1
	;この場合绝顶经验が１以上必要
	SIF EXP:2 < 1
		K |= 2
ELSE
	K = 256
ENDIF

;绝顶经验が必要な場合
SIF EXP:2 < E
	I |= 2
;精液经验が必要な場合
SIF EXP:20 < E
	I |= 2

;顺从が侍奉精神＋１レベルでないと能力不足
IF ABL:10 < ABL:16 + 1
	I |= 4
	J |= 4
	K |= 4
ENDIF

;异常经验が不足
IF F > EXP:50
	I |= 2
	J |= 2
	K |= 2
ENDIF

IF I == 0 || J == 0 || K == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF
;
;
;