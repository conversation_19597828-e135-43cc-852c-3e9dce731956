; TITLE.ERB - 游戏标题画面文件

@SYSTEM_TITLE
; 清空屏幕并显示游戏标题
CLEARLINE 50

; 显示游戏标题
PRINTL
PRINTL
PRINTL "=========================================="
PRINTL "          骑士与玩偶的传说"
PRINTL "=========================================="
PRINTL
PRINTL "         一个关于骑士与玩偶的故事"
PRINTL
PRINTL "=========================================="
PRINTL

; 显示菜单选项
PRINTL "请选择："
PRINTL "[0] 新游戏"
PRINTL "[1] 加载游戏"
PRINTL "[2] 设置"
PRINTL "[3] 退出游戏"
PRINTL

; 等待用户输入
INPUT

; 处理用户选择
SIF RESULT == 0
    CALL NEW_GAME
SIF RESULT == 1
    CALL LOAD_GAME
SIF RESULT == 2
    CALL SETTINGS_MENU
SIF RESULT == 3
    QUIT

; 如果输入无效，重新显示标题
CALL SYSTEM_TITLE

RETURN

@NEW_GAME
; 新游戏初始化
CLEARLINE 50
PRINTL "开始新游戏..."
PRINTL

; 初始化游戏数据
CALL INIT_GAME_DATA

; 显示开场剧情
CALL OPENING_STORY

; 进入主游戏循环
CALL MAIN_GAME_LOOP

RETURN

@LOAD_GAME
; 加载游戏
CLEARLINE 50
PRINTL "加载游戏功能尚未实现"
PRINTL "按任意键返回主菜单..."
INPUT
CALL SYSTEM_TITLE
RETURN

@SETTINGS_MENU
; 设置菜单
CLEARLINE 50
PRINTL "设置菜单功能尚未实现"
PRINTL "按任意键返回主菜单..."
INPUT
CALL SYSTEM_TITLE
RETURN

@INIT_GAME_DATA
; 初始化游戏数据
GAME_FLAG:0 = 1  ; 游戏开始标志
KNIGHT_RANK = 0  ; 初始骑士等级
DOLL_COMPLETION = 0  ; 初始玩偶完成度
KNIGHT_HONOR = 100  ; 初始荣誉值

; 初始化角色数据
TARGET = 0
BOUGHT = -1

RETURN

@OPENING_STORY
; 开场剧情
CLEARLINE 50
PRINTL "在一个遥远的王国里..."
PRINTL "骑士团守护着和平与正义..."
PRINTL "而神秘的玩偶工匠们..."
PRINTL "创造着拥有生命的奇迹..."
PRINTL
PRINTL "你，作为一名见习骑士..."
PRINTL "即将踏上属于自己的冒险之路..."
PRINTL
PRINTL "按任意键继续..."
INPUT

RETURN

@MAIN_GAME_LOOP
; 主游戏循环
CLEARLINE 50
PRINTL "=========================================="
PRINTL "          主菜单"
PRINTL "=========================================="
PRINTL
PRINTL "请选择行动："
PRINTL "[0] 训练"
PRINTL "[1] 探索"
PRINTL "[2] 工坊"
PRINTL "[3] 状态"
PRINTL "[4] 保存游戏"
PRINTL "[5] 返回标题"
PRINTL

INPUT

SIF RESULT == 0
    CALL TRAINING_MENU
SIF RESULT == 1
    CALL EXPLORATION_MENU
SIF RESULT == 2
    CALL WORKSHOP_MENU
SIF RESULT == 3
    CALL STATUS_DISPLAY
SIF RESULT == 4
    CALL SAVE_GAME
SIF RESULT == 5
    CALL SYSTEM_TITLE

; 继续主游戏循环
CALL MAIN_GAME_LOOP

RETURN

@TRAINING_MENU
; 训练菜单
CLEARLINE 50
PRINTL "=========================================="
PRINTL "          训练场"
PRINTL "=========================================="
PRINTL
PRINTL "选择训练项目："
PRINTL "[0] 剑术训练"
PRINTL "[1] 体能训练"
PRINTL "[2] 魔法训练"
PRINTL "[3] 返回主菜单"
PRINTL

INPUT

SIF RESULT == 0
    CALL SWORD_TRAINING
SIF RESULT == 1
    CALL PHYSICAL_TRAINING
SIF RESULT == 2
    CALL MAGIC_TRAINING
SIF RESULT == 3
    CALL MAIN_GAME_LOOP

; 训练完成后返回训练菜单
CALL TRAINING_MENU

RETURN

@SWORD_TRAINING
; 剑术训练
PRINTL "你开始了剑术训练..."
PRINTL "挥剑、格挡、突刺..."
PRINTL "你的剑术技巧有所提升！"
PRINTL
KNIGHT_HONOR += 5
PRINTL "荣誉值 +5"
PRINTL
CALL AFTERTRAIN
RETURN

@PHYSICAL_TRAINING
; 体能训练
PRINTL "你开始了体能训练..."
PRINTL "跑步、举重、耐力练习..."
PRINTL "你的体能有所提升！"
PRINTL
KNIGHT_HONOR += 3
PRINTL "荣誉值 +3"
PRINTL
CALL AFTERTRAIN
RETURN

@MAGIC_TRAINING
; 魔法训练
PRINTL "你开始了魔法训练..."
PRINTL "冥想、咒语练习、魔力控制..."
PRINTL "你的魔法能力有所提升！"
PRINTL
KNIGHT_HONOR += 4
PRINTL "荣誉值 +4"
PRINTL
CALL AFTERTRAIN
RETURN

@EXPLORATION_MENU
; 探索菜单
PRINTL "探索功能尚未实现"
PRINTL "按任意键返回主菜单..."
INPUT
RETURN

@WORKSHOP_MENU
; 工坊菜单
PRINTL "工坊功能尚未实现"
PRINTL "按任意键返回主菜单..."
INPUT
RETURN

@STATUS_DISPLAY
; 状态显示
CLEARLINE 50
PRINTL "=========================================="
PRINTL "          角色状态"
PRINTL "=========================================="
PRINTL
PRINTFORMW "骑士等级: %d", KNIGHT_RANK
PRINTFORMW "荣誉值: %d", KNIGHT_HONOR
PRINTFORMW "玩偶完成度: %d%%", DOLL_COMPLETION
PRINTL
PRINTL "按任意键返回主菜单..."
INPUT
RETURN

@SAVE_GAME
; 保存游戏
PRINTL "保存游戏功能尚未实现"
PRINTL "按任意键返回主菜单..."
INPUT
RETURN
