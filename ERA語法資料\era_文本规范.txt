﻿文本术语规范


本页面规范了所有ERA语法中与文本相关的术语。所有其他关于ERA语法的页面都应当以本页面为参考。


==文本的混乱使用==

在ERA脚本在进行文本输出和文字处理时经常会发生混乱的情况。

例如下面PRINT系指令：

 	PRINT 蓝色夏威夷
 		;=> 蓝色夏威夷
 	PRINT "蓝色夏威夷"
 		;=> 蓝色夏威夷
 	PRINTS "蓝色夏威夷"
 		;=> 蓝色夏威夷
 	PRINTS 蓝色夏威夷
 		;=> 发生错误（无法识别的标识符“蓝色夏威夷”）

同样一段文字“蓝色夏威夷”，却有两种写法（带双引号和不带双引号），同时不同PRINT指令对写法的要求不同。

再例如下面的赋值语句：

 	RESULTS = 天使之吻
 		; RESULTS的值为『天使之吻』
 		
 	STR = RESULTS
 		; STR的值为『RESULTS』
 	STR = "RESULTS"
 		; STR的值为『"RESULTS"』
 	STR = %RESULTS%
 		; STR的值为『天使之吻』
 	STR '= RESULTS
 		; STR的值为『天使之吻』
 	STR '= "RESULTS"
 		; STR的值为『RESULTS』
 	STR '= %RESULTS%
 		;=> 发生错误（非法运算）
 	STR '= "%RESULTS%"
 		; STR的值为『%RESULTS%』
 	STR '= @"%RESULTS%"
 		; STR的值为『天使之吻』

上面的示例试图将变量STR赋值为变量RESULTS的值，有不止一种实现的方式，而其他方式则会出现问题。

为了准确描述这些文本使用的情况，以使读者正确理解和使用文本，本文对'''文本'''和'''字符串'''两个概念进行界定。



== 文本 ==

本文规定，ERB脚本中所有具有功能性的、非运算符号、非标识符、没有被英文双引号封闭文字被称为'''文本'''。

文本通常仅为指令的参数和文本赋值运算符右侧的内容。

例如：
 	PRINT 蓝色夏威夷

 指令<code>PRINT</code>的参数是一段文本<code>蓝色夏威夷</code>。

例如：
 	RESULTS = 天使之吻

 文本赋值运算符（<code>=</code>）的右侧是一段文本（<code>天使之吻</code>）。

文本与下文“字符串”的概念相区别。


== 字符串 ==

本文规定，ERB脚本中所有具有功能性的、被英文双引号封闭文字被称为'''字符串'''。

字符串除了是一种书写格式外，同时也是与数值相对应的一种值类型。
除了用于指令的参数和文本赋值外，字符串也可以作为函数/方法的参数。

例如：
 	PRINTS "蓝色夏威夷"

 指令<code>PRINTS</code>的参数是一个字符串<code>"蓝色夏威夷"</code>。

例如：
 	RESULTS '= "天使之吻"

 赋值运算符<code>'=</code>的右侧是一个字符串（<code>"天使之吻"</code>）。

例如：
  	LOCALS = SUBSTRINGU("蓝色夏威夷", 2)

 方法<code>SUBSTRINGU</code>的第一参数是字符串，返回值也是字符串。

字符串除了与上文“文本”的概念外，也作为值类型与“数值”的概念相区别。


== 字符串类型 ==

当描述值类型时，使用术语“字符串类型”。

特别在描述变量时，使用“字符串类型变量”或“字符串变量”来描述。

例如：
 <code>LOCALS</code>是一个字符串类型变量。


== 文本表达式 ==

本文规定，运算结果为字符串的表达式称为“文本表达式”。

特别的，在且仅在函数签名中可以简写为“文本式”。

例如：
 	SUBSTRING <文本式>, <算式>, <算式>

 <code>"啊啊"+"哦哦"</code>是一个文本表达式。


== FORM格式文本 ==

本文规定，形如<code>%RESULTS%</code>的文本称为“FORM格式文本”。
这种格式的语法被称为“FORM格式语法”或“FORM语法”。

例如：
 	PRINTFORML %"主人"+"公",10,LEFT%

 指令<code>PRINTFORML</code>的参数是FORM格式文本（<code>%"主人"+"公",10,LEFT%</code>）。
 其中<code>"主人"+"公"</code>是一个文本表达式。

== FORM格式字符串 ==

本文规定，文本内容带有FORM语法的字符串称为“FORM格式字符串”。

例如：
 	STR '= @"%RESULTS%"

 其中<code>"%RESULTS%"</code>是一个FORM格式字符串。
 运算符<code>@</code>用来解析字符串中的FORM语法。



== 文本索引 ==

在*.csv声明的一些变量可以通过文本或字符串来引用变量的元素。

本文规定，这个文本或字符串称为这个变量的“文本索引”或“文本参数”。

例如，abl.csv中定义了<code>2, 技巧</code>的情况下：
 	ABL:技巧 += 1
 	ABL:2 += 1
 	ABL:"技巧" += 1
 	ABL:(ABLNAME:2) += 1

 其中，<code>2</code>是变量<code>ABL</code>的数值索引，
 <code>技巧</code>、<code>"技巧"</code>、<code>(ABLNAME:2)</code>是<code>ABL</code>的文本索引。


== 参见 ==

* [[Emuera用语集]]
* [[Emuera扩展语法]]
* [[运算]]
* [[FORM语法]]