﻿;eraIM@Sから流用しました

;=================================================
;調教処理及び珠計算に関する関数群
;=================================================
;
;調教後の死亡判定を追加(2009/11/14 byPawapokya-
;能力値の上昇はメイン画面で行うよう変更(2009/11/25 byPawapokya-
;
;=================================================
;BEGIN TRAIN後最初に呼び出される関数
;=================================================
@EVENTTRAIN
#PRI
;主人公の射精を0に
BASE:MASTER:2 = 0
;いちおう調教対象と助手も
BASE:TARGET:2 = 0
SIF ASSI >= 0
	BASE:ASSI:2 = 0
BASE:TARGET:3 = 0
BASE:MASTER:4 = 0

;射精フラグ、处女丧失フラグなどをリセット
REPEAT 200
	TFLAG:COUNT = 0
REND

;前回の調教テキストを空に
;TSTR:90 = 　

;調教者は誰か
IF ASSIPLAY == 0
	PLAYER = MASTER
ELSE
	PLAYER = ASSI
ENDIF

;记录目标与助手，以备人物切换
ASSI:1 = ASSI
TARGET:1 = TARGET

;时常发情ボーナス：润滑と欲情が3000からスタート
IF TALENT:TARGET:271
	PALAM:TARGET:3 = 3000
	PALAM:TARGET:5 = 3000
ENDIF

;死斗场の収入初期化
TFLAG:402 = 0

;初始化TRAIN_NAME
CALL TRAIN_NAME_INIT

CALL PRITRAIN_MESSAGE

;=================================================
;EVENTTRAINの後に毎ターン呼び出される関数
;=================================================
@SHOW_STATUS
DRAWLINE
PRINTV DAY+1
PRINT 日
IF TIME == 0
	PRINTL (午前)
ELSE
	PRINTL (午后)
ENDIF
PRINTFORM %SAVESTR:TARGET% 调教中   调教者:
IF ASSIPLAY
	SETCOLOR 0xFF1493
	PRINTS SAVESTR:ASSI
	PRINT (助手)
	RESETCOLOR
ELSE
	SETCOLOR 0x87CEFA
	PRINTS NAME:MASTER
	RESETCOLOR
ENDIF
SIF ASSI  > 0 && ASSIPLAY == 0
	PRINTFORM   助手:%SAVESTR:ASSI%
PRINT   

CALL SHOW_EQUIP_2
CALL LIFE_BAR
CALL VITAL_BAR
;調教時ステータス画面に服装表示を捻じ込んでみた
;同様のやり方で下着を個別指定にすれば捗ると思います（
PRINT 【
CALL PRINT_CLOTHTYPE
PRINT 】

PRINTL 

IF EX:0 > 0
	PRINTFORM [
	IF TALENT:122 && TALENT:121
		PRINT 阴茎
	ELSE
		PRINT 阴蒂
	ENDIF
	PRINTFORM 绝顶：{EX:0}次]  
ENDIF
SIF EX:1 > 0
	PRINTFORM [私处绝顶：{EX:1}次]  
SIF EX:2 > 0
	PRINTFORM [肛门绝顶：{EX:2}次]  
SIF EX:3 > 0
	PRINTFORM [乳房绝顶：{EX:3}次]  
SIF EX:4 > 0
	PRINTFORM [%CSTR:7%绝顶：{EX:4}次]  
SIF EX:5 > 0
	PRINTFORM [喷乳：{EX:5}次]  
SIF EX:0 || EX:1 || EX:2 || EX:3 || EX:4 || EX:5
	PRINTL  
	
PRINT_PALAM TARGET

SIF MAXBASE:2 == 0
	MAXBASE:2 = 10000
SIF MAXBASE:2 != 5000 && TALENT:133
	MAXBASE:2 = 5000
SIF MAXBASE:MASTER:2 == 0
	MAXBASE:MASTER:2 = 10000
SIF MAXBASE:MASTER:2 != 5000 && TALENT:MASTER:133
	MAXBASE:MASTER:2 = 5000
IF ASSI >= 0
	IF MAXBASE:ASSI:2 == 0
		MAXBASE:ASSI:2 = 10000
	ELSEIF MAXBASE:ASSI:2 != 0 && TALENT:ASSI:133
		MAXBASE:ASSI:2 = 5000
	ENDIF
ENDIF

IF (TALENT:MASTER:121 || TALENT:MASTER:122) && (TALENT:MASTER:135 || (TALENT:MASTER:135&& BASE:MASTER:2 >= 2000)) == 0 && TARGET != MASTER
	PRINT 射精（
	SETCOLOR 0x87CEFA
	PRINTS NAME:MASTER
	RESETCOLOR
	PRINT ）
	RESETCOLOR
	SIF STRLENSU(NAME:MASTER) < 4
		PRINTV "　" * (4-STRLENSU(NAME:MASTER))
	BAR BASE:MASTER:2,MAXBASE:MASTER:2,32
	PRINTFORM ({BASE:MASTER:2}/{MAXBASE:MASTER:2})
	SIF TEQUIP:35
		PRINT 避孕套使用中
	PRINTL 
ENDIF

IF ASSIPLAY
	IF (TALENT:ASSI:121 || TALENT:ASSI:122) && (TALENT:ASSI:135 == 0 || (TALENT:ASSI:135&& BASE:ASSI:2 >= 2000))
		PRINT 射精（
		SETCOLOR 0xFF1493
		PRINTS SAVESTR:ASSI
		RESETCOLOR
		PRINT ）
		SIF STRLENSU(SAVESTR:ASSI) < 4
			PRINTV "　" * (4-STRLENSU(SAVESTR:ASSI))
		BAR BASE:ASSI:2,MAXBASE:ASSI:2,32
		PRINTFORM ({BASE:ASSI:2}/{MAXBASE:ASSI:2})
		SIF TEQUIP:36
			PRINT 避孕套使用中
		PRINTL 
	ENDIF
ENDIF

IF (TALENT:TARGET:121 || TALENT:TARGET:122) && (TALENT:TARGET:135 == 0 || (TALENT:TARGET:135&& BASE:TARGET:2 >= 2000))
	PRINT 射精（
	PRINTS SAVESTR:TARGET
	PRINT ）
	SIF STRLENSU(SAVESTR:TARGET) < 4
		PRINTV "　" * (4-STRLENSU(SAVESTR:TARGET))
	BAR BASE:2,MAXBASE:2,32
	PRINTFORM ({BASE:2}/{MAXBASE:2})
	SIF TEQUIP:37
		PRINT 避孕套使用中
	PRINTL 
ENDIF

IF TALENT:MASTER:130
	SIF MAXBASE:MASTER:3 == 0
		MAXBASE:MASTER:3 = 10000
	PRINT 母乳（
	PRINTS SAVESTR:MASTER
	PRINT ）
	SIF STRLENSU(SAVESTR:MASTER) < 4
		PRINTV "　" * (4-STRLENSU(SAVESTR:MASTER))	
	BAR BASE:MASTER:3,MAXBASE:MASTER:3,32
	PRINTFORML ({BASE:MASTER:3}/{MAXBASE:MASTER:3})
ENDIF

IF ASSI > 0
IF TALENT:ASSI:130 
	SIF MAXBASE:ASSI:3 == 0
		MAXBASE:ASSI:3 = 10000
	PRINT 母乳（
	PRINTS SAVESTR:ASSI
	PRINT ）
	SIF STRLENSU(SAVESTR:ASSI) < 4
		PRINTV "　" * (4-STRLENSU(SAVESTR:ASSI))	
	BAR BASE:ASSI:3,MAXBASE:ASSI:3,32
	PRINTFORML ({BASE:ASSI:3}/{MAXBASE:ASSI:3})
ENDIF
ENDIF

IF TALENT:TARGET:130
	SIF MAXBASE:3 == 0
		MAXBASE:3 = 10000
	PRINT 母乳（
	PRINTS SAVESTR:TARGET
	PRINT ）
	SIF STRLENSU(SAVESTR:TARGET) < 4
		PRINTV "　" * (4-STRLENSU(SAVESTR:TARGET))	
	BAR BASE:3,MAXBASE:3,32
	PRINTFORML ({BASE:3}/{MAXBASE:3})
ENDIF

IF TEQUIP:89
	SIF MAXBASE:MASTER:4 == 0
		MAXBASE:MASTER:4 = 10000
	PRINT 射精（犬）
	PRINTV "　" * 3
	BAR BASE:MASTER:4,MAXBASE:MASTER:4,32
	PRINTFORML ({BASE:MASTER:4}/{MAXBASE:MASTER:4})
ENDIF

IF TEQUIP:90
	SIF MAXBASE:MASTER:4 == 0
		MAXBASE:MASTER:4 = 10000
	PRINT 射精（触手）
	PRINTV "　" * 2
	BAR BASE:MASTER:4,MAXBASE:MASTER:4,32
	PRINTFORML ({BASE:MASTER:4}/{MAXBASE:MASTER:4})
ENDIF

IF TEQUIP:55
	SIF MAXBASE:MASTER:4 == 0
		MAXBASE:MASTER:4 = 10000
	PRINT 射精（死斗场・怪物）
	BAR BASE:MASTER:4,MAXBASE:MASTER:4,32
	PRINTFORML ({BASE:MASTER:4}/{MAXBASE:MASTER:4})
ENDIF
CALL SHOW_EQUIP_1

;设置清除点
CALL SET_CLEAR_POINT

;=================================================
;TRAINでコマンド選択時最初に呼び出されるイベント関数
;=================================================
@EVENTCOM
#PRI
;TFLAG:0～30はコマンドを選択する度に空にする
VARSET TFLAG, 0, 0, 30
TFLAG:100 = 0

REDRAW 1

;=================================================
;TRAINでコマンド選択終了後に呼び出される関数
;=================================================
@EVENTCOMEND
#PRI
;調教キャラの死亡・衰弱判定
IF BASE:0 <= 0 && !FLAG:35
	DRAWLINE
	;死亡時にビデオを使用していた？
	SIF TEQUIP:53
		TFLAG:34 = 1
	PRINTFORML %SAVESTR:TARGET%一动也不动，
	PRINTFORML 对她做什么都不再有反应了……
	WAIT
	BEGIN AFTERTRAIN
;瀕死時に調教を自動終了設定
ELSEIF BASE:0 < 500 && FLAG:35
	DRAWLINE
	PRINTL （体力到了极限。调教结束。）
	WAIT
	BEGIN AFTERTRAIN
ENDIF

IF ASSI > 0
	IF 0
		DRAWLINE
		;死亡時にビデオを使用していた？
		SIF TEQUIP:53
			TFLAG:34 = 1
		PRINTFORML %SAVESTR:ASSI%一动也不动，
		PRINTFORML 对她做什么都不再有反应了……
		WAIT
		BEGIN AFTERTRAIN
	;瀕死時に調教を自動終了設定
	ELSEIF BASE:ASSI:0 < 500
		DRAWLINE
		PRINTL （助手体力到了极限。调教结束。）
		WAIT
		BEGIN AFTERTRAIN
	ENDIF
ENDIF

;=================================================
;BEGIN AFTERTRAIN後最初に呼び出される関数
;=================================================
@EVENTEND
#LATER
PRINTL 调教结束了。
WAIT

;失神時の口上非表示の回復
IF TFLAG:860 == 1
	FLAG:7 = 1
	TFLAG:860 = 0
ELSEIF TFLAG:860 == 2
	FLAG:7 = 2
	TFLAG:860 = 0
ENDIF

;今回の調教対象と助手を記録
FLAG:1 = TARGET:1
FLAG:2 = ASSI:1

;調教後に死んでいる可能性をチェック
CALL CHARADEAD_CHECK

;生きていれば調教後行為のチェック
IF RESULT == 0
	CALL SELF_CHECK
	DRAWLINE
ENDIF

;搾乳した母乳の売却
CALL SELL_MILK

;調教時に録画したビデオを売却
CALL SELL_VIDEO

;死斗场の観戦料
CALL SELL_FIGHTMONEY

;生きていて着衣モードなら調教後の衣類の処理
IF FLAG:37 && BASE:0 > 0
	CALL AFTERTRAIN_CLOTH
	;衣類の再着衣
	CALL RE_CLOTHED
ENDIF

;調教後に死ぬか臨死状態なら珠を獲得せずに、ターゲットを空にしてターン終了
IF BASE:0 < 1 && TARGET != MASTER
	;キャラ削除処理
	A = TARGET

	X = NO:A + 199
	FLAG:X = 1

	TARGET = -1
	FLAG:1 = -1
	ASSI = -1
	
	CALL PARTY_CHAR_DEL, A

	DELCHARA A

	CALL NAME_RESET

	BEGIN TURNEND
ELSEIF BASE:0 < 1 && TARGET == MASTER
	;魔王换人的处理
	CALL MAOU_TENSHIN
	
ENDIF

;善恶值増減
IF EX:1
	PRINTW (私处绝顶使善恶值:-1)
	CALL KARMA, TARGET, -1
ENDIF

IF EX:2
	PRINTW (肛门绝顶使善恶值:-2)
	CALL KARMA, TARGET, -2
ENDIF

;时常发情
IF FLAG:75 == 0 && TALENT:271 == 0
	;润滑の10000分の1を蓄積　润滑が10000を下回る場合はリセット
	IF PALAM:3 >= 10000
		CFLAG:81 += PALAM:3 / 10000
	ELSE
		CFLAG:81 = 0
	ENDIF
	;欲情の10000分の1を蓄積　欲情が10000を下回る場合はリセット
	IF PALAM:5 >= 10000
		CFLAG:82 += PALAM:5 / 10000
	ELSE
		CFLAG:82 = 0
	ENDIF
ENDIF

LOCAL = 0
IF FLAG:400 && TALENT:85
	PRINT *奴隷的愛中回復了気力*
	LOCAL = 700
ELSEIF FLAG:400
	PRINT *印調教奴隷而回復了気力*
	LOCAL = 500
ENDIF
BASE:0:1 += LOCAL
SIF BASE:0:1 > MAXBASE:0:1
	BASE:0:1 = MAXBASE:0:1

;何点数を得られたか
CALL JUEL_CHECK


;切换回原来的目标与助手
ASSI = ASSI:1
TARGET = TARGET:1

;能力値の上昇はメイン画面で行わせる
BEGIN TURNEND
;BEGIN ABLUP
;
;=================================================
;獲得した珠の計数と表示
;=================================================
@JUEL_CHECK

CALL JUEL_CHECK_MAIN


WAIT
;

$INPUT_LOOP_1
CUSTOMDRAWLINE ‥
CALL SHOW_INFO_EXP
CALL SHOW_JUEL
;CALL SHOW_ABLUP_SELECT

;自动升级点数
IF GETBIT(FLAG:5,35)
	
	CALL AUTO_ABLUP
	SIF ASSI > 0
		CALL AUTO_ABLUP, ASSI
	CALL AUTO_ABLUP, MASTER
	
	GOTO LABEL_EXIT
ENDIF
CALL SHOW_ABLUP_SELECT

INPUT

;阴蒂感觉
IF RESULT == 0
	CALL ABLUP0
;乳房感觉
ELSEIF RESULT == 1
	CALL ABLUP1
;私处感觉
ELSEIF RESULT == 2
	CALL ABLUP2
;肛门感觉
ELSEIF RESULT == 3
	CALL ABLUP3
;局部感覚
ELSEIF RESULT == 4
	CALL ABLUP4
;顺从
ELSEIF RESULT == 10
	CALL ABLUP10
;欲望
ELSEIF RESULT == 11
	CALL ABLUP11
;技巧
ELSEIF RESULT == 12
	CALL ABLUP12
;侍奉技术
ELSEIF RESULT == 13
	CALL ABLUP13
;性交技术
ELSEIF RESULT == 14
	CALL ABLUP14
;话术
ELSEIF RESULT == 15
	CALL ABLUP15
;侍奉精神
ELSEIF RESULT == 16
	CALL ABLUP16
;露出癖
ELSEIF RESULT == 17
	CALL ABLUP17
;抖S气质
ELSEIF RESULT == 20
	CALL ABLUP20
;抖M气质
ELSEIF RESULT == 21
	CALL ABLUP21
;百合气质
ELSEIF RESULT == 22
	CALL ABLUP22
;ホモっ気
ELSEIF RESULT == 23
	CALL ABLUP23
;性交中毒
ELSEIF RESULT == 30
	CALL ABLUP30
;自慰中毒
ELSEIF RESULT == 31
	CALL ABLUP31
;精液中毒
ELSEIF RESULT == 32
	CALL ABLUP32
;百合中毒
ELSEIF RESULT == 33
	CALL ABLUP33
;卖淫中毒
ELSEIF RESULT == 37
	CALL ABLUP37
;兽奸中毒
ELSEIF RESULT == 39
	CALL ABLUP39
;局部中毒
ELSEIF RESULT == 40
	CALL ABLUP40
;反抗刻印
ELSEIF RESULT == 99
	CALL ABLUP99
ELSEIF RESULT == 100
	CALL ABLUP100
ELSEIF RESULT == 999
	$LABEL_EXIT
	CALL YOKUBO_UP_CHECK
	CALL CHECK_SELLASSIABLE
	CALL CHECK_SPECIALSKIL, 1
	LOCAL = TARGET
	RETURN 1
ENDIF

GOTO INPUT_LOOP_1

;-----------------------------------
@JUEL_CHECK_MAIN
#DIM JUEL_COUNT
#DIM GET_JUEL
#DIM NEW_LINE
;-----------------------------------

FOR JUEL_COUNT,0,15
	IF PALAM:JUEL_COUNT < PALAMLV:1
		GET_JUEL = 0
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:1*3
		GET_JUEL = 1
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:2
		GET_JUEL = 2
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:2*3
		GET_JUEL = 10
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:3
		GET_JUEL = 20
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:3*2
		GET_JUEL = 100
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:4
		GET_JUEL = 200
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:5
		GET_JUEL = 1000
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:6
		GET_JUEL = 2000
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:7
		GET_JUEL = 3000
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:8
		GET_JUEL = 5000
	ELSEIF PALAM:JUEL_COUNT < PALAMLV:9
		GET_JUEL = 8000
	ELSE
		GET_JUEL = 12000
	ENDIF

	IF JUEL_COUNT == 0
		GOTJUEL:JUEL_COUNT = GET_JUEL + EX:0 * 1000
	ELSEIF JUEL_COUNT == 1
		GOTJUEL:JUEL_COUNT = GET_JUEL+ EX:1 * 1000
	ELSEIF JUEL_COUNT == 2
		GOTJUEL:JUEL_COUNT = GET_JUEL + EX:2 * 1000
	ELSEIF JUEL_COUNT == 14
		GOTJUEL:JUEL_COUNT = GET_JUEL + EX:3 * 1000
	ELSEIF JUEL_COUNT < 11 && JUEL_COUNT != 14
		GOTJUEL:JUEL_COUNT = GET_JUEL
	ELSE
		GOTJUEL:100 += GET_JUEL
	ENDIF
NEXT

;现在保有する珠に今回獲得した珠を加算
;3潤滑,11反感,12不快,13抑鬱は保有する珠として計算されないパラメータ
FOR JUEL_COUNT,0,11
	SIF JUEL_COUNT == 3
		CONTINUE
	JUEL:JUEL_COUNT += GOTJUEL:JUEL_COUNT
NEXT
JUEL:14 += GOTJUEL:14
JUEL:100 += GOTJUEL:100

;否定の珠による相殺を計算
;TFLAG:51～58に相殺前の（変動する可能性のある）珠を記録
FOR JUEL_COUNT,0,7
	SIF JUEL_COUNT == 3
		CONTINUE
	LOCAL:0 = JUEL_COUNT + 51
	LOCAL:1 = JUEL_COUNT + 4
	TFLAG:(LOCAL:0) = JUEL:(LOCAL:1)
NEXT
TFLAG:58 = JUEL:100

$LABEL_1
LOCAL:0 = RAND:3 + 4
LOCAL:1 = JUEL:100 / 2
SIF LOCAL:1 == 0 && JUEL:100 > 0
	LOCAL:1 = 1
SIF JUEL:(LOCAL:0) < LOCAL:1
	LOCAL:1 = JUEL:(LOCAL:0)
JUEL:(LOCAL:0) -= LOCAL:1
JUEL:100 -= LOCAL:1

SIF JUEL:100 > 0 && (JUEL:4 + JUEL:5 + JUEL:6) > 0
	GOTO LABEL_1

$LABEL_2
LOCAL:0 = RAND:3 + 8
LOCAL:1 = JUEL:100 / 2
SIF LOCAL:1 == 0 && JUEL:100 > 0
	LOCAL:1 = 1
SIF JUEL:(LOCAL:0) < LOCAL:1
	LOCAL:1 = JUEL:(LOCAL:0)
JUEL:(LOCAL:0) -= LOCAL:1
JUEL:100 -= LOCAL:1
SIF JUEL:100 > 0 && (JUEL:8 + JUEL:9 + JUEL:10) > 0
	GOTO LABEL_2

DRAWLINE
PRINTFORM 调教结果：
SIF TFLAG:58 > 0
	PRINTFORM 否定点数{TFLAG:58}个抵消。
PRINTL 
CUSTOMDRAWLINE ‥
NEW_LINE = 0
FOR JUEL_COUNT,0,13
	IF JUEL_COUNT <= 3 || JUEL_COUNT == 7 || JUEL_COUNT == 12
		IF JUEL_COUNT == 3
			LOCAL:0 = 14
		ELSEIF JUEL_COUNT == 12
			LOCAL:0 = 15
		ELSE
			LOCAL:0 = JUEL_COUNT
		ENDIF
		IF JUEL_COUNT == 12
			IF CSTR:7 == ""
				PRINTFORM 癖好点数：(
			ELSE
				PRINTFORM %CSTR:7%点数：(
			ENDIF
		ELSE
		PRINTFORM %PALAMNAME:(LOCAL:0)%点数：(
		ENDIF
		N = JUEL:(LOCAL:0) - GOTJUEL:(LOCAL:0)
		CALL FIGURE_INDENT
		SETCOLORBYNAME SkyBlue
		PRINTV JUEL:(LOCAL:0) - GOTJUEL:(LOCAL:0)
		RESETCOLOR
		PRINT  + 
		N = GOTJUEL:(LOCAL:0)
		CALL FIGURE_INDENT
		SETCOLORBYNAME SkyBlue
		PRINTV GOTJUEL:(LOCAL:0)
		RESETCOLOR
		PRINT )            = 
		N = JUEL:(LOCAL:0)
		CALL FIGURE_INDENT
		SETCOLORBYNAME SkyBlue
		PRINTV JUEL:(LOCAL:0)
		RESETCOLOR
	ELSE
		IF JUEL_COUNT == 11
			LOCAL:0 = 58
			LOCAL:1 = 100
		ELSE
			LOCAL:0 = JUEL_COUNT + 47
			LOCAL:1 = JUEL_COUNT
		ENDIF
		PRINTFORM %PALAMNAME:(LOCAL:1)%点数：(
		N = TFLAG:(LOCAL:0) - GOTJUEL:(LOCAL:1)
		CALL FIGURE_INDENT
		SETCOLORBYNAME SkyBlue
		PRINTV TFLAG:(LOCAL:0) - GOTJUEL:(LOCAL:1)
		RESETCOLOR
		PRINT  + 
		N = GOTJUEL:(LOCAL:1)
		CALL FIGURE_INDENT
		SETCOLORBYNAME SkyBlue
		PRINTV GOTJUEL:(LOCAL:1)
		RESETCOLOR
		PRINT ) - 
		N = TFLAG:(LOCAL:0) - JUEL:(LOCAL:1)
		CALL FIGURE_INDENT
		SETCOLORBYNAME LightSalmon
		PRINTV TFLAG:(LOCAL:0) - JUEL:(LOCAL:1)
		RESETCOLOR
		PRINT  = 
		N = JUEL:(LOCAL:1)
		CALL FIGURE_INDENT
		SETCOLORBYNAME SkyBlue
		PRINTV JUEL:(LOCAL:1)
		RESETCOLOR
	ENDIF
	
	; IF NEW_LINE == 1
		PRINTL |
		; NEW_LINE = 0 
	; ELSE
		; PRINT |
		; NEW_LINE = 1
	; ENDIF
	
NEXT
CUSTOMDRAWLINE ‥
PRINTL 以上的点数变化了。


RETURN 0


@FIGURE_INDENT
SIF N < 10000000
	PRINT  
SIF N < 1000000
	PRINT  
SIF N < 100000
	PRINT  
SIF N < 10000
	PRINT  
SIF N < 1000
	PRINT  
SIF N < 100
	PRINT  
SIF N < 10
	PRINT  
RETURN 1
;
@FIGURE_INDENT_SLASH
N = O
CALL FIGURE_INDENT
PRINTFORM {O}/
N = P
CALL FIGURE_INDENT
PRINTFORM {P} 
;

;

@P_C
LOCAL = PREVCOM
TSTR:90 '= TRAINNAME:LOCAL

SIF STRLENSU(TSTR:90) < 1
	TSTR:90 '= TRAIN_NAME:LOCAL
	
SIF STRLENSU(TSTR:90) < 1
	TSTR:90 = 　


;--------------------------------------------------
@TRAIN_NAME_INIT
; 初始化TRAIN_NAME
;--------------------------------------------------
SIF STRLENSU(TRAIN_NAME) > 0
	RETURN
TRAIN_NAME:0 = 爱抚
TRAIN_NAME:1 = 舔阴
TRAIN_NAME:2 = 肛门爱抚
TRAIN_NAME:3 = 自慰
TRAIN_NAME:4 = 口交(主)
TRAIN_NAME:5 = 胸爱抚
TRAIN_NAME:6 = 接吻
TRAIN_NAME:7 = 自己扒开
TRAIN_NAME:8 = 插入手指
TRAIN_NAME:9 = 舔肛
TRAIN_NAME:10 = 振动宝石
TRAIN_NAME:11 = 壶虫
TRAIN_NAME:12 = 振动杖
TRAIN_NAME:13 = 肛门虫
TRAIN_NAME:14 = 阴蒂夹
TRAIN_NAME:15 = 乳头夹
TRAIN_NAME:16 = 榨乳器
TRAIN_NAME:17 = 飞机杯
TRAIN_NAME:18 = 淋浴
TRAIN_NAME:19 = 肛珠
TRAIN_NAME:20 = 正常位
TRAIN_NAME:21 = 背后位
TRAIN_NAME:22 = 对面座位
TRAIN_NAME:23 = 背面座位
TRAIN_NAME:24 = 逆强奸
TRAIN_NAME:25 = 逆肛门强奸
TRAIN_NAME:26 = 正常位肛交
TRAIN_NAME:27 = 背后位肛交
TRAIN_NAME:28 = 对面座位肛交
TRAIN_NAME:29 = 背面座位肛交
TRAIN_NAME:30 = 手淫
TRAIN_NAME:31 = 口交(奴)
TRAIN_NAME:32 = 乳交
TRAIN_NAME:33 = 股间性交
TRAIN_NAME:34 = 骑乘位
TRAIN_NAME:35 = 全身擦洗
TRAIN_NAME:36 = 骑乘位肛交
TRAIN_NAME:37 = 肛门侍奉
TRAIN_NAME:38 = 足交
TRAIN_NAME:40 = 打屁股
TRAIN_NAME:41 = 鞭
TRAIN_NAME:42 = 针
TRAIN_NAME:43 = 眼罩
TRAIN_NAME:44 = 绳子
TRAIN_NAME:45 = 口塞
TRAIN_NAME:46 = 灌肠+肛塞
TRAIN_NAME:47 = 拘束衣穿着
TRAIN_NAME:48 = 践踏
TRAIN_NAME:49 = 肛门电极
TRAIN_NAME:50 = 润滑液
TRAIN_NAME:51 = 媚药
TRAIN_NAME:52 = 利尿剂
TRAIN_NAME:53 = 水晶球
TRAIN_NAME:54 = 野外PLAY
TRAIN_NAME:55 = 放置PLAY
TRAIN_NAME:56 = 交谈
TRAIN_NAME:57 = 羞耻PLAY
TRAIN_NAME:58 = 浴室PLAY
TRAIN_NAME:59 = 新妻PLAY
TRAIN_NAME:60 = 助手接吻
TRAIN_NAME:61 = 强制舔阴
TRAIN_NAME:62 = 侵犯助手
TRAIN_NAME:63 = 磨镜
TRAIN_NAME:64 = ３Ｐ
TRAIN_NAME:65 = 逆侵犯助手
TRAIN_NAME:66 = 双枪口交
TRAIN_NAME:68 = 双人口交
TRAIN_NAME:69 = 六九式
TRAIN_NAME:70 = 双人股间性交
TRAIN_NAME:71 = 双人乳交
TRAIN_NAME:72 = 刮阴毛
TRAIN_NAME:73 = 拨弄发型
TRAIN_NAME:80 = 强制口交
TRAIN_NAME:81 = 拳交
TRAIN_NAME:82 = 肛门拳交
TRAIN_NAME:83 = 两穴拳交
TRAIN_NAME:84 = 刺激Ｇ点
TRAIN_NAME:85 = 放尿
TRAIN_NAME:86 = 饮尿
TRAIN_NAME:87 = 穿环
TRAIN_NAME:89 = 兽奸PLAY
TRAIN_NAME:90 = 乳内插入
TRAIN_NAME:100 = 触手生物
TRAIN_NAME:101 = 触手插入
TRAIN_NAME:102 = 肛交触手
TRAIN_NAME:103 = 触手凌辱阴蒂
TRAIN_NAME:104 = 触手凌辱乳头
TRAIN_NAME:105 = 触手榨乳
TRAIN_NAME:106 = 触手紧缚
TRAIN_NAME:107 = 触手灌肠
TRAIN_NAME:108 = 触手口辱
TRAIN_NAME:109 = 触手凌辱阴茎
TRAIN_NAME:110 = 穿脱衣服
TRAIN_NAME:111 = 撕破衣服
TRAIN_NAME:120 = 插入Ｇ点蹂躏
TRAIN_NAME:121 = 插入子宫口蹂躏
TRAIN_NAME:122 = 阴茎互捅
TRAIN_NAME:123 = 乳夹口交
TRAIN_NAME:124 = 深喉
TRAIN_NAME:125 = 口交时自慰
TRAIN_NAME:126 = 手搓口交
TRAIN_NAME:127 = 真空口交
TRAIN_NAME:128 = 正常位・接吻
TRAIN_NAME:129 = 正常位・胸爱抚
TRAIN_NAME:130 = 正常位ＳＰ
TRAIN_NAME:131 = 背后位・胸爱抚
TRAIN_NAME:132 = 背后位・打屁股
TRAIN_NAME:133 = 站立背后位
TRAIN_NAME:134 = 背后位ＳＰ
TRAIN_NAME:135 = 自助舔舐
TRAIN_NAME:150 = %CSTR:7%調教
TRAIN_NAME:200 = 死斗场
TRAIN_NAME:201 = 助手
TRAIN_NAME:202 = 最下层居民
TRAIN_NAME:203 = 霉菌狗
TRAIN_NAME:204 = 兽人
TRAIN_NAME:205 = 腐烂猪
TRAIN_NAME:206 = 巨魔
TRAIN_NAME:207 = 媚药史莱姆
TRAIN_NAME:208 = 触手


