﻿;=================================================
;秘密ラボ
;=================================================
@SECRET_LABO
P = 0


$DRAW_PAGE
LOCAL = LINECOUNT
REDRAW 0
CLEARLINE LINECOUNT - LOCAL
CUSTOMDRAWLINE =
PRINTL 魔界的大门
PRINTL 《可以对奴隶进行肉体和精神的魔改》
DRAWLINE
PRINTV DAY+1
PRINT 日
IF TIME == 0
	PRINTL  午前
ELSE
	PRINTL  午后
ENDIF

PRINTFORML 所持金：{MONEY}点
DRAWLINE
IF P == 0
	CALL LABO_PAGE1
ELSEIF P == 1
	CALL LABO_PAGE2
ELSEIF P == 2
	CALL LABO_PAGE3
ELSE
	CALL LABO_PAGE4
ENDIF

REPEAT LOCAL + 22 - LINECOUNT
	PRINTL
REND


DRAWLINE
PRINTLC  [997] - 前一页
PRINTLC  [999] - 返回
PRINTLC  [998] - 后一页
PRINTL

REDRAW 1
$INPUT_LOOP
INPUT

IF RESULT == 0
	CALL MODIFY_BUSTUP
ELSEIF RESULT == 1
	CALL MODIFY_BUSTDOWN
ELSEIF RESULT == 2
	CALL MODIFY_BONYU
ELSEIF RESULT == 3
	CALL MODIFY_FUTANARI
ELSEIF RESULT == 4
	CALL MODIFY_FUTANARI_ERASE
ELSEIF RESULT == 5
	CALL MODIFY_ANIMAL
ELSEIF RESULT == 6
	CALL MODIFY_ANIMAL_ERASE
ELSEIF RESULT == 7
	CALL MODIFY_DEIMMATURITY
ELSEIF RESULT == 8
	CALL MODIFY_AMNESIA
ELSEIF RESULT == 9
	CALL MODIFY_REMOVEHAIR
ELSEIF RESULT == 10
	CALL MODIFY_BONYU_ERASE
ELSEIF RESULT == 11
	CALL MODIFY_OMORASHI_ERASE
ELSEIF RESULT == 12
	CALL SHOJO_SAISEI
ELSEIF RESULT == 13
	CALL SHOJO_SEAL
ELSEIF RESULT == 14
	CALL SHOJO_SEAL_OFF
ELSEIF RESULT == 15
	CALL TATOO_SET_OFF
ELSEIF RESULT == 16
	CALL MODIFY_HAIR_COLOR
ELSEIF RESULT == 17
	CALL MODIFY_SKIN_COLOR
ELSEIF RESULT == 18
	CALL BLOCK_FEELING
ELSEIF RESULT == 19
	CALL EXTRA_PREG_MARK
ELSEIF RESULT == 20
	CALL EXTRA_PREG_ERASE
ELSEIF RESULT == 21
	CALL TRANS_SEX
ELSEIF RESULT == 22
	CALL SET_FREE_TRAIN
ELSEIF RESULT == 23
	CALL TRANS_SPECIALTALENT
ELSEIF RESULT == 30
	C = 5000
	B = 64
	CALL BRAIN_WASHING
ELSEIF RESULT == 31
	C = 8000
	B = 133
	CALL BRAIN_WASHING
ELSEIF RESULT == 32
	C = 10000
	B = 132
	CALL BRAIN_WASHING
ELSEIF RESULT == 33
	C = 10000
	B = 83
	CALL BRAIN_WASHING
ELSEIF RESULT == 50 && ITEM:90 == 0
	CALL BOUGT_TENTACLES
ELSEIF RESULT == 51 && EXP:MASTER:81
	CALL RESULECTION
ELSEIF RESULT == 52 && EXP:MASTER:81
	CALL GIVEN_HUMAN_LIFE
ELSEIF RESULT == 54 && EXP:MASTER:81
	CALL CURE_INSANE
ELSEIF RESULT == 55 && EXP:MASTER:81
	CALL REGET_CHASTITY_KEY
ELSEIF RESULT == 56
	CALL SUMMON_SLAVE
ELSEIF RESULT == 59
	CALL HORN
ELSEIF RESULT == 60
	CALL EVILAPP
ELSEIF RESULT == 64
	CALL DEMON_REBIRTH
ELSEIF RESULT == 65
	CALL SOULBOUND
ELSEIF RESULT == 66
	CALL SOULBOUND_ERASE
ELSEIF RESULT == 67
	CALL ENCHARMED_ERASE
ELSEIF RESULT == 68
	IF FLAG:82 == 0 && CHARANUM > 60
	    PRINTW 勇者数量过多
		GOTO INPUT_LOOP
	ELSEIF FLAG:87 == 0 && FLAG:89 == 0 && FLAG:91 == 0 && CHARANUM > 65
		PRINTW 勇者数量过多
		GOTO INPUT_LOOP
	ELSEIF ((FLAG:87 * FLAG:89 == 0) && (FLAG:89 * FLAG:91 == 0) && (FLAG:91 * FLAG:87 == 0)) && CHARANUM > 70
		PRINTW 勇者数量过多
		GOTO INPUT_LOOP
	ELSEIF (FLAG:87 == 0 || FLAG:89 == 0 || FLAG:91 == 0) && CHARANUM > 75
		PRINTW 勇者数量过多
		GOTO INPUT_LOOP
	ELSEIF FLAG:92 < 15  && CHARANUM > 80	
	    PRINTW 勇者数量过多
		GOTO INPUT_LOOP
	ELSEIF CHARANUM >= MAX_CHARANUM
		PRINTW 勇者数量过多
		GOTO INPUT_LOOP
	ENDIF
	CALL CHAR_CREATE(0)	
ELSEIF RESULT == 70
	C = 5000
	B = 0
	CALL ST_UP_LABO
ELSEIF RESULT == 71
	C = 5000
	B = 1
	CALL ST_UP_LABO
ELSEIF RESULT == 72
	C = 5000
	B = 2
	CALL ST_UP_LABO
ELSEIF RESULT == 73
	C = 5000
	B = 3
	CALL ST_UP_LABO
ELSEIF RESULT == 74
	CALL MAGICRESIST
ELSEIF RESULT == 999
	RETURN 0
ELSEIF RESULT == 997
	P += 3
	P %= 4
ELSEIF RESULT == 998
	P += 1
	P %= 4
ELSE
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF

GOTO DRAW_PAGE

;----------------------------------------------------
@LABO_PAGE1
;----------------------------------------------------
PRINTL
PRINTL □肉体改造
PRINTL
PRINTL  [ 0] - 丰胸改造               （20000点）
PRINTL  [ 1] - 平胸改造               （10000点）
PRINTL  [ 2] - 加入母乳体质           （50000点）
PRINTL  [ 3] - 扶她化                 （50000点）
PRINTL  [ 4] - 去扶她化               （10000点）
PRINTL  [ 5] - 附上动物耳朵           （2000点）
PRINTL  [ 6] - 去除动物耳朵           （1000点）
PRINTL  [ 7] - 性成熟            　   （10000点）
PRINTL  [ 8] - 记忆消去               （100000点）
PRINTL  [ 9] - 阴部永久脱毛           （5000点）
PRINTL  [10] - 消去母乳体质           （10000点）
PRINTL  [11] - 消除漏尿癖             （10000点）

;----------------------------------------------------
@LABO_PAGE2
;-----------------------------------------------------
PRINTL
PRINTL □肉体改造
PRINTL
PRINTL  [12] - 处女膜再生术           （100000点）
PRINTL  [13] - 施加私处封印           （10000点）
PRINTL  [14] - 解除私处封印           （10000点）
PRINTL  [15] - 刺青的刻印/消去        （10000点）
PRINTL  [16] - 头发颜色改变           （5000点）
PRINTL  [17] - 肤色改变               （5000点）
PRINTL  [18] - 感觉封锁               （20000点）
PRINTL  [19] - 异常妊娠体质           （20000点）
PRINTL  [20] - 消除异常妊娠体质       （35000点）
PRINTL  [21] - 变性                   （200000点）
PRINTL  [22] - 自由局部调教设定       （20000点）
PRINTL  [23] - 淫乱爱慕互换           （500000点）
;----------------------------------------------------
@LABO_PAGE3
;----------------------------------------------------
PRINTL □其他
PRINTL
SIF ITEM:90 == 0
PRINTL  [50] - 购买触手生物           （50000点）

IF EXP:MASTER:81
PRINTL  [51] - 死者苏生               （勋章交换）
PRINTL  [52] - 赋予生命               （勋章交换）
PRINTL  [54] - 安抚崩坏的心           （勋章交换）
PRINTL  [55] - 寻访贞操带钥匙         （勋章交换）
ENDIF
PRINTL  [56] - 召唤影之仆从           （100000点）
PRINTL  [59] - 赋予犄角               （20000点）
PRINTL  [60] - 恶魔体征改造           （20000点）
PRINTL  [64] - 转生的秘法             （50000点）
PRINTL  [65] - 魂缚的诅咒             （10000点）
PRINTL  [66] - 魂缚的解咒             （50000点）
PRINTL  [67] - 狂王俘虏的消去         （50000点）
PRINTL  [68] - 生命摇篮　             （起价500000点，按素质加价，上百万是正常的）


;----------------------------------------------------
@LABO_PAGE4
;----------------------------------------------------
PRINTL
PRINTL □战斗
PRINTL
PRINTL  [70] - HP＋10                 （5000点）
PRINTL  [71] - 气力＋10               （5000点）
PRINTL  [72] - 攻击＋1                （5000点）
PRINTL  [73] - 防御＋1                （5000点）
PRINTL  [74] - 赋予魔法耐性           （50000点）
PRINTL
PRINTL □洗脑 （助手用）
PRINTL
PRINTL  [30] -【无视污垢】            （5000点）
PRINTL  [31] -【早泄】                （8000点）
PRINTL  [32] -【幼稚】                （10000点）
PRINTL  [33] -【抖Ｓ】                （10000点）
;
;-------------------------------------------------
;巨乳化
;-------------------------------------------------
@MODIFY_BUSTUP
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不多，胸不大
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 改造后，改造对象的乳房将增大到一个新的层次
PRINTL 要为谁丰胸？

DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:122
	PRINTFORMW 【%TALENTNAME:122%】不能巨乳化。
	RETURN 0
ELSEIF TALENT:RESULT:119
	PRINTFORMW 知足吧，%SAVESTR:RESULT%的胸，已经是奇尺大乳了。
	RETURN 0
ENDIF

SIF RESULT == 1001 || RESULT == 1000
	GOTO INPUT_LOOP

IF TALENT:RESULT:110 || TALENT:RESULT:114
	PRINTFORML %SAVESTR:RESULT%胸部伟岸，要更上一层楼，需要50000点。
	C = 50000
	SIF MONEY < C
		GOTO INPUT_LOOP
	PRINTL 这样还要继续么？
ELSE
	PRINTFORML 为%SAVESTR:RESULT%进行巨乳化改造吗？
ENDIF

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 && TALENT:T:116
	PRINTFORMW 《%SAVESTR:T%的【%TALENTNAME:109%】消去了》
	TALENT:T:116 = 0
	TALENT:T:109 = 1
ELSEIF RESULT == 0 && TALENT:T:109
	PRINTFORMW 《%SAVESTR:T%的胸是标准大小》
	TALENT:T:109 = 0
ELSEIF RESULT == 0 && TALENT:T:110
	PRINTFORMW 《%SAVESTR:T%获得【%TALENTNAME:114%】了》
	TALENT:T:110 = 0
	TALENT:T:114 = 1
ELSEIF RESULT == 0 && TALENT:T:114
	PRINTFORMW 《%SAVESTR:T%获得【%TALENTNAME:119%】了》
	TALENT:T:114 = 0
	TALENT:T:119 = 1
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%获得【%TALENTNAME:110%】了》
	TALENT:T:110 = 1
ELSE
	RETURN 0
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
;対象があなたでなく、コンフィグ設定してあるならバストサイズ更新
IF T != 0
	IF GETBIT(FLAG:5,12) || GETBIT(FLAG:5,15)
		;CALL CHAR_BUST_REGENERATE_WAPPED, T
		CALL CHAR_SIZE_GENERATE, T, CFLAG:T:451, 1
		CFLAG:T:454 = RESULT:3
		CFLAG:T:455 = RESULT:4
	ENDIF
ENDIF

RETURN 1
;
;-------------------------------------------------
;贫乳化
;-------------------------------------------------
@MODIFY_BUSTDOWN
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 改造对象的乳房将变小到一个新的层次。
PRINTL 要推平谁的胸？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:122
	PRINTFORMW 【%TALENTNAME:122%】不能贫乳化。
	RETURN 0
ELSEIF TALENT:RESULT:116
	PRINTFORMW %SAVESTR:RESULT%已经平如镜子，放过她吧。
	RETURN 0
ENDIF

;SIF RESULT == 1001 || RESULT == 1000
;	GOTO INPUT_LOOP

PRINTFORML 对%SAVESTR:RESULT%进行贫乳改造吗？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 && TALENT:T:119
	PRINTFORMW 《%SAVESTR:T%获得了【%TALENTNAME:114%】》
	TALENT:T:119 = 0
	TALENT:T:114 = 1
ELSEIF RESULT == 0 && TALENT:T:114
	PRINTFORMW 《%SAVESTR:T%获得了【%TALENTNAME:110%】》
	TALENT:T:114 = 0
	TALENT:T:110 = 1
ELSEIF RESULT == 0 && TALENT:T:110
	PRINTFORMW 《%SAVESTR:T%的胸是标准大小》
	TALENT:T:110 = 0
ELSEIF RESULT == 0 && TALENT:T:109
	PRINTFORMW 《%SAVESTR:T%的胸部线条完全没有了》
	TALENT:T:109 = 0
	TALENT:T:116 = 1
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%获得了【%TALENTNAME:109%】》
	TALENT:T:109 = 1
ELSE
	RETURN 0
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
;対象があなたでなく、コンフィグ設定してあるならバストサイズ更新
IF T != 0
	IF GETBIT(FLAG:5,12) || GETBIT(FLAG:5,15)
		;CALL CHAR_BUST_REGENERATE_WAPPED, T
		CALL CHAR_SIZE_GENERATE, T, CFLAG:T:451, 1
		CFLAG:T:454 = RESULT:3
		CFLAG:T:455 = RESULT:4
	ENDIF
ENDIF

RETURN 1
;
;-------------------------------------------------
;母乳体质化
;-------------------------------------------------
@MODIFY_BONYU
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 50000

IF MONEY < C
	PRINTW 有钱的孩子才有奶喝
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 活化改在对象的乳腺，让她分泌母乳。
PRINTL 要将谁母乳化？

DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:122
	PRINTFORMW 【%TALENTNAME:122%】不能改造母乳体质。
	RETURN 0
ELSEIF TALENT:RESULT:109 || TALENT:RESULT:116
	PRINTFORMW 死心吧，这么小的胸，要挤奶也无从下手。
	RETURN 0
ELSEIF TALENT:RESULT:130
	PRINTFORMW %SAVESTR:RESULT%已经是母乳体质了。
	RETURN 0
ENDIF
	
;SIF RESULT == 1001 || RESULT == 1000
;	GOTO INPUT_LOOP

PRINTFORML 将%SAVESTR:RESULT%母乳体质化吗？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%的母乳，现在随时为你待命了》
	TALENT:T:130 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
CALL N_BREAST_GROW, T
RETURN 1
;
;-------------------------------------------------
;フタナリ化
;-------------------------------------------------
@MODIFY_FUTANARI
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 50000

IF MONEY < C
	PRINTW 这么穷，就不要这么变态啦。
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 将改造对象的阴蒂给阳具化，
PRINTL 在卵巢内加入精囊一样的组织。
PRINTL 要将谁扶她化？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:121 || TALENT:RESULT:122
	PRINTFORMW %SAVESTR:RESULT%已经有兵器了。
	RETURN 0
ENDIF

;SIF RESULT == 1001 || RESULT == 1000
;	GOTO INPUT_LOOP

T = RESULT

PRINTFORML 给%SAVESTR:RESULT%怎么样的阳具呢？？

PRINTL  [0] - 普通
PRINTL  [1] - 巨根
PRINTL  [2] - 短小包茎
PRINTL  [3] - 包茎
PRINTL  [4] - 马阴茎
PRINTL  [999] - 停止

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT >= 0 && RESULT <= 4
	PRINTFORMW 《%SAVESTR:T%获得【%TALENTNAME:121%】了》
	PRINT 阴茎的状态：
	IF RESULT == 1
		PRINTW 《巨根》
	ELSEIF RESULT == 2
		PRINTW 《短小包茎》
	ELSEIF RESULT == 3
		PRINTW 《包茎》
	ELSEIF RESULT == 4
		PRINTW 《馬阴茎》
	ELSE
		PRINTW 《普通》
	ENDIF
	TALENT:T:121 = 1
	TALENT:T:326 = 0
	TALENT:T:318 = RESULT
	TALENT:T:1 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;フタナリ消去
;-------------------------------------------------
@MODIFY_FUTANARI_ERASE
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000

IF MONEY < C
	PRINTW 钱不够，快快去挣钱
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 扶她消去
PRINTL 要消去谁的扶她呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:122
	PRINTFORMW 本作暂不提供阉割功能！
	RETURN 0
ELSEIF TALENT:RESULT:121 == 0
	PRINTFORMW 没有可消去的阴茎
	RETURN 0
ENDIF

;SIF RESULT == 1001 || RESULT == 1000
;	GOTO INPUT_LOOP

T = RESULT

PRINTFORML 将%SAVESTR:RESULT%的阴茎消去么？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%的【%TALENTNAME:121%】消去了》
	TALENT:T:121 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;动物耳朵を付ける
;-------------------------------------------------
@MODIFY_ANIMAL
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 2000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 将用动物的遗传因子覆盖改造对象的一部分遗传因子。
PRINTL 赋予谁动物耳朵呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:124
	PRINTFORMW %SAVESTR:RESULT%已经有动物耳朵了。
	RETURN 0
ENDIF

;SIF RESULT == 1001 || RESULT == 1000
;	GOTO INPUT_LOOP

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%动物耳朵吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不好

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	IF TALENT:T:314 == 2
		PRINTFORMW 《%NAME:T%取得人狼的象征【%TALENTNAME:124%】了！》
	ELSE
		PRINTFORMW 《%NAME:T%长出【%TALENTNAME:124%】了》
	ENDIF
	TALENT:T:124 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;动物耳朵を消去
;-------------------------------------------------
@MODIFY_ANIMAL_ERASE
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 1000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 消去动物耳朵
PRINTL 消去谁的动物耳朵？

DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:124 == 0
	PRINTFORMW 没有动物耳朵给你消去
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 要消去%SAVESTR:RESULT%的动物耳朵吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	IF TALENT:T:314 == 2
		PRINTFORMW 《%NAME:T%失去了人狼的象征【%TALENTNAME:124%】了……》
	ELSE
		PRINTFORMW 《%NAME:T%的【%TALENTNAME:124%】消去了》
	ENDIF
	TALENT:T:124 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;白虎にする
;-------------------------------------------------
@MODIFY_REMOVEHAIR
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 5000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 阴毛长了还分叉？魔王帮你一劳永逸！
PRINTL 帮谁永久脱毛呢？？

DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:125
	PRINTFORMW %SAVESTR:RESULT%本来就是白虎。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 帮%SAVESTR:RESULT%永久脱毛吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%获得【%TALENTNAME:125%】了》
	TALENT:T:125 = 1
	TALENT:T:310 = 1
	TALENT:T:311 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;
;-------------------------------------------------
;未熟を除去
;-------------------------------------------------
@MODIFY_DEIMMATURITY
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 把改造对象的性器发育，可以进行正常性行为。
PRINTL 要消去谁的未熟呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:135 == 0
	PRINTFORMW %SAVESTR:RESULT%不是未熟的人。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 消去%SAVESTR:RESULT%的未熟么？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%的【%TALENTNAME:135%】消去了》
	PRINTFORMW 《%SAVESTR:T%的身体发育了》
	TALENT:T:135 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
;除去未熟后，性征成長，早熟
IF T != 0
	IF TALENT:T:122 || TALENT:T:121
		SIF TALENT:T:318 > 1
			TALENT:T:318 -= RAND:2
		;念のため
		SIF TALENT:T:318 < 0
			TALENT:T:318 = 0
	ENDIF
	IF TALENT:T:122 == 0
		IF GETBIT(FLAG:5,12) || GETBIT(FLAG:5,15)
			;CALL CHAR_BUST_REGENERATE_WAPPED, T
			CALL CHAR_SIZE_GENERATE, T, CFLAG:T:451, 1
			CFLAG:T:454 = RESULT:3
			CFLAG:T:455 = RESULT:4
		ENDIF
	ENDIF
ENDIF
RETURN 1
;
;-------------------------------------------------
;記憶を消去
;-------------------------------------------------
@MODIFY_AMNESIA
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23

C = 100000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 维持改造对象的肉体及感觉不变，
PRINTL 将她的记忆恢复到调教开始之前。
PRINTL 要消除谁的记忆呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0 
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF RESULT == MASTER
	GOTO INPUT_LOOP
ENDIF

D = RESULT

PRINTFORML 要消除%SAVESTR:D%的記憶吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 

	;助手だった場合は解除
	SIF ASSI == D
		ASSI = 0
	SIF FLAG:2 == D
		FLAG:2 = -1

	;ABL初期化
	FOR LOCAL , 0 , 100
		ABL:D:LOCAL = 0
	NEXT

	;EXP初期化しない
	;FOR LOCAL , 0 , 100
	;	EXP:D:LOCAL = 0
	;NEXT

	;MARK初期化
	FOR LOCAL , 0 , 100
		MARK:D:LOCAL = 0
	NEXT

	;JUEL初期化
	FOR LOCAL , 0 , 100
		JUEL:D:LOCAL = 0
	NEXT

	;特殊性癖初期化 自慰狂い（74）～乳狂い（78）
	FOR LOCAL , 74, 79
		TALENT:D:LOCAL = 0
	NEXT

	;陥落系素質の初期化
	TALENT:D:85 = 0		;愛
	TALENT:D:86 = 0		;妄信

	;TALENT:D:76 = 0	;淫乱
	;TALENT:D:180 = 0	;娼婦
	;TALENT:D:181 = 0	;傾城
	;TALENT:D:183 = 0	;常連客
	;TALENT:D:184 = 0	;求愛

	;CFLAGの初期化
	CFLAG:D:0 = 0		;売却及び助手可能
	CFLAG:D:2 = 0		;好感度
	CFLAG:D:10 = 0		;調教経験回数

	PRINTFORML 《%SAVESTR:D%は調教開始後の一切の記憶を失った》

	WAIT

	;妊娠していた場合
	IF TALENT:D:153
		PRINTFORML 彼女本人の感覚からすれば、突然に膨れ上がった自分の腹部を見下ろして、
		PRINTFORML %CALLNAME:D%は呆然としている……
		WAIT
		IF TALENT:D:155 == 0 && TALENT:D:12 == 0
			PRINTFORML %CALLNAME:D%的心中有什么东西坏掉了……
			PRINTFORML %CALLNAME:D%的精神【%TALENTNAME:9%】了
			TALENT:D:9 = 1
			WAIT
		ENDIF
	ENDIF
	;育児中だった場合
	IF TALENT:D:154
		PRINTFORML %CALLNAME:D%猛然发现自己在抚养着婴儿……
		WAIT
		IF TALENT:D:155 || TALENT:D:63
			PRINTFORML 自己的乳房被陌生的婴儿含在嘴里、%CALLNAME:D%露出了不可思议的神情、
			PRINTFORML 但似乎下定了决心、%SAVESTR:D%抱起婴儿，把自己的乳头托到了婴儿的嘴边。
			PRINTFORML 《%SAVESTR:D%继续照顾起了孩子》
			WAIT
		ELSE
			PRINTFORML 自己的乳房被陌生的婴儿含在嘴里、%CALLNAME:D%露出了惊异莫名的神情
			CALL CHILD_CARE_CHANGE_NURSE(C)
			
		ENDIF
	ENDIF

ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;		############## 旧ソース ##############

[SKIPSTART]
P = 100000

IF MONEY < P
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP

PRINTL 维持改造对象的肉体及感觉不变，
PRINTL 将她的记忆恢复到调教开始之前。
PRINTL 要消除谁的记忆呢？

$INPUT_LOOP_MENU
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT < 1 || RESULT >= CHARANUM
	CLEARLINE 1
	GOTO INPUT_LOOP
ELSEIF CFLAG:RESULT:1 != 0 && CFLAG:RESULT:1 != 10
	CLEARLINE 1
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF

C = RESULT

PRINTFORML 确认消去%SAVESTR:C%的记忆吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	
	;清除刻印
	VARSET MARK:C:0, 0, 0, 5
	
	;清除能力
	VARSET ABL:C:0, 0, 10, 40
	
	;清除珠子
	VARSET PALAM:C:0, 0, 3
	;PALAM:C:100 = 0
	
	;清除经验（生产，战斗除外）
	VARSET EXP:C:0, 0, 0, 60
	VARSET EXP:C:0, 0, 61, 80
	
	;初吻，初体验
	CFLAG:C:15 = 0
	CFLAG:C:16 = -1
	CSTR:C:3 =
	CSTR:C:4 =
	
	;调教次数
	CFLAG:C:10 = 0
	
	;好感度
	CFLAG:C:2 = 0
	
	;调教素质
	VARSET TALENT:C:0, 0, 74, 79
	VARSET TALENT:C:0, 0, 230, 234
	TALENT:C:271 = 0
	TALENT:C:272 = 0
	
	;崩坏
	TALENT:C:9 = 0
	
	PRINTFORML 《%SAVESTR:C%失去了调教开始后的一切记忆》

	WAIT

	;妊娠していた場合
	IF TALENT:C:153 
		PRINTFORML %SAVESTR:C%猛然发现自己的肚子鼓胀着…………
		PRINTFORML %SAVESTR:C%的大脑一时之间转不过来，呆住了…………
		WAIT
		IF TALENT:C:155 == 0 && TALENT:C:12 == 0
			PRINTFORML %SAVESTR:C%的心中有什么东西坏掉了……
			PRINTFORML %SAVESTR:C%的精神【%TALENTNAME:9%】了。
			TALENT:C:9 = 1
			WAIT
		ENDIF
	ENDIF
	;育儿中だった場合
	IF TALENT:C:154
		PRINTFORML %SAVESTR:C%猛然发现自己在抚养着婴儿…………
		WAIT
		IF TALENT:C:155 || TALENT:C:63
			PRINTFORML 自己的乳房被陌生的婴儿含在嘴里，%SAVESTR:C%露出了不可思议的神情。
			PRINTFORML 但似乎下定了决心，%SAVESTR:C%抱起婴儿，把自己的乳头托到了婴儿的嘴边。
			PRINTFORML 《%SAVESTR:C%继续照顾起了孩子》
			WAIT
		ELSE
			PRINTFORMW 自己的乳房被陌生的婴儿含在嘴里，%SAVESTR:C%露出了惊异莫名的神情。
			CALL CHILD_CARE_CHANGE_NURSE(C)
			
		ENDIF
	ENDIF

ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= P
EX_FLAG:4444 -= P
RETURN 1

[SKIPEND]
;		############## 旧ソース ここまで ##############

;
;-------------------------------------------------
;母乳体质を消去
;-------------------------------------------------
@MODIFY_BONYU_ERASE
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 乳腺的退化改造。
PRINTL 要消去谁的母乳体质呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:130 == 0
	PRINTFORMW 没有母乳体质
	RETURN 0
ELSEIF TALENT:RESULT:153
	PRINTFORMW 孕妇无法消去母乳体质
	RETURN 0
ELSEIF TALENT:RESULT:154
	PRINTFORMW 育儿中的女人无法消去母乳体质
	RETURN 0
ENDIF

PRINTFORML 消去%SAVESTR:RESULT%的母乳体质么？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%不再分泌母乳了》
	TALENT:T:130 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
CALL N_BREAST_REVERSE, T
RETURN 1
;
;-------------------------------------------------
;漏尿癖を消去
;-------------------------------------------------
@MODIFY_OMORASHI_ERASE
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 治疗漏尿的问题
PRINTL 消除谁的漏尿癖？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:57 == 0
	PRINTFORMW 没有漏尿癖
	RETURN 0
ENDIF

PRINTFORML 消去%SAVESTR:RESULT%的漏尿癖吗？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%的漏尿癖被治好了》
	TALENT:T:57 = 0
	EXP:T:31 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;处女膜再生
;-------------------------------------------------
@SHOJO_SAISEI
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 100000
;金がなければ終了
IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 生命没有第二次，但处女膜可以。
PRINTFORML 再生谁的处女膜？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
;【男人】は除外
ELSEIF TALENT:RESULT:122
	PRINTW 怎么看都是不可能的了，真的谢谢了。
	RETURN 0
;【处女】は除外
ELSEIF TALENT:RESULT:0
	PRINTFORM %SAVESTR:RESULT%本来就是处女。
	RETURN 0
ENDIF

PRINTFORML 再生%SAVESTR:RESULT%的处女膜吗？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%获得了【%TALENTNAME:0%】》
	TALENT:T:0 = 1
	CFLAG:T:71 += 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;-------------------------------------------------
;貞操封印
;-------------------------------------------------
@SHOJO_SEAL
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000
;金がなければ終了
IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 封印对象的性器。
PRINTFORML 要封印谁的性器呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
;封印済みは除外
ELSEIF TALENT:RESULT:273
	PRINTFORM %SAVESTR:RESULT%的性器已经被封印了。
	RETURN 0
ENDIF

PRINTFORML 封印%SAVESTR:RESULT%的性器吗？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%获得了【%TALENTNAME:273%】》
	TALENT:T:273 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;-------------------------------------------------
;貞操封印解除
;-------------------------------------------------
@SHOJO_SEAL_OFF
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000
;金がなければ終了
IF MONEY < C
	PRINTW 穷鬼玩啥处女啊！
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 解除对象的性器封印
PRINTFORML 要解除谁的封印呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
;封印済みは除外
ELSEIF TALENT:RESULT:273 == 0
	PRINTFORM %SAVESTR:RESULT%本来就没有被封印。
	RETURN 0
ENDIF

PRINTFORML 解除%SAVESTR:RESULT%的封印吗？

T = RESULT

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORMW 《%SAVESTR:T%的【%TALENTNAME:273%】失去了》
	TALENT:T:273 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;
;-------------------------------------------------
;刺青
;-------------------------------------------------
@TATOO_SET_OFF
#DIMS TATOO_NAME,20
#DIM COST
#DIM TATOO_COUNT
#DIM TATOO_TARGET
#DIM TATOO_SELECT
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23

;TATOO_TARGET = 刺青を受けるひと
;TATOO_SELECT = 選ばれた刺青

;刺青を入れる場所リスト。CSTRの場所に対応
TATOO_NAME:10 = 脸
TATOO_NAME:11 = 胸
TATOO_NAME:12 = 背
TATOO_NAME:13 = 下腹
TATOO_NAME:14 = 屁股
TATOO_NAME:15 = 性器
TATOO_NAME:16 = 肛门
TATOO_NAME:17 = 大腿

COST = 10000
;金がなければ終了
IF MONEY < COST
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 消除对象的刺青
PRINTFORML 要消除谁的刺青？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ENDIF

PRINTFORML %SAVESTR:RESULT%的刺青
PRINTL  

TATOO_TARGET = RESULT


FOR TATOO_COUNT,10, 20
	SIF TATOO_NAME:TATOO_COUNT != ""
		PRINTFORML  [{TATOO_COUNT}] %TATOO_NAME:TATOO_COUNT%	 - 『%CSTR:TATOO_TARGET:TATOO_COUNT%』
NEXT

PRINTL  [999] - 停止

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF TATOO_NAME:RESULT != ""
	PRINTFORML %TATOO_NAME:RESULT%的刺青被处理了。
ELSE
	GOTO INPUT_LOOP
ENDIF

TATOO_SELECT = RESULT

PRINTFORML 请自由输入想雕刻的刺青，留空代表将要被消去。
PRINTFORM 现在雕刻的刺青是：

IF CSTR:TATOO_TARGET:TATOO_SELECT != ""
	PRINTFORML 『%CSTR:TATOO_TARGET:TATOO_SELECT%』。
ELSE
	PRINTFORML 没有
ENDIF

INPUTS

IF RESULTS != ""
	PRINTFORMW 在%TATOO_NAME:TATOO_SELECT%雕刻『%RESULTS%』刺青吗？
ELSE
	PRINTFORMW 消去%TATOO_NAME:TATOO_SELECT%的刺青吗？
ENDIF

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	IF RESULTS != ""
		PRINTFORMW 《%TATOO_NAME:TATOO_SELECT%上雕刻了『%RESULTS%』的刺青》
	ELSE
		PRINTFORMW 《%TATOO_NAME:TATOO_SELECT%的刺青消去了》
	ENDIF
ELSE
	GOTO INPUT_LOOP
ENDIF

CSTR:TATOO_TARGET:TATOO_SELECT = %RESULTS%


MONEY -= COST
EX_FLAG:4444 -= COST
RETURN 1

;-------------------------------------------------
;头发颜色を変える
;-------------------------------------------------
@MODIFY_HAIR_COLOR
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
#DIM COL

C = 5000
IF MONEY < C
	PRINTW 金钱不足
	RETURN 0
ENDIF

DO
	$INPUT_LOOP
	CUSTOMDRAWLINE =
	PRINTL 想给谁改变头发颜色？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
ENDIF
LOOP !INRANGE(RESULT, 0, CHARANUM - 1) || BASE:RESULT:0 < 1
T = RESULT

DO
	PRINTFORML 变成什么颜色？（現在：%GET_LOOK_INFO(T, "头发颜色")%）
	PRINTL [0] 金色
	PRINTL [1] 栗色
	PRINTL [2] 黒色
	PRINTL [3] 红色
	PRINTL [4] 银色
	PRINTL [5] 蓝色
	PRINTL [6] 绿色
	PRINTL [999] - 算了，居然没有光头……
	INPUT
	SIF RESULT == 999
		RESTART
LOOP !INRANGE(RESULT, 0, 6)
COL = RESULT + 1

DO
	SWAP TALENT:T:头发颜色, COL
	PRINTFORML 要将%SAVESTR:T%的头发变成%GET_LOOK_INFO(T, "头发颜色")%吗？
	PRINTL  [0] - 确定
	PRINTL  [1] - 取消
	SWAP TALENT:T:头发颜色, COL
	INPUT
	SIF RESULT == 1
		RETURN 0
LOOP RESULT != 0

TALENT:T:头发颜色 = COL
PRINTFORMW 《%SAVESTR:T%的发色变成【%GET_LOOK_INFO(T, "头发颜色")%】了》

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;-------------------------------------------------
;肌の色を変える
;-------------------------------------------------
@MODIFY_SKIN_COLOR
#DIM COL
#DIMS CURRENT
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23

C = 5000
IF MONEY < C
	PRINTW 没钱还想整容？！
	RETURN 0
ENDIF

DO
	$INPUT_LOOP
	CUSTOMDRAWLINE =
	PRINTL 给谁变换肤色？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
ENDIF
LOOP !INRANGE(RESULT, 0, CHARANUM - 1) || BASE:RESULT:0 < 1
T = RESULT

IF TALENT:T:白皙
	CURRENT = 白皙
ELSEIF TALENT:T:褐色肌肤
	CURRENT = 褐色肌肤
ELSEIF TALENT:T:恶魔肌肤
	CURRENT = 恶魔肌肤
ELSE
	CURRENT = 普通肤色
ENDIF

LOCALS:0 = 普通肌肤
LOCALS:1 = 白皙
LOCALS:2 = 褐色肌肤
DO
	PRINTFORML 想要变成什么肤色？（現在：%CURRENT%）
	REPEAT 3
		PRINTFORML [{COUNT}] %LOCALS:COUNT%
	REND
	PRINTL [999] - 取消
	INPUT
	SIF RESULT == 999
		RESTART
LOOP !INRANGE(RESULT, 0, 2)
COL = RESULT
DO
	PRINTFORML 将%SAVESTR:T%变为%LOCALS:COL%吗？
	PRINTL  [0] - 确定
	PRINTL  [1] - 取消
	INPUT
	SIF RESULT == 1
		RETURN 0
LOOP RESULT != 0

TALENT:T:白皙 = 0
TALENT:T:褐色肌肤 = 0
TALENT:T:恶魔肌肤 = 0
IF COL == 1
	TALENT:T:白皙 = 1
ELSEIF COL == 2
	TALENT:T:褐色肌肤 = 1
ENDIF

PRINTFORMW 《%SAVESTR:T%的%CURRENT%变成%LOCALS:COL%了》

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;
;-------------------------------------------------
;感覚封鎖
;-------------------------------------------------
@BLOCK_FEELING
#DIM CID
#DIM PID
#DIM PAID
#DIM FLAG_B
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23

C = 20000
IF MONEY < C
	PRINTW 金钱不足
	RETURN 0
ENDIF

$CHAR_TOP
CUSTOMDRAWLINE =
PRINTFORML 感觉封锁，被封锁的部位，感觉被锁定。其它性感带的感觉提升。
PRINTFORML 封锁谁的感觉？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO CHAR_TOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO CHAR_TOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO CHAR_TOP
ENDIF

CID = RESULT

$PART_TOP

PRINTFORML 封锁%SAVESTR:CID%哪个部位？
PRINTL 

FLAG_B = TALENT:CID:101 & 2
SIF FLAG_B
	SETCOLOR 128, 128, 128
PRINTFORML 　[\@ FLAG_B ? - # 0 \@] 阴核感觉　\@ FLAG_B ? 已经封锁 #  \@
RESETCOLOR

FLAG_B = TALENT:CID:103 & 2
SIF FLAG_B
	SETCOLOR 128, 128, 128
PRINTFORML 　[\@ FLAG_B ? - # 1 \@] 私处感觉　\@ FLAG_B ? 已经封锁 #  \@
RESETCOLOR

FLAG_B = TALENT:CID:105 & 2
SIF FLAG_B
	SETCOLOR 128, 128, 128
PRINTFORML 　[\@ FLAG_B ? - # 2 \@] 肛门感觉　\@ FLAG_B ? 已经封锁 #  \@
RESETCOLOR

FLAG_B = TALENT:CID:107 & 2
SIF FLAG_B
	SETCOLOR 128, 128, 128
PRINTFORML 　[\@ FLAG_B ? - # 3 \@] 乳房感觉　\@ FLAG_B ? 已经封锁 #  \@
RESETCOLOR

PRINTL 
PRINTL 　[9] 选择角色
DRAWLINE
PRINTL  [999] - 取消

$INPUT_LOOP2

INPUT

PID = RESULT
PAID = PID
SIF PID >= 1 && PID < 3
	PAID += 1
SIF PID == 3
	PAID = 1

IF PID >= 0 && PID < 4
	IF ABL:CID:PAID > 0
		PRINTL 已经超过LV1以上的部位无法封锁
		WAIT
		GOTO PART_TOP
	ENDIF
	IF TALENT:CID:(PID * 2 + 101) & 2
		PRINTL 已经封锁过了
		WAIT
		GOTO PART_TOP
	ENDIF
	PRINTL 　　此项改造属于小白鼠专用，魔王表示一但实行就无法逆转
	PRINTL 　　确定要执行？
	PRINTL 
	PRINTL 　　　[0] - 走你！
	PRINTL 　　　[1] - 容我三思……

	INPUT

	IF RESULT == 0
		TALENT:CID:(PID * 2 +101) |= 2
		MONEY -= C
		EX_FLAG:4444 -= C
		PRINTFORML 　　《%SAVESTR:CID%的\@ PID == 0 ? 阴核 #\@\@ PID == 1 ? 私处 #\@\@ PID == 2 ? 肛门 #\@\@ PID == 3 ? 乳房 #\@被封锁了》
		WAIT
		PRINTL 
	ELSE
	ENDIF
	SIF MONEY >= C
		GOTO PART_TOP
ELSEIF RESULT == 9
	GOTO CHAR_TOP
ELSEIF RESULT == 999
	RETURN 0

ENDIF


;
;-------------------------------------------------
;性転換
;-------------------------------------------------
@TRANS_SEX
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 200000
IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 更改对象的性别。
PRINTFORML 一但换了，就不能再换回原来的了。除非是魔王。
PRINTFORML 要更换谁的性别？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
;既に性転換済のキャラは除外
ELSEIF CFLAG:RESULT:70 && RESULT != MASTER
	PRINTFORMW %SAVESTR:RESULT%已经被转性过了。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 更改%SAVESTR:RESULT%的性别吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	;男人ならそれを外す
	IF TALENT:T:122
		PRINTFORML 《%SAVESTR:T%的【%TALENTNAME:122%】被消去了》
		TALENT:T:122 = 0
		TALENT:T:0 = 1
		TALENT:T:1 = 0
	;男人でなければ男人をつける
	ELSE
		;扶她ならそれを外す
		IF TALENT:T:121
			PRINTFORML 《%SAVESTR:T%的【%TALENTNAME:121%】被消去了》
			TALENT:T:121 = 0
		ENDIF
		PRINTFORML 《%SAVESTR:T%获得【%TALENTNAME:122%】了》
		TALENT:T:122 = 1
		TALENT:T:0 = 0
		TALENT:T:1 = 1
	ENDIF
	Z = 1
	PRINTFORMW %EXPNAME:50%＋{Z}
	EXP:T:50 += Z
	CFLAG:T:70 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;洗脳
;-------------------------------------------------
;C:費用
;B:追加する素質
;-------------------------------------------------
@BRAIN_WASHING
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 外部改写洗脑对象的深层心理。
PRINTFORML 给谁附加【%TALENTNAME:B%】？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:B
	PRINTFORMW %SAVESTR:RESULT%已经有【%TALENTNAME:B%】了。
	RETURN 0
ELSEIF RESULT != 0 && CFLAG:RESULT:0 < 2
	PRINTFORMW 不能洗脑不可做助手的角色。
	RETURN 0
ELSEIF TALENT:RESULT:121 == 0 && TALENT:RESULT:122 == 0 && B == 133
	PRINTFORMW 不是【%TALENTNAME:122%】和【%TALENTNAME:121%】不能附加该素质，没有相应设备。
	RETURN 0
ELSEIF TALENT:RESULT:152
	PRINTFORMW 【%TALENTNAME:152%】的人不能被洗脑。
	RETURN 0
ELSEIF RESULT == MASTER
	GOTO INPUT_LOOP
ENDIF

T = RESULT

PRINTFORML 为%SAVESTR:RESULT%附加【%TALENTNAME:B%】吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0 
	PRINTFORML %SAVESTR:T%获得【%TALENTNAME:B%】了。
	TALENT:T:B = 1
	WAIT
ELSE
	GOTO INPUT_LOOP
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1
;
;-------------------------------------------------
;触手生物の購入
;-------------------------------------------------
@BOUGT_TENTACLES
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 50000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML %ITEMNAME:90%是身体改造失败的性奴隶的悲催下场……
PRINTFORML 　购买%ITEMNAME:90%吗？
DRAWLINE
PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 0
	PRINTFORML 《%ITEMNAME:90%入手了》
	ITEM:90 = 1
	MONEY -= C
	EX_FLAG:4444 -= C
	WAIT
	RETURN 1
ELSEIF RESULT == 1
	RETURN 0
ENDIF

GOTO INPUT_LOOP
;
;-------------------------------------------------
;人間としての命を与える
;-------------------------------------------------
@GIVEN_HUMAN_LIFE
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
IF EXP:MASTER:81 <= 0
	PRINTW 人的生命是金钱无法购买的……
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 人的生命是无法直接触摸的
PRINTL 然而某些特殊之人
PRINTL 可以通过转换他人生命的方式为其延长寿命
PRINTL 为谁续命？

	DRAWLINE
	CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
	PRINTLC [1000] - 上一页
	PRINTLC [999] - 取  消
	PRINTLC [1001] - 下一页

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
;【爱慕】がないとダメ
ELSEIF TALENT:RESULT:85 == 0
	PRINTFORMW %SAVESTR:RESULT%已经没有继续留在人间的理由了。
	RETURN 0
ELSEIF BASE:RESULT:10 == 0 && TALENT:RESULT:124 == 0
	PRINTFORMW %SAVESTR:RESULT%已经拥有超长的寿命了
	RETURN 0
ELSEIF BASE:RESULT:10 == 0
	PRINTFORML %SAVESTR:RESULT%并非人类、
	PRINTL 作为她那个种族的特性，原本已经远超人类的寿命
	PRINT 已经无法再延长了
ENDIF

T = RESULT

PRINTFORML 为%SAVESTR:RESULT%延长寿命？

PRINTL  [0] - 你一秒，我一秒，蛤蛤蛤蛤…………
PRINTL  [1] - 算了，水表还没换

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	IF TALENT:T:124
		PRINTFORML %SAVESTR:T%的寿命被延长了
		TALENT:T:124 = 0
	ENDIF
	IF BASE:T:10 > 0
		PRINTFORML %SAVESTR:T%的寿命还有很久，无需延长。
		BASE:T:10 = 0
	ENDIF
	WAIT
	;口上
	TFLAG:13 = 15
	CALL SELF_KOJO
ELSE
	GOTO INPUT_LOOP
ENDIF

PRINTFORML 《失去了【%TALENTNAME:398%】》
EXP:MASTER:81 = 0
WAIT

RETURN 1
;
;-------------------------------------------------
;死者を甦らせる
;-------------------------------------------------
@RESULECTION
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
IF EXP:MASTER:81 <= 0
	PRINTW 人的生命可是无法购买的……
	RETURN 0
ENDIF

;一度に所有できる奴隷はEXTRA30人、それ以外は10人まで
IF CHARANUM > 30
	PRINTW 这个世界好像已经没有亡者容身之所了
	RETURN 0
ELSEIF FLAG:5 != 9 && CHARANUM > 10
	PRINTW 这个世界好像已经没有亡者容身之所了
	RETURN 0
ENDIF

D = 0
REPEAT 100
	C = COUNT + 1000
	SIF FLAG:C < 0
		D += 1
REND
IF D == 0
	PRINTW 找不到想要唤醒的人
	RETURN 0
ENDIF

$INPUT_LOOP_00
CUSTOMDRAWLINE =
PRINTL 过去从这个世界上消失和逝去的人，
PRINTL 所有的记忆都将被忘记，
PRINTL 好似重获新生一般出现在你面前。
PRINTL ……这样的结果，是你想要的吗？
DRAWLINE
PRINTL  [0] - 确定
PRINTL  [1] - 取消

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT != 0
	GOTO INPUT_LOOP_00
ENDIF

$INPUT_LOOP_01
DRAWLINE
PRINTL 想要苏醒谁？
DRAWLINE
REPEAT 100
	C = COUNT + 1000
	D = COUNT + 100
	SIF FLAG:C <= -2
		PRINTFORML  [{D}] - %ITEMNAME:D%
REND

PRINTL  [999] - 取消

INPUT

D = RESULT - 99
C = RESULT + 900

IF RESULT == 999
	RETURN 0
ELSEIF RESULT < 100 || RESULT > 100 + 99
	GOTO INPUT_LOOP_01
ELSEIF FLAG:C < 0
	ADDCHARA D
	CALL ADDCHARA_EX, CHARANUM-1
	;蘇生したキャラには購入フラグを立てる
	FLAG:C = -1
	C = CHARANUM - 1
	PRINTFORML 《%SAVESTR:C%被从彼岸召唤回来了》

	;主人公を変更している場合の処理
	TARGET = C
	;CALL SPECIAL_MASTER_CHECK

	WAIT
ELSE
	GOTO INPUT_LOOP_01
ENDIF

PRINTFORML 《获得了【%TALENTNAME:398%】》
EXP:MASTER:81 = 0
WAIT

RETURN 1
;
;-------------------------------------------------
;正気に戻す
;-------------------------------------------------
@CURE_INSANE
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
IF EXP:MASTER:81 <= 30
	PRINTW 勋章，是最好的药啊魔王大人！
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 也许对这些动不动就坏掉的脆弱女孩来说，坏掉反而更幸福吧！
PRINTL 面对这等弱者，没必要浪费时间慢慢哄！食脑魔，给我上！！
PRINTL 消耗三十个勋章，恢复谁的理智？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:9 == 0 && TALENT:RESULT:123 == 0 
	PRINTFORMW %SAVESTR:RESULT%的精神没有崩坏。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 恢复%SAVESTR:RESULT%的理智么？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	IF TALENT:T:9
		PRINTFORMW 《%SAVESTR:T%的瞳孔，再度射出理性的光辉了。》
		TALENT:T:9 = 0
	ENDIF
	IF TALENT:T:123
		PRINTFORMW 《%SAVESTR:T%从无尽的噩梦中苏醒了。》
		TALENT:T:123 = 0
	ENDIF
ELSE
	GOTO INPUT_LOOP
ENDIF

PRINTFORML 《【%TALENTNAME:398%】不见了》
EXP:MASTER:81 -= 30
WAIT

RETURN 1


;
;-------------------------------------------------
;貞操帯のカギを探す
;-------------------------------------------------
@REGET_CHASTITY_KEY
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
IF EXP:MASTER:81 <= 0
	PRINTW 穷……是世上最无可奈何的事…………
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 把那该死的贞操带钥匙找回来！
PRINTL 要寻找谁的贞操带钥匙呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF CFLAG:RESULT:49 == 0
	PRINTFORMW 没有%CALLNAME:RESULT%贞操带钥匙的必要。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 确认寻找%SAVESTR:RESULT%的贞操带钥匙吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%的贞操带钥匙找到了》
	CFLAG:T:49 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF

PRINTFORML 《【%TALENTNAME:398%】不见了》
EXP:MASTER:81 = 0
WAIT

RETURN 1

;----------------------------
@HORN
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 角可是很贵的
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 让奴隶的头上长出犄角
PRINTL 要让谁长出犄角呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:264 == 1
	PRINTFORMW %SAVESTR:RESULT%已经长着犄角了。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 让%SAVESTR:RESULT%长出犄角吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%长出了犄角》
	TALENT:T:264 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@EVILAPP
;----------------------------
CUSTOMDRAWLINE =
PRINTFORML 要进行什么样的恶魔改造呢？？
DRAWLINE
PRINTL  [1] - 恶魔的蓝色肌肤
PRINTL  [2] - 恶魔的翅膀
PRINTL  [3] - 恶魔的尾巴
PRINTL  [4] - 恶魔的眼睛
PRINTLC [999] - 返  回
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1
	CALL BLUESKIN
ELSEIF RESULT == 2
	CALL EVILWING
ELSEIF RESULT == 3
	CALL EVILTAIL
ELSEIF RESULT == 4
	CALL EVILSIGHT
ENDIF

;----------------------------
@BLUESKIN
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF


$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 改造成为恶魔的蓝色肌肤
PRINTL 赋予谁蓝色肌肤？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:244 == 1
	PRINTFORMW %SAVESTR:RESULT%的肌肤已经是蓝色的了。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%恶魔肌肤吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	IF TALENT:T:253 == 1 || TALENT:T:255 == 1
		PRINTFORMW 《%SAVESTR:T%的\@(TALENT:T:253 == 1) ? 褐色肌肤 # 白皙\@变成恶魔肌肤了》
	ELSE
		PRINTFORMW 《%SAVESTR:T%获得恶魔般的肌肤了》
	ENDIF
	TALENT:T:244 = 1
	TALENT:T:253 = 0
	TALENT:T:255 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@EVILWING
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 给对象赋予恶魔的翅膀。
PRINTL 给谁赋予恶魔的翅膀呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:245 == 1
	PRINTFORMW %SAVESTR:RESULT%已经长着恶魔的翅膀了。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%恶魔的翅膀吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%长出了恶魔的翅膀》
	TALENT:T:245 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1


;----------------------------
@EVILTAIL
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 赋予对象恶魔的尾巴。
PRINTL 要赋予谁恶魔的尾巴呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:246 == 1
	PRINTFORMW %SAVESTR:RESULT%已经长着恶魔的尾巴了。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%恶魔的尾巴？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%长出恶魔的尾巴了》
	TALENT:T:246 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@EVILSIGHT
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 赋予对象恶魔的眼睛。
PRINTL 要赋予谁恶魔的眼睛呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:247 == 1
	PRINTFORMW %SAVESTR:RESULT%已经有恶魔的眼睛了。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%恶魔的眼睛？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%获得了恶魔的眼睛》
	TALENT:T:247 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@DEMON_REBIRTH
;----------------------------
#DIM L_I
#DIM L_T
#DIM L_ID
#DIM TYPEDATA,12,10
#DIM COND, 12
#DIMS CONDDESCS, 12, 6
#DIM TYPEN
#DIMS TYPENAME
#DIM L_COUNT
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
#DIM TEMP
C = 50000

IF MONEY < C
	PRINTW 没钱还想换壳？
	RETURN 0
ENDIF

L_COUNT = LINECOUNT
$DRAW_PAGE
CLEARLINE LINECOUNT - L_COUNT
CUSTOMDRAWLINE =
PRINTL 转生的秘法将会抽取对象的灵魂，将其投入魔族的身体
PRINTL 抽取谁的灵魂？
DRAWLINE 
;PRINTL [0] 你
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO DRAW_PAGE
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO DRAW_PAGE
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO DRAW_PAGE
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO DRAW_PAGE
;ELSEIF TALENT:RESULT:种族 == 9
;	PRINTFORMW %SAVESTR:RESULT%已经是魔族了
;	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

IF TYPEDATA:0:0 <= 0
	;男巫/女巫/男祭司/女忍/黑暗骑士/女祭司/忍者
	TYPEDATA:0:0 = 133,143,153,163,160,170
	;丧尸/丧尸虫/丧尸猎犬/吸血鬼/死亡领主
	TYPEDATA:1:0 = 104,113,114,172,181
	;兽人/巨魔/食人魔/巨人
	TYPEDATA:2:0 = 110,130,121,150
	;哥布林/熊地精/小仙子/报丧女妖/小精灵/无头骑士
	TYPEDATA:3:0 = 101,111,123,134,164,171
	;狗头人/蜥蜴人
	TYPEDATA:4:0 = 100,120
	;黏液怪/石像鬼/史莱姆/幽灵/眼魔
	TYPEDATA:5:0 = 102,131,142,173,184
	;食铠者/地狱猎犬/奇美拉/食脑魔/梦魇
	TYPEDATA:6:0 = 122,141,151,162,183
	;龙头苍蝇/毒蜈蚣/死亡蝎
	TYPEDATA:7:0 = 103,124,174
	;藤蔓怪/吸血树
	TYPEDATA:8:0 = 112,144
	;小恶魔/下等恶魔/大恶魔
	TYPEDATA:9:0 = 132,140,180
	;夜少女/魅魔/莉莉丝
	TYPEDATA:10:0 = 154,152,182
	
	VARSET COND, 1
	SIF !TALENT:T:恶魔肌肤
		COND:1 = 0
	SIF !TALENT:T:魁梧
		COND:2 = 0
	SIF (TALENT:T:恶魔肌肤 && TALENT:T:恶魔眼睛 && TALENT:T:恶魔翅膀 && TALENT:T:恶魔尾巴) == 0
		COND:9 = 0
	SIF (TALENT:T:恶魔肌肤 && TALENT:T:恶魔眼睛 && TALENT:T:恶魔翅膀 && TALENT:T:恶魔尾巴 && TALENT:T:淫乱) == 0
		COND:10 = 0

	CONDDESCS:1:1 = [恶魔肌肤]
	CONDDESCS:2:1 = [魁梧]
	CONDDESCS:9:1 = [恶魔肌肤]
	CONDDESCS:9:2 = [恶魔眼睛]
	CONDDESCS:9:3 = [恶魔翅膀]
	CONDDESCS:9:4 = [恶魔尾巴]
	CONDDESCS:9:0 = %CONDDESCS:9:1%%CONDDESCS:9:2%%CONDDESCS:9:3%%CONDDESCS:9:4%
	CONDDESCS:10:1 = %CONDDESCS:9:1%
	CONDDESCS:10:2 = %CONDDESCS:9:2%
	CONDDESCS:10:3 = %CONDDESCS:9:3%
	CONDDESCS:10:4 = %CONDDESCS:9:4%
	CONDDESCS:10:5 = [淫乱]
	CONDDESCS:10:0 = %CONDDESCS:9:0%%CONDDESCS:10:5%
ENDIF

DRAWLINE
IF TALENT:T:种族 != 9 && TALENT:T:现种族 > 0
	PRINTFORML 要让%SAVESTR:T%（%ITEMNAME:(TALENT:T:现种族)%）转生为何种魔族？
ELSE
	PRINTFORML 要让%SAVESTR:T%转生为何种魔族？
ENDIF
DRAWLINE
LOCAL = 0
FOR L_T, 0, 11
	FOR L_I, 0, 10
		L_ID = TYPEDATA:L_T:L_I
		SIF L_ID <= 0
			BREAK
		SIF TALENT:T:现种族 == L_ID
			CONTINUE
		SIF LOCAL % PRINTCPERLINE() == 0
			PRINT  
			
		SIF !COND:L_T || CFLAG:T:9 < ITEMPRICE:L_ID / 20
			SETCOLORBYNAME GRAY
		PRINTFORMLC [{L_T*10 + L_I,3}] %ITEMNAME:L_ID%
		RESETCOLOR
		
		LOCAL ++
		SIF LOCAL % PRINTCPERLINE() == 0
			PRINTL
	NEXT
NEXT
SIF !LINEISEMPTY()
	PRINTL
DRAWLINE
PRINTL [999] 返回


$INPUT_LOOP_1
INPUT 999

SIF RESULT == 999
	RESTART

L_T = RESULT / 10
L_I = RESULT % 10
L_ID = TYPEDATA:L_T:L_I

IF L_ID <= 0
	CLEARLINE 1
	GOTO INPUT_LOOP_1
ELSEIF !COND:L_T || CFLAG:T:9 < ITEMPRICE:L_ID / 20
	PRINTFORM %SAVESTR:T%必须达到Lv{ITEMPRICE:L_ID / 20}（当前Lv{CFLAG:T:9}）
	IF !COND:L_T
		PRINTFORM 且具备
		FOR COUNT, 1, 5
			TEMP = 243+COUNT
			SIF (L_T >= 9 && !TALENT:T:TEMP)|| L_T < 9
				SETCOLORBYNAME GRAY
			PRINTFORM %CONDDESCS:L_T:COUNT%
			RESETCOLOR
		NEXT
		IF L_T == 10
			SIF TALENT:T:76 == 0
				SETCOLORBYNAME GRAY
			PRINTFORM %CONDDESCS:L_T:5%
			RESETCOLOR
		ENDIF
	ENDIF
	WAIT
	CLEARLINE 2
	GOTO INPUT_LOOP_1
ELSEIF TALENT:T:现种族 == L_ID
	PRINTFORMW %SAVESTR:T%已经是%ITEMNAME:L_ID%了
	RETURN 0
ENDIF

TYPEN = L_ID
TYPENAME = %ITEMNAME:L_ID%


$INPUT_LOOP

PRINTFORML 确定要将%SAVESTR:T%转生为%TYPENAME%吗？
PRINTL  [0] - 确定
PRINTL  [1] - 取消

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	SIF TALENT:T:种族 != 9
		TALENT:T:原种族 = TALENT:T:种族
	TALENT:T:种族 = 9
	TALENT:T:现种族 = TYPEN
	PRINTFORMW 浓郁的魔力充斥四周………
	PRINTFORMW %SAVESTR:T%的转生仪式成功了，已经转生为
	PRINTFORMW 《【魔族中的%ITEMNAME:(TALENT:T:现种族)%】了》

	;付加素質
	;転生を繰り返して素質収集が出来ないようとりあえず限定的に
	SELECTCASE TYPENAME
	CASE "狗头人", "丧尸猎犬", "地狱猎犬", "奇美拉", "梦魇"
		CALL LABO_DR_GET_TALENT, T, 124, "头上长出【動物耳】了！"
	CASE "魅魔", "莉莉丝"
		CALL LABO_DR_GET_TALENT, T, 91, "的肉体上散发出致命的诱惑，获得了【魅惑】……"
;追加してもいいかわからない
[SKIPSTART]
	CASE "小仙子", "小精灵"
		CALL LABO_DR_GET_TALENT, T, 263, "的身体缩小、变成【小人体型】了"
	CASE "黏液怪", "史莱姆"
		CALL LABO_DR_GET_TALENT, T, 261, "的身体，变成黏稠的【史莱姆】了……"
	CASE "食铠者", "食脑魔", "藤蔓怪", "吸血树"
		CALL LABO_DR_GET_TALENT, T, 262, "的身上，长出了【触手】……"
	CASE "女忍", "忍者"
		CALL LABO_DR_GET_TALENT, T, 251, "学会了隐藏在影子暗处。获得了【忍术】！"
	CASE "男祭司", "女祭司"
		CALL LABO_DR_GET_TALENT, T, 242, "得到了邪神的眷顾，获得了【法术】！"
	CASE "女巫", "男巫"
		CALL LABO_DR_GET_TALENT, T, 241, "学会了操控魔力，获得了【魔术】！"
	CASE "黑暗骑士"
		CALL LABO_DR_GET_TALENT, T, 240, "获得了暗黑之力，学会了【战术】！"
[SKIPEND]
	ENDSELECT

	;头发颜色
	SIF RAND:3 == 0
		CALL LABO_DR_CHANGE_HAIR_COLOR, T, (RAND:7 + 1)

ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

@LABO_DR_GET_TALENT, ARG, ARG:1, ARGS
IF !TALENT:ARG:(ARG:1)
	TALENT:ARG:(ARG:1) = 1
	PRINTL
	PRINTFORMW %SAVESTR:ARG%%ARGS%
ENDIF

@LABO_DR_CHANGE_HAIR_COLOR, ARG, ARG:1
IF TALENT:ARG:头发颜色 != ARG:1
	PRINTFORM %SAVESTR:ARG%的头发颜色从%GET_LOOK_INFO(ARG, "头发颜色")%
	TALENT:ARG:头发颜色 = ARG:1
	PRINTFORMW 变成%GET_LOOK_INFO(ARG, "头发颜色")%了
ENDIF


;----------------------------
@SOULBOUND
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 10000

IF MONEY < C
	PRINTW 金钱不足
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 魂缚实施之后，目标在心智上将不会再有半点动摇，
PRINTL 各种刻印和好感度的增减都会被锁定，
PRINTL 要对谁施展？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 1 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:魂缚
	PRINTFORMW %SAVESTR:RESULT%的灵魂已经被束缚过了
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML %SAVESTR:RESULT%施展魂缚吗？

PRINTL  [0] - 确定
PRINTL  [1] - 取消

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%的灵魂》
	TALENT:T:魂缚 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@SOULBOUND_ERASE
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 50000

IF MONEY < C
	PRINTW 金钱不足
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 将目标从魂缚的状态解除
PRINTL 要选谁做为目标呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 1 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF !TALENT:RESULT:魂缚
	PRINTFORMW %SAVESTR:RESULT%的灵魂没有被束缚
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML %SAVESTR:RESULT%的魂缚状态解除吗？

PRINTL  [0] - 确定
PRINTL  [1] - 取消

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%的魂缚状态解除了》
	TALENT:T:魂缚 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@ENCHARMED_ERASE
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 50000

IF MONEY < C
	PRINTW 金钱不足
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 把目标曾被狂王俘虏的印记消去
PRINTL 要消去谁的？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF !TALENT:RESULT:狂王俘虏
	PRINTFORMW 你确定%SAVESTR:RESULT%曾被狂王俘虏过？
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 将%SAVESTR:RESULT%被狂王俘虏的印记消去？

PRINTL  [0] - 确定
PRINTL  [1] - 取消

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%的【%TALENTNAME:280%】被消去了》
	TALENT:T:狂王俘虏 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@ST_UP_LABO
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINT 强化奴隶的
IF B == 0
	PRINT HP
ELSEIF B == 1
	PRINT 气力
ELSEIF B == 2
	PRINT 攻击
ELSE
	PRINT 防御
ENDIF
PRINTL 
PRINTL 要强化谁呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

;リミットに届いているか
IF B == 0
	IF MAXBASE:T:0 >= (2000 + CFLAG:T:9 * 50)
		PRINTL *HP的成长到极限了*
		PRINTW *请提高等级*
		RETURN 0
	ENDIF
ELSEIF B == 1
	IF MAXBASE:T:1 >= (2000 + CFLAG:T:9 * 50)
		PRINTL *气力的成长到极限了*
		PRINTW *请提高等级*
		RETURN 0
	ENDIF
ELSEIF B == 2
	IF CFLAG:T:13 >= CFLAG:T:9 * 5
		PRINTL *攻击值的成长到极限了*
		PRINTW *请提高等级*
		RETURN 0
	ENDIF
ELSE
	IF CFLAG:T:14 >= CFLAG:T:9 * 5
		PRINTL *防御值的成长到极限了*
		PRINTW *请提高等级*
		RETURN 0
	ENDIF
ENDIF


D = MONEY/C

;リミットに届いているか
IF B == 0
	LOCAL = (2000 + CFLAG:T:9 * 50 - MAXBASE:T:0 ) / 10
	SIF LOCAL < D
		D = LOCAL
ELSEIF B == 1
	LOCAL = (2000 + CFLAG:T:9 * 50 - MAXBASE:T:1 ) / 10
	SIF LOCAL < D
		D = LOCAL
ELSEIF B == 2
	LOCAL = (CFLAG:T:9 * 5 - CFLAG:T:13 )
	SIF LOCAL < D
		D = LOCAL
ELSE
	LOCAL = (CFLAG:T:9 * 5 - CFLAG:T:14 )
	SIF LOCAL < D
		D = LOCAL
ENDIF

PRINTFORML 强化%SAVESTR:RESULT%多少次呢？(1-{D})




INPUT

IF RESULT == 0
	RETURN 0
ELSEIF RESULT >= 1 && RESULT <= D
	IF B == 0
		PRINTW HP强化了。
		MAXBASE:T:0 += 10*RESULT
	ELSEIF B == 1
		PRINTW 气力强化了。
		MAXBASE:T:1 += 10*RESULT
	ELSEIF B == 2
		PRINTW 攻击强化了。
		CFLAG:T:13 += RESULT
	ELSE
		PRINTW 防御强化了。
		CFLAG:T:14 += RESULT
	ENDIF
ELSE
	PRINTL 数值太大了。
	GOTO INPUT_LOOP
ENDIF
MONEY -= C*RESULT
EX_FLAG:4444 -= C*RESULT
;(stick增加，修正实际攻防)
A = T
CALL WEAPON_RESTORE
RETURN 1

;----------------------------
@MAGICRESIST
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 50000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 魔法耐性可以抵挡敌方的魔法攻击。
PRINTL 赋予谁魔法耐性呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:257 == 1
	PRINTFORMW %SAVESTR:RESULT%已经拥有魔法耐性了。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%魔法耐性吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%获得了魔法耐性》
	TALENT:T:257 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@EXTRA_PREG_MARK
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 赋予异常妊娠体质
PRINTL 要赋予给谁？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:340 == 1
	PRINTFORMW %SAVESTR:RESULT%已经拥有了异常妊娠体质。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 赋予%SAVESTR:RESULT%异常妊娠体质吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%获得了异常妊娠体质》
	TALENT:T:340 = 1
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@EXTRA_PREG_ERASE
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 35000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 消除异常妊娠体质
PRINTL 要给谁消除？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF TALENT:RESULT:340 == 0
	PRINTFORMW %SAVESTR:RESULT%并不是异常妊娠体质。
	RETURN 0
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

T = RESULT

PRINTFORML 要消除%SAVESTR:RESULT%异常妊娠体质吗？

PRINTL  [0] - 好的
PRINTL  [1] - 不要

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	PRINTFORMW 《%SAVESTR:T%的异常妊娠体质消除了》
	TALENT:T:340 = 0
ELSE
	GOTO INPUT_LOOP
ENDIF
MONEY -= C
EX_FLAG:4444 -= C
RETURN 1

;----------------------------
@SET_FREE_TRAIN
;----------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 20000

IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 设定自由局部调教内容
PRINTL 要对谁设定呢？
DRAWLINE
CALL LIFE_LIST(NO_PAGE,,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT
SIF RESULT == 0
	RESULT = MASTER
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ELSEIF CFLAG:RESULT:1 == 2
	PRINTFORMW %SAVESTR:RESULT%还不在你的统治之下。
	RETURN 0
ENDIF

LOCAL = RESULT

SIF CSTR:RESULT:7 != ""
	PRINTFORML 设定新的内容的话%CSTR:RESULT:7%调教成果将被重置。

PRINTFORML 要对%SAVESTR:RESULT%进行自由局部调教设定吗？

PRINTL  [0] - 是
PRINTL  [1] - 放弃

INPUT

IF RESULT == 1
	RETURN 0
ELSEIF RESULT == 0
	
ELSE
	GOTO INPUT_LOOP
ENDIF

PRINTFORML 请输入新的自由局部调教项目。
PRINTFORML 发送空白将会重置。

INPUTS
CSTR:LOCAL:7 = %RESULTS%
ABL:LOCAL:4 = 0
ABL:LOCAL:40 = 0
JUEL:LOCAL:15 = 0

IF CSTR:LOCAL:7 == ""
	PRINTFORMW 自由局部调教重置完毕。
ELSE
	PRINTFORMW %CSTR:LOCAL:7%调教设定完毕。
ENDIF

MONEY -= C
EX_FLAG:4444 -= C
RETURN 1



;---------------------------------
@TRANS_SPECIALTALENT
;---------------------------------
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 23
C = 500000
IF MONEY < C
	PRINTW 钱不够
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTFORML 把对象从淫乱改为爱慕
PRINTFORML 或者爱慕改为淫乱
PRINTFORML 不过必须要满足 爱慕淫乱的条件哦
DRAWLINE
CALL LIFE_LIST(NO_PAGE,2,NUM_PAGE)
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PRINTL 

INPUT

IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT		
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT == 1001		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP
ELSEIF RESULT < 1 || RESULT >= CHARANUM
	GOTO INPUT_LOOP
;臨死中のキャラは排除
ELSEIF BASE:RESULT:0 < 1
	GOTO INPUT_LOOP
ENDIF

T = RESULT

PRINTFORML 要改变%SAVESTR:RESULT%吗？

	;爱慕变淫乱
	IF TALENT:T:85 && ABL:T:11 >= 3 && ABL:T:0+ABL:T:1+ABL:T:2+ABL:T:3 >= 10 && EXP:T:50 >= 3 && MARK:T:1 == 3 && MARK:T:2 == 3
		PRINTFORML 《%SAVESTR:T%的【%TALENTNAME:85%】被消去了》
		TALENT:T:85 = 0
		TALENT:T:76 = 1
        PRINTFORML %SAVESTR:T%看你的眼神，好像忘记了你还有上半身…
        PRINTFORML %SAVESTR:T%沉迷于%CALLNAME:MASTER%给予的快感之中了……
        PRINTFORMW %SAVESTR:T%获得了【%TALENTNAME:76%】。	
		GOTO INPUT_END
	;淫乱变爱慕
	ELSEIF TALENT:T:76 && ABL:T:10 >= 3 && EXP:T:21 >= 200  && TALENT:T:85 == 0 && MARK:T:2 == 3 && ABL:T:16 >= 3
		PRINTFORML 《%SAVESTR:T%的【%TALENTNAME:76%】被消去了》
		TALENT:T:76 = 0
		TALENT:T:85 = 1
	    PRINTFORMW %SAVESTR:T%柔情似水地看着你…
		PRINTFORML %SAVESTR:T%因%CALLNAME:MASTER%的行为而感到喜悦。想粘着你，想为你分忧，为你做些什么…渴望着你的宠爱。
		PRINTFORMW %SAVESTR:T%获得了【%TALENTNAME:85%】。
		GOTO INPUT_END
	ELSE
		PRINTFORMW %SAVESTR:T%改变失败了
		GOTO INPUT_LOOP
	ENDIF
	

$INPUT_END

MONEY -= C
EX_FLAG:4444 -= C

;----------------------------
@SUMMON_SLAVE
;----------------------------

C = 100000

IF MONEY < C
	PRINTW 召唤资金可是很贵的
	RETURN 0
ENDIF

$INPUT_LOOP
CUSTOMDRAWLINE =
PRINTL 读取CSV来生成奴隶
PRINTL 还需要消耗30的等级和30个肉便器
DRAWLINE
IF CFLAG:0:9 < 30
	PRINTW 等级不足
	RETURN 0
ENDIF

IF FLAG:83 < 30
	PRINTW 肉便器数量不足
	RETURN 0
ENDIF

PRINTFORML 条件达成。要生成奴隶吗？
PRINTFORML 若要生成，请输入要奴隶的编号

PRINTL  [0] - 不生成

INPUT

IF RESULT == 0
	RETURN 0
ELSEIF RESULT > 199 || RESULT < 150
	PRINTW 请确认对象的收录编号在150以上199以下
	GOTO INPUT_LOOP
ENDIF

LOCAL = RESULT

EXISTCSV LOCAL

IF RESULT == 0
	PRINTW 所选奴隶并不存在
	GOTO INPUT_LOOP
ENDIF

ADDCHARA LOCAL
CALL ADDCHARA_EX, CHARANUM-1
LOCAL:1 = CHARANUM - 1
SAVESTR:(LOCAL:1) = %NAME:(LOCAL:1)%
CSTR:(LOCAL:1):1 = %NAME:(LOCAL:1)%
LOCAL:2 = TARGET
TARGET = LOCAL:1
CALL WEARING_CLOTH_ABLE
TARGET = LOCAL:2

PRINTL *****************************************
PRINT 从魔王的影子中将
PRINTS SAVESTR:(LOCAL:1)
PRINTL 召唤了出来！
PRINTL *****************************************

;魔王の影
TALENT:(LOCAL:1):292 = 1
;召喚酔い
CFLAG:(LOCAL:1):1 = 11
CFLAG:(LOCAL:1):420 = 1

FOR LOCAL:2,800,810
	CFLAG:(LOCAL:1):(LOCAL:2) = 0
NEXT

CFLAG:0:9 -= 30
FLAG:83 -= 30
MONEY -= C
EX_FLAG:4444 -= C
PRINTL 作为代价等级和肉便器减少了30
PRINTFORMW %SAVESTR:(LOCAL:1)%的召喚并不完全
PRINTFORMW 请找出 种族・性格・理由・成为勇者前的生活・发色・瞳色 其中一项有与之共通的奴隶做成肉便器
PRINTFORMW 各个素质均有相符后耗费30个肉便器将%SAVESTR:(LOCAL:1)%解放出来

WAIT


RETURN 1



