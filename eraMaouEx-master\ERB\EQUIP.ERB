﻿;装備品
;魔力を持ったアーティファクトたち
;WEAPONのW配列にすべてを格納して扱う
;指輪は購入後設置
;武器はUSE_EX_ITEM.ERBで入手

;W:1  = 識別番号（0～999）
;W:2  = 強度（0～10）
;W:3  = 効果（強度で効果が増減する）
;W:4  = 値段（実際は強度によって補正がかかる）
;W:5  = 呪いフラグ
;W:6  = 特殊
;	0ビット(&  1):毒追加
;	1ビット(&  2):火炎効果
;	2ビット(&  4):冷気効果
;	3ビット(&  8):電撃効果
;	4ビット(& 16):
;	5ビット(& 32):
;	6ビット(& 64):
;	7ビット(&128):
;	8ビット(&256):
;W:7  = 装備個所（0-武装 1-装飾）
;W:8  = メモ。主に引数
;W:9  = ダメージ強化（ダメージが何％になるか）
;W:10 = 弾消費（数値が大きいほど多く消費する。弾はCFLAG:571）
;W:11 = ミス（何％でミスをするか）
;W:12 = 気力回復（数値が大きいほど多く回復する。負だと気力消費）
;W:13 = 連続攻撃（何％で連続攻撃に連鎖するか）
;W:14 = 防御ダメージ（防御へのダメージが何％になるか）
;W:15 = 弾が尽きたときの挙動（1:ダメージ半減 2:攻撃不可）
;W:16 = 気力ダメージ（気力へのダメージが何％になるか）
;W:17 = 接頭語

;(CFLAG:550～559は装備品枠)
;CFLAG:550 = 武装
;CFLAG:551 = 装飾
;CFLAG:552 = 装飾2
;ここに格納するのは識別番号と強度を合成した数字
;格納番号 = (接頭語 * 100000) + (強度 * 1000) + 識別番号

;効果（強度がマイナスの場合、逆の効果）
;0  无
;1  ダメージ増加
;2  装備強化
;3  速度UP
;4  HP回復
;5  気力回復
;6  容易陷落
;7  攻撃変動
;8  防御変動
;9  支配
;10 经验值増加
;11 装備劣化
;12 速度減少
;13 HP・気力減少
;14 攻撃・防御減少
;15 洗脳
;16 陷阱避け
;17 侵攻強化
;18 キャンプ
;19 侵攻弱化＆キャンプ禁止
;20 陷阱誘発

;-------------------------------------------------
@EQUIP_CHECK
;-------------------------------------------------
;W:8と同一の効果の強度合計を算出する。Aはキャラ

SIF A < 0
	RETURN 0
LOCAL = 0
W:0 = CFLAG:A:551
CALL EQUIP_DATABASE
IF RESULT
	SIF W:3 == W:8
		LOCAL += W:2
ENDIF

W:0 = CFLAG:A:552
CALL EQUIP_DATABASE
IF RESULT
	SIF W:3 == W:8
		LOCAL += W:2
ENDIF

RETURN LOCAL

;------------------------------------------------
@REMOVE_CURSE
;------------------------------------------------
;呪われたアイテムを解呪してきれいなアイテムにする
;対象のアイテムはITEMを格納したW:8。解呪者はA。階層はCFLAG:A:501
;RETURN 0は装備しない 1は装備

CALL GET_EQUIP_NUM

;入手階層に応じた強度になる
W:0 += CFLAG:A:501 * 1000

CALL EQUIP_DATABASE

SIF RESULT == 0
	RETURN 0

;呪われてないならリターン
SIF W:5 == 0
	RETURN 0

;神官と忍者以外は高確率で失敗し呪い品装着
IF TALENT:A:202 == 0 && TALENT:A:207 == 0 && RAND:3 == 0
	PRINTFORMW %SAVESTR:A%解咒失败了！
	RETURN 1
ELSEIF RAND:8 == 0
	PRINTFORMW %SAVESTR:A%解咒失败了！
	RETURN 1
ENDIF

PRINTFORMW %SAVESTR:A%解咒成功。

D = RAND:100
IF D < 20
	W:1 = 8
ELSEIF D < 40
	W:1 = 7
ELSEIF D < 60
	W:1 = 4
ELSEIF D < 80
	W:1 = 5
ELSEIF D < 85
	W:1 = 17
ELSEIF D < 90
	W:1 = 16
ELSEIF D < 95
	W:1 = 18
ELSEIF D < 96
	W:1 = 3
ELSEIF D < 97
	W:1 = 2
ELSEIF D < 98
	W:1 = 9
ELSEIF D < 99
	W:1 = 10
ELSEIF D < 100
	W:1 = 1
ELSE
	W:1 = 0
ENDIF

;解呪品は地味に強度アップする
SIF W:2 < 10
	W:2 += 1

W:0 = W:1 + W:2 * 1000
CALL EQUIP_DATABASE
RETURN 1
;------------------------------------------------
@CURSE_EQUIP_RING
;------------------------------------------------
;装饰的戒指を呪われたアイテムにする
;対象のアイテムはW:0 ITEM:300
;最大10個まで

REPEAT 10
	SIF ITEM:300 <= 0
		RETURN 0
	
	ITEM:300 -= 1
	
	D = RAND:100
	
	IF D < 20
		W:1 = 13
	ELSEIF D < 40
		W:1 = 14
	ELSEIF D < 60
		W:1 = 19
	ELSEIF D < 80
		W:1 = 20
	ELSEIF D < 90
		W:1 = 12
	ELSEIF D < 95
		W:1 = 11
	ELSEIF D < 98
		W:1 = 6
	ELSEIF D < 100
		W:1 = 15
	ELSE
		W:1 = 13
	ENDIF
	
	;初期レベルは0
	W:2 = 0
	
	W:0 = W:1
	
	PRINT 你把装饰戒指制造成
	CALL PRINT_EQUIPTYPE_RING
	PRINTL 了
	
	CALL EQUIP_GET
REND
WAIT
RETURN 1

;------------------------------------------------
@EQUIP_SELECT
;------------------------------------------------
;宝箱のアイテムを装備するか、階層で判定する。Aはキャラ。階層はCFLAG:A:501
;呪われたアイテムを装備している場合装備更新できない

SIF A < 0
	RETURN 0

;宝箱チェック
;宝箱チェック
IF CFLAG:A:1 == 12
	;イベント中
	CALL CAMPAIGN_EQUIP_SELECT,CFLAG:A:501
	X = RESULT
	SIF X < 300 
		RETURN 0
ELSE
	Y = CFLAG:A:501 + 339
	X = FLAG:Y
	SIF X < 300 
		RETURN 0
	;アイテム消費
	IF ITEM:X <= 0
		RETURN 0
	ELSE
		ITEM:X -= 1
	ENDIF
ENDIF

PRINTW 勇者发现了宝箱！

W:8 = X

W:0 = CFLAG:A:551

CALL EQUIP_DATABASE

IF RESULT && W:2 < CFLAG:A:501 && W:5 == 0
	CALL REMOVE_CURSE
	IF RESULT && W:7 == 1
		CFLAG:A:551 = W:0
		PRINT 勇者把
		CALL PRINT_EQUIPTYPE_RING
		PRINTW 装备上了。
		RETURN 0
	ENDIF
ENDIF

W:0 = CFLAG:A:552

CALL EQUIP_DATABASE

IF RESULT && W:2 < CFLAG:A:501 && W:5 == 0
	CALL REMOVE_CURSE
	IF RESULT && W:7 == 1
		CFLAG:A:552 = W:0
		PRINT 勇者把
		CALL PRINT_EQUIPTYPE_RING
		PRINTW 装备上了。
		RETURN 0
	ENDIF
ENDIF

PRINTW 似乎没什么好東西。

RETURN 0

;------------------------------------------------
@EQUIP_DATABASE
;------------------------------------------------

SIF W:0 < 0
	RETURN 0

;W:0に入れた格納番号から識別番号をW:1に、強度をW:2に、接頭語をW:17に
W:1 = W:0 % 1000
W:2 = W:0 % 100000
W:2 /= 1000
W:17 = W:0 / 100000


IF W:1 == 0
	;装饰戒指
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 1
	;破坏戒指
	W:3 = 1
	W:4 = 10000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 2
	;守护戒指
	W:3 = 2
	W:4 = 10000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 3
	;加速戒指
	W:3 = 3
	W:4 = 50000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 4
	;再生戒指
	W:3 = 4
	W:4 = 20000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 5
	;意志戒指
	W:3 = 5
	W:4 = 20000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 6
	;欲望戒指
	W:3 = 6
	W:4 = 5000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 7
	;怪力戒指
	W:3 = 7
	W:4 = 8000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 8
	;强韧戒指
	W:3 = 8
	W:4 = 8000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 9
	;支配戒指
	W:3 = 9
	W:4 = 100000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 10
	;成长戒指
	W:3 = 10
	W:4 = 70000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 11
	;虚弱戒指
	W:3 = 11
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 12
	;钝重戒指
	W:3 = 12
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 13
	;死之戒指
	W:3 = 13
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 14
	;衰弱戒指
	W:3 = 14
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 15
	;洗脑戒指
	W:3 = 15
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 16
	;陷阱回避戒指
	W:3 = 16
	W:4 = 1000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 17
	;侵攻戒指
	W:3 = 17
	W:4 = 1000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 18
	;结界戒指
	W:3 = 18
	W:4 = 1000
	W:5 = 0
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 19
	;试炼戒指
	W:3 = 19
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 20
	;不幸戒指
	W:3 = 20
	W:4 = 1000
	W:5 = 1
	W:6 = 0
	W:7 = 1
ELSEIF W:1 == 40
	;剑
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 100
	W:10 = 0
	W:11 = 0
	W:12 = 0
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 41
	;法杖
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 80
	W:10 = 0
	W:11 = 0
	W:12 = 20
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 42
	;鞭子
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 80
	W:10 = 0
	W:11 = 0
	W:12 = 0
	W:13 = 0
	W:14 = 120
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 43
	;匕首
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 70
	W:10 = 0
	W:11 = 0
	W:12 = 0
	W:13 = 30
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 44
	;手里剑
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 100
	W:10 = 1
	W:11 = 0
	W:12 = 0
	W:13 = 30
	W:14 = 100
	W:15 = 1
	W:16 = 100
ELSEIF W:1 == 45
	;弓箭
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 150
	W:10 = 1
	W:11 = 0
	W:12 = 0
	W:13 = 00
	W:14 = 100
	W:15 = 2
	W:16 = 100
ELSEIF W:1 == 46
	;权杖
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 120
	W:10 = 0
	W:11 = 10
	W:12 = 0
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 47
	;战锤
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 150
	W:10 = 0
	W:11 = 30
	W:12 = 0
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 48
	;镰刀
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 100
	W:10 = 0
	W:11 = 10
	W:12 = 20
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 49
	;触手
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 100
	W:10 = 0
	W:11 = 0
	W:12 = -10
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 120
ELSEIF W:1 == 50
	;细剑
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 80
	W:10 = 0
	W:11 = 0
	W:12 = 0
	W:13 = 50
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 51
	;偃月刀
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 135
	W:10 = 0
	W:11 = 20
	W:12 = 0
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSEIF W:1 == 52
	;指虎
	W:3 = 0
	W:4 = 100
	W:5 = 0
	W:6 = 0
	W:7 = 0
	W:9 = 65
	W:10 = 0
	W:11 = 0
	W:12 = 0
	W:13 = 40
	W:14 = 100
	W:15 = 0
	W:16 = 100
ELSE
	;黒の指輪・剑
	W:0 = 0
	W:1 = 0
	W:2 = 0
	W:3 = 0
	W:4 = 100
	W:5 = 1
	W:6 = 0
	W:7 = 1
	W:9 = 100
	W:10 = 0
	W:11 = 0
	W:12 = 0
	W:13 = 0
	W:14 = 100
	W:15 = 0
	W:16 = 100
ENDIF

;エンチャント効果
IF W:17 == 1
	;巨人
	;ミス増加・ダメージ強化
	W:9 += 30
	W:11 += 20
ELSEIF W:17 == 2
	;ポイズン
	;毒フラグ・ダメージ弱化
	W:6 += 1
	W:9 -= 10
ELSEIF W:17 == 3
	;デス
	;気力消費・ダメージ強化
	W:9 += 40
	W:12 -= 30
ELSEIF W:17 == 4
	;スラッシュ
	;連続効果・ダメージ弱化
	W:9 -= 10
	W:13 += 20
ELSEIF W:17 == 5
	;ファイア
	;ミス増加・火炎フラグ
	W:6 += 2
	W:11 += 10
ELSEIF W:17 == 6
	;アイス
	;気力消費・冷気フラグ
	W:6 += 4
	W:12 -= 10
ELSEIF W:17 == 7
	;サンダー
	;ミス増加・ダメージ増加・気力消費・電撃フラグ
	W:6 += 8
	W:9 += 20
	W:11 += 10
	W:12 -= 10
ELSEIF W:17 == 8
	;マジカル
	;ダメージ減少・気力回復
	W:9 -= 10
	W:12 += 20
ELSEIF W:17 == 9
	;ダーク
	;気力消費・気力ダメージ増加
	W:12 -= 10
	W:16 += 20
ENDIF

;+強化によってダメージ増加
W:9 += W:2 * 5

RETURN 1

;-----------------------------------------------------
;名前表示
;-----------------------------------------------------
;武装
@PRINT_EQUIPTYPE_WEAPON

;W:0に入れた格納番号から識別番号をW:1に、強度をW:2に、接頭語をW:17に
W:1 = W:0 % 1000
W:2 = W:0 % 100000
W:2 /= 1000
W:17 = W:0 / 100000

SETCOLORBYNAME LightSalmon

;接頭語名

IF W:17 == 1
	PRINT 巨型
ELSEIF W:17 == 2
	PRINT 剧毒
ELSEIF W:17 == 3
	PRINT 致命
ELSEIF W:17 == 4
	PRINT 强击
ELSEIF W:17 == 5
	PRINT 烈火
ELSEIF W:17 == 6
	PRINT 寒冰
ELSEIF W:17 == 7
	PRINT 雷霆
ELSEIF W:17 == 8
	PRINT 魔导
ELSEIF W:17 == 9
	PRINT 暗黑
ENDIF

;武器の識別番号は40～69を指定

IF W:1 == 40
	PRINT 剑
ELSEIF W:1 == 41
	PRINT 法杖
ELSEIF W:1 == 42
	PRINT 鞭
ELSEIF W:1 == 43
	PRINT 匕首
ELSEIF W:1 == 44
	PRINT 手里剑
ELSEIF W:1 == 45
	PRINT 箭
ELSEIF W:1 == 46
	PRINT 权杖
ELSEIF W:1 == 47
	PRINT 战锤
ELSEIF W:1 == 48
	PRINT 镰刀
ELSEIF W:1 == 49
	PRINT 触手
ELSEIF W:1 == 50
	PRINT 细剑
ELSEIF W:1 == 51
	PRINT 偃月刀
ELSEIF W:1 == 52
	PRINT 指虎
ELSEIF W:1 == 53
	PRINT 
ELSEIF W:1 == 54
	PRINT 
ELSEIF W:1 == 55
	PRINT 
ELSEIF W:1 == 56
	PRINT 
ELSEIF W:1 == 57
	PRINT 
ELSEIF W:1 == 58
	PRINT 
ELSEIF W:1 == 59
	PRINT 
ELSEIF W:1 == 60
	PRINT 
ELSE
	PRINT 剑
	W:0 = 40
	W:1 = 40
	W:2 = 0
ENDIF

SIF W:2 != 0
	PRINTFORM +{W:2}

RESETCOLOR

;-------------------------------------------------------------------
;装飾。RINGだけど指輪以外もそのうち追加するかも
@PRINT_EQUIPTYPE_RING
;-------------------------------------------------------------------
;W:0に入れた格納番号から識別番号をW:1に、強度をW:2に、接頭語をW:17に
W:1 = W:0 % 1000
W:2 = W:0 % 100000
W:2 /= 1000
W:17 = W:0 / 100000

;指輪の識別番号は0～39を指定

SETCOLORBYNAME LightSalmon

IF W:1 == 0
	PRINT 装饰戒指
ELSEIF W:1 == 1
	PRINT 破坏戒指
ELSEIF W:1 == 2
	PRINT 守护戒指
ELSEIF W:1 == 3
	PRINT 加速戒指
ELSEIF W:1 == 4
	PRINT 再生戒指
ELSEIF W:1 == 5
	PRINT 意志戒指
ELSEIF W:1 == 6
	PRINT 欲望戒指
ELSEIF W:1 == 7
	PRINT 怪力戒指
ELSEIF W:1 == 8
	PRINT 强韧戒指
ELSEIF W:1 == 9
	PRINT 支配戒指
ELSEIF W:1 == 10
	PRINT 成长戒指
ELSEIF W:1 == 11
	PRINT 虚弱戒指
ELSEIF W:1 == 12
	PRINT 钝重戒指
ELSEIF W:1 == 13
	PRINT 死亡戒指
ELSEIF W:1 == 14
	PRINT 衰弱戒指
ELSEIF W:1 == 15
	PRINT 洗脑戒指
ELSEIF W:1 == 16
	PRINT 陷阱回避戒指
ELSEIF W:1 == 17
	PRINT 侵攻戒指
ELSEIF W:1 == 18
	PRINT 结界戒指
ELSEIF W:1 == 19
	PRINT 试炼戒指
ELSEIF W:1 == 20
	PRINT 不幸戒指
ELSE
	PRINT 暗黑戒指
	W:0 = 0
	W:1 = 0
	W:2 = 0
ENDIF

SIF W:2 != 0
	PRINTFORM +{W:2}

RESETCOLOR

RETURN 0

;-------------------------------------------------------------------
;入手
@EQUIP_GET
;-------------------------------------------------------------------
;W:0に入れた格納番号から識別番号をW:1に。基本的に一個入手

;無しの場合終了
SIF W:0 < 0
	RETURN 0

W:1 = W:0 % 1000

X = 300 + W:1

SIF X < 300
	X = 300

ITEM:X += 1

SIF ITEM:X > 99
	ITEM:X = 99

RETURN 0

;-------------------------------------------------------------------
;ITEMナンバーから識別番号入手
@GET_EQUIP_NUM
;-------------------------------------------------------------------
;W:8に入れたITEMナンバーから識別番号をW:0に

W:0 = W:8 - 300

SIF W:0 < 0
	W:0 = 0

RETURN 0

;-------------------------------------------------------------------
@EQUIP_POWERUP, ARG:0
;-------------------------------------------------------------------
;Wに入れた装備品の能力をキャラの能力で強化する
;EQUIP_DATABASE後に使用すること
;ARG:0がキャラ

;初心者（ダメージ減少・ミス増加）
IF TALENT:(ARG:0):291 == 1
	W:9 -= 10
	W:11 += 10
ENDIF

;恶魔尾巴（ダメージ強化）
SIF TALENT:(ARG:0):246 == 1
	W:9 += 10

;悪魔目（気力ダメージ強化）
SIF TALENT:(ARG:0):247 == 1
	W:16 += 10

;独眼（ダメージ強化・ミス増加）
IF TALENT:(ARG:0):259 == 1
	W:9 += 10
	W:11 += 10
ENDIF

;额头天眼（気力ダメージ強化・気力消費）
IF TALENT:(ARG:0):260 == 1
	W:16 += 10
	W:12 -= 10
ENDIF

;角（ダメージ強化・気力消費）
IF TALENT:(ARG:0):264 == 1
	W:9 += 10
	W:12 -= 10
ENDIF

;エルフの弓装備で連続ボーナス
SIF W:1 == 45 && TALENT:(ARG:0):314 == 1
	W:13 += 10

;天使は気力が回復する
SIF TALENT:(ARG:0):314 == 6
	W:12 += 10

;ダークエルフ（気力ダメージ強化）
SIF TALENT:(ARG:0):314 == 7
	W:16 += 10

;堕天使は連続攻撃
SIF TALENT:(ARG:0):314 == 8
	W:13 += 10

;魔族は防御ダメージ
SIF TALENT:(ARG:0):314 == 9
	W:14 += 10

;能力者効果

IF TALENT:(ARG:0):275
	;火之能力者
	GETBIT W:6, 1
	IF RESULT
		;火属性の場合、火力強化
		W:9 += 10
	ELSE
		;無属性の場合、ノーリスクで火属性化
		W:6 += 2
	ENDIF
ENDIF

IF TALENT:(ARG:0):276
	;冰之能力者
	GETBIT W:6, 2
	IF RESULT
		;冷気属性の場合、連続強化
		W:13 += 10
	ELSE
		;無属性の場合、ノーリスクで冷気属性化
		W:6 += 4
	ENDIF
ENDIF

IF TALENT:(ARG:0):277
	;雷之能力者
	GETBIT W:6, 3
	IF RESULT
		;電撃属性の場合、連続・攻撃強化
		W:13 += 5
		W:9 += 5
	ELSE
		;無属性の場合、ノーリスクで電撃属性化
		W:6 += 8
	ENDIF
ENDIF

IF TALENT:(ARG:0):278
	;光之能力者
	IF W:12 > 0
		;気力回復の場合、連続・攻撃・気力回復強化
		W:13 += 5
		W:9 += 5
		W:12 += 5
	ELSE
		;気力回復が無い場合、気力回復強化
		W:12 += 10
	ENDIF
ENDIF

IF TALENT:(ARG:0):279
	;暗之能力者
	IF W:16 > 0
		;気力ダメージ強化の場合、連続・攻撃・気力ダメージ強化
		W:13 += 5
		W:9 += 5
		W:16 += 5
	ELSE
		;気力ダメージ強化が無い場合、気力ダメージ強化
		W:16 += 10
	ENDIF
ENDIF

RETURN 0

;-------------------------------------------------------------------
@EQUIP_ST_SHOW, ARG:0
;-------------------------------------------------------------------
;Wに入れた装備品の能力を表示する
;ARG:0がキャラ

W:0 = CFLAG:ARG:550

CALL PRINT_EQUIPTYPE_WEAPON
PRINTL  
CALL EQUIP_DATABASE
CALL EQUIP_POWERUP,ARG

SIF W:5
	PRINTL *带有诅咒
SIF W:6 & 1
	PRINTL *带有毒液
SIF W:6 & 2
	PRINTL *带火
SIF W:6 & 4
	PRINTL *带寒冰
SIF W:6 & 8
	PRINTL *带电
;SIF W:7 == 0
;	PRINTL *それは武器である
;SIF W:7 == 1
;	PRINTL *それは装飾品である
SIF W:9
	PRINTFORML *{W:9}的打击力
SIF W:11
	PRINTFORML *{W:11}％概率打偏
SIF W:12 > 0
	PRINTFORML *恢复{W:12}气力
SIF W:12 < 0
	PRINTFORML *消费{W:12 * -1}气力
SIF W:13
	PRINTFORML *{W:13}％概率二连击
SIF W:14 != 100
	PRINTFORML *打击防御{W:14}％
SIF W:16 != 100
	PRINTFORML *打击气力{W:16}％

RETURN 2

;-----------------------------------------------
@SHOW_BUTTON_EQUIP(NUM, ARG)
#DIM NUM
;-----------------------------------------------
;キャラの能力表示において[装備詳細]ボタンを表示する
;引数NUMはボタンの数値、ARGは対象キャラの番号

LOCAL = CHECK_ABLE_TO_SHOW_EQUIP(ARG)
IF LOCAL == 1
	; 条件に合わないならボタン自体を表示しない
	RETURN 0
ENDIF
PRINTFORM [{NUM}] 装备情报　
RESETCOLOR
RETURN 0

;-----------------------------------------------
@CHECK_ABLE_TO_SHOW_EQUIP,ARG
#FUNCTION
;-----------------------------------------------
;装備品のステータスを見れるか判定する
;ARGはキャラ

;売却可なら見れる
SIF CFLAG:ARG:0 > 0
	RETURNF 0
;信頼度20以上なら見れる
SIF CFLAG:ARG:2 >= 20
	RETURNF 0
;従順1以上なら見れる
SIF ABL:ARG:10 > 0
	RETURNF 0
;目立ちたがりなら見れる
SIF TALENT:ARG:28
	RETURNF 0
;カルマ0以下なら見れる
SIF CFLAG:ARG:151 <= 0
	RETURNF 0

;見せられないよ
RETURNF 1

