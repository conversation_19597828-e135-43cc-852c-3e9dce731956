﻿2016/11/06 eramaou v.0.790用パッチ

雑多なバグなどの修正＋実験室に「記憶を消去する」を追加

■ 書いた人
　作者とは別の人
　eraは初めていじりますがよろしく。

■ 含まれるファイル

DUNGEON.ERB
DUNGEON_BITCH_LOG.ERB
EVENT_K1_自信家.ERB
MARRIAGE_DAY.ERB
SHOP_LABO.ERB
SYSTEM.ERB

■ 概要
1. 16/10/22 UP分 自信家モンスター結婚妊娠口上を読み込み時エラーが発生する問題

2. 娼館街を建てて娼婦を働かせると魔王様の元に帰ってこない問題

3. 街で勇者が売春する際、特定のパターンで客の名前が正常に表示されない問題

4. 奴隷同士の結婚生活表示で、「*名前1と*名前2の～」と表示される問題

5. マオと他の奴隷が恋人になるor結婚すると名前が正常に表示されない問題
　ただし既にマオが加入済みの場合は解消せず、新規にゲーム開始した場合に解消する

6. 実験室でおもらし癖を治療する際、放尿経験を0にする処理を追加
　※ 調教で[おもらし癖]がついたキャラを治療しても次ターンで復帰するため、
　　[おもらし癖]付与の仕様からして半ば死に項目だと思ったので追加しましたが、
　　問題あったら該当コードは消して下さい。

7. 実験室に「記憶を消去する」を追加
　※ 再ステ振り欲しいなあというのと、
　　記憶にないけど体は開発済みで反応しちゃうのいいよね…ということで書きました。
　　これも問題あったら該当コードは消して下さい。

　初期化されるもの：
　　能力（ABL）全て
　　刻印（MARK）全て
　　珠（JUEL）の取得数
　　特殊性癖（自慰・尻穴・乳狂い、セックス狂）
　　陥落系素質（愛、淫乱、妄信）
　　売却、助手可能フラグ
　　好感度
　　調教経験回数

　精愛味覚、強化素質（淫核～淫肛、性豪）、売春素質はそのまま


■ 変更内容詳細

1. 16/10/22 UP分 自信家モンスター結婚妊娠口上を読み込み時エラーが発生する問題

対象ファイル： EVENT_K1_自信家.ERB

　行	内容（変更）
 6941:	ELSEIF TALENT:TARGET:136 == 1 && CFLAG:601 == 900 &&  (TALENT:MASTER:122 == 0 && CFLAG:MASTER:601 == 900 && CFLAGT:MASTER:602 > 40)  
　↓
 6941:	ELSEIF TALENT:TARGET:136 == 1 && CFLAG:601 == 900 &&  (TALENT:MASTER:122 == 0 && CFLAG:MASTER:601 == 900 && CFLAG:MASTER:602 > 40)  


2. 娼館街を複数階に渡って建て、娼婦を働かせると魔王様の元に帰ってこない問題

対象ファイル： DUNGEON.ERB

　行	内容（変更）
  258:	D:20 = 100
　↓
  258:	D:20 = 90

※ 娼婦が客を探すと WALK = 0 になるため、
　階層進行度100で前の階層に戻る → 次ターンは階層進行度0で次の階層へ進む → 次のターンで前の階層に戻る→ …
　でループしていると思われる。


3. 街で勇者が売春する際、特定のパターンで客の名前が表示されない問題

対象ファイル： DUNGEON_BITCH_LOG.ERB

　行	内容（変更）
  861:	LOCALS = FS_BITCH("TOWN_GIRL", KYAKU)
　↓
  861:	LOCALS = %FS_BITCH("TOWN_GIRL", KYAKU)%


4. 奴隷同士の結婚生活表示で、「*名前1と*名前2の～」と表示される問題

対象ファイル： MARRIAGE_DAY.ERB

　行	内容（変更）
   54:	PRINTFORM *%SAVESTR:MARRY_CHARA%
　↓
   54:	PRINTFORM %SAVESTR:MARRY_CHARA%

5. マオと奴隷が恋人になるor結婚すると名前が正常に表示されない問題

対象ファイル： SYSTEM.ERB

　行	内容（追加）
   86:	CFLAG:6 = 1

※マオ加入時にフラグが建ってない（NULL？）ため名前の交換がうまく行ってないと思われるが、
　開発中の機能のようですので、この辺りの調整は作者様にお願いしたいです。


6. 実験室でおもらし癖を治療する際、放尿経験を0にする処理を追加

対象ファイル： SHOP_LABO.ERB

　行	内容（追加）
 1280:	EXP:T:31 = 0


7. 実験室に「記憶を消去する」を追加

対象ファイル： SHOP_LABO.ERB

　行	内容（変更）
  176:	;PRINTL  [8] - 記憶を消去する
　↓
  176:	PRINTL  [8] - 記憶を消去する

　行	内容（追加）
  838 ～ 974	：追加ソース 
　975 ～ 1169	：旧ソース
