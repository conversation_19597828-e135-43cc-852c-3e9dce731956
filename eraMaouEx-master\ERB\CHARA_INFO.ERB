﻿;[101]能力の表示以下の処理

;-----------------------------------------------
@CHARA_INFO
#DIM NO_PAGE
#DIM TMP_TARGET
#DIM SORT_SELECT
#DIM SORT_ACT
#DIM CHARA_SORT, 1000
#DIM L_LCOUNT
#DIM CONST NUM_PAGE = 24
;-----------------------------------------------

$DRAW_LIST
L_LCOUNT = LINECOUNT

REDRAW 0

;标题栏
CUSTOMDRAWLINE =
PRINTFORM 请选择一个角色以了解详细信息。\t\t<第{NO_PAGE+1}页> 
SIF !MASTER
	PRINTFORML (总计{CHARANUM}人)
SIF MASTER
	PRINTFORML (总计{CHARANUM-1}人)
DRAWLINE 

$SELECT_LOOP

;列出清单
IF SORT_SELECT == 1200
	CALL SHOW_CHARA_INFO_LIST(NO_PAGE)
ELSEIF SORT_SELECT == 1300
	CALL SHOW_CHARA_ACT_LIST(NO_PAGE, SORT_ACT, CHARA_SORT)
ELSEIF SORT_SELECT == 1400
	CALL SHOW_CHARA_MONEY_LIST(NO_PAGE, CHARA_SORT)
ELSEIF SORT_SELECT == 1500
	CALL SHOW_CHARA_DEBT_LIST(NO_PAGE, CHARA_SORT)
ELSE
	SORT_SELECT = 1200
	CALL SHOW_CHARA_INFO_LIST(NO_PAGE)
ENDIF
PRINTFORM [1200] %"编号",12,LEFT%
PRINTFORM [1300] %"状态",12,LEFT%
PRINTFORM [1400] %"所持金",12,LEFT%
PRINTFORM [1500] %"借金",12,LEFT%
PRINTL
DRAWLINE
PRINTLC [997] 上一页
PRINTLC [999] 返回	
PRINTLC [998] 下一页
PRINTL

;输入
$INPUT_LOOP3
INPUT

;处理输入
SELECTCASE RESULT
CASE 1200,1400,1500,1300
	IF SORT_SELECT != RESULT
		SORT_SELECT = RESULT
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO DRAW_LIST
	ELSEIF RESULT == 1300
		SORT_ACT++
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO DRAW_LIST
	ENDIF
CASE 999		;返回
	REDRAW 1
	RETURN 0 
CASE 997		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO DRAW_LIST
	ENDIF
CASE 998		;下一页
	IF (NO_PAGE+1) * NUM_PAGE <= CHARANUM
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
		GOTO DRAW_LIST
	ENDIF
CASE 0 TO CHARANUM -1
	;個別情報表示へ
	SIF RESULT == 0 && MASTER
		RESULT = MASTER
	IF SORT_SELECT == 1200
		CALL CHARA_INFO_INDIVIDUAL_WAPPED(RESULT)
	ELSE
		CALL CHARA_INFO_INDIVIDUAL(RESULT, CHARA_SORT)
	ENDIF
	IF RESULT == 1
		REDRAW 1
		;返回值1结束当前回合
		;其他返回值回到人物列表
		RETURN 1
	ENDIF
	GOTO DRAW_LIST
ENDSELECT

CLEARLINE 1
GOTO INPUT_LOOP3


;-----------------------------------------------
@SHOW_CHARA_INFO_LIST(NO_PAGE = 0)
#DIM MAX_NUM_LEN = 0
#DIM MAX_NAME_LEN = 0
#DIM MAX_LV_LEN = 0
#DIM MAX_ATK_LEN = 0
#DIM MAX_DEF_LEN = 0
#DIM NO_PAGE
#DIM CONST NUM_PAGE = 24
;-----------------------------------------------
;存在するキャラの一覧表示
;全員出してるので表示人数は{CHARANUM}人となる
 
;キャラの番号、名前の文字数、レベル、攻撃、防御の最大桁数をそれぞれ取得
MAX_NUM_LEN = STRLENS(TOSTR((NO_PAGE+1)*NUM_PAGE));番号
FOR COUNT, 0, CHARANUM
	MAX_NAME_LEN = MAX(STRLENS(SAVESTR:COUNT), MAX_NAME_LEN);名前
	MAX_LV_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:9)), MAX_LV_LEN);レベル
	MAX_ATK_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:13)), MAX_ATK_LEN);攻撃
	MAX_DEF_LEN = MAX (STRLENS(TOSTR(CFLAG:COUNT:14)), MAX_DEF_LEN);防御
NEXT

LIST_HEADER = NO_PAGE*NUM_PAGE + 1
LIST_FOOTER = (NO_PAGE+1)*NUM_PAGE + 1
SIF LIST_HEADER > MASTER && MASTER
	LIST_HEADER ++
SIF LIST_FOOTER > MASTER && MASTER
	LIST_FOOTER ++
; 第一行，魔王
LOCALS = [{0,MAX_NUM_LEN}]
PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT% 　　　　 %NAME:MASTER, MAX_NAME_LEN,LEFT% LV{CFLAG:MASTER:9, MAX_LV_LEN,RIGHT}

;绘制列表
FOR COUNT, LIST_HEADER, LIST_FOOTER
	
	SIF COUNT == MASTER
		CONTINUE
	IF COUNT >= CHARANUM
		PRINTL
		CONTINUE
	ENDIF
	;番号
	LOCALS = [{COUNT,MAX_NUM_LEN}]
	PRINTFORM %LOCALS, MAX_NUM_LEN + 2, RIGHT% 
	;行動状態
	CALL SHOW_CHARA_ACT(COUNT)
	;名前、レベル
	PRINTFORM  %SAVESTR:COUNT,MAX_NAME_LEN,LEFT% LV{CFLAG:COUNT:9,MAX_LV_LEN,RIGHT}

	;攻撃力/防御力、善恶值
	PRINTFORM  攻击{CFLAG:COUNT:13,MAX_ATK_LEN,LEFT}/防御{CFLAG:COUNT:14,MAX_DEF_LEN,LEFT} 善恶值{CFLAG:COUNT:151,4,RIGHT}
	;体力・気力バー
	PRINTFORM  HP%BARSTR(BASE:COUNT:0,MAXBASE:COUNT:0,8)% 气%BARSTR(BASE:COUNT:1,MAXBASE:COUNT:1,8)%
	;爱、淫乱
	IF TALENT:COUNT:85
		SETCOLOR 255,100,100
		PRINT <爱  慕>
		RESETCOLOR
	ELSEIF TALENT:COUNT:淫乱
		SETCOLOR 255,100,100
		PRINT <淫  乱>
		RESETCOLOR
	ELSE
		SETCOLOR 100,100,100
		PRINT <未陷落>
		RESETCOLOR
	ENDIF
	;お気に入り
	IF CFLAG:COUNT:700
		PRINTFORM  [☆]
	ELSE
		PRINTS " "*5
	ENDIF
		;组队标记
	IF CFLAG:COUNT:1 == 2
		SETCOLOR 255,100,100
	ELSEIF CFLAG:COUNT:1 == 3
		SETCOLOR 100,255,255
	ENDIF
	;小队成员
	IF CFLAG:COUNT:533 > 0 && CFLAG:COUNT:531 == 0 && CFLAG:COUNT:532 == 0 && CFLAG:COUNT:533 != COUNT
		PRINTFORM  %@"队员<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	;小队队长
	ELSEIF CFLAG:COUNT:533 > 0
		PRINTFORM  %@"队长<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	ELSE
		PRINTFORM  %"",MAX_NUM_LEN + 6, LEFT%
	ENDIF
	SIF CFLAG:COUNT:1 == 2 || CFLAG:COUNT:1 == 3
		RESETCOLOR
		
	;帰還フラグ
	IF CFLAG:COUNT:507 == 1
		SETCOLOR 200,200,100
		PRINTFORM  <归还中>
		RESETCOLOR
	ELSE
		PRINTS " "*9
	ENDIF
	PRINTL 
NEXT



;-----------------------------------------------
@SHOW_CHARA_ACT_LIST(NO_PAGE = 0, ACT = 2, CHARA_SORT)
#DIM MAX_NUM_LEN = 0
#DIM MAX_NAME_LEN = 0
#DIM MAX_LV_LEN = 0
#DIM MAX_ATK_LEN = 0
#DIM MAX_DEF_LEN = 0
#DIM NO_PAGE
#DIM ACT
#DIM CONST NUM_PAGE = 24
#DIM REF CHARA_SORT
#DIM L_POS = 0
;-----------------------------------------------
VARSET CHARA_SORT
ACT %= 2

MAX_NUM_LEN = STRLENS(TOSTR(CHARANUM))
REPEAT CHARANUM
	MAX_NAME_LEN = MAX(STRLENS(SAVESTR:COUNT), MAX_NAME_LEN);名前
	MAX_LV_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:9)), MAX_LV_LEN);レベル
	MAX_ATK_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:13)), MAX_ATK_LEN);攻撃
	MAX_DEF_LEN = MAX (STRLENS(TOSTR(CFLAG:COUNT:14)), MAX_DEF_LEN);防御
	
	SIF COUNT == MASTER
		CONTINUE
	
	FOR L_POS, 0, CHARANUM
	
		IF CHARA_SORT:L_POS <= 0
			CHARA_SORT:L_POS = COUNT
			BREAK
		ENDIF
		
		LOCAL = COUNT, CHARA_SORT:L_POS
		IF ACT == 0 
			RESULT = COMPARE_CHARA_ACT(LOCAL, LOCAL:1, 2)
		ELSEIF (CFLAG:LOCAL:1 == 2 || CFLAG:LOCAL:1 == 3) && (CFLAG:(LOCAL:1):1 == 2 || CFLAG:(LOCAL:1):1 == 3)
			RESULT = ENEMY_COMPARE(LOCAL, LOCAL:1)
		ELSE
			RESULT = COMPARE_CHARA_ACT(LOCAL, LOCAL:1, 2)
		ENDIF
		IF RESULT < 0 
			ARRAYSHIFT CHARA_SORT, 1, COUNT, L_POS, COUNT
			BREAK
		ENDIF		
	NEXT
REND

LIST_HEADER = NO_PAGE*NUM_PAGE
LIST_FOOTER = (NO_PAGE+1)*NUM_PAGE


; 第一行，魔王
LOCALS = [{0,MAX_NUM_LEN}]
PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT%  　　　　%NAME:MASTER, MAX_NAME_LEN,LEFT% LV{CFLAG:MASTER:9, MAX_LV_LEN,RIGHT}
;绘制列表
FOR L_POS, LIST_HEADER, LIST_FOOTER
	COUNT = CHARA_SORT:L_POS
	
	IF L_POS >= CHARANUM -1 || COUNT == EX_FLAG:0
		PRINTL
		CONTINUE
	ENDIF

	
	;番号
	LOCALS = [{COUNT,MAX_NUM_LEN}]
	PRINTFORM %LOCALS, MAX_NUM_LEN + 2, RIGHT% 
	;行動状態
	CALL SHOW_CHARA_ACT(COUNT)
	;名前、レベル
	PRINTFORM  %SAVESTR:COUNT,MAX_NAME_LEN,LEFT% LV{CFLAG:COUNT:9,MAX_LV_LEN,RIGHT}

	;攻撃力/防御力、善恶值
	PRINTFORM  攻击{CFLAG:COUNT:13,MAX_ATK_LEN,LEFT}/防御{CFLAG:COUNT:14,MAX_DEF_LEN,LEFT} 善恶值{CFLAG:COUNT:151,4,RIGHT}
	;体力・気力バー
	PRINTFORM  HP%BARSTR(BASE:COUNT:0,MAXBASE:COUNT:0,8)% 气%BARSTR(BASE:COUNT:1,MAXBASE:COUNT:1,8)%
		
	;お気に入り
	IF CFLAG:COUNT:700
		PRINTFORM  [☆]
	ELSE
		PRINTS " "*5
	ENDIF
	
	;组队标记
	IF CFLAG:COUNT:1 == 2
		SETCOLOR 255,100,100
	ELSEIF CFLAG:COUNT:1 == 3
		SETCOLOR 100,255,255
	ENDIF
	;小队成员
	IF CFLAG:COUNT:533 > 0 && CFLAG:COUNT:531 == 0 && CFLAG:COUNT:532 == 0 && CFLAG:COUNT:533 != COUNT
		PRINTFORM  %@"队员<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	;小队队长
	ELSEIF CFLAG:COUNT:533 > 0
		PRINTFORM  %@"队长<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	ELSEIF CFLAG:COUNT:1 == 3
		PRINTFORM  %"",MAX_NUM_LEN + 6, LEFT%
	ELSE
		PRINTFORM  %"",MAX_NUM_LEN + 7, LEFT%
	ENDIF
	SIF CFLAG:COUNT:1 == 2 || CFLAG:COUNT:1 == 3
		RESETCOLOR
		
	;帰還フラグ
	IF CFLAG:COUNT:507 == 1
		SETCOLOR 200,200,100
		PRINTFORM  <归>
		RESETCOLOR
	ELSE
		PRINTS " "*4
	ENDIF
	
	;结婚
	PRINT [婚:
IF CFLAG:COUNT:601 == 900
	PRINT 野狗
ELSEIF CFLAG:COUNT:601 == 901
	PRINT 你
ELSEIF CFLAG:COUNT:601 == 0
	;主婦?人妻の場合、結婚対象がいる
	;IF TALENT:COUNT:315 == 21 || TALENT:COUNT:157
	;	PRINT 故乡丈夫
		;IF TALENT:COUNT:122
		;	IF ABL:COUNT:23
		;	PRINT 妻子
	;	ABL:COUNT:315丈夫
	;ELSE
	;	PRINT 无
	;ENDIF
	CALL CHARA_MARRIGE_BEFORE, COUNT
ELSEIF CFLAG:COUNT:601 == 902
    CALL NAME_LOVER,CFLAG:COUNT:606,1
ELSE
	CALL SEARCH_FAMILY,COUNT,"MARRIAGE"
	IF EX_TALENT:COUNT:2 && RESULT < 0
		PRINT 无
	ELSEIF CFLAG:MASTER:601 == CFLAG:COUNT:6
		PRINTFORM %SAVESTR:MASTER%
	ELSEIF CFLAG:COUNT:601 == 0 && !EX_TALENT:COUNT:2
		PRINTFORM %GET_LOOK_INFO(COUNT, "婚史")%
	ELSEIF CFLAG:COUNT:601 == 0 && EX_TALENT:COUNT:2
		PRINTFORM 无
	ELSE
		LOCAL = CFLAG:COUNT:601
		LOCAL %= 10
		IF LOCAL == 9
		CALL SEARCH_FAMILY,COUNT,"MARRIAGE"
			IF RESULT > 0
				PRINTFORM %SAVESTR:RESULT%
			ELSE
				PRINT 无
			ENDIF
		ELSE
			PRINTFORM %ITEMNAME:(CFLAG:COUNT:601)%
		ENDIF
	ENDIF
ENDIF

PRINT ]
	
	PRINTL 
NEXT


@CHARA_MARRIGE_BEFORE, ARG
	LOCAL = TALENT:ARG:320
		;家族設定の有無
	LOCAL:1 = LOCAL % 10
	IF LOCAL:1 == 0
		PRINT 无
		RETURN
	ENDIF
	;对象の有無
	LOCAL:1 = LOCAL % 100000
	LOCAL:2 = LOCAL % 10000000000

	SELECTCASE LOCAL:1 / 10000
		CASE 0
			PRINT 无
		CASE 1
			SELECTCASE LOCAL:2 / 1000000000
				CASE 0,4,8
					PRINT 故乡丈夫
				CASE 1,5,7
					PRINT 故乡扶她
				CASEELSE
					PRINT 故乡妻子
			ENDSELECT
		CASE 2
			PRINT 无
		CASE 3
			SELECTCASE LOCAL:2 / 1000000000
				CASE 0,4,8
					PRINT 故乡丈夫
				CASE 1,5,7
					PRINT 故乡扶她
				CASEELSE
					PRINT 故乡妻子
			ENDSELECT
		CASE 4
			SELECTCASE LOCAL:2 / 1000000000
				CASE 0,4,8
					PRINT 故乡丈夫
				CASE 1,5,7
					PRINT 故乡扶她
				CASEELSE
					PRINT 故乡妻子
			ENDSELECT
		CASE 5
			LOCALS = 无
		CASEELSE
			LOCALS = 秘密
	ENDSELECT

;-----------------------------------------------
@SHOW_CHARA_MONEY_LIST(NO_PAGE = 0, CHARA_SORT)
#DIM MAX_NUM_LEN = 0
#DIM MAX_NAME_LEN = 0
#DIM MAX_RACE_LEN = 0
#DIM MAX_MIND_LEN = 0
#DIM MAX_LV_LEN = 0
#DIM MAX_MONEY_LEN = 0
#DIM MAX_MONEY
#DIM MONEY_ID
#DIM RANK_COUNT
#DIM REF CHARA_SORT
#DIM NO_PAGE
#DIM CONST NUM_PAGE = 24
;-----------------------------------------------
;存在するキャラの所持金順表示
;全員出してるので表示人数は{CHARANUM}人となる

;キャラの番号、名前の文字数、レベル、所持金の最大桁数をそれぞれ取得
MAX_NUM_LEN = STRLENS(TOSTR(CHARANUM));番号
FOR COUNT, 0, CHARANUM
	MAX_NAME_LEN = MAX(STRLENS(SAVESTR:COUNT), MAX_NAME_LEN);名前
	MAX_LV_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:9)), MAX_LV_LEN);レベル
	MAX_MONEY_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:580)), MAX_MONEY_LEN);所持金
	MAX_RACE_LEN = MAX(STRLENS(GET_LOOK_INFO(COUNT, "种族12")), MAX_RACE_LEN);种族
	MAX_MIND_LEN = MAX(STRLENS(GET_LOOK_INFO(COUNT, "性格")), MAX_MIND_LEN);性格
NEXT

;ソート
;MAX_MONEY = 最大値
MAX_MONEY = -9999

;最大値を算出
FOR MONEY_ID, 1, CHARANUM
	MAX_MONEY = MAX(CFLAG:MONEY_ID:580, MAX_MONEY);所持金
NEXT

;無限ループ避け
LOCAL:1 = 0

;最大値の次に大きい数記憶
LOCAL:2 = -9999

RANK_COUNT = 0

WHILE RANK_COUNT < CHARANUM -1
	
	FOR MONEY_ID, 1, CHARANUM
		SIF MONEY_ID == MASTER
			CONTINUE
		;现在の最大値と同じものを選択
		IF MAX_MONEY == CFLAG:MONEY_ID:580
			
			;リストにIDを記憶
			CHARA_SORT:RANK_COUNT = MONEY_ID
			;順位を進める
			RANK_COUNT++
			
		ELSEIF CFLAG:MONEY_ID:580 < MAX_MONEY && CFLAG:MONEY_ID:580 > LOCAL:2
			
			;现在の最大値ではない、现在の最大値の次に大きい数記憶
			LOCAL:2 = CFLAG:MONEY_ID:580
			
		ENDIF
		
	NEXT
	
	;次にソートする最大値をセット
	MAX_MONEY = LOCAL:2
	LOCAL:2 = -9999
	
	;無限ループ避け
	LOCAL:1++
	SIF LOCAL:1 > 1000
		BREAK
	
WEND


LIST_HEADER = NO_PAGE*NUM_PAGE
LIST_FOOTER = (NO_PAGE+1)*NUM_PAGE


;1列ずつ表示
;まおーさま
LOCALS = [{0,MAX_NUM_LEN}]
PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT% 　　　　 %NAME:MASTER, MAX_NAME_LEN,LEFT% LV{CFLAG:MASTER:9, MAX_LV_LEN,RIGHT}
;他の人たち
FOR RANK_COUNT, LIST_HEADER, LIST_FOOTER
	;順位順に引き出す
	COUNT = CHARA_SORT:RANK_COUNT
	;魔王様の重複表示を避ける
	;逆に表示したい時はコメントアウトする
	
	IF RANK_COUNT >= CHARANUM -1 || COUNT == EX_FLAG:0
		PRINTL
		CONTINUE
	ENDIF
	
	;番号
	PRINTFORM [{COUNT,MAX_NUM_LEN}] 
	;行動状態
	CALL SHOW_CHARA_ACT(COUNT)
	;名前、レベル
	PRINTFORM  %SAVESTR:COUNT,MAX_NAME_LEN,LEFT% LV{CFLAG:COUNT:9,MAX_LV_LEN,RIGHT}
	
	;种族と性格
	PRINTFORM  %GET_LOOK_INFO(COUNT, "种族12"),MAX_RACE_LEN,LEFT% - %GET_LOOK_INFO(COUNT, "性格"),MAX_MIND_LEN,LEFT%
	
	;所持金
	PRINTFORM  所持金:{CFLAG:COUNT:580,MAX_MONEY_LEN,LEFT} 
	
	;愛、淫乱
	IF TALENT:COUNT:85
		SETCOLOR 255,100,100
		PRINT <爱慕>　
		RESETCOLOR
	ELSEIF TALENT:COUNT:淫乱
		SETCOLOR 255,100,100
		PRINT <淫乱>　
		RESETCOLOR
	ELSE
		SETCOLOR 100,100,100
		PRINT <未陷落>
		RESETCOLOR
	ENDIF
	;お気に入り
	IF CFLAG:COUNT:700
		PRINTFORM  [☆]
	ELSE
		PRINTS " "*5
	ENDIF
		;组队标记
	IF CFLAG:COUNT:1 == 2
		SETCOLOR 255,100,100
	ELSEIF CFLAG:COUNT:1 == 3
		SETCOLOR 100,255,255
	ENDIF
	;小队成员
	IF CFLAG:COUNT:533 > 0 && CFLAG:COUNT:531 == 0 && CFLAG:COUNT:532 == 0 && CFLAG:COUNT:533 != COUNT
		PRINTFORM  %@"队员<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	;小队队长
	ELSEIF CFLAG:COUNT:533 > 0
		PRINTFORM  %@"队长<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	ELSE
		PRINTFORM  %"",MAX_NUM_LEN + 6, LEFT%
	ENDIF
	SIF CFLAG:COUNT:1 == 2 || CFLAG:COUNT:1 == 3
		RESETCOLOR
		
	;帰還フラグ
	IF CFLAG:COUNT:507 == 1
		SETCOLOR 200,200,100
		PRINTFORM  <归还中>
		RESETCOLOR
	ELSE
		PRINTS " "*9
	ENDIF
	PRINTL 
NEXT


;-----------------------------------------------
@SHOW_CHARA_DEBT_LIST(NO_PAGE = 0, CHARA_SORT)
#DIM MAX_NUM_LEN = 0
#DIM MAX_NAME_LEN = 0
#DIM MAX_LV_LEN = 0
#DIM MAX_RACE_LEN = 0
#DIM MAX_MIND_LEN = 0
#DIM MAX_DEBT_LEN = 0
#DIM MAX_DEBT
#DIM DEBT_ID
#DIM RANK_COUNT
#DIM REF CHARA_SORT
#DIM NO_PAGE
#DIM CONST NUM_PAGE = 24
;-----------------------------------------------
;存在するキャラの所持金順表示
;全員出してるので表示人数は{CHARANUM}人となる

;キャラの番号、名前の文字数、レベル、所持金の最大桁数をそれぞれ取得
MAX_NUM_LEN = STRLENS(TOSTR(CHARANUM));番号
FOR COUNT, 0, CHARANUM
	MAX_NAME_LEN = MAX(STRLENS(SAVESTR:COUNT), MAX_NAME_LEN);名前
	MAX_LV_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:9)), MAX_LV_LEN);レベル
	MAX_DEBT_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:582)), MAX_DEBT_LEN);借金
	MAX_RACE_LEN = MAX(STRLENS(GET_LOOK_INFO(COUNT, "种族12")), MAX_RACE_LEN);种族
	MAX_MIND_LEN = MAX(STRLENS(GET_LOOK_INFO(COUNT, "性格")), MAX_MIND_LEN);性格
NEXT

;ソート
;MAX_DEBT = 最小値
MAX_DEBT = 9999

;最大値を算出
FOR DEBT_ID, 1, CHARANUM
	MAX_DEBT = MIN(CFLAG:DEBT_ID:582, MAX_DEBT);借金
NEXT

;無限ループ避け
LOCAL:1 = 0

;最大値の次に大きい数記憶
LOCAL:2 = 9999

RANK_COUNT = 0

WHILE RANK_COUNT < CHARANUM -1
	
	FOR DEBT_ID, 1, CHARANUM
		SIF DEBT_ID == MASTER
			CONTINUE
		;现在の最小値と同じものを選択
		IF MAX_DEBT == CFLAG:DEBT_ID:582
			
			;リストにIDを記憶
			CHARA_SORT:RANK_COUNT = DEBT_ID
			;順位を進める
			RANK_COUNT++
			
		ELSEIF CFLAG:DEBT_ID:582 > MAX_DEBT && CFLAG:DEBT_ID:582 < LOCAL:2
			
			;现在の最小値ではない、现在の最小値の次に小さい数記憶
			LOCAL:2 = CFLAG:DEBT_ID:582
			
		ENDIF
		
	NEXT
	
	;次にソートする最大値をセット
	MAX_DEBT = LOCAL:2
	LOCAL:2 = 9999
	
	;無限ループ避け
	LOCAL:1++
	SIF LOCAL:1 > 1000
		BREAK
	
WEND

LIST_HEADER = NO_PAGE*NUM_PAGE
LIST_FOOTER = (NO_PAGE+1)*NUM_PAGE


;1列ずつ表示
;まおーさま
LOCALS = [{0,MAX_NUM_LEN}]
PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT% 　　　　 %NAME:MASTER, MAX_NAME_LEN,LEFT% LV{CFLAG:MASTER:9, MAX_LV_LEN,RIGHT}
;他の人たち
FOR RANK_COUNT, LIST_HEADER, LIST_FOOTER
	;順位順に引き出す
	COUNT = CHARA_SORT:RANK_COUNT
	;魔王様の重複表示を避ける
	;逆に表示したい時はコメントアウトする
	
	IF RANK_COUNT >= CHARANUM -1 || COUNT == EX_FLAG:0
		PRINTL
		CONTINUE
	ENDIF
	
	;番号
	LOCALS = [{COUNT,MAX_NUM_LEN}]
	PRINTFORM %LOCALS, MAX_NUM_LEN + 2, RIGHT% 
	;行動状態
	CALL SHOW_CHARA_ACT(COUNT)
	;名前、レベル
	PRINTFORM  %SAVESTR:COUNT,MAX_NAME_LEN,LEFT% LV{CFLAG:COUNT:9,MAX_LV_LEN,RIGHT}
	
	;种族と性格
	PRINTFORM  %GET_LOOK_INFO(COUNT, "种族12"),MAX_RACE_LEN,LEFT% - %GET_LOOK_INFO(COUNT, "性格"),MAX_MIND_LEN,LEFT%
	
	;所持金
	PRINTFORM  　借金:{0-CFLAG:COUNT:582,MAX_DEBT_LEN,LEFT} 
	
	;愛、淫乱
	IF TALENT:COUNT:85
		SETCOLOR 255,100,100
		PRINT <爱慕>　
		RESETCOLOR
	ELSEIF TALENT:COUNT:淫乱
		SETCOLOR 255,100,100
		PRINT <淫乱>　
		RESETCOLOR
	ELSE
		SETCOLOR 100,100,100
		PRINT <未陷落>
		RESETCOLOR
	ENDIF
	;お気に入り
	IF CFLAG:COUNT:700
		PRINTFORM  [☆]
	ELSE
		PRINTS " "*5
	ENDIF
	;组队标记
	IF CFLAG:COUNT:1 == 2
		SETCOLOR 255,100,100
	ELSEIF CFLAG:COUNT:1 == 3
		SETCOLOR 100,255,255
	ENDIF
	;小队成员
	IF CFLAG:COUNT:533 > 0 && CFLAG:COUNT:531 == 0 && CFLAG:COUNT:532 == 0 && CFLAG:COUNT:533 != COUNT
		PRINTFORM  %@"队员<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	;小队队长
	ELSEIF CFLAG:COUNT:533 > 0
		PRINTFORM  %@"队长<{CFLAG:COUNT:533}>",MAX_NUM_LEN + 6, LEFT%
	ELSE
		PRINTFORM  %"",MAX_NUM_LEN + 6, LEFT%
	ENDIF
	SIF CFLAG:COUNT:1 == 2 || CFLAG:COUNT:1 == 3
		RESETCOLOR
		
	;帰還フラグ
	IF CFLAG:COUNT:507 == 1
		SETCOLOR 200,200,100
		PRINTFORM  <归还中>
		RESETCOLOR
	ELSE
		PRINTS " "*9
	ENDIF
	PRINTL 
NEXT


;-----------------------------------------------
@SHOW_CHARA_ACT(ARG)
;-----------------------------------------------
;ARG番のキャラの状態（侵攻中とか）を表示する
;CHARA_INFOで使用
IF CFLAG:ARG:1 == 2
	SETCOLOR 255,100,100
	LOCALS = {CFLAG:ARG:501}F侵攻中
	PRINTFORM %LOCALS,8,RIGHT%
ELSEIF CFLAG:ARG:1 == 3
	SETCOLOR 100,255,255
	LOCALS = {CFLAG:ARG:501}F迎击中
	PRINTFORM %LOCALS,8,RIGHT%
ELSEIF CFLAG:ARG:1 == 0
	SETCOLOR 100,100,255
	LOCALS = [可调教]
	PRINTFORM %LOCALS,8,RIGHT%
ELSEIF CFLAG:ARG:1 == 7
	SETCOLOR 100,255,100
	LOCALS = [ 苗床 ]
	PRINTFORM %LOCALS,8,RIGHT%
ELSEIF CFLAG:ARG:1 == 8
	SETCOLOR 100,255,100
	LOCALS = [拘束台]
	PRINTFORM %LOCALS,8,RIGHT%
ELSEIF CFLAG:ARG:1 == 9
	SETCOLOR 255,0,0
	LOCALS = [ NTR中]
	PRINTFORM %LOCALS,8,RIGHT%
ELSEIF CFLAG:ARG:1 == 10
	SETCOLOR 100,255,100
	LOCALS = [育儿室]
	PRINTFORM %LOCALS,8,RIGHT%
ELSE
	LOCALS = -F　―　
	PRINTFORM %LOCALS,8,RIGHT%
ENDIF
RESETCOLOR


;-----------------------------------------------
@COMPARE_CHARA_ACT(ARG, ARG:1, ARG:2 = 2)
;-----------------------------------------------
#FUNCTION

SIF ARG == ARG:1
	RETURNF 0

;0=調教中　1=待機　2=侵攻中　3=迎撃中　4=死亡　5=迎撃成功　
;6=迎撃失敗 7=苗床 8=晒し台 9=NTR中 10=育児室
LOCAL = (CFLAG:ARG:1 + 11 - ARG:2) %11
LOCAL:1 = (CFLAG:(ARG:1):1 + 11 - ARG:2) %11

SIF (LOCAL != LOCAL:1)
	RETURNF LOCAL < LOCAL:1 ? -1 # 1
	
SIF CFLAG:ARG:1 == 2 || CFLAG:ARG:1 == 3 && CFLAG:ARG:501 != CFLAG:(ARG:1):501
	RETURNF CFLAG:ARG:501 < CFLAG:(ARG:1):501 ? -1 # 1
	
RETURNF ARG < ARG:1 ? -1 # 1


;-----------------------------------------------
@CHARA_INFO_INDIVIDUAL_WAPPED(ARG)
;-----------------------------------------------
IF LOCAL:999 != CHARANUM
	REPEAT CHARANUM
		LOCAL:COUNT = COUNT + 1
	REND
	LOCAL:999 = CHARANUM
ENDIF

CALL CHARA_INFO_INDIVIDUAL(ARG, LOCAL)


;-----------------------------------------------
@CHARA_INFO_INDIVIDUAL(ARG, CHARA_SORT)
#DIM NO_SUB_PAGE
#DIM L_LCOUNT = 0
#DIM L_INDX = 0
#DIM REF CHARA_SORT
;-----------------------------------------------
;显示ARG序号的角色信息

$DRAW_PAGE
L_LCOUNT = LINECOUNT
REDRAW 0

ARG = LIMIT(ARG, 0, CHARANUM-1)
L_INDX = ARG != MASTER ? FINDELEMENT(CHARA_SORT,ARG) # - 1

; SIF MASTER == ARG && NO_SUB_PAGE >= 2
	; NO_SUB_PAGE = 2
	; 魔王只有0~2页


;显示角色信息
CALL SHOW_CHARA_INFO(ARG,NO_SUB_PAGE)

;显示按钮
IF NO_SUB_PAGE == 0

;████修改点4████
IF directToHomePage == 1
	directToHomePage = 0
	RETURN 0
ELSE 
	ARG = LIMIT(ARG, 0, CHARANUM-1)
ENDIF 
REPEAT CHARANUM
	IF TALENT:COUNT:292
		CFLAG:COUNT:820 = 666666
	ENDIF 
REND
;████修改点4████

CALL SHOW_BUTTON_NAME_EDIT(0,ARG,0) ;名前を変える
CALL SHOW_BUTTON_NAME_EDIT(1,ARG,1) ;名前を戻す
CALL SHOW_BUTTON_JOB_CHANGE(2,ARG) ;転職
CALL SHOW_BUTTON_TEMPTATION(3,ARG) ;魔の诱惑
CALL SHOW_BUTTON_MARRIAGE(4,ARG) ;結婚
CALL SHOW_BUTTON_CHILD_CARE(5,ARG) ;育児室の訪問

SIF IS_ABLE_TO_ABILITY_UP(ARG)
	PRINT [10] 提升能力　

SIF ARG != 0
	PRINTFORM [9] \@ CFLAG:ARG:700 ? 取消收藏　 # 收藏　 \@
	
ELSEIF NO_SUB_PAGE == 1 || NO_SUB_PAGE == 2
	
SIF IS_TRAINABLE(ARG) == 0
	PRINT [6] 设为目标　
SIF IS_ASSISTABLE(ARG) == 0
	PRINT [7] 设为助手　

CALL SHOW_BUTTON_EQUIP(16,ARG) ;装備の確認

;CALL SHOW_BUTTON_BICH_LEVEL(18,ARG) ;売春積極性
CALL PTJ_BUTTON(ARG)

SIF IS_ABLE_TO_CLOTH(ARG)
	PRINT [11] 更换服装　

SIF CFLAG:ARG:1 == 8
	PRINT [12] 解除固定　
	
SIF CFLAG:ARG:1 == 3
	PRINT [13] 强行召回　
	
SIF CFLAG:ARG:1 == 0 && (BASE:ARG:0 < MAXBASE:ARG:0 || BASE:ARG:1 < MAXBASE:ARG:1)
	PRINT [14] 回复体力　
	
SIF CFLAG:ARG:1 == 0
	PRINT [15] 提升等级　
	
SIF CFLAG:ARG:1 == 0
	PRINT [17] 灵魂转移

[IF_DEBUG]
	PRINT [99] 修改角色
[ENDIF]
ENDIF
PRINTL
DRAWLINE

PRINTLC [101] 前页
PRINTLC [100] 返回
PRINTLC [102] 后页
PRINTL
IF ARG <= 0 || ARG == MASTER
	PRINTLC 　
ELSE
	PRINTLC [500] 前一人
ENDIF
	PRINTLC 　
IF L_INDX >= CHARANUM - 2
	PRINTLC 　
ELSE
	PRINTLC [600] 后一人
ENDIF

;入力処理
$INPUT_LOOP
INPUT

SELECTCASE RESULT
CASE 99
	TRYCALL CHAR_DEBUG(ARG)
	GOTO DRAW_PAGE
CASE 100
	REDRAW 1
	RETURN 0
CASE 101	; 上一页
	IF NO_SUB_PAGE <= 0
		NO_SUB_PAGE = 0
		GOTO CASE_ELSE
	ENDIF
	CLEARLINE LINECOUNT-L_LCOUNT
	NO_SUB_PAGE--
	GOTO DRAW_PAGE
CASE 102	; 下一页
	IF NO_SUB_PAGE >= 3
		NO_SUB_PAGE = 3
		GOTO CASE_ELSE
	ENDIF
	CLEARLINE LINECOUNT-L_LCOUNT
	NO_SUB_PAGE++
	GOTO DRAW_PAGE
CASE 500	; 前一人
	SIF L_INDX == MASTER && CHARA_SORT && MASTER
		L_INDX--
	IF L_INDX < 0
		GOTO CASE_ELSE
	ELSEIF L_INDX == 0
		ARG = MASTER
	ELSE
		ARG = CHARA_SORT:(L_INDX-1)
	ENDIF
	CLEARLINE LINECOUNT-L_LCOUNT
	GOTO DRAW_PAGE
CASE 600	; 后一人

	IF ARG == MASTER && CHARA_SORT == MASTER && MASTER
		ARG = CHARA_SORT:(L_INDX+2)
	ELSEIF ARG == MASTER
		ARG = CHARA_SORT:0
	ELSEIF L_INDX < 0
		GOTO CASE_ELSE
	ELSE 
		ARG = CHARA_SORT:(L_INDX+1)
	ENDIF
	CLEARLINE LINECOUNT-L_LCOUNT
	GOTO DRAW_PAGE
CASE 6	; 设为调教对象 ref @SELECT_TARGET
	IF IS_TRAINABLE(ARG) == 0
		;調教可能な対象
		TARGET = ARG
		FLAG:1 = TARGET ;前回の調教対象を上書き
		PRINTFORMW %SAVESTR:ARG%成为了调教对象……
		GOTO DRAW_PAGE
	ELSE
		GOTO CASE_ELSE
	ENDIF
CASE 7	; 设置助手 ref SELECT_ASSI
	IF IS_ASSISTABLE(ARG) == 0
		;助手可能な対象
		ASSI = ARG
		FLAG:2 = ASSI ;前回の助手を上書き
		PRINTFORMW %SAVESTR:ARG%成为了助手……
		GOTO DRAW_PAGE
	ELSE
		GOTO CASE_ELSE
	ENDIF
CASE 10 ; 能力提升
	SIF !IS_ABLE_TO_ABILITY_UP(ARG)
		GOTO CASE_ELSE
	CALL ABILITY_UP_CORE(ARG)
	GOTO DRAW_PAGE
CASE 11 ; 变更服装
	SIF !IS_ABLE_TO_CLOTH(ARG)
		GOTO CASE_ELSE
	LOCAL = TARGET
	CALL TAILOR_CORE(ARG)
	TARGET = LOCAL
	GOTO DRAW_PAGE
CASE 12 ;拘束台解放
	SIF CFLAG:ARG:1 != 8
		GOTO CASE_ELSE
	CFLAG:ARG:1 = 0
	RESULT = 0
CASE 13 ;强行召回
	SIF CFLAG:ARG:1 != 3
		GOTO CASE_ELSE
	CALL CHARA_INFO_CALLBACK,ARG
	RESULT = 0
CASE 14 ;回复体力
	SIF CFLAG:ARG:1 != 0 || !(BASE:ARG:0 < MAXBASE:ARG:0 || BASE:ARG:1 < MAXBASE:ARG:1)
		GOTO CASE_ELSE
	CALL CHARA_INFO_RECOVER_HP,ARG
	CLEARLINE LINECOUNT-L_LCOUNT
	GOTO DRAW_PAGE
CASE 15 ;提升等级
	SIF CFLAG:ARG:1 != 0
		GOTO CASE_ELSE
	CALL CHARA_INFO_UP_LEVEL,ARG
	CLEARLINE LINECOUNT-L_LCOUNT
	GOTO DRAW_PAGE
CASE 17 ;灵魂转移
	SIF CFLAG:ARG:1 != 0
		GOTO CASE_ELSE
	CALL TRANSFER_SOUL, ARG
	ARG = RESULT
	;CLEARLINE LINECOUNT-L_LCOUNT
	GOTO DRAW_PAGE
CASE 0
	; 名前変更
	CALL CHARA_INFO_NAME_EDIT(ARG,0)
CASE 1
	; 名前のリセット
	CALL CHARA_INFO_NAME_EDIT(ARG,1)
CASE 2
	; 転職
	CALL CHARA_INFO_JOB_CHANGE(ARG)
CASE 3
	; 魔の诱惑
	CALL TEMPTATION(ARG)
CASE 4
	; 結婚
	CALL MARRIAGE(ARG)
CASE 5
	; 育児室
	CALL CHILD_CARE_CHARA(ARG)
CASE 8
	CALL RANDOM_SELF_CALL(ARG,1)
	CLEARLINE LINECOUNT-L_LCOUNT
	GOTO DRAW_PAGE
CASE 9
	IF ARG != 0
		CFLAG:ARG:700 = !CFLAG:ARG:700
		GOTO DRAW_PAGE
	ENDIF
CASE 16
	LOCAL = LINECOUNT
	CALL EQUIP_ST_SHOW, ARG
	WAIT
	GOTO DRAW_PAGE
CASE 18
	IF GETBIT(EX_FLAG:9000,2)
		CALL SET_PTJ_LEVEL, ARG
	ELSE
		CALL SET_BICH_LEVEL, ARG
	ENDIF
CASE IS >= 15000		;跳转到其他角色
	LOCAL = RESULT - 15000
	SIF LOCAL >= CHARANUM
		GOTO CASE_ELSE
	ARG = LOCAL
	GOTO DRAW_PAGE
CASEELSE
	$CASE_ELSE
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDSELECT

;返り値による処理
;一応、0なら再度一覧表示、1ならターンエンド、2なら再入力となるようにしてある
IF RESULT == 0 || RESULT == 1
	REDRAW 1
	RETURN RESULT
ENDIF
GOTO INPUT_LOOP

