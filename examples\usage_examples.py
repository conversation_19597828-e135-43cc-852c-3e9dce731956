"""
示例：如何使用ERA AI Agent系统
"""

import asyncio
import logging
from pathlib import Path

# 导入系统组件
from era_ai_agent import (
    initialize_config, 
    get_config, 
    GameConfig, 
    ERAAgentWorkflow,
    get_game_generator
)

async def basic_game_generation_example():
    """基本游戏生成示例"""
    print("=== 基本游戏生成示例 ===")
    
    # 1. 初始化配置
    config_initialized = initialize_config(".env")
    if not config_initialized:
        print("配置初始化失败，请检查.env文件")
        return
        
    # 2. 创建游戏配置
    game_config = GameConfig(
        game_title="我的第一个ERA游戏",
        game_description="这是一个使用AI生成的ERA游戏示例",
        character_count=3,
        features=["basic", "training", "shop"],
        output_path="./example_game"
    )
    
    print(f"游戏标题: {game_config.game_title}")
    print(f"角色数量: {game_config.character_count}")
    print(f"游戏特性: {', '.join(game_config.features)}")
    
    # 3. 使用代理生成器生成游戏
    generator = get_game_generator()
    
    try:
        results = await generator.generate_complete_game(game_config)
        
        if "error" not in results:
            print("\\n✓ 游戏生成成功!")
            print(f"生成的ERB文件: {len(results['erb_files'])}")
            print(f"生成的CSV文件: {len(results['csv_files'])}")
            print(f"生成的配置文件: {len(results['config_files'])}")
            print(f"创建的角色: {len(results['characters'])}")
            
            # 保存文件
            saved_files = await generator.save_game_files(game_config, results)
            print(f"\\n保存了 {len(saved_files)} 个文件到: {game_config.output_path}")
            
        else:
            print(f"\\n✗ 游戏生成失败: {results['error']}")
            
    except Exception as e:
        print(f"\\n✗ 生成过程出错: {e}")

async def workflow_based_generation_example():
    """基于工作流的生成示例"""
    print("\\n=== 工作流生成示例 ===")
    
    # 创建游戏配置
    game_config = GameConfig(
        game_title="工作流ERA游戏",
        game_description="使用LangGraph工作流生成的ERA游戏",
        character_count=5,
        features=["basic", "training", "shop", "battle"],
        output_path="./workflow_game"
    )
    
    # 创建工作流
    config = get_config()
    workflow = ERAAgentWorkflow(config.database_path)
    
    try:
        # 运行工作流
        result = await workflow.run_workflow(game_config, thread_id="example_workflow")
        
        if result["status"] == "success":
            print("\\n✓ 工作流执行成功!")
            print(f"完成的步骤: {', '.join(result['completed_steps'])}")
            print(f"生成的文件: {len(result['generated_files'])}")
            
            # 显示消息摘要
            print(f"\\n代理消息数量: {len(result['messages'])}")
            
        else:
            print(f"\\n✗ 工作流执行失败: {result['error']}")
            
    except Exception as e:
        print(f"\\n✗ 工作流出错: {e}")

async def rag_knowledge_query_example():
    """RAG知识查询示例"""
    print("\\n=== RAG知识查询示例 ===")
    
    try:
        from era_ai_agent.core.rag import get_rag_system
        
        rag = get_rag_system()
        
        # 查询ERA语法相关问题
        questions = [
            "如何定义ERB函数",
            "CSV文件的格式是什么", 
            "SYSTEM_TITLE函数的作用",
            "如何使用FLAG变量"
        ]
        
        for question in questions:
            print(f"\\n问题: {question}")
            try:
                answer = rag.query(question, "syntax")
                print(f"回答: {answer['context'][:200]}...")
            except Exception as e:
                print(f"查询失败: {e}")
                
    except Exception as e:
        print(f"RAG系统不可用: {e}")

async def mcp_tools_example():
    """MCP工具使用示例"""
    print("\\n=== MCP工具使用示例 ===")
    
    try:
        from era_ai_agent.tools.mcp_tools import get_tool_manager
        
        tool_manager = get_tool_manager()
        
        # 获取所有可用工具
        tools = tool_manager.get_all_schemas()
        print(f"可用工具数量: {len(tools)}")
        
        for tool in tools:
            print(f"- {tool['name']}: {tool['description']}")
            
        # 使用ERB语法验证工具
        erb_code = '''@TEST_FUNCTION
#FUNCTION
PRINTL Hello World
RETURN 1
'''
        
        result = await tool_manager.call_tool(
            "validate_erb_syntax", 
            {"erb_code": erb_code}
        )
        
        if result["success"]:
            validation = result["result"]
            print(f"\\nERB语法验证结果:")
            print(f"有效: {validation['valid']}")
            print(f"错误: {validation['errors']}")
            print(f"警告: {validation['warnings']}")
        else:
            print(f"工具调用失败: {result['error']}")
            
    except Exception as e:
        print(f"MCP工具不可用: {e}")

def character_customization_example():
    """角色定制示例"""
    print("\\n=== 角色定制示例 ===")
    
    # 定义自定义角色
    custom_characters = [
        {
            "id": 0,
            "name": "艾莉娜",
            "callname": "艾莉",
            "nickname": "小艾",
            "description": "温柔的精灵法师",
            "personality": "善良、聪明、有点害羞"
        },
        {
            "id": 1, 
            "name": "卡特琳娜",
            "callname": "卡特",
            "nickname": "小猫",
            "description": "勇敢的人类战士",
            "personality": "勇敢、直率、有正义感"
        },
        {
            "id": 2,
            "name": "露娜",
            "callname": "露娜",
            "nickname": "月月",
            "description": "神秘的月精灵",
            "personality": "神秘、优雅、有点傲慢"
        }
    ]
    
    print("自定义角色列表:")
    for char in custom_characters:
        print(f"- {char['name']} ({char['callname']}): {char['description']}")
        
    # 这些角色信息可以在游戏生成时使用
    print("\\n这些角色信息可以在GameConfig中使用，或者通过CharacterAgent生成时参考。")

async def advanced_configuration_example():
    """高级配置示例"""
    print("\\n=== 高级配置示例 ===")
    
    # 显示当前配置
    config = get_config()
    
    print("当前系统配置:")
    print(f"- LM Studio: {config.lm_studio_base_url}")
    print(f"- Ollama: {config.ollama_base_url}")
    print(f"- 数据库: {config.database_path}")
    print(f"- ERA数据路径: {config.era_syntax_data_path}")
    print(f"- 启用RAG: {config.enable_rag}")
    print(f"- 启用MCP工具: {config.enable_mcp_tools}")
    print(f"- 最大并发代理: {config.max_concurrent_agents}")
    
    # 性能设置示例
    print(f"\\n性能设置:")
    print(f"- 代理超时: {config.agent_timeout}秒")
    print(f"- RAG Top-K: {config.rag_top_k}")
    print(f"- 嵌入批次大小: {config.embedding_batch_size}")

async def main():
    """主示例函数"""
    print("ERA AI Agent System 使用示例")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 运行各种示例
        await basic_game_generation_example()
        await workflow_based_generation_example() 
        await rag_knowledge_query_example()
        await mcp_tools_example()
        character_customization_example()
        await advanced_configuration_example()
        
        print("\\n" + "=" * 50)
        print("所有示例执行完成!")
        print("\\n使用说明:")
        print("1. 确保LM Studio和Ollama服务正在运行")
        print("2. 检查.env配置文件中的路径设置")
        print("3. 根据需要调整GameConfig参数")
        print("4. 生成的游戏文件可以直接与Emuera引擎一起使用")
        
    except Exception as e:
        print(f"\\n示例执行出错: {e}")
        print("请检查系统配置和服务状态")

if __name__ == "__main__":
    asyncio.run(main())