﻿;eraIM@Sから導入しました(eramaou)


;=================================================
;媚药中毒関連の関数群
;=================================================
;-------------------------------------------------
;媚药中毒の発病と回復
;-------------------------------------------------
@APHRODISIAC_ADDICT
;体内媚药残留度を7日ごとに1減らす
IF (DAY + 1) % 7 == 0
	IF CFLAG:31 > 0
		CFLAG:31 -= 1
		SIF CFLAG:31 < 0
			CFLAG:31 = 0
	ENDIF
			
	;[媚药中毒]の場合は禁断症状チェックも行う
	IF TALENT:46
	    IF  CFLAG:1 != 9
		    IF CFLAG:32
			   CFLAG:32 = 0
		    ELSE
			   CALL PRECIPITATE_WITHDRAWAL
			ENDIF   
		ENDIF
	ENDIF
ENDIF

;【媚药中毒】の消失判定
;体内媚药残留度が0になれば[媚药中毒]消失
IF CFLAG:31 == 0 && TALENT:46
	PRINTFORML %SAVESTR:TARGET%的样子变了……
	PRINTFORML 体内的媚药效果被根除，%SAVESTR:TARGET%的药瘾消失了。
	PRINTFORML %SAVESTR:TARGET%的【%TALENTNAME:46%】消除了。
	WAIT
	TALENT:46 = 0
ENDIF
;【媚药中毒】の取得判定
;[容易上瘾]がなければ体内媚药残留度が12以上、あれば9以上で[媚药中毒]取得
IF ((TALENT:86 == 0 && CFLAG:31 >= 12) || (TALENT:72 && CFLAG:31 >= 9)) && TALENT:46 == 0
	PRINTFORML %SAVESTR:TARGET%的样子有点奇怪……
	PRINTFORML 媚药的过量使用，令%SAVESTR:TARGET%沾上药瘾了。
	PRINTFORML %SAVESTR:TARGET%获得了【%TALENTNAME:46%】。
	WAIT
	TALENT:46 = 1
	;中毒になりたてということで、ボーナスとして体内媚药残留度を一律15以上に（おめでとう！）
	SIF CFLAG:31 < 15
		CFLAG:31 = 15
ENDIF
;【疯狂】の取得判定
;[容易上瘾]がなければ体内媚药残留度が40以上、あれば30以上で[疯狂]取得
IF (CFLAG:31 >= 40 || (TALENT:72 && CFLAG:31 >= 30)) && TALENT:123 == 0
	PRINTFORML %SAVESTR:TARGET%的样子有点奇怪……
	PRINTFORML %SAVESTR:TARGET%随着媚药的过量使用，人也变得暴躁了。
	PRINTFORML %SAVESTR:TARGET%获得了【%TALENTNAME:123%】。
	PRINTL 
	TALENT:123 = 1
ENDIF
;【廃人】の取得判定
;[容易上瘾]がなければ体内媚药残留度が100以上、あれば75以上で[崩坏]取得
IF (CFLAG:31 >= 100 || (TALENT:72 && CFLAG:31 >= 75)) && TALENT:9 == 0
	PRINTFORML %SAVESTR:TARGET%的样子有点奇怪……
	PRINTFORML %SAVESTR:TARGET%随着媚药的过量使用，完全变成了废人。
	PRINTFORML %SAVESTR:TARGET%的精神变成【%TALENTNAME:9%】了。
	PRINTL 
	TALENT:9 = 1
ENDIF
;-------------------------------------------------
;禁断症状イベント
;-------------------------------------------------
@PRECIPITATE_WITHDRAWAL
;U=[治疗][献身的]持ちによる禁断症状緩和効果
;V=禁断症状の酷さ
;W=ステータス悪化の内容(＆ステータス悪化が生じたか否か)
U = 0
V = 0
W = 0
PRINTFORML %SAVESTR:TARGET%在诉说着自己的身体不适应症状。
PRINTL 看来，春药中毒的禁断症状出现了……
WAIT

IF ITEM:26
	$INPUT_LOOP_01
	PRINTFORML 给予%SAVESTR:TARGET%媚药吗？
	PRINTL  [0] - 好的
	PRINTL  [1] - 不要
	INPUT
	IF RESULT == 0
		IF CFLAG:1 == 2
			PRINTFORML %SAVESTR:TARGET%为了获得媚药，诱惑着魔王军上下人众。
			PRINTFORMW 流下了贪欲的口水，完全忘记了作为勇者的使命。
			PRINTFORML %SAVESTR:TARGET%陷落了。
			CFLAG:1 = 0
			MONEY += 100 * CFLAG:9
			EX_FLAG:4444 += 100 * CFLAG:9
			PRINTFORMW 获得{100 * CFLAG:9}G！
			CFLAG:506 = 1
			CFLAG:507 = 0
			
			CALL PARTY_DEL, TARGET
		ENDIF
		PRINTFORML %SAVESTR:TARGET%抢过了装着浓缩媚药的瓶子，
		PRINTFORML 马上如饥似渴地一饮而尽，放心地叹了一口气。
		WAIT
		CFLAG:31 += 1
		ITEM:26 -= 1
		RETURN 0
	ELSEIF RESULT != 1
		GOTO INPUT_LOOP_01
	ENDIF	
ENDIF

IF CFLAG:1 == 2
	PRINTFORML 数小时后%SAVESTR:TARGET%身体的颤抖终于停了下来。
	;体力が減る
	BASE:0 -= 300
	SIF BASE:0 < 1
		BASE:0 = 1
	RETURN 0
ENDIF


;[治疗][献身的]持ちの人数をカウント
;ただし待機中のみカウント
REPEAT CHARANUM
	SIF TALENT:COUNT:117 && CFLAG:COUNT:1 == 0
		U += 1
	SIF TALENT:COUNT:63 && CFLAG:COUNT:1 == 0
		U += 1
REND

;体内媚药残留度と[治疗][献身的]持ちによる禁断症状緩和効果に応じてチェック回数決定
V = (CFLAG:31 / 10) + 1 - U
IF V < 1
	V = 1
ELSEIF V > 10
	V = 10
ENDIF

;チェック回数は最低1回～最高10回
REPEAT V
	;40%の確率でステータス悪化イベント発生
	;最高の10回チェックした場合は99.996%の確率で発生する計算
	IF RAND:100 < 40
		CALL SUFFER_FROM_WITHDRAWAL
		BREAK
	ENDIF
REND

IF W
	PRINTFORML 被禁断症状折磨着的%SAVESTR:TARGET%痛苦地在地上打滚，
	PRINTL 持续数小时的癫狂的行为也终于让她感到累了，症状被平息，终于老实了下来。
	PRINTL 不过，不仅是她本人，护理人员也感到筋疲力尽了……
	WAIT 
	PRINTFORML %SAVESTR:TARGET%的体力和气力衰退了。

	;とりあえず表示はせずにこっそり減らす方向で
	MAXBASE:0 -= 50
	SIF BASE:0 > MAXBASE:0
		BASE:0 = MAXBASE:0
	;最大体力は600まで下がる（それ以下にはならない）
	SIF MAXBASE:0 < 600
		MAXBASE:0 = 600
	
	MAXBASE:1 -= 50
	SIF BASE:1 > MAXBASE:1
		BASE:1 = MAXBASE:1
	;最大気力は100まで下がる（それ以下にはならない）
	SIF MAXBASE:1 < 100
		MAXBASE:1 = 100
	
	BASE:0 -= 500
	SIF BASE:0 < 1
		BASE:0 = 1

	;看病していた人もぐったりします
	;ただし待機中のみ看病します
	REPEAT CHARANUM
		;[治疗][献身的]持ちは確実に看病している
		IF (TALENT:COUNT:117 || TALENT:COUNT:63) && CFLAG:COUNT:1 == 0
			BASE:COUNT:0 -= 200
			SIF BASE:COUNT:0 < 1
				BASE:COUNT:0 = 1
		;そうでない場合でも1/3の確率で看病に当たっている
		ELSEIF RAND:3 == 0 && CFLAG:COUNT:1 == 0
			BASE:COUNT:0 -= 200
			SIF BASE:COUNT:0 < 1
				BASE:COUNT:0 = 1
		ENDIF
	REND
ELSE
	PRINTFORML 数小时后，%SAVESTR:TARGET%身体的颤抖终于停止了，
	PRINTL 护理人员也辛苦了，
	PRINTL 这次总算平安度过了……
	WAIT
	;でも体力は減る
	BASE:0 -= 300
	SIF BASE:0 < 1
		BASE:0 = 1

	;看病していた人もぐったりします
	REPEAT CHARANUM
		;[治疗][献身的]持ちは確実に看病している
		IF TALENT:COUNT:117 || TALENT:COUNT:63
			BASE:COUNT:0 -= 100
			SIF BASE:COUNT:0 < 1
				BASE:COUNT:0 = 1
		;そうでない場合でも1/3の確率で看病に当たっている
		ELSEIF RAND:3 == 0
			BASE:COUNT:0 -= 100
			SIF BASE:COUNT:0 < 1
				BASE:COUNT:0 = 1
		ENDIF
	REND
ENDIF
PRINTL 

@SUFFER_FROM_WITHDRAWAL
W = RAND:50 - V + (U * 2)

IF W < 5
	IF TALENT:9 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_WRECK
	ELSEIF TALENT:123 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_CRAZY
	ELSEIF RELATION:0 == 0 || RELATION:0 > 50
		CALL PRECIPITATE_WITHDRAWAL_BE_A_MISANTHROPIST
	ELSEIF TALENT:34 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_RESISTER
	ELSEIF TALENT:26 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_DEPRESSION
	ELSEIF TALENT:22 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
	ELSE
		CALL PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
	ENDIF
ELSEIF W < 10
	IF TALENT:123 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_CRAZY
	ELSEIF RELATION:0 == 0 || RELATION:0 > 50
		CALL PRECIPITATE_WITHDRAWAL_BE_A_MISANTHROPIST
	ELSEIF TALENT:34 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_RESISTER
	ELSEIF TALENT:26 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_DEPRESSION
	ELSEIF TALENT:22 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
	ELSE
		CALL PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
	ENDIF
ELSEIF W < 15
	IF TALENT:34 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_RESISTER
	ELSEIF TALENT:26 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_DEPRESSION
	ELSEIF TALENT:22 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
	ELSE
		CALL PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
	ENDIF
ELSEIF W < 20
	IF TALENT:34 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_RESISTER
	ELSEIF TALENT:26 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_DEPRESSION
	ELSEIF TALENT:22 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
	ELSE
		CALL PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
	ENDIF
ELSEIF W < 25
	IF TALENT:26 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_DEPRESSION
	ELSEIF TALENT:22 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
	ELSE
		CALL PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
	ENDIF
ELSEIF W < 30
	IF TALENT:22 == 0
		CALL PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
	ELSE
		CALL PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
	ENDIF
ELSE
	PRINTL 
ENDIF
PRINTL 

W = 1


@PRECIPITATE_WITHDRAWAL_FALL_INTO_DISFAVOR
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，被%CALLNAME:MASTER%嫌弃了。
;こっそり好感度をマイナス
CFLAG:2 -= 200

@PRECIPITATE_WITHDRAWAL_BE_A_ATHYMIA
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，连感情都失去了。
PRINTFORMW %SAVESTR:TARGET%获得了【感情淡薄】。
TALENT:22 = 1

@PRECIPITATE_WITHDRAWAL_BE_A_DEPRESSION
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，对人生看法灰暗。
PRINTFORMW %SAVESTR:TARGET%获得了【悲观的】。
TALENT:26 = 1

@PRECIPITATE_WITHDRAWAL_BE_A_RESISTER
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，变得不和善了。
PRINTFORMW %SAVESTR:TARGET%获得了【抵抗】。
TALENT:34 = 1

@PRECIPITATE_WITHDRAWAL_BE_A_MISANTHROPIST
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，被%CALLNAME:MASTER%嫌弃了。
IF RELATION:0 == 0
	RELATION:0 = 50
ELSE
	RELATION:0 -= 50
	SIF RELATION:0 < 30
		RELATION:0 = 30
ENDIF
;こっそり好感度もマイナス
CFLAG:2 -= 200

@PRECIPITATE_WITHDRAWAL_BE_A_CRAZY
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，充满攻击性了。
PRINTFORMW %SAVESTR:TARGET%获得了【疯狂】。
TALENT:123 = 1

@PRECIPITATE_WITHDRAWAL_BE_A_WRECK
PRINTFORMW %SAVESTR:TARGET%的样子明显不对头……
PRINTFORMW %SAVESTR:TARGET%饱受禁断症状的痛苦，完全变成一个废人了。
PRINTFORMW %SAVESTR:TARGET%的精神【崩坏】了……
TALENT:19 = 1

