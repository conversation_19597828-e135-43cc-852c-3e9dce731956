﻿

◆◆◆ 家族構成設定パッチ ◆◆◆

※注意※
maou450用ぱっちっち20150903を適用した上で、このパッチを導入してください
せっかく手を入れてくださった出産経験、上書きする形になって申し訳ないです

勇者の家族構成の設定を行うパッチです。引き継いだデータやあなたは、
すべて「秘密」扱いになります

◆使用した領域
Talent.csv
TALENT:320 = 家族構成
;0=秘密
;            1の位 = 1のとき、家族構成設定あり
;           10の位 = 勇者以前の結婚歴（回数）
;          100の位 = 勇者以前に生んだ娘
;        1,000の位 = 勇者以前に生んだ息子
;       10,000の位 = 結婚相手との関係
;（0=未婚 1=結婚 2=離婚 3=現在の伴侶と重婚 4=現在の伴侶と契り、離婚を宣言）
;（5=死別）
;      100,000の位 = 姉の数
;    1,000,000の位 = 兄の数
;   10,000,000の位 = 妹の数
;  100,000,000の位 = 弟の数
;1,000,000,000の位 = 自分と結婚相手の性別
;（自分相手 0=女男 1=女ふた 2=女女 3=ふた女 4=ふた男 5=ふたふた）
;（6=男女 7=男ふた 8=男男）

◆初期設定
LOOK.ERB
好きなもの設定の後に家族設定セクションを追加

◆表示
LOOK.ERB
@GET_LOOK_INFO
呼び出しに対応
@LOOK_INFO
表示に対応
CHARA_INFO_SHOW.ERB
結婚相手表示移動

◆イベントでの整合性
EXECUTION.ERB
家族に言及していた場所を対応
抜けがあったら気軽に教えてください

◆出産経験を設定どおりに
CHAR_MAKE.ERB

◆結婚によって重婚状態に
CHARA_MARRIAGE.ERB
元夫との離婚宣言は未実装。口上とかで設定してもいいかも

結婚回数は未実装ですが、設定はされているので何かに使えたら

;--------------------------

◆おまけのデバッグ
COMF3_自慰.ERB
催眠の罠であなたが対象になるバグ修正

LOOK.ERB 269行目
IFに直しておきました

SHOP_LABO.ERB
609、668、1901、1902行目
==に直しておきました

◆おまけの設定
eramaouフラグまとめ.txt
産卵フラグを記載
　TFLAG:120 = 調教対象がVワームを産卵
　TFLAG:121 = 調教対象がAワームを産卵

SYSTEM_SOURCE_SUB1.ERB
TFLAGに代入



