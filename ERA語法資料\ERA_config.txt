﻿==环境==
[[File:Era config environment.jpg|thumb|『环境』标签页]]

=== 启用鼠标 ===
マウスを使用する

是否接受来自鼠标的输入。

=== 显示菜单栏 ===
メニューを使用する

是否显示菜单栏以及菜单。

=== 启用调试命令 ===
デバッグコマンドを使用する

主控制台是否接受[[调试命令]]。默认为NO。

调试控制台不受此选项控制，始终可以输入调试命令。

=== 允许多重启动 ===
多重起動を許可する

是否允许同时运行多个Emuera实例。

=== 启用键盘宏 ===
キーボードマクロを使用する

是否允许使用F1~F12的键盘宏。

=== 自动保存进度 ===
オートセーブを行なう

是否在游戏画面回到主界面（<code>BEGIN SHOP</code>指令执行）的同时进行自动保存。

注意自动保存功能可能由于ERB脚本中具体的实现而并不能发挥作用。

=== 在sav文件夹中创建存档 ===
セーブデータをsavフォルダ内に作成する

将存档文件直接保存在程序所在的文件夹中，还是将其保存在名为SAV的子文件夹中。
默认为NO。

当设置为YES，且SAV文件夹不存在时，程序将弹出对话框询问是否自动移动sav文件。

若在对话框中选择“是”，则程序将会把所有现存的global.sav和save*.sav文件全部转移到SAV文件夹中。

当设置从YES切换为NO时，请手动移动sav文件。

=== 记录日志的行数 ===

履歴ログの行数

主控制台记录的日志的最大行数。下限为500行。

=== 死循环超时警告（毫秒） ===
無限ループ警告までのミリ秒数

指定一个时间长度，若从用户上次输入到现在的时间超过这个时间长度，则弹出警告对话框。

若果设置为0，则这一功能被禁用。

下面的脚本虽然是一个无限循环，但由于循环体内有<code>WAIT</code>指令，因此不会被弹出警告。

 	$LOOP
 	WAIT
 	GOTO LOOP

如果在消息中点击右键跳过，则仍会弹出警告。{{Uncertain}}

=== 使用存档数量 ===

使用するセーブデータ数

加载游戏画面中显示的存档数量。下限20，上限80。

===关联文本编辑器 ===
関連づけるテキストエディタ

设置点击错误信息时用来打开脚本文件的文本编辑器。

===编辑器运行参数===
コマンドライン引数

在使用文本编辑器打开脚本文件时的命令行参数。（注：Notepad++为<code>-n</code>）



== 显示 ==
[[File:Era config display.jpg|thumb|center|『显示』标签页]]



== 窗口 ==
[[File:Era config window.jpg|thumb|center|『窗口』标签页]]



== 文字 ==
[[File:Era config text.jpg|thumb|center|『文字』标签页]]



== 系统 ==
[[File:Era config sys.jpg|thumb|center|『系统』标签页]]



== 系统2 ==
[[File:Era config sys2.jpg|thumb|center|『系统2』标签页]]



== 兼容性 ==
[[File:Era config comp.jpg|thumb|center|『兼容性』标签页]]



== 调试 ==
[[File:Era config debug.jpg|thumb|center|『调试』标签页]]



== 外部链接 ==
* {{Jp}}Emuera Wiki(2015), [https://osdn.jp/projects/emuera/wiki/config コンフィグ設定]

[[分类:EmueraWiki]]