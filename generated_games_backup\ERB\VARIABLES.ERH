#DEFINE MAX_CHARANUM 90

;==================================================
; 游戏全局状态标志
DIM FLAG, 100
; 临时游戏状态标志，通常在调教开始时重置
DIM TFLAG, 100
; 剧情事件标志
DIM EVENT_FLAG, 50
; 当前游戏状态 (0:主菜单, 1:训练, 2:商店, 3:事件, 4:休息, 5:地图)
DIM GAME_STATE
; 当前所在地点ID
DIM CURRENT_LOCATION

; 商店相关变量
DIM SHOP_ITEM_PRICE, 100
DIM SHOP_ITEM_STOCK, 100
DIM SHOP_LAST_SELECTED_ITEM
DIMS SHOP_ITEM_NAME, 100

; 调教相关变量
DIM PALAM_LV_BOUNDARY, 20
DIM EXP_LV_BOUNDARY, 20
; 用于储存保留的TRAINNAEM, 函数@TRAIN_NAME_INIT用于初始化
DIMS TRAIN_NAME, 500

; 角色生成相关变量
; 非ユニーク性格的TALENT番号的配列
DIM ID_OF_GENERAL_CHARASTERISTICS, 20
; 头发颜色的配列
DIMS ARR_HAIRCOLOR, 12
; 眼睛颜色的配列
DIMS ARR_EYECOLOR, 10
; 肤色的配列
DIMS ARR_SKINCOLOR, 5
; 种族名称的配列
DIMS ARR_RACE_NAME, 10

; 游戏世界相关变量
DIMS ARR_LOCATION_NAME, 20

; 骑士与玩偶主题特有变量
DIM KNIGHT_RANK ; 骑士等级
DIM DOLL_COMPLETION ; 玩偶完成度或制作进度