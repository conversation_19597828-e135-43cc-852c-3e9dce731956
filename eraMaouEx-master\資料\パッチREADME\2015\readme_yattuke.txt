﻿＊改変など取り扱いはご自由に＊

[パッチ製作環境]
eramaou v0.400fix1 に20150710現在の後続パッチ全部のせ状態

[このパッチに含まれるファイル]
・CHARA_BODY.ERB
・EVENT_PREGNANCY.ERB
・SHOP_LABO.ERB
・System.erb
・readme_yattuke.txt

[どんなパッチ？]
パッチ効能は主なところで2つあります。
・まおーさまが痴呆化します。
・まおーさまがおっぱい星人になります。

[実際的には？]
・System.erb
　これを導入することでまおーさまが目の前にいるはずの勇者さんのことを胡乱に認識しだします。
　つまり初期ランダム奴隷のリロールができるようになります。
　こんな子だったかな？　いや、違うな…？
　実存在である勇者さんを間近に置きながら、好き放題現実を塗り替えてください。認識は力です。

・CHARA_BODY.ERB / EVENT_PREGNANCY.ERB / SHOP_LABO.ERB
　これらを導入することでまおーさまがおっぱいサイズの変化を見逃さなくなります。
　つまり胸特徴の変化に合わせてバストサイズとそれに伴う体重変化が更新反映されるようになります。
　妊娠/育児終了での増減、ラボでの増減に対応しているはず。他に変動するとこあったかしら？

　ついでにCHARA_BODY.ERBに関してはバグフィックス（？）も。
　元ファイルでいうところの584行目、GETBIT(FLAG:5,13) →GETBIT(FLAG:5,15)　じゃないかと思うの。
　スリーサイズフラグを読みにいってないせいでまっさらなコンフィグ状態から名前OFFスリーサイズONにすると
　詳細ステータスの生成がされずに0が並ぶスリーサイズ画面になっちゃうのね。

[変更点]
・System.erb
	138行目あたりへ、ENDING.ERB から要素固定した奴隷獲得処理を移植した。
・CHARA_BODY.ERB
	@CHAR_SIZE_GENERATE に引数追加。0で通常のまま、1を渡されたら胸関連のみつまみ食いの飛び飛び処理をするように。
	そのためにラベルを2つ($CALC_BUST, $CALC_WEIGHT)追加。
	および上述の修正。
・EVENT_PREGNANCY.ERB
	794・815行目あたり、それぞれの胸部変化処理でコンフィグONになっているとき
	@CHAR_SIZE_GENERATE をつまみ食いモードで呼ぶように。
・SHOP_LABO.ERB
	280・357行目あたり、それぞれの胸部変化処理でコンフィグONになっているとき
	@CHAR_SIZE_GENERATE をつまみ食いモードで呼ぶように。


[ここまでお付き合いいただいた方へ]
ありがとうございました。


[そのほか]
@CHAR_SIZE_GENERATE の処理を読み直すに、そのまま回してRESULT:3と4だけ使えばよかった気がしてきた。
いやでも計算量減るから負荷的にやさしくなるかもだし……せっかくだからこのまま上げてみることに。

性転換に胸部素質のリセット処理とかが入ってない。意外。要修正なのかしら。

それから正直リロール部分はモジュール化されてると思ってたからびっくり。
メンテ性や（ロック項目を増やしたり変えたりといった）拡張性のことを考えると @CHAR_MAKE 内に統合してしまいたい感じ。
気力があったらがんばってみたい。