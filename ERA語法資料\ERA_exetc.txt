﻿[[PageOutline(start=3, depth=4, type=unordered)]]
=== 一般
==== 行末コメント
{{{
	A = B ;AにBを代入
}}}
このように行の末尾にコメントを挿入できます[[BR]]
ただし、一部例外があり、「PRINT」命令のように引数が単純文字列の命令の場合にはコメント化されず文字列の一部と評価されます
{{{
	PRINT foobar;ほげほげ
}}}
この場合、「foobar;ほげほげ」がPRINTされます

==== 行の連結
{{{
	{
		DIM CONST HOGE =
			1,2,3,4
	}
}}}
と書くと「#DIM CONST HOGE = 1,2,3,4」と解釈されます[[BR]]
'{'、'}'の行にはホワイトスペース以外の他の文字が存在してはなりません[[BR]]
改行記号のある位置には半角スペースが補われます[[BR]]
つまり関数名・変数名の途中で行を分割することはできないほか、[[BR]]
PRINTなどを分割すると表示文字列の中に改行だった半角スペースが含まれます[[BR]]
Emueraの文法解釈において行連結処理はコメントの解釈より先に行われます[[BR]]
つまり
{{{
	{
		#DIM CONST HOGE =
			1,2,3,4 ;コメント
			,5,6,7,8
	}
}}}
は「#DIM CONST HOGE = 1,2,3,4 ;コメント ,5,6,7,8」となり、[[BR]]
「,5,6,7,8」は行末コメントの一部とみなされ無視されます

==== 特別なコメント行
===== ;!;
Emuera、eramakerのいずれでも、;で始まる行はコメント行とみなされますが、Emueraは;!;で始まる行はコメントではなく有効な行とみなします。[[BR]]
eramakerでは実行してほしくない文を記述する際に使ってください。[[BR]]
例えば@SHOWSHOPに以下のスクリプトを加えるとEmueraでの動作を禁止できます。[[BR]]
{{{
	;!;PRINTW このスクリプトはEmueraでは実行できません
	;!;QUIT
}}}
{{{また、[SKIPSTART]や[SKIPEND]と併用することで、以下のスクリプトのようにEmuera以外での動作を禁止できます。}}}[[BR]]
Emuera以外では実行してほしくない文を記述する際に使ってください。
{{{
	;!;[SKIPSTART]
	PRINTW このスクリプトはEmuera以外では実行できません
	QUIT
	;!;[SKIPEND]
}}}
===== ;#;
;#;で始まる行はデバッグモード時のみ実行されます。[[BR]]
非デバッグモード時ではコメント行と見なされ実行されません。[[BR]]
ただし、DEBUG系命令は元々非デバッグモード時には無視されるので、;#;を行頭に付ける必要はありません。[[BR]]
同様にデバッグ変数も非デバッグモード時には空文字列又は0のため、エラーの心配はありません。[[BR]]
デバッグモードについては[debug こちら]を参照してください。

==== キャラクタ配列
eramakerではおそらくキャラクタ作成用の配列が100個しか用意されていません。[[BR]]
そのため、chara3.csvとchara03.csvとchara3B.csvでそれぞれ別のキャラを定義しても一人しか有効になりません。[[BR]]
Emueraではキャラクタはメモリが許す限りいくらでも定義できます。[[BR]]
また、"chara*.csv"に該当すればchara101.csv、charaABC.csvなどどんなファイルでも読みに行きます。[[BR]]
キャラの番号が重複し、ADDCHARA又はADDSPCHARAの際に複数の候補がある場合、先に読み込まれた方のみが有効になります。

==== 整数型の値の範囲
eramakerで扱える整数は32ビット符号付整数、すなわち-2147483648～2147483647の範囲です。[[BR]]
Emueraでは吉里吉里と同じく64ビット符号付整数、-9223372036854775808～9223372036854775807の範囲の値を扱います。


==== 配列変数への一括代入
{{{
	A:10 = 1,2,3
	DA:0:0 = 1,2,3
}}}
上のように書いた場合、A:10～A:12にそれぞれ1,2,3の値が代入されます[[BR]]
下のような多次元配列では、DA:0:0～DA:0:2にそれぞれ1,2,3の値が代入されます[[BR]]
DA:0:0～DA:0:99の次にDA:1:0に代入することはなく、配列外参照エラーになります[[BR]]
ただし、複合代入演算には使えません（A += 1,2,3 などは不可）。[[BR]]
また、文字列型配列変数への代入に一括代入を用いる場合、[exetc#h4-.E6.96.87.E5.AD.97.E5.88.97.E5.BC.8F.E3.82.92.E7.94.A8.E3.81.84.E3.81.9F.E6.96.87.E5.AD.97.E5.88.97.E5.A4.89.E6.95.B0.E3.81.B8.E3.81.AE.E4.BB.A3.E5.85.A5 文字列式を用いた代入]を行わなくてはなりません
{{{
	;STR:20に「いちご,メロン,ブルーハワイ」という文字列が代入される
	STR:20 = いちご,メロン,ブルーハワイ
	;STR:20～STR22にそれぞれ「いちご」「メロン」「ブルーハワイ」が代入される
	STR:20 '= "いちご", "メロン", "ブルーハワイ"
}}}

==== FORM構文を用いた文字列変数への代入
文字列変数への代入の際、PRINTFORMと同じ形式で代入する文字列を指定できます。
{{{
	SAVESTR:0 = %RESULTS%
}}}
この文でSAVESTR:0にRESULTSの中身を代入することができます。[[BR]]
同じ文はeramakerではSAVESTR:0に%RESULTS%という文字列そのものを代入したことになります。[[BR]]
Emueraで%RESULTS%という文字列そのものを代入したい場合、以下のように書いてください。
{{{
	SAVESTR:0 = \%RESULT\%
}}}
\記号の直後の文字はシステム記号として扱われません。[[BR]]
\記号自体を文字列に含めたい場合は\\としてください。

あまりないケースですが、eramakerとEmueraで同じ動作をさせたい場合は以下のように書く必要があります。[[BR]]
{{{
	;!;SAVESTR:0 = \%RESULT\%
	;!;[SKIPSTART]
	SAVESTR:0 = %RESULTS%
	;!;[SKIPEND]
}}}

==== 文字列式を用いた文字列変数への代入
ver1813以降のEmueraでは新たに、代入演算子'=と文字列式を用いて文字列変数への代入を行うことができます。
{{{
	;「STR = あいう」と同様
	STR '= "あいう"
	;「STR = %TSTR:0%いろは」と同様
	STR '= TSTR:0 + "いろは"
}}}

==== 文字列による配列変数の要素の指定
以下の変数について、引数を*.csvで定義した文字列にして呼び出すことができます。[[BR]]
Emueraの新規変数についての詳細は[exvar Emueraで追加された拡張文法 - 定数・変数]をご覧ください。
{{{
	ITEM (item.csv)
	ITEMSALES (item.csv)
	LOSEBASE (base.csv)
	BASE (base.csv)
	MAXBASE (base.csv)
	ABL (abl.csv)
	TALENT (talent.csv)
	EXP (exp.csv)
	MARK (mark.csv)
	RELATION (chara*.csv)
	UP (palam.csv)
	DOWN (palam.csv)
	PALAM (palam.csv)
	JUEL (palam.csv)
	GOTJUEL (palam.csv)
	STAIN (stain.csv)
	SOURCE (source.csv)
	EX (ex.csv)
	NOWEX (ex.csv)
	TEQUIP (tequip.csv)
	EQUIP (equip.csv)
	FLAG (flag.csv)
	TFLAG (tflag.csv)
	CFLAG (cflag.csv)
	STR (strname.csv)
	SAVESTR (savestr.csv)
	以下はEmueraから追加された変数
	ITEMPRICE (item.csv)
	DOWNBASE (base.csv)
	CUP (palam.csv)
	CDOWN (palam.csv)
	TCVAR (tcvar.csv)
	TSTR (tstr.csv)
	CSTR (cstr.csv)
	CDFLAG (cdflag1.csv, cdflag2.csv)
	GLOBAL (global.csv)
	GLOBALS (globals.csv)
}}}
例えばabl.csvに"2,技巧"という定義があれば、以下の4つの行は同じ意味になります。
{{{
	ABL:技巧 += 1
	ABL:2 += 1
	ABL:"技巧" += 1
	ABL:(ABLNAME:2) += 1
}}}
RELATIONについてはNAME、CALLNAMEのいずれでも指定できます。[[BR]]
同名で複数の定義がある場合、先に定義されている方が呼ばれます。[[BR]]
例えばabl.csvに"2,技巧"と"4,技巧"があり、"2,技巧"の方が前の行で定義されているならば、"ABL:技巧"は"ABL:2"になります。[[BR]]
文字列は式や変数でも可能です。その場合は以下のように()を付けてください。
{{{
	ABL:(RESULTS:0) = ABL:(RESULTS:0) + 1
}}}
()を省略した場合、アイテム名と変数名が同一になる場合があります。その場合は変数が優先されます。[[BR]]
例えばabl.csvに"0,ローター"という定義がある場合、
{{{
	@HOGE
	#DIM ローター, 0
	ローター = 1
	PRINTFORML {ABL:ローター}
}}}
この場合、0番目のABLではなく1番目のABLであると解釈されます。[[BR]]
同様に、アイテム名が数値の場合、数値としての解釈が優先されます。[[BR]]
例えばabl.csvに"0,10"という定義をしABL:10を参照した場合は0番目のABLとは解釈されず10番目のABLとなります。[[BR]]

これはchara*.csv内の定義においても使用できます。[[BR]]
例えばabl.csvに"2,技巧"という定義があれば、以下の2つの行は同じ意味になります。
{{{
	能力,2,2
	能力,技巧,2
}}}
ただし、相性(RELATION)には使えません。[[BR]]
chara*.csvを読んでいる段階ではシステムがcharaの名前とNOの対応を把握していないためです。

==== 書式付文字列（FORM構文）拡張
{{{PRINTFORMなどでつかわれる書式付文字列のうち、{}、%%に表示桁数（文字数）を指定できます。}}}[[BR]]
{{{{変数・数式等, 表示桁数, 揃え(LEFT or RIGHT)}、%変数、文字列式等, 表示桁数, 揃え(LEFT or RIGHT)%}}}という形式で指定します。[[BR]]
文字数は全角文字を2文字と数えます。[[BR]]
表示桁数（文字数）に足りない部分は半角スペースが追加されます。[[BR]]
通常は右揃えですが、キーワードLEFTを指定すると左揃えになります。[[BR]]
指定された表示桁数よりもとの桁数の方が大きい場合、そのまま表示します。
{{{
	A = 123456
	STR:0 = あいう
	PRINTFORML [{A}]
	PRINTFORML [{A,10}]
	PRINTFORML [{A,10,LEFT}]
	PRINTFORML [%STR:0%]
	PRINTFORML [%STR:0,10%]
	PRINTFORML [%STR:0,10,LEFT%]
	PRINTFORML [{A,2}]
	PRINTFORML [%STR:0,2%]
;結果
[123456]
[    123456]
[123456    ]
[あいう]
[    あいう]
[あいう    ]
[123456]
[あいう]
}}}

==== 文字列式中での書式付文字列（FORM構文）の使用
PRINTSやユーザー定義の式中関数の引数といった文字列式中でFORM構文を使用するとエラーになります。[[BR]]
そのため文字列式中で書式付文字列を使用する場合、文字列式中で定文字列を使うときに"～"を使用するのと同じように[[BR]]
@"～"を使用します。
また、@"～"の中の文字列が\@～\@を用いた三項演算子による記載のみである場合、@"～"を省略して直接\@～\@と書くことができます[[BR]]

正しい例
{{{
	;代入はFORM構文
	STR:0 = あいう
	;加算は文字列式
	RESULTS += STR:0
	;文字列式に定文字列を使用する例
	RESULTS += "えお"
	;文字列式にFORM構文を使用する例
	PRINTS @"%RESULTS%かきくけこ"

	;以下の4行はすべて同じである
	PRINTS STR:0 + "！"
	PRINTFORM %STR:0%！
	PRINTS @"%STR:0%！"
	PRINTFORM %STR:0 + "！"%
}}}
間違った例
{{{
	;中身が「RESULTS」になる
	STR:0 = RESULTS
	;エラーが出る
	RESULTS += えお
	;エラーが出る
	RESULTS += %STR:0%
	;「@"」と「"」も表示される
	PRINTFORM @"%RESULTS%かきくけこ"
}}}

==== INPUTS系でのマクロ構文の使用
INPUTSやそれに類する入力受付命令で、マクロ式を用いることができます。[[BR]]
マクロの書式については[howto 利用方法]のマクロの項目を確認してください。[[BR]]
マクロ構文を使わず、単なる文字列として()を使用する場合、\を用いてエスケープしてください。