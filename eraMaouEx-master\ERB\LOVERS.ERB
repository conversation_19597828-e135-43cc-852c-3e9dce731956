﻿
;勇者にスパイを接近させて交際させ
;堕落を誘発させるシステムです
;悪い恋人に騙されて身を崩す女の子いいよね……

;------------------------------------
@ENTER_LOVER, ARG:0
;------------------------------------
;ARG:0 = 対象勇者

SIF ARG:0 < 0
	RETURN 0

PRINTFORML 派遣魔王的手下外出，设下恋爱陷阱诱惑对象勇者堕落
PRINTFORML 选择派遣谁外出诱惑%SAVESTR:(ARG:0)%呢？
DRAWLINE
PRINTL [ 0] 谁都不派遣

FOR LOCAL, 1, 100
	CALL NAME_LOVER,LOCAL,0
	IF RESULT == 1
		PRINTFORM [{LOCAL,2,RIGHT}] 
		CALL NAME_LOVER,LOCAL,1
		PRINT 　
	ENDIF
	
	LOCAL:1 = LOCAL % 20
	SIF LOCAL:1 == 0
		PRINTL  
NEXT
SIF !LINEISEMPTY()
	PRINTL
PRINTL [999] 取消
DRAWLINE

;入力
$INPUT_LOOP
INPUT

;戻る
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 0
	LOCAL = 0
ELSE
	LOCAL = RESULT
	;対象が存在しているか判定する
	CALL NAME_LOVER,LOCAL,0
	SIF RESULT == 0
		GOTO INPUT_LOOP
ENDIF

;恋人派遣確定

LOCAL:1 = CFLAG:(ARG:0):606
CFLAG:(ARG:0):606 = LOCAL

;愛情度リセット
SIF LOCAL != LOCAL:1
	CFLAG:(ARG:0):607 = 0

IF LOCAL > 0
	PRINTFORM %SAVESTR:(ARG:0)%为目标
	CALL NAME_LOVER,LOCAL,1
	PRINTW 开始接触邂逅了
ELSE
	PRINTFORMW 引诱%SAVESTR:(ARG:0)%的行动正在进行中
ENDIF

RETURN 1

;------------------------------------
@NAME_LOVER, ARG:0, ARG:1
;------------------------------------
;ARG:0 = 対象恋人
;ARG:1 = プリントフラグ。1のときプリントする

LOCALS = 

SIF ARG:0 <= 0
	RETURN 0
	
; 1～20  人間
;21～40  異種族
;41～60  女性?ふたなり
;61～80  ショタ
;81～100 犬?家畜
;200     キャラ名

SELECTCASE ARG:0
CASE 1
	LOCALS = 温柔的青年
CASE 2
	LOCALS = 威严的彪形大汉
CASE 3
	LOCALS = 粗野的流氓
CASE 4
	LOCALS = 大腹便便的中年人
CASE 21
	LOCALS = 丑陋的兽人
CASE 22
	LOCALS = 精灵美男子
CASE 23
	LOCALS = 暗黑精灵
CASE 24
	LOCALS = 奴隷
CASE 41
	LOCALS = 妓女
CASE 42
	LOCALS = 女学生
CASE 43
	LOCALS = 贵妇
CASE 44
	LOCALS = 女骑士
CASE 61
	LOCALS = 懦弱少年
CASE 62
	LOCALS = 戴眼鏡的男学生
CASE 63
	LOCALS = 活泼的少年
CASE 64
	LOCALS = 可爱的男学生
CASE 81
	LOCALS = 大型宠物狗
CASE 82
	LOCALS = 爱马
CASE 83
	LOCALS = 宠物猪
CASE 84
	LOCALS = 农家的牛
CASE 200
	LOCALS = 恋人
ENDSELECT

SIF LOCALS == ""
	RETURN 0

SIF ARG:1 == 1
	PRINTFORM %LOCALS,14,LEFT%

RETURN 1

;------------------------------------
@DUNGEON_TOWN_LOVER, ARG:0
#DIM LOVER
#DIM LOVE_UP
#DIM LOVE_LV
#DIM LOVE_ID
#DIM LOVE_EXP,50
#DIM MARRIAGE
#DIM RACE
#DIM NO_KARMA
;------------------------------------
;ARG:0 = 対象勇者

;街中での逢瀬
;LOVE_EXP:0 = プレイ回数
;LOVE_EXP:1 = 煙草フラグ
;LOVE_EXP:2 = 薬物フラグ
;LOVE_EXP:3 = フェラフラグ
;LOVE_EXP:4 = V性交フラグ
;LOVE_EXP:5 = A性交フラグ
;LOVE_EXP:6 = レズフラグ
;LOVE_EXP:7 = 獣姦フラグ
;LOVE_EXP:8 = 被写フラグ
;LOVE_EXP:9 = 前戯フラグ

FOR LOCAL,0,50
	LOVE_EXP:LOCAL = 0
NEXT


LOCALS = ERROR

SIF ARG:0 <= 0
	RETURN 0

LOVER = CFLAG:(ARG:0):606
LOVE_LV = CFLAG:(ARG:0):607
NO_KARMA = 0
;902が代入されている場合、恋人と結婚状態
MARRIAGE = CFLAG:(ARG:0):601

IF LOVER <= 0
	;恋人がいない場合、早速作りに行く
	CALL DUNGEON_TOWN_LOVER_CHARA_ENTER,ARG:0
	SIF RESULT == 0
		RETURN 0
	LOVER = CFLAG:(ARG:0):606
ENDIF

LOVE_ID = CFLAG:(ARG:0):610

; 1～20  人間
;21～40  異種族
;41～60  女性?ふたなり
;61～80  ショタ
;81～100 犬?家畜

SELECTCASE LOVER
CASE 1
	LOCALS = 温柔的青年
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙……
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV <= 0
		PRINTFORM “偶然之下”%LOCALS%帮了%SAVESTR:(ARG:0)%一把……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ENDIF
CASE 2
	LOCALS = 威严的彪形大汉
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%为了和%LOCALS%生孩子而努力准备着……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某次%SAVESTR:(ARG:0)%陷入危机之时，被%LOCALS%出手相救……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%去%LOCALS%的家里做客、被推倒了……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ENDIF
CASE 3
	LOCALS = 粗野的流氓
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚，%SAVESTR:(ARG:0)%努力配合着%LOCALS%各种花样的性要求……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%为了和%LOCALS%生孩子而努力准备着……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
		;煙草フラグ
		LOVE_EXP:1 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;フェラフラグ
		LOVE_EXP:3 += 2
		;A性交フラグ
		LOVE_EXP:5 += 1
		;煙草フラグ
		LOVE_EXP:1 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某一天%SAVESTR:(ARG:0)%在街头被%LOCALS%搭讪了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%去%LOCALS%的家中相会、没多久，两人抽起了事后烟……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
		;煙草フラグ
		LOVE_EXP:1 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
		;煙草フラグ
		LOVE_EXP:1 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;A性交フラグ
		LOVE_EXP:5 += 1
		;フェラフラグ
		LOVE_EXP:3 += 2
		;煙草フラグ
		LOVE_EXP:1 += 2
	ENDIF
CASE 4
	LOCALS = 大腹便便的中年人
	IF LOVE_LV <= 0
		PRINTFORM 某天在酒吧里，%LOCALS%请%SAVESTR:(ARG:0)%喝酒并搭讪了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%开始进出%LOCALS%的家、并收到了昂贵的首饰作为礼物……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;A性交フラグ
		LOVE_EXP:5 += 1
		;フェラフラグ
		LOVE_EXP:3 += 2
	ENDIF
CASE 21
	LOCALS = 丑陋的兽人
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV <= 0
		PRINTFORM “偶然之下”%LOCALS%帮了%SAVESTR:(ARG:0)%一把……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;フェラフラグ
		LOVE_EXP:3 += 1
	ENDIF
CASE 22
	LOCALS = 精灵美男子
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%为了和%LOCALS%生孩子而努力准备着……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM “偶然之下”%LOCALS%帮了%SAVESTR:(ARG:0)%一把……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;フェラフラグ
		LOVE_EXP:3 += 1
	ENDIF
CASE 23
	LOCALS = 暗黑精灵
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚，%SAVESTR:(ARG:0)%努力配合着%LOCALS%各种花样的性要求……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%为了和%LOCALS%生孩子而努力准备着……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
		;煙草フラグ
		LOVE_EXP:1 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;フェラフラグ
		LOVE_EXP:3 += 2
		;A性交フラグ
		LOVE_EXP:5 += 1
		;煙草フラグ
		LOVE_EXP:1 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某一天%SAVESTR:(ARG:0)%在街头被%LOCALS%搭讪了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%去%LOCALS%的家中相会、没多久，两人抽起了事后烟……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
		;煙草フラグ
		LOVE_EXP:1 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
		;煙草フラグ
		LOVE_EXP:1 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;A性交フラグ
		LOVE_EXP:5 += 1
		;フェラフラグ
		LOVE_EXP:3 += 2
		;煙草フラグ
		LOVE_EXP:1 += 2
		;薬物フラグ
		LOVE_EXP:2 += 1
	ENDIF
CASE 24
	LOCALS = 奴隷
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某天，路过的%SAVESTR:(ARG:0)%被%LOCALS%工作的身影吸引了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%找上了奴隶主，表示想和%LOCALS%说说话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%从奴隶主手中买下了%LOCALS%，并开始和%LOCALS%约会了……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 3
		;フェラフラグ
		LOVE_EXP:3 += 1
	ENDIF
CASE 41
	LOCALS = 妓女
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;レズフラグ
		LOVE_EXP:6 += 3
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某天，%SAVESTR:(ARG:0)%帮了“身处困难”的%LOCALS%一把…………
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%约好、一起吃饭……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;レズフラグ
		LOVE_EXP:6 += 2
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始亲热……
		;レズフラグ
		LOVE_EXP:6 += 3
	ENDIF
CASE 42
	LOCALS = 女学生
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;レズフラグ
		LOVE_EXP:6 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某天，%SAVESTR:(ARG:0)%帮了“身处困难”的%LOCALS%一把…………
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%约好、一起吃饭……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始亲热……
		;レズフラグ
		LOVE_EXP:6 += 2
	ENDIF
CASE 43
	LOCALS = 贵妇
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;レズフラグ
		LOVE_EXP:6 += 3
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某一天，%SAVESTR:(ARG:0)%被%LOCALS%打招呼了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次见面、说起了工作的事……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%约好、一起吃饭……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%去开房了……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;レズフラグ
		LOVE_EXP:6 += 2
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;レズフラグ
		LOVE_EXP:6 += 3
	ENDIF
CASE 44
	LOCALS = 女骑士
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;レズフラグ
		LOVE_EXP:6 += 3
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某一天，%SAVESTR:(ARG:0)%被%LOCALS%打招呼了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次见面、说起了工作的事……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%约好、一起吃饭……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;レズフラグ
		LOVE_EXP:6 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始亲热……
		;レズフラグ
		LOVE_EXP:6 += 3
	ENDIF
CASE 61
	LOCALS = 懦弱少年
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某天，%SAVESTR:(ARG:0)%帮了“身处困难”的%LOCALS%一把…………
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%一起出去玩、直到黄昏才回来……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ENDIF
CASE 62
	LOCALS = 戴眼鏡的男学生
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM %SAVESTR:(ARG:0)%接受了家庭教师的委托，开始辅导%LOCALS%的功课……
	ELSEIF LOVE_LV < 10
		PRINTFORM %LOCALS%对%SAVESTR:(ARG:0)%的辅导不是很上心、总是说一些其他的事……
	ELSEIF LOVE_LV < 20
		PRINTFORM %LOCALS%对%SAVESTR:(ARG:0)%的辅导不是很上心、总爱说些私人话题……
	ELSEIF LOVE_LV < 30
		PRINTFORM %LOCALS%根本不在意%SAVESTR:(ARG:0)%教了什么、总是说一些让人心跳加速的情话……
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%总是在辅导中被打断，然后和%LOCALS%开始做其他的事情……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ENDIF
CASE 63
	LOCALS = 活泼的少年
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某天，%SAVESTR:(ARG:0)%帮了“身处困难”的%LOCALS%一把…………
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%再次相遇，说着毫无营养的话……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%一起出去玩、直到黄昏才回来……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%已经可以自由进出%LOCALS%的家，还会为对方准备料理……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%已经住进了%LOCALS%的家……
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%一进家门，就被%LOCALS%抱住开始激烈的性交……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 1
	ENDIF
CASE 64
	LOCALS = 可爱的男子学生
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%越来越熟悉对方的身体……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%过着和睦而又淫荡的新婚生活……
		;V性交フラグ
		LOVE_EXP:4 += 2
		;フェラフラグ
		LOVE_EXP:3 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM %SAVESTR:(ARG:0)%接受了家庭教师的委托，开始辅导%LOCALS%的功课……
	ELSEIF LOVE_LV < 10
		PRINTFORM %LOCALS%对%SAVESTR:(ARG:0)%的辅导不是很上心、总是说一些其他的事……
	ELSEIF LOVE_LV < 20
		PRINTFORM %LOCALS%对%SAVESTR:(ARG:0)%的辅导不是很上心、总爱说些私人话题……
	ELSEIF LOVE_LV < 30
		PRINTFORM %LOCALS%根本不在意%SAVESTR:(ARG:0)%教了什么、只顾着说那些让人心跳加速的情话……
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%开始约会了……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%总是在辅导中被打断，然后和%LOCALS%开始做其他的事情……
		;V性交フラグ
		LOVE_EXP:4 += 1
		;フェラフラグ
		LOVE_EXP:3 += 3
	ENDIF
CASE 81
	LOCALS = 大型宠物狗
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，还是有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%终于开始交尾……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%像母狗一样被%LOCALS%骑在身下没日没夜的猛艹……
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 有一天、%SAVESTR:(ARG:0)%从宠物商店把光鲜亮丽的%LOCALS%买回家了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%总喜欢带着%LOCALS%举止过分亲昵的到处游玩、引来无数非议……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%终于明白了自己对%LOCALS%的心意……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%开始故意挑逗%LOCALS%……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%成功的让%LOCALS%性奋起来了……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%开始被%LOCALS%艹上瘾了……
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 2
	ENDIF
CASE 82
	LOCALS = 马
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%终于开始交尾……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%像母马一样被%LOCALS%骑在身下没日没夜的猛艹…………
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某一天，%SAVESTR:(ARG:0)%将坐骑%LOCALS%买回来了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%带着%LOCALS%举止过分亲昵的到处游玩、引来无数非议……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%终于明白了自己对%LOCALS%的心意……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%开始故意挑逗%LOCALS%……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%成功的让%LOCALS%性奋起来了……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%开始被%LOCALS%艹上瘾了……
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 2
	ENDIF
CASE 83
	LOCALS = 宠物猪
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%终于开始交尾……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%像母猪一样被%LOCALS%骑在身下没日没夜的猛艹…………
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 有一天、%SAVESTR:(ARG:0)%在宠物商店看上了%LOCALS%，并买回家了…………
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%带着%LOCALS%举止过分亲昵的到处游玩、引来无数非议……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%终于明白了自己对%LOCALS%的心意……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%开始故意挑逗%LOCALS%……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%成功的让%LOCALS%性奋起来了……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%开始被%LOCALS%艹上瘾了……
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 3
	ENDIF
CASE 84
	LOCALS = 农家的牛
	IF MARRIAGE == 902 && LOVE_LV < 10
		;結婚10日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%相处时，依旧有些笨拙…………
	ELSEIF MARRIAGE == 902 && LOVE_LV < 30
		;結婚30日未満
		PRINTFORM 新婚的%SAVESTR:(ARG:0)%和%LOCALS%终于开始交尾……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSEIF MARRIAGE == 902
		;結婚30日以降
		PRINTFORM %SAVESTR:(ARG:0)%像母牛一样被%LOCALS%骑在身下没日没夜的猛艹…………
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 2
	ELSEIF LOVE_LV <= 0
		PRINTFORM 某一天，%SAVESTR:(ARG:0)%发现身边似乎具有不可思议魅力的%LOCALS%了……
	ELSEIF LOVE_LV < 10
		PRINTFORM %SAVESTR:(ARG:0)%为了接近照顾%LOCALS%，特意去农家帮忙……
	ELSEIF LOVE_LV < 20
		PRINTFORM %SAVESTR:(ARG:0)%终于明白了自己对%LOCALS%的心意……
	ELSEIF LOVE_LV < 30
		PRINTFORM %SAVESTR:(ARG:0)%开始故意挑逗%LOCALS%……
	ELSEIF LOVE_LV < 40
		PRINTFORM %SAVESTR:(ARG:0)%成功的让%LOCALS%性奋起来了……
		;フェラフラグ
		LOVE_EXP:3 += 1
		;獣姦フラグ
		LOVE_EXP:7 += 1
	ELSE
		PRINTFORM %SAVESTR:(ARG:0)%开始被%LOCALS%艹上瘾了……
		;フェラフラグ
		LOVE_EXP:3 += 2
		;V性交フラグ
		LOVE_EXP:4 += 2
		;獣姦フラグ
		LOVE_EXP:7 += 3
	ENDIF
CASE 200
	;勇者间的恋愛
	LOCALS = 
	CALL SEARCH_FAMILY,ARG:0,"LOVE"
	LOVE_ID = RESULT
	NO_KARMA = 1
	SIF LOVE_ID < 0
		RETURN 0
	LOCALS += SAVESTR:LOVE_ID
	IF CFLAG:(ARG:0):1 == 2 && CFLAG:LOVE_ID:1 == 2
		;まず、双方侵攻中の場合
		;パーティを結成していないとダメ
		SIF CFLAG:(ARG:0):533 <= 0
			RETURN 0
		;同じパーティにいないとダメ
		SIF CFLAG:(ARG:0):533 != CFLAG:LOVE_ID:533
			RETURN 0
		
		PRINTFORM %SAVESTR:(ARG:0)%吻了%LOCALS%……
		;キスフラグ
		LOVE_EXP:0 += 1
		
	ELSEIF CFLAG:(ARG:0):1 == 0 && CFLAG:LOVE_ID:1 == 0
		;双方が奴隷の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%不顾场合地开始做爱……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向%LOCALS%洗脑了魔族的美好……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%从%LOCALS%了解到了魔族的美好……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%互相鼓励着不屈不挠的内心……
		ENDIF
		
	ELSEIF CFLAG:(ARG:0):1 == 7 && CFLAG:LOVE_ID:1 == 7
		;双方が苗床の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%和%LOCALS%互相赞美着怀有身孕的肚子……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向%LOCALS%に讲述着孕育着魔族的美妙之处……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%见到了孕育着魔族的%LOCALS%后，内心崩溃了……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%与%LOCALS%下了绝不因成为魔族的生育工具而屈服的决心……
		ENDIF
		
	ELSEIF CFLAG:(ARG:0):1 == 8 && CFLAG:LOVE_ID:1 == 8
		;双方が晒し台の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%与%LOCALS%比拼着谁更能让观众感到满足……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向%LOCALS%に炫耀着让观众满足的自己……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%见到了一脸痴像的%LOCALS%后，内心崩溃了……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%与%LOCALS%下了绝不因向众人献媚而屈服的决心……
		ENDIF
		
	ELSEIF CFLAG:(ARG:0):1 == 0 && CFLAG:LOVE_ID:1 == 2
		;自分が奴隷の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%期待着%LOCALS%成为魔族的那一天……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%相信着%LOCALS%一定会来救援……
		ENDIF
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 2 && CFLAG:LOVE_ID:1 == 0
		;相手が奴隷の場合
		
		IF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%听说了%LOCALS%堕为魔族的事，备受罪恶感的折磨……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%发誓一定要救%LOCALS%出来……
		ENDIF
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 3 && CFLAG:LOVE_ID:1 == 2
		;自分が迎撃の場合
		
		PRINTFORM %SAVESTR:(ARG:0)%期待着亲手将%LOCALS%转化为魔族的那一天……
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 2 && CFLAG:LOVE_ID:1 == 3
		;相手が迎撃の場合
		
		PRINTFORM %SAVESTR:(ARG:0)%听说了%LOCALS%在魔王军中活跃的传闻，内心十分的混乱……
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 7 && CFLAG:LOVE_ID:1 == 2
		;自分が苗床の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%不断地幻想着%LOCALS%在自己身旁一同孕育魔族的场面……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%不断地幻想着%LOCALS%前来拯救自己的场面……
		ENDIF
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 2 && CFLAG:LOVE_ID:1 == 7
		;相手が苗床の場合
		
		IF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%听说了%LOCALS%乐于孕育魔族的传言，异样的兴奋并自慰了……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%听说了%LOCALS%孕育魔族的传言，哭着自慰了……
		ENDIF
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 8 && CFLAG:LOVE_ID:1 == 2
		;自分が晒し台の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向着寄给%LOCALS%的水晶球高兴地展现着被侵犯的场面……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%向着寄给%LOCALS%的水晶球哭着求救……
		ENDIF
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 2 && CFLAG:LOVE_ID:1 == 8
		;相手が晒し台の場合
		
		IF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%看着收到的水晶球中显现的%LOCALS%淫荡的话语，异样的兴奋得难以自己地自慰了……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%看着收到的水晶球中显现的%LOCALS%惨烈的身姿，因罪恶感而落泪并自慰了……
		ENDIF
		
		PRINTL  
		RETURN 0
		
	ELSEIF CFLAG:(ARG:0):1 == 7 && CFLAG:LOVE_ID:1 == 0
		;自分が苗床で相手が奴隷の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%怀有身孕的腹部，%LOCALS%十分怜爱地抚摸着……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向%LOCALS%讲述着孕育魔族的快感……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%感受到%LOCALS%向自己怀有身孕的腹部投来了怜爱的目光，%SAVESTR:(ARG:0)%感到十分绝望……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%因孕育着魔族的事向%LOCALS%谢罪……
		ENDIF
		
	ELSEIF CFLAG:(ARG:0):1 == 0 && CFLAG:LOVE_ID:1 == 7
		;自分が奴隷で相手が苗床の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%十分怜爱地抚摸着%LOCALS%怀有身孕的腹部……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向%LOCALS%に讲述着孕育着魔族的美妙之处……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%对沉溺于孕育魔族快感的%LOCALS%感到绝望……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%让孕育着魔族的%LOCALS%鼓起勇气坚持下去……
		ENDIF
		
	ELSEIF CFLAG:(ARG:0):1 == 8 && CFLAG:LOVE_ID:1 == 0
		;自分が晒し台で相手が奴隷の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%为了让%LOCALS%痴态似地把腰越抬越高……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向绝望了的%LOCALS%に展现着自己的痴态……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%感受到了%LOCALS%向被凌辱的自己投来恋爱的目光，感到十分绝望……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%因被凌辱的事向%LOCALS%谢罪……
		ENDIF
		
	ELSEIF CFLAG:(ARG:0):1 == 0 && CFLAG:LOVE_ID:1 == 8
		;自分が奴隷で相手が晒し台の場合
		
		IF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 > 0
			;双方が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%和裸露于人前的%LOCALS%接吻着自慰了……
		ELSEIF CFLAG:(ARG:0):0 > 0 && CFLAG:LOVE_ID:0 == 0
			;自分が陥落している場合
			PRINTFORM %SAVESTR:(ARG:0)%向绝望了的%LOCALS%诉说着魔族的美好……
		ELSEIF CFLAG:(ARG:0):0 == 0 && CFLAG:LOVE_ID:0 > 0
			;相手が陥落している
			PRINTFORM %SAVESTR:(ARG:0)%对欢乐地抬起屁股的%LOCALS%感到了异样的兴奋，开始自慰了起来……
		ELSE
			;どちらも陥落していない
			PRINTFORM %SAVESTR:(ARG:0)%向裸露于人前的%LOCALS%哭着谢罪，感同身受的自慰了起来……
		ENDIF
		
	;もっと分岐挟める気がする
	ELSE
		RETURN 0
	ENDIF
	
	
ENDSELECT

;貞操帯?処女封印
IF CFLAG:(ARG:0):42 == 79 && (CFLAG:(ARG:0):40 & 64) && FLAG:37
	;貞操帯の場合アナルに
	LOVE_EXP:5 += LOVE_EXP:4
	LOVE_EXP:4 = 0
ELSEIF TALENT:(ARG:0):273
	;貞操封印の場合アナルに
	LOVE_EXP:5 += LOVE_EXP:4
	LOVE_EXP:4 = 0
ENDIF

;キスボーナス
SIF LOVE_LV >= 10
	LOVE_EXP:0 += 1
SIF LOVE_LV >= 20
	LOVE_EXP:0 += 1
SIF LOVE_LV >= 30
	LOVE_EXP:0 += 2
SIF LOVE_LV >= 40
	LOVE_EXP:0 += 3
SIF LOVE_LV >= 60
	LOVE_EXP:0 += 5

;フェラボーナス
IF LOVE_EXP:3 > 0
	;技巧
		LOVE_EXP:3 += ABL:(ARG:0):12
	;奉仕技術
		LOVE_EXP:3 += ABL:(ARG:0):13
	;奉仕精神
		LOVE_EXP:3 += ABL:(ARG:0):16
	;精液中毒
		LOVE_EXP:3 += ABL:(ARG:0):32
	;舌使い
	SIF TALENT:(ARG:0):52
		LOVE_EXP:3 += 1
	;汚臭鈍感
	SIF TALENT:(ARG:0):61
		LOVE_EXP:3 += 1
	;汚臭敏感
	SIF TALENT:(ARG:0):62
		LOVE_EXP:3 -= 1
	;汚臭敏感+精液中毒
	SIF TALENT:(ARG:0):62 && ABL:(ARG:0):32 > 0
		LOVE_EXP:3 += ABL:(ARG:0):32 * 3
	
ENDIF

;V性交ボーナス
IF LOVE_EXP:4 > 0
	;V感覚
		LOVE_EXP:4 += ABL:(ARG:0):2
	;V鈍感
	SIF TALENT:(ARG:0):103
		LOVE_EXP:4 -= 1
	;V敏感
	SIF TALENT:(ARG:0):104
		LOVE_EXP:4 += 1
	;セックス狂
	SIF TALENT:(ARG:0):75
		LOVE_EXP:4 += 1
	;淫壺
	SIF TALENT:(ARG:0):232
		LOVE_EXP:4 += 1
	
ENDIF

;A性交ボーナス
IF LOVE_EXP:5 > 0
	;A感覚
		LOVE_EXP:5 += ABL:(ARG:0):3
	;A鈍感
	SIF TALENT:(ARG:0):105
		LOVE_EXP:5 -= 1
	;A敏感
	SIF TALENT:(ARG:0):106
		LOVE_EXP:5 += 1
	;尻穴狂い
	SIF TALENT:(ARG:0):77
		LOVE_EXP:5 += 1
	;淫肛
	SIF TALENT:(ARG:0):233
		LOVE_EXP:5 += 1
	
ENDIF

;オトコでない場合、レズフラグボーナス
IF LOVE_EXP:6 > 0 && TALENT:(ARG:0):122 == 0
	;レズっ気
		LOVE_EXP:6 += ABL:(ARG:0):22
	;レズ中毒
		LOVE_EXP:6 += ABL:(ARG:0):33
	;両刀
	SIF TALENT:(ARG:0):81
		LOVE_EXP:6 += 1
	;男嫌い
	SIF TALENT:(ARG:0):82
		LOVE_EXP:6 += 1
ENDIF

;獣姦フラグボーナス
IF LOVE_EXP:7 > 0
	;種族人狼
	SIF TALENT:(ARG:0):种族 == 2
		LOVE_EXP:7 += 1
	;かわいい動物が好き
	SIF TALENT:(ARG:0):317 == 12
		LOVE_EXP:7 += 1
	;獣姦中毒
		LOVE_EXP:7 += ABL:(ARG:0):39
	;牝犬
	SIF TALENT:(ARG:0):136
		LOVE_EXP:7 += 3
	
	;Vフラグボーナス
	SIF LOVE_EXP:4 > 0
		LOVE_EXP:4 += LOVE_EXP:7
	;フェラボーナス
	SIF LOVE_EXP:3 > 0
		LOVE_EXP:3 += LOVE_EXP:7
	
ENDIF

;被写フラグボーナス
;LOVE_EXP:8

;臆病
SIF TALENT:(ARG:0):10
	LOVE_EXP:8 -= 1
;自制心
SIF TALENT:(ARG:0):20
	LOVE_EXP:8 -= 1
;好奇心
SIF TALENT:(ARG:0):23
	LOVE_EXP:8 += 1
;一線越えない
SIF TALENT:(ARG:0):27
	LOVE_EXP:8 -= 1
;目立ちたがり
SIF TALENT:(ARG:0):28
	LOVE_EXP:8 += 2
;露出狂
SIF TALENT:(ARG:0):89
	LOVE_EXP:8 += 3
;露出癖
	LOVE_EXP:8 += ABL:(ARG:0):17

;前戯フラグボーナス
;LOVE_EXP:9

;性交の前戯、レズ行為の合計
LOVE_EXP:9 += LOVE_EXP:5 + LOVE_EXP:4 + LOVE_EXP:6

IF LOVE_EXP:9 > 0
	;C感覚
		LOVE_EXP:9 += ABL:(ARG:0):0
	;B感覚
		LOVE_EXP:9 += ABL:(ARG:0):1
	;C鈍感
	SIF TALENT:(ARG:0):101
		LOVE_EXP:9 -= 1
	;C敏感
	SIF TALENT:(ARG:0):102
		LOVE_EXP:9 += 1
	;B鈍感
	SIF TALENT:(ARG:0):107
		LOVE_EXP:9 -= 1
	;B敏感
	SIF TALENT:(ARG:0):108
		LOVE_EXP:9 += 1
	;自慰狂い
	SIF TALENT:(ARG:0):74
		LOVE_EXP:9 += 1
	;乳狂い
	SIF TALENT:(ARG:0):78
		LOVE_EXP:9 += 1
	;淫核
	SIF TALENT:(ARG:0):230
		LOVE_EXP:9 += 1
	;淫乳
	SIF TALENT:(ARG:0):231
		LOVE_EXP:9 += 1
	
ENDIF

;追加文

IF LOVE_EXP:0 > 0 && CFLAG:(ARG):16 == 0
	PRINTFORML ?初吻?
	CFLAG:(ARG):16 = 1
	CSTR:(ARG:0):4 = %LOCALS%
ELSEIF LOVE_EXP:3 > 0 && CFLAG:(ARG):16 == 0
	PRINTFORML ?初吻?
	CFLAG:(ARG):16 = 101
	CSTR:(ARG:0):4 = %LOCALS%
ENDIF

IF TALENT:(ARG:0):0 == 1 && LOVE_EXP:4 > 0
	PRINTFORML ?处女丧失?
	TALENT:(ARG:0):0 = 0
	CFLAG:(ARG):15 = 100
	CSTR:(ARG:0):3 = %LOCALS%
ENDIF

SIF LOVE_EXP:8 > 0
	PRINTFORML %SAVESTR:(ARG:0)%特意将水晶球保留了下来、向认识的人炫耀……

;清算

PRINTL  

;キス回数
SIF LOVE_EXP:0 > 0
	PRINTFORM 接吻：{LOVE_EXP:0}次 
;煙草回数
SIF LOVE_EXP:1 > 0
	PRINTFORM 吸烟：{LOVE_EXP:1}根 
;薬物経験
IF LOVE_EXP:2 > 0
	PRINTFORM 药物经验＋{LOVE_EXP:2} 
	EXP:(ARG:0):57 += LOVE_EXP:2
ENDIF

;フェラ経験
IF LOVE_EXP:3 > 0
	PRINTFORM 口交经验＋{LOVE_EXP:3} 
	EXP:(ARG:0):22 += LOVE_EXP:3
ENDIF

;快C?快B
;性交の前戯、レズ行為の合計
IF LOVE_EXP:9 > 0
	PRINTFORM %PALAMNAME:0%点数＋{LOVE_EXP:9 * 5} 
	JUEL:(ARG:0):0 += LOCAL * 5
	PRINTFORM %PALAMNAME:14%点数＋{LOVE_EXP:9 * 5} 
	JUEL:(ARG:0):14 += LOCAL * 5
ENDIF

;V経験?快V
IF LOVE_EXP:4 > 0
	PRINTFORM 私处经验＋{LOVE_EXP:4} 
	EXP:(ARG:0):0 += LOVE_EXP:4
	PRINTFORM %PALAMNAME:1%点数＋{LOVE_EXP:4 * 5} 
	JUEL:(ARG:0):1 += LOVE_EXP:4 * 5
ENDIF

;A経験?快A
IF LOVE_EXP:5 > 0
	PRINTFORM 肛门经验＋{LOVE_EXP:5} 
	EXP:(ARG:0):1 += LOVE_EXP:5
	PRINTFORM %PALAMNAME:2%点数＋{LOVE_EXP:5 * 5} 
	JUEL:(ARG:0):2 += LOVE_EXP:5 * 5
ENDIF

;性交経験
IF (LOVE_EXP:5 + LOVE_EXP:4)  > 0
	PRINTFORM 性交经验＋{(LOVE_EXP:5 + LOVE_EXP:4)} 
	EXP:(ARG:0):5 += (LOVE_EXP:5 + LOVE_EXP:4)
ENDIF

;精液経験
LOCAL = (LOVE_EXP:5 + LOVE_EXP:4 + LOVE_EXP:3) / 2
IF LOCAL > 0
	PRINTFORM 精液经验＋{LOCAL} 
	EXP:(ARG:0):20 += LOCAL
ENDIF

;獣姦経験
IF LOVE_EXP:7  > 0
	PRINTFORM 兽奸经验＋{LOVE_EXP:7} 
	EXP:(ARG:0):56 += LOVE_EXP:7
ENDIF

;被写経験
IF LOVE_EXP:8  > 0
	PRINTFORM 拍摄经验＋{LOVE_EXP:8} 
	EXP:(ARG:0):70 += LOVE_EXP:8
ENDIF

;膣内射精
;V経験?快V
IF (LOVE_EXP:4 / 2) > 0
	SELECTCASE LOVER
	CASE 1 TO 20
		;成人男性
		RACE = 4
		CFLAG:(ARG:0):105 = LOVE_EXP:4
	CASE 21 TO 40
		;異種族男性
		RACE = 6
		CFLAG:(ARG:0):107 = LOVE_EXP:4
	CASE 41 TO 60
		;女性?ふたなり
		RACE = 4
		CFLAG:(ARG:0):105 = LOVE_EXP:4
	CASE 61 TO 80
		;少年
		RACE = 4
		CFLAG:(ARG:0):105 = LOVE_EXP:4
	CASE 81
		;犬
		RACE = 5
		CFLAG:(ARG:0):106 = LOVE_EXP:4
	CASE 82 TO 100
		;その他の獣
		RACE = 6
		CFLAG:(ARG:0):107 = LOVE_EXP:4
	ENDSELECT
	CALL NAKADASHI_CHECK , ARG:0 , RACE
ENDIF


IF MARRIAGE != 902
	PRINTW (善恶值减少:-1)
	CALL KARMA, ARG:0, -1
ELSE
	WAIT
ENDIF

CFLAG:(ARG:0):607 += 1

RETURN 1

;------------------------------------
@DUNGEON_TOWN_LOVER_CHARA_ENTER, ARG:0
#DIM LOVER
;------------------------------------
;ARG:0の勇者が同じく勇者の恋人を探す

;恋人フラグが無いとダメ
SIF GETBIT(FLAG:8,2) == 0
	RETURN 0

;恋人がすでにいるとダメ
SIF CFLAG:(ARG:0):606 > 0
	RETURN 0

;配偶者がすでにいるとダメ
SIF CFLAG:(ARG:0):601 > 0
	RETURN 0

LOCAL = RAND:CHARANUM

;自分自身の場合
;SIF LOCAL == ARG:0
;	LOCAL = RAND:CHARANUM

;同じ状況でないとダメ
;同じく侵攻中とか、同じく奴隷状態など
SIF CFLAG:LOCAL:1 != CFLAG:(ARG:0):1
	RETURN 0

;対象に恋人がすでにいるとダメ
SIF CFLAG:LOCAL:606 > 0
	RETURN 0

;対象に配偶者がすでにいるとダメ
SIF CFLAG:LOCAL:601 > 0
	RETURN 0

IF TALENT:(ARG:0):122 == 1 && TALENT:LOCAL:122 == 1
	;オトコ - オトコの場合
	;BLっ気が双方に無いとダメ
	SIF ABL:(ARG:0):23 == 0 && ABL:LOCAL:23 == 0
		RETURN 0
ELSEIF  TALENT:(ARG:0):122 == 0 && TALENT:LOCAL:122 == 0
	;女 - 女の場合
	;レズっ気が双方に無いとダメ
	SIF ABL:(ARG:0):22 == 0 && ABL:LOCAL:22 == 0
		RETURN 0
ENDIF

;侵攻中の場合同じパーティにいないとダメ
SIF CFLAG:(ARG:0):1 == 2 && (CFLAG:(ARG:0):533 != CFLAG:LOCAL:533)
	RETURN 0

IF LOCAL == 0
	;あなたの場合愛じゃないとダメ
	SIF TALENT:(ARG:0):85 == 0
		RETURN 0
	PRINTFORML %SAVESTR:(ARG:0)%向你表白了……
	PRINTFORML 要接受吗？
	CALL SELECT_YES_NO
	SIF RESULT == 1
		RETURN 0
ENDIF

;ID情報を交換
CALL CHARA_ID_OUTPUT,ARG:0
CFLAG:LOCAL:610 = RESULT
CALL CHARA_ID_OUTPUT,LOCAL
CFLAG:(ARG:0):610 = RESULT

;名前情報を交換
CFLAG:LOCAL:608 = CFLAG:(ARG:0):6
CFLAG:(ARG:0):608 = CFLAG:LOCAL:6

;恋人フラグをON
CFLAG:LOCAL:606 = 200
CFLAG:(ARG:0):606 = 200
SIF ARG:0 != 0 && LOCAL != ARG:0
	PRINTFORMW %SAVESTR:(ARG:0)%被%SAVESTR:LOCAL%吸引了，两人开始交往了……
SIF LOCAL == ARG:0
	PRINTFORMW %SAVESTR:(ARG:0)%被自己的%GET_LOOK_INFO(ARG:0,"魅力点")%吸引了……

RETURN 1










