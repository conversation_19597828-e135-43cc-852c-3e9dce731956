﻿大文字小文字の違いを無視する:YES
_Rename.csvを利用する:NO
_Replace.csvを利用する:YES
マウスを使用する:YES
メニューを使用する:YES
デバッグコマンドを使用する:NO
多重起動を許可する:YES
オートセーブを行なう:YES
キーボードマクロを使用する:YES
ウィンドウの高さを可変にする:YES
描画インターフェース:TEXTRENDERER
ウィンドウ幅:760
ウィンドウ高さ:480
ウィンドウ位置X:0
ウィンドウ位置Y:0
起動時のウィンドウ位置を指定する:NO
起動時にウィンドウを最大化する:NO
履歴ログの行数:5000
PRINTCを並べる数:3
PRINTCの文字数:25
フォント名:ＭＳ ゴシック
フォントサイズ:18
一行の高さ:19
文字色:192,192,192
背景色:0,0,0
選択中文字色:255,255,0
履歴文字色:192,192,192
フレーム毎秒:5
最大スキップフレーム数:3
スクロール行数:1
無限ループ警告までのミリ秒数:5000
表示する最低警告レベル:1
ロード時にレポートを表示する:NO
ロード時に引数を解析する:NO
呼び出されなかった関数を無視する:YES
関数が見つからない警告の扱い:IGNORE
関数が呼び出されなかった警告の扱い:IGNORE
ボタンの途中で行を折りかえさない:NO
サブディレクトリを検索する:NO
読み込み順をファイル名順にソートする:NO
表示するセーブデータ数:20
eramaker互換性に関する警告を表示する:YES
システム関数の上書きを許可する:YES
システム関数が上書きされたとき警告を表示する:YES
関連づけるテキストエディタ:notepad
テキストエディタコマンドライン指定:USER_SETTING
エディタに渡す行指定引数:
同名の非イベント関数が複数定義されたとき警告する:NO
解釈不可能な行があっても実行する:NO
CALLNAMEが空文字列の時にNAMEを代入する:NO
セーブデータをsavフォルダ内に作成する:NO
擬似変数RANDの仕様をeramakerに合わせる:NO
関数・属性については大文字小文字を無視しない:NO
全角スペースをホワイトスペースに含める:YES
セーブデータをUTF-8で保存する:YES
アーカイブを中国語形式で保存する:NO
ファイルエンコーディング:AUTO_SHIFTJIS
読み込まれたトランスレータ:
ver1739以前の非ボタン折り返しを再現する:NO
内部で使用する東アジア言語:JAPANESE
ONEINPUT系命令でマウスによる2文字以上の入力を許可する:NO
イベント関数のCALLを許可する:NO
SPキャラを使用する:NO
セーブデータをバイナリ形式で保存する:NO
ユーザー関数の全ての引数の省略を許可する:NO
ユーザー関数の引数に自動的にTOSTRを補完する:NO
FORM中の三連記号を展開しない:NO
TIMESの計算をeramakerにあわせる:NO
キャラクタ変数の引数を補完しない:NO
文字列変数の代入に文字列式を強制する:NO
