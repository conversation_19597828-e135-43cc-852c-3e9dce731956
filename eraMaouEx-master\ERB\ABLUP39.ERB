﻿;兽奸中毒のLvUP処理とその可否判定
;eraIm@s_ver.0.17βdのスクリプトをベースに処理を簡略化
;eraIM@Sから導入しました(eramaou)

;-------------------------------------------------
;兽奸中毒のLvUP
;-------------------------------------------------
@ABLUP39
DRAWLINE
;PRINTL 奴隶的兽奸成瘾加深了。
;PRINTL 兽奸中毒越高，越容易在兽交中感到满足，
;PRINTL 只有频繁而激烈的兽交，能安抚她那躁动的心。
;CUSTOMDRAWLINE ‥
;兽奸中毒はLv5が上限
;[淫乱][动物耳朵][牝犬]が付いている場合はLv10まで开放
IF ABL:39 >= 5 && (TALENT:76 == 0 && TALENT:124 == 0 && TALENT:136 == 0)
	PRINTW 需要特殊素质才能继续提升
	RETURN 0
ELSEIF ABL:39 >= 10
	PRINTW 已达最高级
	RETURN 0
;精液中毒＋百合中毒＋兽奸中毒は11以上にならない
;でも、珠が沢山あるの場合はレベルアップできる。
ELSEIF ABL:32 + ABL:33 + ABL:39 >= 10
	IF JUEL:5 < ABL:39 * ABL:39 * 4000 || JUEL:6 < ABL:39 * ABL:39 * 4000
	PRINTFORML 精液中毒({ABL:32})＋百合中毒({ABL:33})＋兽奸中毒({ABL:39})上限为10
	PRINTFORML 至少达成%PALAMNAME:5%点数{ABL:39 * ABL:39 * 4000}点或%PALAMNAME:6%点数{ABL:39 * ABL:39 * 4000}点的其中一项
	PRINTFORMW 方可提升当前兽奸中毒的等级
	RETURN 0
	ENDIF
ENDIF

;必要な欲情点数
A = 0
;必要な屈服点数
B = 0
;必要な兽奸经验
C = 0
;必要な异常经验回数
F = 0

CALL DECIDE_ABLUP39

;上げるときは异常经验必要（素質：[淫乱][容易上瘾]なら無視できる）
SIF F > 0
	PRINTFORML %EXPNAME:50%{F}以上(现在{EXP:50})且

;欲望が必要
PRINTFORML %ABLNAME:11%LV{ABL:39 + 1}以上(现在LV{ABL:11})且

PRINTFORM [0] - %PALAMNAME:5%点数×{JUEL:5}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 
PRINTFORML 　　　%PALAMNAME:6%点数×{JUEL:6}/{B}
PRINTFORML 　　　%EXPNAME:56%　{EXP:56}/{C}
PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 0) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:39 += 1
IF RESULT == 0
	JUEL:5 -= A
	JUEL:6 -= B
ENDIF

PRINTFORML %ABLNAME:39%变为LV{ABL:39}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP39
;-------------------------------------------------
ABL:39 ++

IF I == 0
	JUEL:5 -= A
	JUEL:6 -= B
ENDIF


;--------------------------------------------------
;レベルアップに必要な珠計算、レベルアップ予告処理
;--------------------------------------------------
@DECIDE_ABLUP39
;兽奸中毒はLv5が上限
;[淫乱][动物耳朵][牝犬]が付いている場合はLv10まで开放
SIF ABL:39 >= 10
	RETURN 0
SIF ABL:39 >= 5 && (TALENT:76 == 0 && TALENT:124 == 0 && TALENT:136 == 0)
	RETURN 0
;精液中毒＋百合中毒＋兽奸中毒は11以上にならない
SIF ABL:32 + ABL:33 + ABL:39 >= 30
	RETURN 0

;条件別にＯＫかダメかを記録する
I = 0

IF ABL:39 == 0
	A = 2000
	B = 2000
	C = 30
ELSEIF ABL:39 == 1
	A = 5000
	B = 5000
	C = 100
ELSEIF ABL:39 == 2
	A = 10000
	B = 10000
	C = 220
ELSEIF ABL:39 == 3
	A = 20000
	B = 20000
	C = 400
ELSEIF ABL:39 == 4
	A = 30000
	B = 30000
	C = 800
ELSEIF ABL:39 == 5
	A = 45000
	B = 45000
	C = 1600
ELSEIF ABL:39 == 6
	A = 75000
	B = 75000
	C = 2000
ELSEIF ABL:39 == 7
	A = 100000
	B = 100000
	C = 2800
ELSEIF ABL:39 == 8
	A = 200000
	B = 200000
	C = 4000
ELSEIF ABL:39 == 9
	A = 300000
	B = 300000
	C = 6000
ENDIF

IF ABL:32 + ABL:33 + ABL:39 >= 10
	A = ABL:39 * ABL:39 * 4000
	B = ABL:39 * ABL:39 * 4000	
ENDIF

;戒备森严
IF TALENT:27
	IF ABL:37 == 3
		TIMES A , 2.00
		TIMES B , 2.00
		TIMES C , 2.00
	ELSEIF ABL:37 == 4
		TIMES A , 2.50
		TIMES B , 2.50
		TIMES C , 2.50
	ELSEIF ABL:37 >= 5
		TIMES A , 3.00
		TIMES B , 3.00
		TIMES C , 3.00
	ENDIF
ENDIF

F = 0
;２以上に上げるときは异常经验必要（素質：[容易上瘾][淫乱][牝犬]なら無視できる）
SIF ABL:39 >= 2 && (TALENT:72 == 0 && TALENT:76 == 0 && TALENT:136 == 0)
		F = ABL:39 + 1

;克制
IF TALENT:20
	TIMES A , 2.50
	TIMES B , 2.50
	TIMES C , 1.50
ENDIF
;接受快感
IF TALENT:70
	TIMES A , 0.75
	TIMES B , 0.75
;否定快感
ELSEIF TALENT:71
	TIMES A , 1.75
	TIMES B , 1.75
ENDIF
;容易上瘾
IF TALENT:72
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
ENDIF
;倒錯的
IF TALENT:80
	TIMES A , 0.75
	TIMES B , 0.75
	TIMES C , 0.75
ENDIF
;疯狂
IF TALENT:123
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
ENDIF
;动物耳朵
IF TALENT:124
	TIMES A , 0.80
	TIMES B , 0.80
	TIMES C , 0.80
ENDIF
;牝犬
IF TALENT:136
	TIMES A , 0.50
	TIMES B , 0.50
	TIMES C , 0.50
ENDIF
;爱慕(心が主人に向いているので獣になびきにくい)
IF TALENT:85
	TIMES A , 1.80
	TIMES B , 1.80
	TIMES C , 1.50
ENDIF

;最低でも１回・１個は必要
SIF A < 1
	A = 1
SIF B < 1
	B = 1
SIF C < 1
	C = 1

;欲望が必要
SIF ABL:11 < ABL:39 + 1
	I |= 4
;欲情点数は足りている？
SIF JUEL:5 < A
	I |= 1
;屈服点数は足りている？
SIF JUEL:6 < B
	I |= 1

;兽奸经验は足りている？
SIF EXP:56 < C
	I |= 2
;异常经验は足りている？
SIF EXP:50 < F
	I |= 2

IF I == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF

;
;
;