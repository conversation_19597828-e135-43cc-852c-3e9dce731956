﻿

ERA语法基础
==================================================

本文面向初学者，从一个ERB文件为线索，帮助初学者能够大概读懂游戏脚本文件。

关于详细ERA的语法和Emuera的用法请参考
	Emuera Wiki
	http://sourceforge.jp/projects/emuera/wiki/FrontPage

	
文件
==================================================
使用Emuera实现的ERA游戏除却程序、设置、存档、日志、说明文件外，
主要有三种文件：CSV文件，ERB脚本文件，ERH脚本头文件。

这里我们从包含游戏主要代码的ERB文件开始。



注释
==================================================
首先你需要能够区分ERB文件中哪些是发挥作用的代码，哪些是作者留下的注释或者不再发挥作用的代码。
ERA文件通过英文半角分号为注释符号，注释通常单独成行。
例如：
		PRINTW Hello World
		;在屏幕上输出一行Hello World
		;（这两行是注释）



函数（関数）
==================================================
通常一个ERB文件的第一行不是注释的话那么差不多一定是一个函数声明。
例如：
		@CHAR_AGE_GENERATE, ARG

其中行首的“@”表示此行声明函数，CHAR_AGE_GENERATE是函数名，ARG是函数参数。
下面都是一些的声明：
		@RACE_CONFIG
		;不带参数的函数
		@CHAR_SIZE_GENERATE, ARG:0, ARG:1
		;有两个数值参数的函数
		@INPUT_YN(ARGS:0 = "是", ARGS:1 = "不是", ARG = 1)
		;有2个文本值参数，1个数值参数，且3个参数都有默认值的函数
		;用括号封闭参数 与 只用逗号分隔参数 的两种声明方式并没有太大区别

函数声明之后到下一个函数声明之前的所有代码都是这个函数的实现。

例如下面便是一个完整的函数：
		;Hello World函数
		@HELLO_WORLD
		PRINTW Hello World!
		;在屏幕上输出一行Hello World!
		

ERA游戏的实现本质上是通过不断调用一个又一个的函数，
我们可以看到ERB文件实际上也是由一个一个函数组成的。



指令（命令）
==================================================
每个函数都具有一定的功能，也就是说函数一定是要做些什么的。
这包括数据处理、逻辑判断、过程处理以及用户交互。

这些操作有些可以通过脚本的语法实现，有一些则必须要通过“指令”来明确的告诉程序来做什么。

你也可以理解为指令是程序内置的一些“函数”用于实现在脚本里实现不了的功能。

例如你要在屏幕上显示一行Hello World，那么你必须要用PRINT系列指令。
例如你要调用另一个函数，那么你必须要用CALL指令。

		@SYSTEM_TITLE
		CALL HELLO_WORLD
		;使用CALL指令调用HELLO_WORLD函数
		
		@HELLO_WORLD
		PRINTW Hello World!
		;使用PRINTW指令在屏幕上输出一行Hello World!



变量（変数）
==================================================
在充分理解函数和指令之后接下来一个重要概念为数据的容器——“变量”。

ERA中变量的值类型主要有两种：数值（整型）变量，文本值（字符串）变量。

		LOCAL = 1
		LOCALS = Hello World!

在这样的前提下，变量可以细分为数组变量（配列変数）、角色变量（CHARA変数）、模拟变量（擬似変数）等。

数组变量表示一个从 0 开始的序列，如：
		LOCAL:0 = 100
		LOCAL:1 = 15
		LOCALS:0 = Hello World!
		LOCALS:1 = This is d.f.32!!

其中 LOCAL:0 可以简写为 LOCAL 。

角色变量表示会随游戏登场角色增减而改变的变量，如：
		NAME:0 = 主人公
		CALLNAME:0 = 你
		ABL, EXP, TALENT, JUEL, CFLAG, CSTR 等

角色变量的第一索引省略时使用 TARGET 当前值而非 0 。
也就是说 TALENT:183 是 TALENT:TARGET:183 的简写。（TALENT有两个索引）

变量我们先介绍到这里了。



条件语句
==================================================
条件语句主要包括 IF ~ ENDIF, SIF, SELECTCASE ~ ENDSELECT 三种。


IF ~ ENDIF
--------------------------------------------------

		IF LOCAL == 1
			PRINTW LOCAL等于1
		ELSEIF LOCAL == 2
			PRINTW LOCAL等于2
		ELSEIF (LOCAL == 3) || (LOCAL == 4)
			PRINTW LOCAL等于3或4
		ELSEIF (LOCAL >= 5) && (LOCAL <= 10)
			PRINTW LOCAL大于等于5小于等于10
		ELSE
			PRINTW LOCAL以外的情况
		ENDIF

SIF
--------------------------------------------------
	
		SIF LOCAL == 1
			PRINTW LOCAL等于1

SELECTCASE ~ ENDSELECT 
--------------------------------------------------
	
		SELECTCASE LOCAL
			CASE 1
				PRINTW LOCAL等于1
			CASE 2
				PRINTW LOCAL等于2
			CASE 3, 4
				PRINTW LOCAL等于3或4
			CASE 5 TO 10
				PRINTW LOCAL大于等于5小于等于10
			CASEELSE
				PRINTW LOCAL以外的情况
		ENDSELECT
	