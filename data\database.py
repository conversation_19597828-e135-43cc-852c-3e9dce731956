"""
SQLite数据库管理器
用于存储ERA游戏生成过程中的状态、配置和生成数据
"""

import sqlite3
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import asyncio
import aiosqlite

from core.types import AgentState, GameConfig, AgentMessage

class ERADatabase:
    """ERA游戏生成数据库管理器"""
    
    def __init__(self, db_path: str = "era_agent.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
        
    def initialize_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建项目表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    config TEXT,  -- JSON格式的游戏配置
                    status TEXT DEFAULT 'created',
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # 创建代理状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS agent_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    agent_id TEXT,
                    status TEXT,
                    data TEXT,  -- JSON格式的代理数据
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            ''')
            
            # 创建消息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    sender TEXT,
                    receiver TEXT,
                    message_type TEXT,
                    content TEXT,  -- JSON格式的消息内容
                    timestamp REAL,
                    created_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            ''')
            
            # 创建生成文件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS generated_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    file_path TEXT,
                    file_type TEXT,  -- erb, csv, config等
                    content TEXT,
                    size INTEGER,
                    created_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            ''')
            
            # 创建ERA语法知识库表（用于RAG）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS era_knowledge (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT,  -- 语法类别：functions, variables, commands等
                    title TEXT,
                    content TEXT,
                    embedding BLOB,  -- 向量嵌入
                    metadata TEXT,  -- JSON格式的元数据
                    created_at TEXT
                )
            ''')
            
            # 创建模板表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    type TEXT,  -- erb_function, csv_structure等
                    content TEXT,
                    description TEXT,
                    created_at TEXT
                )
            ''')
            
            # 创建工作流执行历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS workflow_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    workflow_id TEXT,
                    step_name TEXT,
                    status TEXT,  -- pending, running, completed, failed
                    input_data TEXT,
                    output_data TEXT,
                    error_message TEXT,
                    execution_time REAL,
                    created_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            ''')
            
            conn.commit()
            self.logger.info("Database initialized successfully")
            
    def create_project(self, name: str, description: str, game_config: GameConfig) -> int:
        """创建新项目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            config_json = json.dumps({
                "game_title": game_config.game_title,
                "game_description": game_config.game_description,
                "character_count": game_config.character_count,
                "features": game_config.features,
                "output_path": game_config.output_path,
                "encoding": game_config.encoding
            })
            
            cursor.execute('''
                INSERT INTO projects (name, description, config, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, description, config_json, now, now))
            
            project_id = cursor.lastrowid
            conn.commit()
            
            self.logger.info(f"Created project {name} with ID {project_id}")
            return project_id
            
    def update_agent_state(self, project_id: int, agent_id: str, status: str, data: Dict[str, Any] = None):
        """更新代理状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            data_json = json.dumps(data) if data else None
            
            # 检查是否已存在该代理状态
            cursor.execute('''
                SELECT id FROM agent_states 
                WHERE project_id = ? AND agent_id = ?
            ''', (project_id, agent_id))
            
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有记录
                cursor.execute('''
                    UPDATE agent_states 
                    SET status = ?, data = ?, updated_at = ?
                    WHERE project_id = ? AND agent_id = ?
                ''', (status, data_json, now, project_id, agent_id))
            else:
                # 创建新记录
                cursor.execute('''
                    INSERT INTO agent_states (project_id, agent_id, status, data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (project_id, agent_id, status, data_json, now, now))
                
            conn.commit()
            
    def save_message(self, project_id: int, message: AgentMessage):
        """保存代理消息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            content_json = json.dumps(message.content)
            
            cursor.execute('''
                INSERT INTO messages (project_id, sender, receiver, message_type, content, timestamp, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (project_id, message.sender, message.receiver, message.message_type, 
                  content_json, message.timestamp, now))
                  
            conn.commit()
            
    def save_generated_file(self, project_id: int, file_path: str, file_type: str, content: str):
        """保存生成的文件信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            file_size = len(content.encode('utf-8'))
            
            cursor.execute('''
                INSERT INTO generated_files (project_id, file_path, file_type, content, size, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (project_id, file_path, file_type, content, file_size, now))
            
            conn.commit()
            
    def get_project_status(self, project_id: int) -> Dict[str, Any]:
        """获取项目状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取项目基本信息
            cursor.execute('''
                SELECT name, description, config, status, created_at, updated_at
                FROM projects WHERE id = ?
            ''', (project_id,))
            
            project = cursor.fetchone()
            if not project:
                return None
                
            # 获取代理状态
            cursor.execute('''
                SELECT agent_id, status, data, updated_at
                FROM agent_states WHERE project_id = ?
            ''', (project_id,))
            
            agents = cursor.fetchall()
            
            # 获取生成文件统计
            cursor.execute('''
                SELECT file_type, COUNT(*), SUM(size)
                FROM generated_files WHERE project_id = ?
                GROUP BY file_type
            ''', (project_id,))
            
            file_stats = cursor.fetchall()
            
            return {
                "project": {
                    "name": project[0],
                    "description": project[1], 
                    "config": json.loads(project[2]),
                    "status": project[3],
                    "created_at": project[4],
                    "updated_at": project[5]
                },
                "agents": [
                    {
                        "agent_id": agent[0],
                        "status": agent[1],
                        "data": json.loads(agent[2]) if agent[2] else None,
                        "updated_at": agent[3]
                    }
                    for agent in agents
                ],
                "file_stats": [
                    {
                        "type": stat[0],
                        "count": stat[1],
                        "total_size": stat[2]
                    }
                    for stat in file_stats
                ]
            }
            
    def save_era_knowledge(self, category: str, title: str, content: str, 
                          embedding: Optional[bytes] = None, metadata: Dict[str, Any] = None):
        """保存ERA语法知识到知识库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            metadata_json = json.dumps(metadata) if metadata else None
            
            cursor.execute('''
                INSERT INTO era_knowledge (category, title, content, embedding, metadata, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (category, title, content, embedding, metadata_json, now))
            
            conn.commit()
            
    def search_era_knowledge(self, category: Optional[str] = None, 
                           keywords: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """搜索ERA语法知识"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = "SELECT id, category, title, content, metadata FROM era_knowledge WHERE 1=1"
            params = []
            
            if category:
                query += " AND category = ?"
                params.append(category)
                
            if keywords:
                for keyword in keywords:
                    query += " AND (title LIKE ? OR content LIKE ?)"
                    params.extend([f"%{keyword}%", f"%{keyword}%"])
                    
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            return [
                {
                    "id": result[0],
                    "category": result[1],
                    "title": result[2],
                    "content": result[3],
                    "metadata": json.loads(result[4]) if result[4] else None
                }
                for result in results
            ]
            
    def save_template(self, name: str, template_type: str, content: str, description: str = ""):
        """保存模板"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            
            cursor.execute('''
                INSERT OR REPLACE INTO templates (name, type, content, description, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, template_type, content, description, now))
            
            conn.commit()
            
    def get_template(self, name: str) -> Optional[Dict[str, Any]]:
        """获取模板"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT name, type, content, description, created_at
                FROM templates WHERE name = ?
            ''', (name,))
            
            result = cursor.fetchone()
            if result:
                return {
                    "name": result[0],
                    "type": result[1],
                    "content": result[2],
                    "description": result[3],
                    "created_at": result[4]
                }
            return None
            
    def log_workflow_step(self, project_id: int, workflow_id: str, step_name: str, 
                         status: str, input_data: Dict[str, Any] = None, 
                         output_data: Dict[str, Any] = None, error_message: str = None,
                         execution_time: float = 0.0):
        """记录工作流步骤执行"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            input_json = json.dumps(input_data) if input_data else None
            output_json = json.dumps(output_data) if output_data else None
            
            cursor.execute('''
                INSERT INTO workflow_executions 
                (project_id, workflow_id, step_name, status, input_data, output_data, 
                 error_message, execution_time, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (project_id, workflow_id, step_name, status, input_json, 
                  output_json, error_message, execution_time, now))
                  
            conn.commit()
            
    def get_workflow_history(self, project_id: int) -> List[Dict[str, Any]]:
        """获取工作流执行历史"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT workflow_id, step_name, status, input_data, output_data, 
                       error_message, execution_time, created_at
                FROM workflow_executions 
                WHERE project_id = ?
                ORDER BY created_at DESC
            ''', (project_id,))
            
            results = cursor.fetchall()
            
            return [
                {
                    "workflow_id": result[0],
                    "step_name": result[1],
                    "status": result[2],
                    "input_data": json.loads(result[3]) if result[3] else None,
                    "output_data": json.loads(result[4]) if result[4] else None,
                    "error_message": result[5],
                    "execution_time": result[6],
                    "created_at": result[7]
                }
                for result in results
            ]
            
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # 清理旧的工作流执行记录
            cursor.execute('''
                DELETE FROM workflow_executions WHERE created_at < ?
            ''', (cutoff_date,))
            
            # 清理旧的消息记录
            cursor.execute('''
                DELETE FROM messages WHERE created_at < ?
            ''', (cutoff_date,))
            
            conn.commit()
            self.logger.info(f"Cleaned up data older than {days} days")

# 异步数据库操作类
class AsyncERADatabase:
    """异步ERA数据库操作"""
    
    def __init__(self, db_path: str = "era_agent.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
    async def get_project_status_async(self, project_id: int) -> Dict[str, Any]:
        """异步获取项目状态"""
        async with aiosqlite.connect(self.db_path) as db:
            # 获取项目信息
            async with db.execute('''
                SELECT name, description, config, status, created_at, updated_at
                FROM projects WHERE id = ?
            ''', (project_id,)) as cursor:
                project = await cursor.fetchone()
                
            if not project:
                return None
                
            # 获取代理状态
            agents = []
            async with db.execute('''
                SELECT agent_id, status, data, updated_at
                FROM agent_states WHERE project_id = ?
            ''', (project_id,)) as cursor:
                async for row in cursor:
                    agents.append({
                        "agent_id": row[0],
                        "status": row[1],
                        "data": json.loads(row[2]) if row[2] else None,
                        "updated_at": row[3]
                    })
                    
            return {
                "project": {
                    "name": project[0],
                    "description": project[1],
                    "config": json.loads(project[2]),
                    "status": project[3],
                    "created_at": project[4],
                    "updated_at": project[5]
                },
                "agents": agents
            }
            
    async def save_message_async(self, project_id: int, message: AgentMessage):
        """异步保存消息"""
        async with aiosqlite.connect(self.db_path) as db:
            now = datetime.now().isoformat()
            content_json = json.dumps(message.content)
            
            await db.execute('''
                INSERT INTO messages (project_id, sender, receiver, message_type, content, timestamp, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (project_id, message.sender, message.receiver, message.message_type,
                  content_json, message.timestamp, now))
                  
            await db.commit()