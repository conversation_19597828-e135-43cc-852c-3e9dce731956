﻿@CHAR_CUSTOM, ARG , ARG:1
#DIM L_PAGE
#DIM L_LCOUNT
#DIM PRICE

SWAP TARGET, ARG
L_LCOUNT = LINECOUNT
;初始价格
CALL CHARA_COST , TARGET
PRICE = RESULT

$DRAW_PAGE
REDRAW 0
CLEARLINE LINECOUNT - L_LCOUNT
L_LCOUNT = LINECOUNT

CUSTOMDRAWLINE =
IF ARG:1 == 0
    PRINTFORML 设定角色属性（%SAVESTR:TARGET%）	角色现价值为{PRICE}		<{L_PAGE+1}/5>
ELSE	
    PRINTFORML 修改角色属性（%SAVESTR:TARGET%）			<{L_PAGE+1}/5>
ENDIF	
DRAWLINE

IF INRANGE(L_PAGE, 0, 2)
	CALL CHAR_CUSTOM_TALENT_PAGE(L_PAGE,ARG:1)
ELSE
	CALL CHAR_CUSTOM_LOOK_PAGE(L_PAGE-3)
ENDIF

IF LINECOUNT - L_LCOUNT < 27
	REPEAT 27 + L_LCOUNT - LINECOUNT
		PRINTL
	REND
ENDIF

DRAWLINE
PRINTLC [997] 前一页
PRINTLC [999] 确定

SIF ARG:1 == 0
    PRINTLC [996] 取消
	
PRINTLC [998] 后一页
PRINTL

$INPUT_LOOP
INPUT

IF RESULT == 999
    ;素质检查
	IF ARG:1 == 0
		CALL TALENT_EMPTY_CHECK ,TARGET
		SIF RESULT == 1
			GOTO DRAW_PAGE
		;魔族新勇者随机成为黑暗救世主、九尾、混沌龙
        SIF TALENT:A:314 == 9
            TALENT:A:322 = RAND:3 + 191		
		;人物初始设定（抄自CHARA_MAKE）
		A = TARGET
		;经验级别
		CFLAG:A:9 = 1
		EXP:A:80 = 0
		;初始位置
		CFLAG:A:1 = 0
		;职业、基础
		CALL CM_BASE
		;善恶
		CALL CM_KIND
		;服装设定
		CALL CM_CLOTH	;SET_CHAR_CLOTH
		;一人称の設定
		CALL RANDOM_SELF_CALL, A
		;年齢or身長などを表示する設定の場合は身体データを設定
		IF GETBIT(FLAG:5,12) || GETBIT(FLAG:5,15)
			CALL CHAR_BODY_GENERATE_WAPPED, A
		ENDIF
		;妊娠设定出产日期
		IF TALENT:A:153 || TALENT:A:341 || TALENT:A:342 || TALENT:A:343
		CFLAG:A:110 = DAY + 10 + RAND:6
		CFLAG:A:111 = 0
		ENDIF
		;再次检查价格
		CALL CHARA_COST , TARGET
		PRICE = RESULT
		PRINTFORML %SAVESTR:TARGET%的最终价格是{PRICE}点，可以吗？
		PRINTL [1] 好，就是这样了！  [2] 我还想再修改一下。 
		INPUT
		$LOOP
		IF RESULT == 1
		   IF MONEY < PRICE 
			  PRINTW 钱不够，还是重新设定吧！
			  GOTO DRAW_PAGE
		   ELSE
			  PRINTFORMW 花费金钱{PRICE}点，%SAVESTR:TARGET%现已加入豪华午餐。
			  MONEY -= PRICE  
			  EX_FLAG:4444 -= PRICE
			  REDRAW 1
			  SWAP TARGET, ARG
			  RETURN 
		   ENDIF
		ELSEIF  RESULT == 2
		   GOTO DRAW_PAGE 
		ELSE   
		   GOTO LOOP
		ENDIF
    ELSEIF ARG:1 == 1
	    PRINTFORMW %SAVESTR:TARGET%现已加入豪华午餐。
        REDRAW 1
	    SWAP TARGET, ARG
		RETURN 
    ENDIF		
ELSEIF RESULT == 998
	SIF L_PAGE < 4
		L_PAGE ++
	GOTO DRAW_PAGE
ELSEIF RESULT == 997
	SIF L_PAGE > 0
		L_PAGE --
	GOTO DRAW_PAGE
ELSEIF RESULT == 996
	REDRAW 1
	SWAP TARGET, ARG
	DELCHARA ARG
	RETURN 
ENDIF

IF INRANGE(L_PAGE, 0, 2)	
	CALL CHAR_CUSTOM_TALENT_DEAL(RESULT)
	;储存CHAR_CUSTOM_TALENT_DEAL输出
    P = RESULT	
	;每次素质变更检查价格
	CALL CHARA_COST , TARGET
	PRICE = RESULT
	;恢复CHAR_CUSTOM_TALENT_DEAL输出
	RESULT = P
	
ELSE
	CALL CHAR_CUSTOM_LOOK_DEAL(RESULT)
	SIF TALENT:308 <= 100
	    TALENT:115 = 0
ENDIF

IF RESULT == 0
	GOTO DRAW_PAGE
ELSE
	CLEARLINE 1
	REUSELASTLINE 无效值
	GOTO INPUT_LOOP
ENDIF

RESTART

@CHAR_CUSTOM_TALENT_DEAL(L_TAL)
#DIM L_TAL
#DIM L_I

IF !INRANGE(L_TAL, 0,500)
	RETURN -1
ENDIF

TALENT:TARGET:L_TAL = ! TALENT:TARGET:L_TAL
CALL CONFLICT_CHECK(L_TAL)

;胸围变化
L_I = TALENT:L_TAL
IF GROUPMATCH(L_TAL,109,110,114,116,119)
	TALENT:109 = 0
	TALENT:110 = 0
	TALENT:114 = 0
	TALENT:119 = 0
	TALENT:116 = 0
	TALENT:L_TAL = L_I
	TRYCALL CHAR_BUST_REGENERATE_WAPPED, TARGET
ENDIF
;口上唯一
IF GROUPMATCH(L_TAL,160,161,162,163,164,166,172,173,174)
	TALENT:160 = 0
	TALENT:161 = 0
	TALENT:162 = 0
	TALENT:163 = 0
	TALENT:164 = 0
	TALENT:166 = 0
	TALENT:172 = 0
	TALENT:173 = 0
	TALENT:174 = 0
	TALENT:L_TAL = 1
ENDIF
SIF TALENT:174 == 1
	TALENT:122 = 1
SIF TALENT:166 == 1
	TALENT:122 = 0
;职业唯一
IF INRANGE(L_TAL,200,220)
    FOR L_I ,200 , 221
	    TALENT:L_I = 0
	NEXT
    TALENT:L_TAL = 1
ENDIF	
;精英固定魔族
SIF L_TAL == 220
    TALENT:314 = 9
SIF !(TALENT:314 == 9)
	TALENT:220 = 0
;龍族有角
SIF TALENT:314 == 5
	TALENT:264 = 1
;扶她及男人才有童贞
SIF !TALENT:121 && !TALENT:122
    TALENT:1 = 0
;纤细体型不肥胖
SIF TALENT:308 <= 100
	TALENT:115 = 0
RETURN 0

@CONFLICT_CHECK(ARG)
#DIM L_A
#DIM L_B
{
#DIM CONST PAIRS = 
	0,75, 30,75, 
	10,12,	11,13,	13,18, 14,16,	15,17,	17,18,	
	20,23,	21,23,	22,23,
	20,63,	21,63,	22,63,
	23,24,	25,26,	27,28,
	30,31,	32,33,	
	35,36,	40,41,	42,43,	
	44,45,	50,51,	61,62,	62,64,
	70,71,	74,150, 76,85,
	74,75, 74,77, 74,78, 75,77, 75,78, 77,78, 122,78,
	79,80, 79,81, 79,82, 79,122, 
	80,81, 80,82, 81,82,	
	99,100,		101,102,
	103,104,	105,106, 103,122, 104,122,
	107,108,	111,112,	
	109,110, 109,114, 109,116, 119,109, 119,116, 119,114, 119,110, 122,109, 122,110, 122,114, 122,116, 122,119,
	110,114, 110,116, 114,116,
	121,122,	153,154,	99,263, 153,122, 154,122, 130,122, 155,122, 157,122,
	155,156, 10,161, 26,161,
	60,150, 0,122,
	248,256, 244,253, 244,255, 253,255,
	259,260,
}
	LOCAL = 0
	$SEARCH_LOOP
	LOCAL = FINDELEMENT(PAIRS,ARG,LOCAL)
	;#;PRINTFORML {LOCAL}
	IF LOCAL >= 0	
		L_A = PAIRS:(LOCAL /2 *2)
		L_B = PAIRS:(LOCAL /2 *2 +1)
		;#;PRINTFORML {LOCAL} {L_A} {L_B} {ARG}
		IF TALENT:L_A && TALENT:L_B
			TALENT:L_A = 0
			TALENT:L_B = 0
			TALENT:ARG = 1
		ENDIF
		LOCAL ++
		GOTO SEARCH_LOOP
	ENDIF
; WAIT


@CHAR_CUSTOM_TALENT_PAGE(ARG=0,ARG:1)
#DIM L_I
CALL PRINT_SINGLE_TALENT()

IF ARG == 0


PRINTL ■=== 基本素质 ===■
FOR L_I, 0, 10
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 性格 ===■
FOR L_I, 10, 20
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 性态度 ===■
FOR L_I, 20, 30
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 少女心 ===■
FOR L_I, 30, 40
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 体质 ===■
FOR L_I, 40, 50
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 技术 ===■
FOR L_I, 50, 60
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 洁癖度 ===■
FOR L_I, 60, 65
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 正直度 ===■
FOR L_I, 69, 74
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 特殊性癖 ===■
FOR L_I, 74, 79
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


ELSEIF ARG == 1


PRINTL ■=== 性癖 ===■
FOR L_I, 79, 90
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 魅力 ===■
FOR L_I, 91, 99
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 身体特征 ===■
FOR L_I, 99, 117
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT(119)
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 混杂 ===■
CALL PRINT_SINGLE_TALENT(117)
CALL PRINT_SINGLE_TALENT(118)
CALL PRINT_SINGLE_TALENT(121)
FOR L_I, 122, 160
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 性格（口上） ===■
FOR L_I, 160, 165
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT(166)
CALL PRINT_SINGLE_TALENT(172)
CALL PRINT_SINGLE_TALENT(173)
CALL PRINT_SINGLE_TALENT(174)
IF ARG:1 == 1
    FOR L_I, 165, 180
	SIF L_I == 166 || L_I == 172 || L_I == 173 || L_I == 174
	    CONTINUE
	CALL PRINT_SINGLE_TALENT(L_I)
    NEXT
ENDIF	
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 卖春相关 ===■
FOR L_I, 180, 190
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


ELSEIF ARG == 2


PRINTL ■=== 体調不良 ===■
FOR L_I, 190, 200
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 职业 ===■

FOR L_I, 200, 230
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 强化素质 ===■
FOR L_I, 230, 240
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 战斗技能 ===■
FOR L_I, 240, 270
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 特殊素质 ===■
FOR L_I, 270, 289
	SIF L_I == 280 || L_I == 281 || L_I == 283
		CONTINUE
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 境遇 ===■
FOR L_I, 290, 300
	SIF L_I == 292
		CONTINUE
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


PRINTL ■=== 精英 ===■
FOR L_I, 471, 490
	CALL PRINT_SINGLE_TALENT(L_I)
NEXT
CALL PRINT_SINGLE_TALENT()


ENDIF

	
@PRINT_SINGLE_TALENT(ARG = -1)
IF ARG < 0
	SIF !LINEISEMPTY()
		PRINTL
	RESETCOLOR
	LOCAL = 0
	RETURN 0
ENDIF

SIF STRLENS(TALENTNAME:ARG) < 1
	RETURN 

IF TALENT:ARG
	RESETCOLOR
ELSE
	SETCOLORBYNAME GRAY
ENDIF

SIF LOCAL % 6 == 0
	PRINT   

PRINTBUTTON @"[%TALENTNAME:ARG%]", ARG
LOCAL ++

SIF LOCAL % 6 == 0
	PRINTL

RETURN LOCAL

@TALENT_EMPTY_CHECK ,ARG
;检查素质完备程度，防止自我介绍、口上为空时的错误
#DIM CHECK ,100
CHECK:1 = 0
CHECK:2 = 0
FOR CHECK ,0 ,300
    IF TALENT:ARG:CHECK == 1
	   SELECTCASE CHECK
	         ;口上
	         CASE 160 TO 174
			     CHECK:1 = 1
			 ;职业	 
			 CASE 200 TO 220
                 CHECK:2 = 1			 
	   ENDSELECT
    ENDIF
NEXT	

SIF CHECK:1 == 0
   PRINTL 需要设定性格（口上）
SIF CHECK:2 == 0
   PRINTL 需要有【近卫】及【后代】之外的职业设定
SIF TALENT:ARG:300 == 0
   PRINTL 需要设定发色
SIF TALENT:ARG:301 == 0
   PRINTL 需要设定头发状态
SIF TALENT:ARG:303 == 0
   PRINTL 需要设定头发修剪方式
SIF TALENT:ARG:304 == 0
   PRINTL 需要设定发型
SIF TALENT:ARG:305 == 0
   PRINTL 需要设定眼型
SIF TALENT:ARG:306 == 0
   PRINTL 需要设定瞳色
SIF TALENT:ARG:307 == 0
   PRINTL 需要设定唇型
SIF TALENT:ARG:309 == 0
   PRINTL 需要设定乳头
SIF TALENT:ARG:310 == 0
   PRINTL 需要设定阴毛状态
SIF TALENT:ARG:312 == 0
   PRINTL 需要设定魅力点
SIF TALENT:ARG:313 == 0
   PRINTL 需要设定癖好
SIF TALENT:ARG:317 == 0
   PRINTL 需要设定喜欢的东西
SIF TALENT:ARG:220 && TALENT:ARG:319 == 0
   PRINTL 精英需要设定精英种族
   
IF CHECK:1 && CHECK:2 &&  TALENT:ARG:300 && TALENT:ARG:301 && TALENT:ARG:303 && TALENT:ARG:304 && TALENT:ARG:305 && TALENT:ARG:306 && TALENT:ARG:307 && TALENT:ARG:309 && TALENT:ARG:310 && TALENT:ARG:312 && TALENT:ARG:313 && TALENT:ARG:317 && !(TALENT:ARG:220 && TALENT:ARG:319 == 0)
   CALL CHARA_FIRST_XP , ARG
   PRINTW 人物设定完成
   RETURN 0
ELSE
   PRINTW 请返回继续设定
   RETURN 1
ENDIF   


@CHARA_COST , ARG
;计算人物价格，基础价格50万，各素质价格按以下设置
#DIM COST 
#DIM L_I
COST = 0
FOR L_I ,0 ,500
    IF TALENT:ARG:L_I == 1
	   SELECTCASE L_I

	         CASE 10,13,14,17,37,41,99,125,131,132,134,140,141,142,143
                 COST += 150000	 

			 CASE 11,12,15,16,84,100,133
                 COST -= 50000

			 CASE 20,21,22,24,27,32,34,43,51,79,82
			     COST -= 100000

             CASE 23,25,28,33,42,50,57
			     COST += 150000				 

			 CASE 30,46,56,62,109,112,115,116,135,256
			     COST -= 100000

			 CASE 31,52,55,60,61,63,64,80,81,88,89,110,111,114,118,136,155,157,180,181,182,183,184,185,186,187,275 TO 279,471 TO 485
			     COST += 150000	 

			 CASE 0,1,70,72,91,92,93,102,104,106,108,113,117,124,126,153,154,254,271
			     COST += 150000
			 CASE 200 TO 220,221,222,
			     COST += 10000	 
			 CASE 69,71,101,103,105,107,273,280
			     COST -= 100000	 
			 CASE 9,122,123,150,151,152
                 COST -= 100000
			 CASE 73,74,75,76,77,78,130,230,231,232,233,272
			     COST += 150000
			 CASE 119,240 TO 252,257 TO 263,
			     COST += 200000
			 CASE 85,86
			     COST += 600000	 
			 ENDSELECT
    ENDIF
NEXT	
;粉毛加十万
SIF TALENT:ARG:300 == 11
    COST += 100000
;素质加算价格不为负数
SIF COST < 0
    COST = 0
COST += 500000
RETURN COST	

@CHARA_FIRST_XP , ARG
$LOOP2
LOCAL = 0
LOCALS =
LOCAL:1 = 0
LOCALS:1 =
CFLAG:ARG:16 = LOCAL
CSTR:4 = %LOCALS%
CFLAG:ARG:15 = LOCAL:1
CSTR:3 = %LOCALS:1%
SIF EX_TALENT:ARG:2
    RETURN 0


PRINTL 设定初体验
PRINTL 初吻对象是？
PRINTL [0] 不明 [1] 魔王 [993] 狂王 [994] 怪物 [995] 野狗 [999] 触手 [996] 随机 [997] 自定义输入 [998] 无 
INPUT
LOCAL = RESULT
SELECTCASE LOCAL
      CASE 1
	       PRINTL 初吻位置是？
           PRINT [1] 唇 
		   SIF TALENT:MASTER:121 || TALENT:MASTER:122
		   PRINT [201] 阴茎
		   SIF !TALENT:MASTER:122
		   PRINT [301] 私处
		   PRINT [401] 肛门
		   INPUT
		   IF GROUPMATCH(RESULT,1,201,301,401)
		       LOCALS = %SAVESTR:MASTER%
		       LOCAL = RESULT
		   ELSE
           PRINTL 输入错误，请重新开始。
		   GOTO LOOP2
		   ENDIF
	  CASE 995   
           PRINTL 初吻位置是？
		   PRINTL [1] 肛门 [2] 阴茎 [3] 嘴
		   INPUT
		   IF GROUPMATCH(RESULT,1,2,3)
		       LOCAL += RESULT
		   ELSE
           PRINTL 输入错误，请重新开始。
		   GOTO LOOP2
		   ENDIF
	  CASE 996
	       LOCAL = -1
	  CASE 997
           $LOOP3	  
	       PRINTL 输入初吻对象（留空将会随机生成）：
		   INPUTS
		   LOCALS '= RESULTS
		   SELECTCASE STRLENS(LOCALS)
		   CASE IS > 16
			   PRINTFORMW 太长，请使用全角八字以下。
			   GOTO LOOP3
		   CASE IS > 0
			   PRINTFORMW 新建人物初吻对象为%LOCALS%。
		   CASEELSE
		       PRINTFORMW 随机生成。
			   LOCAL = -1
		   ENDSELECT
		   IF !(LOCAL == -1)
			   PRINTL 初吻位置是？
			   PRINTL [1] 唇 [201] 阴茎 [301] 私处 [401] 肛门
			   INPUT
			   IF GROUPMATCH(RESULT,1,201,301,401)
				   LOCALS = %SAVESTR:MASTER%
				   LOCAL = RESULT
			   ELSE
			   PRINTL 输入错误，请重新开始。
			   GOTO LOOP3
			   ENDIF
		   ENDIF
	  CASE 0
	  CASE 993     	   
	  CASE 994
      CASE 999
	  CASE 998
	      LOCAL = -2 
      CASEELSE
           PRINTL 输入错误，请重新开始。
		   GOTO LOOP2	  
ENDSELECT		   
IF !TALENT:ARG:0
PRINTL 初体验对象是？
PRINTL [1] 魔王 [101] 蠕虫 [102] 触手生物 [103] 野狗 [104] 怪物 [105] 狂王 [996] 随机 [997] 自定义输入 [998] 无 
INPUT
LOCAL:1 = RESULT
SELECTCASE LOCAL:1
      CASE 1
	  CASE 101   
      CASE 102
	  CASE 996
           LOCAL:1 = -1	  
	  CASE 997
           $LOOP4	  
	       PRINTL 输入初体验对象（留空将会随机生成）：
		   INPUTS
		   LOCALS:1 '= RESULTS
		   SELECTCASE STRLENS(LOCALS:1)
		   CASE IS > 16
			   PRINTFORMW 太长，请使用全角八字以下。
			   GOTO LOOP4
		   CASE IS > 0
			   PRINTFORMW 新建人物初体验对象为为%LOCALS:1%。
		   CASEELSE
		       PRINTFORMW 随机生成。
			   LOCAL:1 = -1
		   ENDSELECT
	  CASE 998
	  CASE 103
	  CASE 104     	   
	  CASE 105
      CASEELSE
           PRINTL 输入错误，请重新开始。
		   GOTO LOOP2	  
ENDSELECT		   
ENDIF
A = ARG		   
SIF !EX_TALENT:A:2
	CALL CM_NS_EXP
IF !(LOCAL == -1)
CFLAG:ARG:16 = LOCAL
CSTR:4 = %LOCALS%
ENDIF
IF !(LOCAL:1 == -1)
CFLAG:ARG:15 = LOCAL:1
CSTR:3 = %LOCALS:1%
ENDIF

PRINT 　
IF CFLAG:16 > -1
	LOCAL = CFLAG:16 - 1
	IF LOCAL == -1
		PRINT [初吻对象：不明]
	ELSEIF CFLAG:16 == 992
		PRINTFORM [初吻对象：%CSTR:4%]	
	ELSEIF CFLAG:16 == 993
		PRINT [初吻对象：狂王]
	ELSEIF CFLAG:16 == 994
		PRINT [初吻对象：怪物]
	ELSEIF CFLAG:16 == 995
		PRINT [初吻对象：怪物的阴茎]
	ELSEIF CFLAG:16 == 996
		PRINT [初吻对象：野狗的肛门]
	ELSEIF CFLAG:16 == 997
		PRINT [初吻对象：野狗的阴茎]
	ELSEIF CFLAG:16 == 998
		PRINT [初吻对象：野狗的嘴]
	ELSEIF CFLAG:16 == 999
		PRINT [初吻对象：触手]
	ELSE
		PRINTFORM [初吻对象：%CSTR:4%的
		IF CFLAG:16 < 100
			PRINT 唇]
		ELSEIF CFLAG:16 < 300
			PRINT 阴茎]
		ELSEIF CFLAG:16 < 400
			PRINT 私处]
		ELSEIF CFLAG:16 < 500
			PRINT 肛门]
		ENDIF
	ENDIF
ENDIF

IF CFLAG:15 > 0
	LOCAL = CFLAG:15 - 1
	;初体験がバイブの場合
	IF CFLAG:15 == 101
		PRINT [初体验对象：蠕虫]
	;初体験が触手生物の場合
	ELSEIF CFLAG:15 == 102
		PRINT [初体验对象：触手生物]
	;初体験が犬の場合
	ELSEIF CFLAG:15 == 103
		PRINT [初体验对象：野狗]
	;初体験が怪物の場合
	ELSEIF CFLAG:15 == 104
		PRINT [初体验对象：怪物]
	ELSEIF CFLAG:15 == 105
		PRINT [初体验对象：狂王]
	ELSEIF LOCAL == 0
		PRINTFORM [初体验对象：%SAVESTR:LOCAL%]
	ELSE
		PRINTFORM [初体验对象：%CSTR:3%]
	ENDIF
ENDIF
PRINTL 这样就可以了吗？
PRINTL [0] 好的 [1] 还是改一下吧
INPUT
SIF RESULT == 1
    GOTO LOOP2
RETURN 0