﻿转载信息
==================================================
wiconorance (2015),
【瞎翻】era构文讲座（前篇）,
SS同盟（sstmlt.net）,
Online Availible: https://sstmlt.net/thread-83597-1-1.html
(Accessed At 2015/9/2)

wiconorance (2015),
【瞎翻】era构文讲座（后篇）,
SS同盟（sstmlt.net）,
Online Availible: https://sstmlt.net/thread-83597-1-1.html
(Accessed At 2015/9/2)



作者声明
==================================================
郑重提示：译者的日语水平是零，全篇都是机翻脑补，如在阅读过程中有任何不适请右上角
声明：由于翻译的不完善，校正完成前请勿转载，由翻译失误所带来的任何问题本人不负责解释
另：欢迎各路大神校对指正

前篇：
 【瞎翻】era构文讲座（前篇）
https://sstmlt.net/thread-83597-1-1.html

 后篇：
 【瞎翻】era构文讲座（后篇）
https://sstmlt.net/thread-94486-1-1.html


 另一篇教程：

 開発初心者向き用覚え書き
http://seesaawiki.jp/eratoho/d/% ... 0%a4%a8%bd%f1%a4%ad
 汉化：https://sstmlt.net/thread-83395-1-1.html

 指令表汉化版
https://sstmlt.net/thread-105357-1-1.html

 本文原文：

 改造初心者のためのera basic(ERB)構文講座 ←
http://seesaawiki.jp/eratoho/d/ERB%b9%bd%ca%b8%b9%d6%ba%c2

 後編
http://seesaawiki.jp/eratoho/d/ERB%b9%bd%ca%b8%b9%d6%ba%c22

 特別編
http://seesaawiki.jp/eratoho/d/ERB%b9%bd%ca%b8%b9%d6%ba%c23



 以下是正文（还是，排版什么的见X去吧……嗯……）：


 
ERA构文讲座
================================================== 

１）基本 
--------------------------------------------------

 在讲语法之前有些概念要先说清楚，这部分弄错的话可能会出现很奇怪的错误。

 使用半角文字来编写程序

         输入的所有非日语字符都请用半角字符。
         文段中的全角字符非常容易被忽视，请善用文本编辑器的检索功能
         检索全角空格这个功能应该是很容易找到的。

 每个文件的最后要留出1行一上的空白行

         使用eramaker时可能会碰到这样的问题，在文件的最后没有留出空白行的情况下，
         紧接着的那一行内容不会被识别。
         这是错误“ENDIF”的常见原因之一。
         （Emuera不会有这个问题。）

 分支和循环时请使用缩进

         这并不会直接导致错误，但是请在分支和循环（后面会有讲）的语句中使用缩进。
         缩进是指每行代码前的空白部分，通过加入不同长度的空白，可以很直观地展示出某一段落的起止点。
         一般来说缩进是使用的TAB键（制表符）。
         缩进对程序的处理是不会有影响的，只会在调试的时候更加直观。
         同时也会方便其他人的阅读。
         无论你认为这有没有必要，请尽量在这些语句中使用缩进。

 注释行的起始是 ;（一个半角的分号）

         可以在行文之中通过一个半角分号“;”来引导一行注释。
         注释部分在程序运行的时候是不会被处理的。
         你可以用它来暂时无效化一行或一段代码，
         或者也可以用它加入一些备忘或提示。。
         请注意，注释部分是可以使用全角字符的，但是分号请务必用半角。
         
         ;这里的代码是不会执行的！

 Emuera1.807版以后的新功能

         可以在语句的中间插入半角分号来引导注释了。
         但是，在PRINT系指令中无效，这个半角分号会被识别为字符串的一部分而显示在屏幕上。
         
 A = A + B ;从这以后是注释部分
 PRINT 一二三四 ;这里会跟着前面的一起显示


 请详细而正确地使用注释

         通过正确地对代码进行注释，可以方便编写，也会有利于抓虫。
         而且也可以帮助自己和别人阅读代码。
         同时对于调试和改造也会很有帮助，请务必在程序中附加尽可能详尽的注释。
         但是，错误的注释内容不但没有帮助还会产生误导。
         请务必再三检查注释内容。


２）变量
--------------------------------------------------

 这一部分介绍与运算相关的各种变量。

 变量是？ 

         变量是用来保存数据或计算结果的，也可以用于和其他数据进行比较
         可以理解为用来装数据的一种容器。
         让我们来用例子说明吧。
         
 A = 0
 B = 0
 C = 0

         A = 0的意思是，向变量A中存入一个数值0。
         同理，上面的A,B,C都是变量，而且现在都被写入了0这样一个数值。
         
 B = A + 1
 C = A + 2

         B = A + 1的意思是，向变量B中存入A+1的计算结果。
         在这两句被执行的时候，B和C中的内容会被替换掉。
         现在A的数值是0，B是0+1也就是1，C是0+2也就是2。A没有变化，保持0不变。
         
 A = C
 A = B

         然后是由C到A，也就是把2存入A。现在A就会等于2了。
         但是呢，紧接着又由B到A，也就是把1存入A，A就要等于1了。
         这种情况下，如果对同一个变量进行多次存入的话，后面代入会覆盖前面的结果。
         
 C = C + 2
 A = B + C

         然后，是把C+2存入到C中。
         计算之前C是2，计算后就应该是2+2也就是说C是4了。
         这样的话将B+C存入A的结果，就应该是1+4的结果。
         于是最后我们得到了A是5，B是1，C是4。

 变量的类型

         变量大体可以分成两类。
         像上面的A，B，C那样的存储数字的叫做数值变量，
         存储文字的叫做字符串变量。
         数值变量主要用于计算和条件判断，字符串变量主要用于显示文本内容。
         另外，era中的变量是有数组的概念的。使用数组可以让一个变量里存储数个不同的值。
         举个数组变量的例子吧，就像这样写出A:0，A:1，A:2，A:3 …
         格式是（变量名）:（数组序号）这样。（请务必使用半角的冒号“:”）
         
 A:0 = 0
 A:1 = 1
 A:2 = 2
 A:3 = 0

 A:3 = A:0 + A:1 + A:2

         这一段运行会把A:3变成A:0 + A:1 + A:2的结果，也就是变成0 + 1 + 2。
         运行的结果就是3被存入了A:3。
         
         有一部分的变量，可以支持二维数组。
         二维数组是指，形如 A:0:0, A:0:1, …, A:1:0, A:1:1, …这样表述的数组，可以容纳更多的数值。
         
         至于哪些变量可以使用什么样的数组形式，我写在Emuera Wiki上的变量表中，如有需要请移步。

 变量的使用方法

         基本的变量用法请参阅Emuera Wiki上的变量表。
         在这儿我们着重关注一下一些比较容易出错的地方。

 ·A - Z
         这些变量并没有被记载在变量表中。
         A和A:0的意思是相同的。
         就是说，A:0改变的同时A也会被改变。

 ·FLAG/TFLAG/CFLAG
         FLAG和TFLAG是用于游戏系统或者全局的变量
         对于特定角色的话请使用CFLAG。
         另外，因为非常容易被覆写，请在使用前检查一下FLAG列表。
         至于全FLAG列表，请参阅各版本随行的「変数資料.txt」文件。
         另外，使用eramaker时请注意，虽然说明中是说可以使用0～999的1000个数字，但实际上这里有只幺蛾子（bug），只能使用0～998的999个数字。
         请务必，绝对，一定不要使用999，这会导致数据损坏。
         调教时使用的TFLAG会在每次break的时候重置。
         （使用Emuera的时候，使用CFLAG:999并不会导致数据损坏。同时根据设定，你也可以使用超过1000的数字。）

 ·TEQUIP
         和上述FLAG系一样会有容易被覆写的问题，请在使用前检查FLAG列表。


 关于二维数组

         二位数组形式的变量，主要被用于和角色数据相关的时候。
         因此，最常见的二位数组经常是如下这种构型。
         （能力的种类）:（角色登录编号）:（能力编号）


 例１）TALENT:5:20
         这是指登录编号为5的角色的第20号的素质。
         可能你已经注意到了，这里的登录编号和角色编号是不一样的。
         eratoho里的角色编号为5的是琪露诺
         但是登录编号为5的并不一定是指的琪露诺。
         登录编号是指可以进行调教的角色所拥有的一个按照从商店购入的顺序顺次排列的序号。
         举例子来说，一开始购买了灵梦，再购买琪露诺的话
         琪露诺的登录编号就是2了，在这个情况下如果把灵梦出售的话
         整个列表的所有人的登录编号都会向前移动1，琪露诺的登录编号也会变为1。

 例２）TALENT:100
         这看起来和通常的二维数组不一样。
         但是，这其实是TALENT:TARGET:100的缩写，代指当前的调教目标或
         预定调教的角色的第100号的素质。
         TARGET是指向当前的调教目标或预定调教的角色的角色登录编号的一个变量。
         因为经常被使用，我们可以用TARGET省略掉指定目标角色这一步。

         顺带一提，例1中我所解释的那个TALENT:5:20，
         实际上由于eramaker的问题，并不能正确地表示出角色登录编号为5的那个角色的第20号素质。
         原因大概是因为，程序在读取TALENT:5的时候误认为此处被省略了一个TARGET
         就变成了TALENT:TARGET:5。
         能正确地表示出登录编号为5的角色的第20号素质的语句应该这样写
         
 A = 5
 TALENT:A:20

 　因为这个原因，事先把登录编号存入一个变量之中就成了必要步骤了。
 　（使用Emuera的时候并不会有这个问题。TALENT:5:20可以正确地被程序识别。）

 有关变量的更详细的内容请参阅eramaker的变量说明。


３）运算 
--------------------------------------------------

 运算是数值处理的必要过程。


 基本的四则运算 

 使用基本运算符就可以完成四则运算。
 A + B A与B的和。 
 A - B A与B的差。 
 A * B A与B的积。 
 A / B A与B的商。会忽略小数部分。 
 A % B A除以B的余数。 

 针对自身的运算

 对于形如A = A + B这样的运算可以按下列方法进行简写。
 A += B 等同于A = A + B。 
 A -= B 等同于A = A - B。
 A *= B 等同于A = A * B。
 A /= B 等同于A = A / B。
 A %= B 等同于A = A % B。 

 逻辑运算符

 位（bit）计算时所使用的一种特殊计算。
 A | B A和B之间进行OR（或）逻辑运算并返回结果。 
 A & B A和B之间进行AND（与）逻辑运算并返回结果。 
 　（有关位计算请参阅特别篇的内容）


４）显示
--------------------------------------------------

 这是显示系统的语法说明，PRINT系指令被广泛地用于与口上有关的场合。


 PRINT系指令的种类

         PRINT系指令主要有以下几大类。
         
 PRINT 普通的字符串显示。不涉及变量的使用等等。 
 PRINTV 用于数值变量的显示。 
 PRINTS 用于字符串变量的显示。 
 PRINTFORM 普通的字符串和数值、字符串变量混合表示。

         也有像下面这些并不常使用的。
         
 PRINTFORMS 用于字符串变量的显示。 
 PUTFORM 和PRINTFORM是一样的，但是只能用于SAVEDATA（存档）。

 在每条命令后加入L或者W，就可以完成换行或者等待（在按下Enter之前不会继续）的功能。
 举个例子：

 PRINT 一
 PRINT 二
 PRINTL 三
 PRINTW 四
 PRINTL 五

 这样写的话运行时会是这样的：

 一二三
 四

 这里表示正在等待玩家的输入操作。
 当按下Enter以后，显示结果会是这样的：

 一二三
 四
 五


 PRINT系指令使用说明 

         如上所述，PRINT系的指令虽然有很多专门的应用场合，但实际上，
         在大多数场合下你都可以使用PRINTFORM来代替其他的指令。
         
         如果用来显示一般的句子的话，
 PRINTFORM 一二三四五
         这样就可以了。这个时候请不要忘记在PRINTFORM和文本之间加入一个半角空格。
         
         如果用来显示数值变量的组合的话，
 A = 5
 PRINTFORM 变量Ａ的值是{A}
         像这样把变量名用半角的{}（大括号）括起来。
         
         字符串变量的话，
 STR:0 = 六七**十
 PRINTFORM 一二三四五%STR:0%天王盖地虎
         像这样把变量名用%%括起来。
         
         PRINTFORM也可以附加L或W的后缀，
         PRINTFORML会在显示结束后直接显示下一行而不会等待输入、
         PRINTFORMW则会在换行前暂停下一行的显示，等待用户输入。

         只能用于SAVEDATA中的PUTFORM并不能用PRINTFORM来代替，
         不过SAVEDATA出现的场合本来就不多，也别在乎了。

         
 其他显示 

         如果要在到达某个特定字符后换行，请使用PRINTFORML
         如果在这一行里什么都不想写，或者说，想显示空白行的话，
         也是用PRINTFORML。
         
 PRINTFORML 一二三
 PRINTFORML 
 PRINTFORML 四五

         第二行的PRINTFORML后只需要接一个半角空格，这样的结果是
         
 一二三

 四五

         成功地显示出了空白行。

         另外，也可以通过使用DRAWLINE，

         
４）分支与循环语句
--------------------------------------------------

 条件语句

 无论是分支还是循环，在执行之前都会有用来表示条件的逻辑表达式。

 比较运算

         比较运算式是通过比较变量与变量之间，或变量与数值之间的大小，来获取条件成立与否的句式。
         在比较运算中会使用的运算符有==，!=，<，>，<=，>=，等等。
         其中==表示等于，!=表示不等于，不等号就是字面上的意思啦。
         
                 举个例子，在A=0, B=1, C=0的情况下，
                 下列式子的真假判断为

                 A == B 假 
                 A == C 真
                 A != C 假
                 A > B 假
                 B > C 真 
                 B <= A 假 
                 A >= C 真 

         此外，你可以使用比较句式，利用0或者其他的任何数字来编写条件表达式，这种情况下，0代表假，其他的代表真。
         

 否定（取反）运算（仅限Emuera） 

         在Emuera中有一种叫做取反运算符的符号，用!（半角叹号）表示。
         遇到这种符号时，逻辑运算的结果要取反。
         引用一下上面的例子，
 　　!(A > B) 为真，而!(A <= C) 为假。


 复合条件语句

         同时使用多个条件语句的时候，可以在条件与条件之间使用&&或者||来连接它们。
         &&表示条件之间的“与”运算，||表示条件之间的“或”运算。

 　举个例子，变量A的值为0，同时变量B的值为1作为条件的时候，要写成

                 A == 0 && B == 1

 　这个样子。

 　如果条件变成了变量A的值为0或者变量B的值为0的话，就写成

                 A == 0 || B == 0

 　这样。

 　复杂点的比如说，变量A的值为0而且变量B的值为1，或者变量A的值为0而变量C的值为1的情况，写成

                 (A == 0 && B == 1) || (A == 0 && C == 1)　
                 
         这样，或者
                 
                 A == 0 && (B == 1 || C == 1)

 　这个样子。


 角色的素质判定

         灵活的在程序中使用各种条件语句的话，就可以利用角色的能力、素质等等属性来进行判定。
         
         举个例子，如果在调教中的目标角色为处女的时候，TALENT:TARGET:0 == 1 这个条件判定是成立的。
         持有素质的时候用1表示，没有素质的时候用0表示。
         如果只使用条件 TALENT:TARGET:0 的话，
         也会是在相同的情况——「调教中的角色是处女」——的情况下成立。
         
         反过来说，条件判定是TALENT:TARGET:0 == 0（或者写作 !TALENT:TARGET:0）的时候，
         就表示着当「调教中的角色并不是处女」的时候条件判定才会成立。
         调教中的角色顺从等级在3以上的时候，ABL:TARGET:0 >= 3 判定成立。
         角色的能力和刻印的等级都是类似的数值判定。
         角色的体力和经验这种无法用等级表示的能力，将直接套用数值。
         例如，表示调教中的角色V经验大于等于10的条件判定语句是
         EXP:TARGET:0 >= 10 。

         
 随机数

         语法为 RAND:（数值或者数值型变量）
         这个语句的作用是在给定的范围内生成随机数。
         
         举个例子，
         
                 A = RAND:10 
                 
         这样写的话A的值会是0到9(= 10 - 1)之间的随机数。
         当然，只能是整数。
         
         另外，
         
                 A = 5
                 B = RAND:A

 　的时候，变量B的值会是0到4(= A - 1 = 5 - 1)之间的一个随机的整数。


 分支

         如果我们要执行当○○是××时表示□□，就要用到分支语句。
         分支语句是由 IF 和 SIF 构成的。


                 IF - ELSEIF - ELSE - ENDIF 
                 
         如果○○的话就…
         对于这种情况的处理最常用的是下面这种语法。
                         
                 IF A == 0
                         PRINTFORML 变量A的值是0。
                 ELSEIF B == 0
                         PRINTFORML 变量A的值不是0，变量B的值是0。
                 ELSE
                         PRINTFORML 变量A的值不是0，变量B的值也不是0。
                 ENDIF

         上面就是 IF - ELSEIF - ELSE - ENDIF 系的基本语法格式。
         
         在上面的例子中
         1.如果变量A的值是0的话（IF A == 0），那么就会显示『变量A的值是0。』。
         2.如果变量A的值不为0，而变量B的值为0的话（ELSEIF B == 0），那么就会显示『变量A的值不是0，变量B的值是0。』。
         3.如果所有前面列出的条件都不满足的话（ELSE），那么就会显示『变量A的值不是0，变量B的值也不是0。』。
         
         就像这个样子， IF - ENDIF 之间会按照上述顺序来进行条件语句的判定，
         条件成立的话就会执行相应的执行语句。
         
         在上面的例子之中，执行语句只有一行，而实际上
         会一直执行到下一个 ELSEIF, ELSE, ENDIF 为止的所有语句。
         
                 IF 条件１
                         语句１
                         语句２
                         语句３
                 ELSEIF 条件２
                         语句４
                         语句５
                 ELSE
                         语句６
                 ENDIF

 （条件１成立的时候会执行语句１～３、条件１不成立的情况下而条件２成立的情况下执行
         语句４和５，都不成立的话执行语句6）

         如果需要判定的条件有3个或者更多的话，只需要增加 ELSEIF 就可以了。

                 IF A == 0
                         PRINTFORML 变量A的值是0。
                 ELSEIF B == 0
                         PRINTFORML 变量A的值不是0，变量B的值是0。
                 ELSEIF C == 0
                         PRINTFORML 变量A、B的值都不是0，变量C的值是0。
                 ELSE
                         PRINTFORML 变量A、B、C的值都不是0。
                 ENDIF
                 
         如果判定条件只有一个的话，可以不写ELSEIF部分。

                 IF A == 0
                         PRINTFORML 变量A的值是0。
                 ELSE
                         PRINTFORML 变量A的值不是0。
                 ENDIF

         不满足任何条件时什么都不执行的情况下，可以省略ELSE部分。

                 IF A == 0
                         PRINTFORML 变量A的值是0。
                 ELSEIF B == 0
                         PRINTFORML 变量A的值不是0，变量B的值是0。
                 ENDIF

         IF 和 ENDIF 是必需的，任何情况下都不可以省略。
         忘记写 ENDIF 是一个常见的引起错误的原因，请务必注意。


 SIF 

         是上面的 IF - ENDIF 系语句的简化形式。
         
                 SIF A == 0
                         PRINTFORML 变量A的值是0。
         
         SIF 后面不需要接 ENDIF、ELSEIF 或是 ELSE，
         它的特征是在条件满足的情况下执行之后的一行。
         
         例如
         
                 SIF 条件
                         语句１
                         语句２

         这样写的话，无论条件判定是真还是假，语句2都会执行，与条件无关。
         请务必注意。
         需要执行2行或以上的语句的时候请使用 IF - ELSEIF - ELSE - ENDIF 。

         另外，在 eramaker 之中，像
         
                 SIF 条件
                         ;注释
                         语句１
                 
 　这样的时候，因为SIF之后的一行是注释，所以会与条件的真假无关，必然执行语句1， 请务必注意。
 　（Emuera之中会忽略掉注释行，只有在条件为真的情况下才会执行语句1。）


 IF 与 SIF 的组合

                 IF 条件1
                                 SIF 条件２
                                         语句１
                                 语句２
                                 语句３
                 ENDIF

         这样写的话，当条件1与条件2都满足的时候，语句1、2、3都会运行；
         而当条件2不满足的情况下只会运行语句2和3。
         
                 SIF 条件１
                 　　IF 条件２
                                         语句１
                                         语句２
                 　　ENDIF

         如果倒过来写成这样的话，当条件1不满足的情况下会发生错误。
         （如果条件1不满足的话，「IF 条件２」这一行会被跳过，于是会得到一个使用ENDIF不当的错误。）
         
         有鉴于此，我们建议不要在SIF条件语句之后的一行里写任何IF或者SIF条件语句。


 SELECTCASE - CASE - CASEELSE - ENDSELECT（仅限Emuera） 

         在Emuera中，与IF语法类似的还有一种语法是 SELECTCASE - CASE - CASEELSE - ENDSELECT 。
         这种语法的作用是根据某一个数值变量值的不同作为分支的依据。
         比方说，在随机数处理的过程中，这种语法就会变得非常有用了。
         
                 SELECTCASE A
                         CASE 0
                                 PRINTFORML 变量A的值是0。
                         CASE 1
                                 PRINTFORML 变量A的值是1。
                         CASEELSE
                                 PRINTFORML 变量A的值既不是0也不是1。
                 ENDSELECT

         这样写的话，和下面的IF语句所表达的意思是完全相同的。
         
                 IF A == 0
                         PRINTFORML 变量A的值是0。
                 ELSEIF A == 1    
                         PRINTFORML 变量A的值是1。
                 ELSE                   
                         PRINTFORML 变量A的值既不是0也不是1。
                 ENDIF

         在 SELECTCASE 的语法中， CASE之后的数字用于与 SELECTCASE 后面的数值变量（在例子中是A）进行比较，从而判定分支条件。
         CASE 后面也可以像下面这样写。

                 SELECTCASE A
                         CASE 1, 2, 3
                                 PRINTFORML 变量A的值是１、２、３之中的一个。
                         CASE 4 TO 9
                                 PRINTFORML 变量A的值在4到9之间（包括4和9）。
                         CASE IS >= 50
                                 PRINTFORML 变量A的值大于等于50。
                         CASE 10 TO 20, IS >= 40
                                 PRINTFORML 变量A的值不大于50。
                                 PRINTFORML 变量A的值在10到20之间，或者大于等于40（但是小于等于49）。
                         CASEELSE
                                 PRINTFORML 变量A的值小于等于9（不满足上面的所有条件）。
                 ENDSELECT

         CASE X TO Y 的意思是，数值变量的值在大于等于X，小于等于Y的时候为真。
         CASE IS >= X 的意思是，数值变量的值在大于等于 X 的时候为真。
         此外，如果要在CASE之后写多个条件的话，可以通过半角逗号来隔开。
         上面写的 SELECTCASE 的例子，和下面这段用IF写出来的作用是完全一样的。
         
                 IF A == 1 || A == 2 || A == 3
                         PRINTFORML 变量A的值是１、２、３之中的一个。
                 ELSEIF A >= 4 && A <= 9
                         PRINTFORML 变量A的值在4到9之间（包括4和9）。
                 ELSEIF A >= 50
                         PRINTFORML 变量A的值大于等于50。
                 ELSEIF (A >= 10 && A <= 20) || A >= 40
                         PRINTFORML 变量A的值不大于50。
                         PRINTFORML 变量A的值在10到20之间，或者大于等于40（但是小于等于49）。
                 ELSE
                         PRINTFORML 变量A的值小于等于9（不满足上面的所有条件）。
                 ENDIF


 三元运算符（仅限Emuera） 

         三元运算符，准确的来说它是一种特殊的分支语法，由IF语句派生而来。
         
         三元运算符的语法遵从下列的形式。        
                 <发生变动的变量> = <条件> ? <条件为真时代入变量的值> # <条件为假时代入变量的值>

 　举个栗子，如果变量B的值在变量A的值大于等于3的时候为1，否则为0的话，用IF语句来写是这个样子的。

                 IF A >= 3
                         B = 1
                 ELSE
                         B = 0
                 ENDIF

 　而用三元运算符来写的话，只需要一行就足够了。
                 B = A >= 3 ? 1 # 0

         三元运算符也可以用到字符串变量上。在处理字符串变量的时候，使用三元运算符处理字符串的时候要用\@ 和        \@把三元运算符围起来。 
         
                 PRINTFORML %CALLNAME:TARGET% 是 \@ TALENT:0 ? 处女。 # 非处。 \@

         在上面的例子中，TALENT:TARGET:0 不是0的时候，显示『（TARGET的称呼名）是处女。』，
                 TALENT:TARGET:0 是0时，显示『（TARGET的称呼名）是非处。』。


 循环

         循环是一种能够反复多次重复同样动作的语句。

 REPEAT - REND 

                 REPEAT 次数
                         语句
                 REND

         REPEAT - REND 之间是要反复执行的语句。
         重复的次数等同于REPEAT之后的数值，可以是数字也可以是数值变量，也可以使用像 A + 1 这样的表达式。
         
         举个栗子，
         
                 REPEAT 10
                         PRINTFORML 啊
                 REND

 　这样写的话，会连续十行写一个『啊』字。

 ·COUNT
         变量 COUNT 是，表示已经进行了几次重复的计数变量。
         REPEAT - REND 之内的语句在一开始运行的时候是第1次运行途中（第1次还没有运行完），还没有完成1次循环，因此

                 REPEAT 10
                         PRINTFORML 已经重复了{COUNT}次。
                 REND
                 
         这样写的话，显示出来的是从0次一直到9次。こう記述した場合、表示されるのは0回目から9回目となります。
         请记住 COUNT 的取值范围是从0一直到REPEAT后面的值-1。
         另外，请注意，给COUNT赋值不当是会在循环之中引发错误的。

 ·REPEAT 与 IF, SIF 的组合
         REPEAT - REND 之间是可以使用 IF 和 SIF 语句的。
         
                 REPEAT 10
                         IF COUNT == 5
                                 PRINTFORML 第6次了吗？
                         ELSE
                                 PRINTFORML 第{COUNT + 1}次了
                         ENDIF
                 REND

         这样写的话，在COUNT的值是5，也就是第6次循环的最后会多显示一个“吗？”。
         另外，在 REPEAT - REND 之间也是可以插入其他 REPEAT - REND 的（循环语句的嵌套）。
         但是在这种情况下 COUNT 的值会被第2个 REPEAT - REND 所更改，
         因此不能正常运作。
         要在 REPEAT - REND 之中嵌套的话，
         要在内部的 REPEAT - REND 前后做出对 COUNT 的值的保存与恢复处理。
         （使用Emuera的时候）也可以考虑使用下面将要提到的 FOR - NEXT 语句代替。
         
                 ;REPEAT 的嵌套实例
                 REPEAT 10
                         COUNT:1 = COUNT
                         REPEAT 10
                                 语句
                         REND
                         COUNT = COUNT:1
                 REND


 ·CONTINUE 与 BREAK

         在REPEAT和REND之间，如果要不执行之后的语句直接进入下次循环的话用CONTINUE
         如果不执行后面的语句、并且让循环直接结束的话使用BREAK
         
                 REPEAT 10
                         A = COUNT
                         IF A == 5
                                 CONTINUE
                         ENDIF
                         PRINTFORM {A}:
                 REND

         这个例子之中，在 COUNT 的值为5的时候会执行一次 CONTINUE，实际上显示的会是
         
                 0:1:2:3:4:6:7:8:9:

 　这个样子。另外，

                 REPEAT 10
                         A = COUNT
                         IF A == 5
                                 BREAK
                         ENDIF
                         PRINTFORM {A}:
                 REND

         这一段运行的话，当 COUNT 的值为5的时候会执行 BREAK 跳出剩余的 REPEAT - REND 循环，实际上显示的会是
         
                 0:1:2:3:4:

         这个样子。


 FOR - NEXT（仅限Emuera） 

                 FOR <计数用数值变量>, <数式>, <数式>[, <数式>]
                         循环本体
                 NEXT
                 
         REPEAT - REND 的升级版就是 FOR - NEXT 。
         举例来说，下面这两种写法的功能是完全一样的。
         
                 FOR COUNT, 0, 10
                 　　PRINTFORML {COUNT}次了
                 NEXT
                 
                 REPEAT 10
                 　　PRINTFORML {COUNT}次了
                 REND

         FOR 之后的<计数用数值变量>与 REPEAT 之中的 COUNT 的作用是一样的。
         REPEAT 重复的时候使用的计数变量是固定为 COUNT 的，而在 FOR 之中可以按照自己的喜好来指定。
         因为可以使用别的变量作为计数变量，要实现在 REPEAT 里很麻烦的循环嵌套就会变的很简单了。
         
                 ;嵌套实例
                 FOR A, 0, 10
                         FOR B, 0, 10
                                 语句
                         NEXT
                 NEXT

         FOR 之后的第2个<数式>是循环的计数变量的开始值，
         第3个<数式>是循环的计数变量的终止值。
         
         举例来说，
         
                 FOR COUNT, 3, 8
                         PRINTFORM {COUNT}:
                 NEXT

         运行以后的结果是
         
                 3:4:5:6:7:

         这个样子。
         
         FOR 之后的第4个<数式>是指每次循环时，循环计数变量增加的值。如果省略的话默认为1，循环计数变量会在每次循环的时候加1。
         
         举例来说，
         
                 FOR COUNT, 0, 10, 2
                         PRINTFORM {COUNT}:
                 NEXT

         运行的话，会显示
         
                 0:2:4:6:8:

         这样。

         而且，与 REPEAT - REND 一样，FOR - NEXT也可以使用 CONTINUE 与 BREAK。


 WHILE - WEND（仅限Emuera） 

                 WHILE 条件
                         语句
                 WEND

         条件判定为真的情况下，循环会一直持续进行。
         举例来说，下面这一段的输出结果会是十行「啊」。
         
                 A = 0
                 WHILE A < 10
                         PRINTFORML 啊
                         A += 1
                 WEND
         
         这种情况下，如果忘了写「A += 1」的话，变量 A 的值就会一直是0，
         A < 10 这个条件就会一直判定为真，陷入无限循环，请务必注意。
         WHILE - WEND 之中也是可以使用 CONTINUE 与 BREAK 的。


 DO - LOOP（仅限Emuera） 

                 DO
                         语句
                 LOOP 条件

         条件为真的情况下将一直持续进行循环。
         与 WHILE - WEND 不同的是，由于判定条件语句的位置不同，DO - LOOP 之间的内容是会至少运行一次的。 
         让我们用下面这个例子来说明。
         
                 A = 0
                 WHILE A < 0
                         PRINTFORML 啊
                 WEND
                 
                 A = 0
                 DO
                         PRINTFORML 啊
                 LOOP A < 0
                 
         WHILE 语句是在一开始进行条件的判断，在这个例子里，由于条件判定结果为假，一次 PRINTFORML 都不会执行。
         另一方面，由于 DO - LOOP 是在循环的最后才进行条件的判断，在这个例子中会执行 PRINTFORML 一次，随后因为条件判定为假，终止循环。

         
         此外，如果在 DO - LOOP 循环之内使用 CONTINUE 的话，会跳转到 LOOP 那里而不是 DO 那里，请务必注意。
         下面的这段代码运行时会按照 DO → CONTINUE → LOOP 0 的顺序（而不是按照 DO → CONTINUE → DO 的顺序） 运行，因此不会进入无限循环。

                 DO
                         CONTINUE
                 LOOP 0



５）函数与函数调用 
--------------------------------------------------

         下面为大家介绍的是主要用在机能补丁或者生成变体时要用到的函数与函数调用。

         函数

         有一些固定化的处理语句，可以预先在写其他的地方，在需要的时候就可以直接调用了。
         这叫做函数与函数的调用。
         举例来说，如果我们要把A的值变成5倍大小的话，写成这样
         
                 @A_TEN_TIMES
                 A = A * 10

         @之后的半角英文数字与下划线所组成的是这个函数的名字，
         在名字之后的是函数的内容。        
         
                 A = 0
                 CALL A_TEN_TIMES
                 PRINTFORML 变量Ａ的值是{A}。
                 
                 A = 5
                 CALL A_TEN_TIMES
                 PRINTFORML 变量Ａ的值是{A}。
                 
         运行的结果，上面的会显示变量Ａ的值是0，下面的会显示变量Ａ的值是50。

         要调用已经写好了的函数，需要使用 CALL 语句，语法是 CALL <函数名> 。
         
         此外，也可以使用 JUMP <函数名> 来进行函数的调用。
         CALL 与 JUMP 的不同点是，在函数运行完之后会不会返回原来的位置。
         CALL 调用的函数在运行结束之后会返回原来的位置继续运行，
         而 JUMP 调用的函数则不会返回原来的位置。
         
         此外，当出现同名函数的时候，只有一个函数会被正确地调用（事件函数之类的特殊函数除外）。
         请务必注意不要让函数名发生重复。

 RETURN 与返回值 

         如果要使函数在满足某种条件时中途停止，使用
         RETURN <数值> 这种语句。
         
                 @TEST
                 SIF A == 0
                         RETURN 0
                 A = A * 5

         这个例子中，当函数 TEST 被调用时，如果变量A的值是0，那什么都不会发生，除此之外的情况下A的值会变为5倍。
         另外，使用 RETURN 终止函数的运行，程序会返回到原来的位置，同时变量 RESULT 的值会变为 RETURN 之后指定的值。
         这个时候，RESULT 里保存的值也被称作函数的返回值。
         
         举个栗子，
         
                 @TEST2
                 IF A == 0
                         RETURN 0
                 ELSEIF A == 1
                         RETURN 1
                 ELSEIF A == 2
                         RETURN 2
                 ELSE
                         RETURN 9
                 ENDIF

         写好了上面这样的函数以后，我们按下面的方法调用
         
                 A = 0
                 CALL TEST2
                 PRINTFORML {RESULT}
                 
                 A = 2
                 CALL TEST2
                 PRINTFORML {RESULT}
                 
                 A = 3
                 CALL TEST2
                 PRINTFORML {RESULT}

         从往上下的显示结果分别会是0, 2, 9。
         
         另外，在Emuera中，除了可以使用数值或者数值变量作为返回值以外，也可以同时指定多个返回值，以逗号分隔。


 参数（仅限Emuera） 

         Emuera之中，在函数里是有参数这个概念的。参数是在使用 CALL 调用函数时，传递到函数的变量。
         再用一次上面的例子，这次我们为函数加上参数。

                 @TEST2, ARG
                 IF ARG == 0
                         RETURN 0
                 ELSEIF ARG == 1
                         RETURN 1
                 ELSEIF ARG == 2
                         RETURN 2
                 ELSE
                         RETURN 9
                 ENDIF

         调用的时候这样写。
         
                 CALL TEST2, 0
                 PRINTFORML {RESULT}
                 
                 CALL TEST2, 2
                 PRINTFORML {RESULT}
                 
                 CALL TEST2, 3
                 PRINTFORML {RESULT}

         CALL TEST2, 0 运行的时候，ARG 的值就是 0， 这样就可以在函数内使用 ARG 了。
         参数不只一个的时候，会按照 ARG, ARG:1, ARG:2, …的顺序依次赋值。
         
                 @TEST3, ARG, ARG:1, ARG:2
                 （略）
                 
                 CALL TEST3, 0, 7, 3

         如果想要把参数设置成字符串变量的话，使用 ARGS 作为参数变量。


 本地变量/局部变量（仅限Emuera） 

         像 A, B 之类的单字名变量或者是像 COUNT 那样的许多变量，都是在整个程序内可以调用的全局变量。
         但是呢，这样就会引发一些问题。
         
                 @MAIN
                 FOR COUNT, 0, 10
                         CALL FUNC
                 NEXT

                 @FUNC
                 FOR COUNT, 0, 3
                         （这里随便写点什么）
                 NEXT
                 
         上面的例子之中，函数 MAIN 在运行时，会调用函数 FUNC， 而每次FUNC被调用的时候都会把 COUNT 的值改为3，所以程序会陷入死循环。
         在这个例子之中，如果把函数 FUNC 内 FOR 语句的计数变量改为 COUNT:1 就可以解决这种问题。
         但是变量扩展成的数组有1000个的数量限制，很容易就会用超，这种问题还是有可能发生。
         
         那么，如果有一种只在函数内部使用的变量（局部变量）的话，这个问题就可以解决了。
         这样的局部变量叫做 LOCAL 和 LOCALS。请看下面的例子。
         
                 @EVENTFIRST
                         LOCAL = 123
                         CALL FUNC001
                         PRINTFORML {LOCAL}
                 
                 @FUNC001
                         LOCAL = 567
                         RETURN

         乍一看好像是往同一个叫做 LOCAL 的变量之中代入了两次不同的值， 
         然而，「函数 EVENTFIRST 之中的 LOCAL」与「函数 FUNC001 之中的 LOCAL」是不一样的东西。
         PRINTFORML 的结果是显示 123。
                 
         另外，LOCALS 是 LOCAL 的字符串变量版本。
         先前说明过的 ARG 和 ARGS 也应当被视作一个局部变量。


 LOCAL, ARG 的初始时序 

         使用过其他编程语言的人请务必注意
                 ·LOCAL 和 ARG 在函数被调用的时候并不会初始化
                 ·相同函数每次调用的时候都会使用同样的 LOCAL, ARG
         以上这两点。
         这些特征，在使用函数的递归调用请务必、一定要加以注意。
         
                 @SAMPLE
                 LOCAL += 1
                 IF LOCAL < 10
                 　　CALL SAMPLE
                 ENDIF

         上面这段代码，如果是有过编程经验的人看到的话，大概会这么想
         「LOCAL 是局部变量，在调用的时候会初始化为0，这样在判定的时候 LOCAL 的值就会一直为1，进入无限的递归循环，最后发生溢出错误。 」
         的吧。
         
         但是实际上调用 SAMPLE 函数的时候，第一次的外部调用函数 SAMPLE 时，会递归调用9次，然而第2次以后对函数 SAMPLE 的外部调用，并不会引发递归调用。        
         要问为什么的话，最开始调用的时候 LOCAL 被代入了1，然后递归调用的时候， LOCAL 并没有被初始化归零，而是保持着值为1。
         另外，当递归完成，函数 SAMPLE 返回以后，因为 LOCAL 的最终值被保持在了10，所以即使再次调用 SAMPLE 函数的话，也不会发生递归调用。

         ARG 也是一样的。
         
                 @SAMPLE2, ARG
                 SIF ARG >= 10
                 　　RETURN
                 CALL SAMPLE2, ARG + 1
                 PRINTVL ARG

 　像上面这样写的代码，CALL SAMPLE2, 0 调用的话会显示10次10。


 在表达式中使用的函数（仅限Emuera） 

         在erabasic中，有返回值的指令会把返回值存到 RESULT 中。
         比如说，ABS 这个指令会返回一个表达式的绝对值。
         如果要把变量A的绝对值放到 LOCAL 之中的话就要像下面这样写。
         
                 ABS A
                 LOCAL = RESULT

         另一方面，ABS 指令也可以用一种被称作「在表达式中使用的函数（下面简称式中函数）」的写法，把上面的代码写成下面这样。
         
                 LOCAL = ABS(A)

         「式中函数」正如其名，是直接在表达式之中调用函数，例中的情况下 RESULT 之中的返回值会传递到 LOCAL 之中。
         式中函数可以通过圆括号 (A) 来指定函数的参数。
         多个参数的情况下，在圆括号中使用逗号分隔它们(A,B,C)。
         没有参数的时候只使用空的圆括号 () 。这个情况下即使没有参数，圆括号也不可以省略。

         返回字符串的指令，也是使用同样的方法来写。
         
                 STRLENS STR:0
                 IF RESULT > A
                         SUBSTRING STR:0, A, 1
                         LOCALS:0 = %RESULTS:0%
                 ENDIF
                 
                 IF STRLENS(STR:0) > A
                         LOCALS:0 = %SUBSTRING(STR:0, A, 1)%
                 ENDIF

         上面两段代码所表示的是完全一样的意思。
         有关式中函数的一览表请参阅Emuera Wiki。


 式中函数的定义 

         式中函数的定义与一般函数有所区别。
         在函数定义的时候， @ 行之后接上一行 #FUNCTION，然后将 RETURN 都换成 RETURNF，这样的函数就是式中函数了。
         
                 ;下面这行也可以写作 @TEST2, ARG，不过一般都这么写
                 @TEST2(ARG)
                 #FUNCTION
                 IF ARG == 0
                 　　RETURNF 0
                 ELSEIF ARG == 1
                 　　RETURNF 1
                 ELSEIF ARG == 2
                 　　RETURNF 2
                 ELSE
                 　　RETURNF 9
                 ENDIF

         调用的时候按下面这样写。
         
                 PRINTFORML {TEST2(0)}
                 PRINTFORML {TEST2(2)}
                 PRINTFORML {TEST2(3)}

         要创建文字列型的式中函数的话，请使用 #FUNCTIONS。
         但是请注意，RETURNF 的写法与其他指令（PRINTFORM 之类）是不一样的。


６）标签与按键输入
--------------------------------------------------

 在选择项分支的时候我们经常用到的是标签和按键输入。

                 PRINTFORML 选择吧！
                 PRINTFORML [0] 选项１
                 PRINTFORML [1] 选项２
                 ;在Emuera之中程序会自动为我们查找并转换上面用方括号[ ]围起来的数字为按钮。
                 
                 $INPUT_LOOP
                 INPUT
                 
                 IF RESULT != 0 && RESULT != 1
                 　　GOTO INPUT_LOOP
                 ELSEIF RESULT == 0
                 　　语句１
                 ELSEIF RESULT == 1
                 　　语句２
                 ENDIF

         INPUT 会等待键盘输入（或按键操作），并将输入内容储存在 RESULT 之中。
         这个例子中，输入为0的时候执行语句1，输入为1的时候执行语句2。
         输入既不是0也不是1的时候，条件 RESULT != 0 && RESULT != 1 判定为真，
         会执行 GOTO INPUT_LOOP。
         GOTO （标签名）是一个跳转至 “$（标签名）”一行的指令。
         GOTO 之于 $ 与 CALL 和 JUMP 之于 @ 的关系相似，GOTO 与 $ 必须写在同一个函数之内。
         GOTO INPUT_LOOP 会移动到 $INPUT_LOOP 一行，再次执行 INPUT，
         这样做的话就可以反复询问直到用户做出了有效的输入（0或者1）。

         另外，当同个函数之内使用了多个 $ 与 GOTO 的组合时，
         可能会发生『IF 找不到对应的 ELSEIF, ELSE, ENDIF』之类的错误。
         这种情况下，我们一般把第二个输入处理转移到其他的函数内。


 ７）其他一些需要注意的东西

         角色的追加与删除 

         要追加新角色或者删除旧角色，需要用到的是 ADDCHARA 与 DELCHARA。
         
                 ADDCHARA 1
                 ADDCHARA 5
                 ADDCHARA 9

         Emuera 之中也可以像下面这样写。
         
                 ADDCHARA 1, 5, 9

         ADDCHARA会引用CSV中的角色编号。
         
                 DELCHARA 3
                 DELCHARA 1

         而 DELCHARA 则不会参照角色编号，而是参照角色的登录编号。
         上面的代码追加了角色编号为1，5，9的角色，
         然后会删除登录编号为3，也就是角色编号为9的角色。
         然后会删除登录编号为1，也就是角色编号为1的角色。
         最后留下的角色是角色编号为5的角色。
         如果要继续使用DELCHARA的话，要注意在删除了角色以后，对应的登录编号之后的所有角色，
         她们的登录编号都要向前移。

         此外，请在角色追加、删除之前确认目标角色是否是正在调教中的角色，或是当作助手的角色。
         比如说，如果我们使用 DELCHARA 删除一个作为助手的角色，
         保存在 ASSI 里的助手的登录编号是不会变的。但是，由于删除了一个角色，
         全局上来说后面的角色登录编号会向前移1，
         这样就可能导致不满足助手条件的角色成为助手。


 图形显示

         可以使用指令 BAR（BARL）来创建一个比较直观的图形来显示数值。
         语法是
         
         BAR （数值或变量）,（数值或变量）,（图形长度）
         
                 A = 80
                 B = 100
                 BAR A, B, 10

 　显示结果是

                 [********..]

         这样的。
         A是现在的值，B是最大值。
         现在的值除以最大值，转换成图形显示出来。
         BARL的区别就是在显示之后会自动换行。


 小数的乘法

         era basic之中所有的变量计算全部都是整数，而有种例外就是
         当使用 TIMES 进行小数计算的时候。
         
                 A = 1000
                 TIMES A, 1.5

         这是将变量Ａ赋值1000×1.5=1500。
         另外，即使使用 TIMES 进行的小数运算，其结果也还是整数。


 其他指令

         WAIT…等待输入。值会保存在 RESULT 内。
         QUIT…会终止 eramaker 的运行。主要是在游戏结束的时候使用。