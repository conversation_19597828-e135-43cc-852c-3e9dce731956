﻿;eraWIZから流用

@COM73
;拨弄发型

PRINTL 摆弄发型
STR:0 = 拨弄发型

LOSEBASE:1 += 60

;顺从、露出癖、調教者の技巧が影響
;入るソースは露出、屈従。顺从が高いと情爱、技巧が低いと痛覚、ばっさり切ると逸脱
;バランス的に購入直後の奴隷の髪をばっさり切ると反抗刻印が付く程度に。効果は控えめ。

;ABL:顺从をみる
IF ABL:10 == 0
	SOURCE:13 = 100
ELSEIF ABL:10 == 1
	SOURCE:3 = 10
	SOURCE:13 = 250
ELSEIF ABL:10 == 2
	SOURCE:3 = 100
	SOURCE:13 = 500
ELSEIF ABL:10 == 3
	SOURCE:3 = 250
	SOURCE:13 = 1000
ELSEIF ABL:10 == 4
	SOURCE:3 = 500
	SOURCE:13 = 1500
ELSE
	SOURCE:3 = 1000
	SOURCE:13 = 2000
ENDIF

;ABL:露出癖をみる
IF ABL:17 == 0
	SOURCE:12 = 500
ELSEIF ABL:17 == 1
	SOURCE:12 = 250
ELSEIF ABL:17 == 2
	SOURCE:12 = 100
ELSEIF ABL:17 == 3
	SOURCE:12 = 50
ELSEIF ABL:17 == 4
	SOURCE:12 = 25
ELSE
	SOURCE:12 = 10
ENDIF

;プレイヤーの技巧を見る
IF ABL:PLAYER:12 <= 3
	SOURCE:6 = 100
ELSEIF ABL:PLAYER:12 == 4
	SOURCE:6 = 10
ELSE
	SOURCE:6 = 0
ENDIF

CALL HAIRSET

RETURN 1

@HAIRSET
;X,Yは、カット、セットの変更を行ったかどうかを記録する（セリフ表示のため）
X = 0
Y = 0

;既にショートだと髪は切らない
SIF TALENT:302 <= 100
	GOTO INPUT_LOOP_HAIRSET

$INPUT_LOOP_HAIRCUT
PRINTFORML 剪%SAVESTR:TARGET%的头发吗？
IF TALENT:302 <= 200
	PRINTL 现在[半长]
ELSE
	PRINTL 现在[长]
	PRINTL [0]---适当剪一下
ENDIF
PRINTL [1]---大刀阔斧地剪
PRINTL [2]---不剪
INPUT
IF RESULT == 0 && TALENT:302 >= 201
	L = 2
	SOURCE:14 = 500
ELSEIF RESULT == 1 && TALENT:302 >= 101
	L = 1
	SOURCE:14 = 500
	SIF TALENT:302 >= 201
		SOURCE:14 += 500
ELSEIF RESULT == 2
	L = 0
ELSE
	GOTO INPUT_LOOP_HAIRCUT
ENDIF

IF L
	PRINTL 剪成什么样子呢？
	PRINTL [0]---还是不剪了
	PRINTL [1]---自然的样子
	PRINTL [2]---剪齐整
	PRINTL [3]---强调层次
	PRINTL [4]---强调蓬松感
	INPUT
	C = RESULT
	IF C >= 1 && C <= 4
		X = 1
		PRINTFORML %SAVESTR:PLAYER%把%SAVESTR:TARGET%的头发剪了。
		IF ABL:PLAYER:12 >= 5
			PRINTL 这个手艺，这个效果，去到哪里都可以很自信了吧…
		ELSEIF ABL:PLAYER:12 >= 4
			PRINTL 天生的灵巧发挥了作用，总算完成了…
		ELSE
			PRINTL 没什么经验做这个，完成的效果好像和想象中不同…
		ENDIF
		IF L == 2
			TALENT:302 = 101
		ELSE
			TALENT:302 = 1
		ENDIF
		TALENT:303 = C
		;髪を切ったときは髪型をナチュラルに
		TALENT:304 = 1
	ELSE
		PRINTL 放弃剪发了。
	ENDIF
ENDIF

SIF X
	CALL HAIRSET_TALK_1

PRINTL  
$INPUT_LOOP_HAIRSET
PRINTFORML 把%SAVESTR:TARGET%的头发弄成什么样子？
PRINTL [1]---自然
PRINTL [2]---中分
PRINTL [3]---不均分
IF TALENT:302 >= 101
	PRINTL [4]---长束发
	PRINTL [5]---马尾
	PRINTL [6]---侧马尾
	PRINTL [7]---垂发辫
	PRINTL [8]---双马尾
	PRINTL [9]---顶束发
	PRINTL [10]--侧束发
	IF TALENT:302 >= 201
		PRINTL [11]--鱼骨辫
		PRINTL [12]--卷发
	ENDIF
ENDIF
PRINT 现在的发型：
IF TALENT:304 == 1
	PRINTL 自然
ELSEIF TALENT:304 == 2
	PRINTL 中分
ELSEIF TALENT:304 == 3
	PRINTL 不均分
ELSEIF TALENT:304 == 4
	PRINTL 长束发
ELSEIF TALENT:304 == 5
	PRINTL 马尾
ELSEIF TALENT:304 == 6
	PRINTL 侧马尾
ELSEIF TALENT:304 == 7
	PRINTL 垂发辫
ELSEIF TALENT:304 == 8
	PRINTL 双马尾
ELSEIF TALENT:304 == 9
	PRINTL 顶束发
ELSEIF TALENT:304 == 10
	PRINTL 侧束发
ELSEIF TALENT:304 == 11
	PRINTL 鱼骨辫
ELSEIF TALENT:304 == 12
	PRINTL 卷发
ENDIF
INPUT
IF RESULT < 1 || RESULT > 14
	GOTO INPUT_LOOP_HAIRSET
ELSEIF TALENT:302 <= 200 && RESULT > 11
	GOTO INPUT_LOOP_HAIRSET
ELSEIF TALENT:302 <= 100 && RESULT > 4
	GOTO INPUT_LOOP_HAIRSET
ELSEIF RESULT == TALENT:304
	PRINTFORML %SAVESTR:TARGET%的发型保持原样。
ELSE
	TALENT:304 = RESULT
	PRINTFORM %SAVESTR:PLAYER%把%SAVESTR:TARGET%的发型弄成
	IF TALENT:304 == 1
		PRINT 自然
	ELSEIF TALENT:304 == 2
		PRINT 中分
	ELSEIF TALENT:304 == 3
		PRINT 不均分
	ELSEIF TALENT:304 == 4
		PRINT 长束发
	ELSEIF TALENT:304 == 5
		PRINT 马尾
	ELSEIF TALENT:304 == 6
		PRINT 侧马尾
	ELSEIF TALENT:304 == 7
		PRINT 垂发辫
	ELSEIF TALENT:304 == 8
		PRINT 双马尾
	ELSEIF TALENT:304 == 9
		PRINT 顶束发
	ELSEIF TALENT:304 == 10
		PRINT 侧束发
	ELSEIF TALENT:304 == 11
		PRINT 鱼骨辫
	ELSEIF TALENT:304 == 12
		PRINT 卷发
	ENDIF
	PRINTL 了。
	IF ABL:PLAYER:12 >= 5
		PRINTL 这个手艺，这个效果，去到哪里都可以很自信了吧…
	ELSEIF ABL:PLAYER:12 >= 4
		PRINTL 天生的灵巧发挥了作用，总算完成了…
	ELSE
		PRINTL 没什么经验做这个，完成的效果好像和想象中不同…
	ENDIF
	Y = 1
ENDIF

SIF Y
	CALL HAIRSET_TALK_2

RETURN 1

;髪を切ったときのセリフ
@HAIRSET_TALK_1

RETURN 1

;髪型を変えたときのセリフ
@HAIRSET_TALK_2


RETURN 1
