﻿;eraIM@Sから導入しました

@SHOW_JUEL
CUSTOMDRAWLINE ‥
FOR COUNT, 0, 12
	IF COUNT == 3
		LOCAL = 14
	ELSEIF COUNT == 11
		LOCAL = 100
	ELSEIF COUNT == 12
		LOCAL = 15
	ELSE
		LOCAL = COUNT
	ENDIF
	IF COUNT == 0 && TALENT:TARGET:122
		PRINTFORM  阴茎点数：{JUEL:LOCAL, 6, RIGHT}
	ELSE
		PRINTFORM  %PALAMNAME:LOCAL%点数：{JUEL:LOCAL, 6, RIGHT}
	ENDIF

	;改行
	IF (COUNT+1)%4 == 0
		PRINTL 
	ENDIF
NEXT
PRINTL
CUSTOMDRAWLINE ‥

@SHOW_ABLUP_SELECT
U = 0
REPEAT 40
	SIF COUNT >= 4 && COUNT <=9
		CONTINUE
	SIF COUNT >= 18 && COUNT <= 19
		CONTINUE
	SIF COUNT >= 24 && COUNT <= 29
		CONTINUE
	SIF COUNT >= 34 && COUNT <= 36
		CONTINUE
	SIF COUNT == 38
		CONTINUE

	;男だと私处感觉と百合气质と百合中毒は上げられない
	SIF TALENT:122 && (COUNT == 2 || COUNT == 22 || COUNT == 33)
		CONTINUE
	;女だと断背气质とＢＬ中毒は上げられない
	SIF TALENT:122 == 0 && (COUNT == 23 || COUNT == 34)
		CONTINUE
	X = COUNT
	PRINTPLAIN  
	IF X == 0 && TALENT:101 & 2
		SETCOLOR 128, 128, 128
		PRINT [―]
	ELSEIF X == 1 && TALENT:107 & 2
		SETCOLOR 128, 128, 128
		PRINT [―]
	ELSEIF X == 2 && TALENT:103 & 2
		SETCOLOR 128, 128, 128
		PRINT [―]
	ELSEIF X == 3 && TALENT:105 & 2
		SETCOLOR 128, 128, 128
		PRINT [―]
	ELSE
		PRINTFORM [{X, 2}]
	ENDIF
	IF X == 0 && TALENT:122
		PRINTFORM 阴茎感觉 
	ELSE 
		PRINTFORM %ABLNAME:X,9,LEFT%
	ENDIF
	;文字揃えその２
	; IF X <= 3 || X == 17
		; PRINT   
	; ELSEIF (X >= 10 && X <= 12) || X == 15
		; PRINT     
	; ENDIF	
	PRINTFORM - LV{ABL:X,2}
	CALL DECIDE_ABLUP
	RESETCOLOR
	U += 1
	IF U % 4 == 0
		PRINTL 
	ENDIF
REND 
SIF U % 4 != 0
	PRINTL 

PRINTFORM  [99]%MARKNAME:3% - LV{MARK:3,2}
CALL DECIDE_ABLUP99
SIF RESULT == 1
	PRINT  *
SIF CSTR:7 != ""
	PRINTFORM   [ 4] %CSTR:7%感覚
CALL DECIDE_ABLUP4
SIF RESULT == 1
	PRINT  *
SIF CSTR:7 != ""
	PRINTFORM   [40] %CSTR:7%中毒
CALL DECIDE_ABLUP40
SIF RESULT == 1
	PRINT  *
;IF MARK:10 > 0
[IF_DEBUG]
PRINTFORM   [100]%MARKNAME:10% - LV{MARK:10,2}
CALL DECIDE_ABLUP100
SIF RESULT == 1
	PRINT  *	
[ENDIF]
PRINTL 
CUSTOMDRAWLINE ‥
PRINTL [999] - 能力值提高结束

@DECIDE_ABLUP
;阴蒂感觉
IF X == 0
	CALL DECIDE_ABLUP0
;乳房感觉
ELSEIF  X == 1
	CALL DECIDE_ABLUP1
;私处感觉
ELSEIF X == 2
	CALL DECIDE_ABLUP2
;肛门感觉
ELSEIF X == 3
	CALL DECIDE_ABLUP3
;顺从
ELSEIF X == 10
	CALL DECIDE_ABLUP10
;欲望
ELSEIF X == 11
	CALL DECIDE_ABLUP11
;技巧
ELSEIF X == 12
	CALL DECIDE_ABLUP12
;侍奉技术
ELSEIF X == 13
	CALL DECIDE_ABLUP13
;性交技术
ELSEIF X == 14
	CALL DECIDE_ABLUP14
;话术
ELSEIF X == 15
	CALL DECIDE_ABLUP15
;侍奉精神
ELSEIF X == 16
	CALL DECIDE_ABLUP16
;露出癖
ELSEIF X == 17
	CALL DECIDE_ABLUP17
;抖S气质
ELSEIF X == 20
	CALL DECIDE_ABLUP20
;抖M气质
ELSEIF X == 21
	CALL DECIDE_ABLUP21
;百合气质
ELSEIF X == 22
	CALL DECIDE_ABLUP22
;断背气质
ELSEIF X == 23
	CALL DECIDE_ABLUP23
;性交中毒
ELSEIF X == 30
	CALL DECIDE_ABLUP30
;自慰中毒
ELSEIF X == 31
	CALL DECIDE_ABLUP31
;精液中毒
ELSEIF X == 32
	CALL DECIDE_ABLUP32
;百合中毒
ELSEIF X == 33
	CALL DECIDE_ABLUP33
;卖淫中毒
ELSEIF X == 37
	CALL DECIDE_ABLUP37
;兽奸中毒
ELSEIF X == 39
	CALL DECIDE_ABLUP39
ENDIF
IF RESULT == 1
	PRINT  *
ELSEIF ABL:X >= 10
	PRINT   
ELSE
	PRINT   
ENDIF

RETURN 1


@USERABLUP
IF RESULT == 999
	CALL JUJUN_UP_CHECK
	CALL YOKUBO_UP_CHECK
	BEGIN TURNEND
	RETURN 1
ENDIF

RETURN 0


@AUTO_ABLUP, ARG = -1
LOCAL = TARGET
SIF ARG >= 0
	TARGET = ARG

;优先削去反发印记
CALL DECIDE_ABLUP99
IF RESULT
	CALL CORE_ABLUP99
	PRINTFORML %SAVESTR:TARGET%的%MARKNAME:3%下降为LV{MARK:3}
ENDIF

REPEAT 40
{
	SIF INRANGE(COUNT,4,9) || 
		INRANGE(COUNT,18,19) || 
		INRANGE(COUNT,24,29) ||
		INRANGE(COUNT,34,36) ||
		COUNT == 38
}
		CONTINUE
	;男だと私处感觉と百合气质と百合中毒は上げられない
	SIF TALENT:122 && (COUNT == 2 || COUNT == 22 || COUNT == 33)
		CONTINUE
	;女だと断背气质とＢＬ中毒は上げられない
	SIF TALENT:122 == 0 && (COUNT == 23 || COUNT == 34)
		CONTINUE
	
	;只自动提升部分能力
	SIF COUNT > 15 && GETBIT(FLAG:5,36)
		BREAK
	
	CALL AUTO_ABLUP_CORE, COUNT, 1
REND


TARGET = LOCAL

;--------------------------------------------------
; 自动提升能力等级（ABLUP）
; NUM: 要提升的能力的序号
; INFO: 是否显示等级提示信息（0，1）
@AUTO_ABLUP_CORE, NUM, INFO
;--------------------------------------------------
#DIM NUM
#DIM INFO

SIF ABL:NUM >= 10
	RETURN

RESULT = -1
TRYCALLFORM DECIDE_ABLUP{NUM}

SIF RESULT <= 0 
	RETURN 

RESULT = -1
TRYCALLFORM CORE_ABLUP{NUM}

SIF RESULT >= 0 && INFO
	PRINTFORML %SAVESTR:TARGET%的%ABLNAME:NUM%变为LV{ABL:NUM}
	
RESTART
