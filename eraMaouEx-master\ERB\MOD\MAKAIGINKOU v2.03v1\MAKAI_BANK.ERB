﻿;------------------------------------------------------------
;魔界银行处理相关
;------------------------------------------------------------
@MAKAI_BANK
CALL INTEREST
$BANK
DRAWLINE
PRINTFORML 欢迎进入魔界银行远程客户端，您目前的存款为{EX_FLAG:9001}，现金为{MONEY}。
PRINTL [1] 存款
PRINTL [2] 取款
PRINTL [3] 贷款
PRINTL [4] 还款
PRINTL [9] 返回
DRAWLINE
INPUT
IF RESULT == 1
	PRINTFORML 请输入需要存入的资金数量,返回上层请输入0。(目前的存款为{EX_FLAG:9001}现金为{MONEY})
	$INPUT_LOOP_0
	INPUT
		IF RESULT > 0 && MONEY >= RESULT
		EX_FLAG:9001 += RESULT
		MONEY -= RESULT
		EX_FLAG:4444 -= RESULT
		PRINTFORMW 已转移{RESULT}资金至银行。	
		GOTO BANK
	ELSEIF MONEY < RESULT
		PRINTFORML 您的操作有误，请重新输入,返回上层请输入0。
		GOTO INPUT_LOOP_0
	ELSEIF RESULT == 0
		PRINTFORMW 正在返回上层
		GOTO BANK
	ELSE
		GOTO INPUT_LOOP_0
	ENDIF
ELSEIF RESULT == 2
	PRINTFORML 请输入需要取出的资金数量,返回上层请输入0。(目前的存款为{EX_FLAG:9001}现金为{MONEY})
	$INPUT_LOOP1
	INPUT
		IF RESULT > 0 && EX_FLAG:9001 >= RESULT
		EX_FLAG:9001 -= RESULT
		MONEY += RESULT
		EX_FLAG:4444 += RESULT
		PRINTFORMW 已提取{RESULT}资金。
		GOTO BANK	
		ELSEIF EX_FLAG:9001 < RESULT
		PRINTFORML 您的操作有误，请重新输入,返回上层请输入0。
		GOTO INPUT_LOOP1
	ELSEIF RESULT == 0
		PRINTFORMW 正在返回上层
		GOTO BANK
	ELSE
		GOTO INPUT_LOOP1
	ENDIF
ELSEIF RESULT == 3
		IF CFLAG:0:9<10
		B=100000 + CFLAG:0:9 * 10000
	ELSEIF 	CFLAG:0:9>=10 && CFLAG:0:9<51
		B=200000 + (CFLAG:0:9-10) * 100000
	ELSEIF 	CFLAG:0:9>=51 && CFLAG:0:9<101
		B=4200000 + (CFLAG:0:9-50) * 1000000
	ELSE
		B=******** + (CFLAG:0:9-100) * ********
	ENDIF
	PRINTFORML 请输入贷款金额,返回上层请输入0。(目前欠款为{EX_FLAG:9003}贷款额度为{B})
	PRINTFORML 贷款利息为百分之二十，本息超过额度将无法借贷或追加借贷。
	PRINTFORML 还款期限为三十天，从初次借贷时开始计算,逾期未还款将强制征收本息百分之五十的滞纳金
	$INPUT_LOOP2
	INPUT
		IF RESULT > 0 && RESULT <= (B-EX_FLAG:9003)
		EX_FLAG:9003 += RESULT * 12 / 10	
		MONEY += RESULT
		EX_FLAG:4444 += RESULT
		PRINTFORMW 已贷款{RESULT}资金。
		GOTO BANK	
	ELSEIF RESULT > 0 && RESULT > (B-EX_FLAG:9003)
		PRINTFORML 您的额度不足，请重新输入,返回上层请输入0。
		GOTO INPUT_LOOP2
	ELSEIF RESULT == 0
		PRINTFORMW 正在返回上层
		GOTO BANK
	ELSE
		GOTO INPUT_LOOP2
	ENDIF
ELSEIF RESULT == 4
	PRINTFORML 请输入还款金额,返回上层请输入0。(目前欠款为{EX_FLAG:9003})
	$INPUT_LOOP3
	INPUT
		IF RESULT <= EX_FLAG:9003 && MONEY - RESULT > 0
		EX_FLAG:9003 -= RESULT
		MONEY -= RESULT
		EX_FLAG:4444 -= RESULT
		PRINTFORMW 已偿还{RESULT}资金。
		GOTO BANK

   	ELSEIF MONEY-RESULT < 0
		PRINTFORML 您没有足够的资金，请重新输入,返回上层请输入0。
		GOTO INPUT_LOOP3

	ELSEIF RESULT > EX_FLAG:9003
		PRINTFORMW 您的输入的金额超出欠款额，多余金额将自动转入银行。
		EX_FLAG:9001 += RESULT - EX_FLAG:9003
		MONEY -= RESULT
		EX_FLAG:4444 -= RESULT
		EX_FLAG:9003 = 0
		GOTO BANK

	ELSEIF RESULT == 0
		PRINTFORMW 正在返回上层
		GOTO BANK
	ELSE
		GOTO INPUT_LOOP3
	ENDIF
ELSEIF RESULT == 9
	EX_FLAG:9002= DAY
	RETURN
ENDIF