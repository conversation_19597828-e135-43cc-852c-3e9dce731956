﻿;侍奉技术のLvUP処理とその可否判定
;eraIm@s_ver.0.17βdのスクリプトをベースに処理を簡略化
;eraIM@Sから導入しました(eramaou)


;-------------------------------------------------
;侍奉技术のLvUP
;-------------------------------------------------
@ABLUP13
#DIM TEMP
TEMP = ABL:13
DRAWLINE
;PRINTL 奴隶的侍奉技术提升了。
;PRINTL 侍奉技巧越高，侍奉时越容易满足对方。
;CUSTOMDRAWLINE ‥
;侍奉精神がLv5以上ある場合はLv10まで开放
IF (ABL:13 >= 5 && ABL:16 < 5) || ABL:13 >= 10
	PRINTW 已达最高级
	RETURN 0
;侍奉技术＋性交技术は11以上にならない
;でも、珠が沢山あるの場合はレベルアップできる。
ELSEIF ABL:13 + ABL:14 >= 10
	SIF ABL:13 < ABL:14
		TEMP = ABL:14
	IF JUEL:7 < TEMP * TEMP * 500
	PRINTFORML 侍奉技术({ABL:13})＋性交技术({ABL:14})上限为10
	PRINTFORMW %PALAMNAME:7%点数至少达到{TEMP * TEMP * 500}点、方可突破技术等级限制
	RETURN 0
	ENDIF
ENDIF

;必要な习得点数
A = 0

;条件別にＯＫかダメかを記録する
;习得点数による可否（I=0:可、I&1:点数不足、I&2:経験不足）
I = 0

CALL DECIDE_ABLUP13

;Lv5までは技巧が侍奉技术+1Lvでないといけない
SIF ABL:13 < 5
	PRINTFORML %ABLNAME:12%LV{ABL:13 + 1}以上(现在LV{ABL:12})且
;Lv5からは侍奉精神が侍奉技术+1Lvでないといけない
SIF ABL:13 >= 5
	PRINTFORML %ABLNAME:16%LV{ABL:13 + 1}以上(现在LV{ABL:16})且

PRINTFORM [0] - %PALAMNAME:7%点数×{JUEL:7}/{A} ……
PRINTV GET_ABLUP_STATE(I)
PRINTL 
PRINTL [100] - 停止

INPUT
IF (RESULT < 0 || RESULT > 0) && RESULT != 100
	RESTART
ELSEIF I != 0 && RESULT == 0
	PRINTL 未满足条件
	RESTART
ELSEIF RESULT == 100
	RETURN 0
ENDIF

ABL:13 += 1
SIF RESULT == 0
	JUEL:7 -= A

PRINTFORML %ABLNAME:13%变为LV{ABL:13}。

RETURN 0


;-------------------------------------------------
@CORE_ABLUP13
;-------------------------------------------------
ABL:13 ++

IF I == 0
	JUEL:7 -= A
ENDIF



;--------------------------------------------------
;侍奉技术のLvUP可否判定
;--------------------------------------------------
@DECIDE_ABLUP13
;侍奉精神Lv5以上ある場合はLv10まで开放
#DIM TEMP
TEMP = ABL:14
SIF ABL:14 < ABL:13
	TEMP = ABL:13
IF ABL:13 >= 5 && ABL:16 < 5
	RETURN 0
ELSEIF ABL:13 + ABL:14 >= 10
	RETURN 0
ENDIF

;判定変数を空に
A = 0
I = 0

IF ABL:13 == 0
	A = 5
ELSEIF ABL:13 == 1
	A = 400
ELSEIF ABL:13 == 2
	A = 1000
ELSEIF ABL:13 == 3
	A = 3000
ELSEIF ABL:13 == 4
	A = 6000
ELSEIF ABL:13 == 5
	A = 9000
ELSEIF ABL:13 == 6
	A = 12000
ELSEIF ABL:13 == 7
	A = 16000
ELSEIF ABL:13 == 8
	A = 20000
ELSEIF ABL:13 == 9
	A = 25000
ENDIF

SIF ABL:13 + ABL:14 >= 10
	A = TEMP * TEMP * 500

;戒备森严
IF TALENT:27
	SIF ABL:13 == 3
		TIMES A , 1.50
	SIF ABL:13 == 4
		TIMES A , 2.00
	SIF ABL:13 == 5
		TIMES A , 2.50
	SIF ABL:13 >= 6
		TIMES A , 3.00
ENDIF

;反抗心
IF TALENT:11
	TIMES A , 1.20
ENDIF
;坦率
IF TALENT:13
	TIMES A , 0.95
ENDIF
;嚣张
IF TALENT:16
	TIMES A , 1.20
ENDIF

;高姿态
IF TALENT:15
	TIMES A , 1.20
;低姿态
ELSEIF TALENT:17
	TIMES A , 0.95
ENDIF

;冷漠
IF TALENT:21
	TIMES A , 1.05
ENDIF
;感情淡薄
IF TALENT:22
	TIMES A , 1.05
ENDIF
;好奇心
IF TALENT:23
	TIMES A , 0.95
ENDIF
;保守的
IF TALENT:24
	TIMES A , 1.10
ENDIF

;压抑
IF TALENT:32
	TIMES A , 1.10
;开放
ELSEIF TALENT:33
	TIMES A , 0.90
ENDIF

;抵抗
IF TALENT:34
	TIMES A , 1.20
ENDIF

;害羞
IF TALENT:35
	TIMES A , 1.05
;不知羞耻
ELSEIF TALENT:36
	TIMES A , 0.95
ENDIF

;把柄
IF TALENT:37
	TIMES A , 0.90
ENDIF

;快速学习
IF TALENT:50
	TIMES A , 0.80
;学习缓慢
ELSEIF TALENT:51
	TIMES A , 1.50
ENDIF

;擅用舌头
IF TALENT:52
	TIMES A , 0.90
ENDIF
;献身的
IF TALENT:63
	TIMES A , 0.90
ENDIF
;不怕脏
IF TALENT:64
	TIMES A , 0.90
ENDIF
;容易陷落
IF TALENT:73
	TIMES A , 0.90
ENDIF

;倒錯的
IF TALENT:80
	TIMES A , 0.95
ENDIF
;施虐狂
IF TALENT:83
	TIMES A , 1.10
ENDIF

;侍奉精神
IF ABL:16 < 3
	TIMES A , 1.00
ELSEIF ABL:16 < 6
	TIMES A , 0.95
ELSEIF ABL:16 < 8
	TIMES A , 0.90
ELSEIF ABL:16 < 10
	TIMES A , 0.85
ELSE
	TIMES A , 0.80
ENDIF

;最低でも1個は必要
SIF A < 1
	A = 1

;习得点数は足りている？
SIF JUEL:7 < A
	I |= 1

;Lv5までは技巧が侍奉技术+1Lvでないといけない
SIF ABL:13 < 5 && ABL:12 < ABL:13 + 1
	I |= 2
;Lv5からは侍奉精神が侍奉技术+1Lvでないといけない
SIF ABL:13 >= 5 && ABL:16 < ABL:13 + 1
	I |= 2

IF I == 0
	RETURN 1
ELSE
	RETURN 0
ENDIF
;
;
;