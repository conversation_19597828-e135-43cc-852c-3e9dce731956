#!/usr/bin/env python3
"""
测试修复后的模型处理
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_model_response_parsing():
    """测试模型响应解析的空值处理"""
    print("=== 测试模型响应解析 ===")
    
    try:
        from core.models import ModelManager
        from config.settings import SystemConfig
        
        # 创建测试配置
        config = SystemConfig()
        config.model_provider = "lm_studio"  # 使用本地模型避免API限制
        config.model_name = "test-model"
        config.base_url = "http://localhost:1234/v1"
        
        # 创建模型管理器
        model_manager = ModelManager(config)
        
        # 测试不同类型的响应内容
        test_cases = [
            (None, "None值测试"),
            ("", "空字符串测试"),
            ("正常响应内容", "正常内容测试"),
            ("<answer>标签内容</answer>", "Answer标签测试"),
            ("<think>思考内容</think>实际回答", "Think标签测试"),
            (123, "非字符串类型测试")
        ]
        
        print("测试 _parse_model_response 方法:")
        for content, description in test_cases:
            try:
                result = model_manager._parse_model_response(content)
                print(f"  ✓ {description}: '{content}' -> '{result}'")
            except Exception as e:
                print(f"  ✗ {description}: '{content}' -> 错误: {e}")
                
        return True
        
    except Exception as e:
        print(f"✗ 模型响应解析测试失败: {e}")
        return False

def test_model_configurations():
    """测试不同模型配置"""
    print("\n=== 测试模型配置 ===")
    
    try:
        from core.models import ModelManager
        from config.settings import SystemConfig
        
        # 测试不同的模型配置
        configs = [
            {
                "name": "LM Studio",
                "provider": "lm_studio",
                "model": "local-model",
                "base_url": "http://localhost:1234/v1"
            },
            {
                "name": "OpenAI",
                "provider": "openai", 
                "model": "gpt-3.5-turbo",
                "base_url": None
            },
            {
                "name": "Gemini",
                "provider": "gemini",
                "model": "gemini-pro",
                "base_url": None
            }
        ]
        
        for config_info in configs:
            try:
                config = SystemConfig()
                config.model_provider = config_info["provider"]
                config.model_name = config_info["model"]
                if config_info["base_url"]:
                    config.base_url = config_info["base_url"]
                
                model_manager = ModelManager(config)
                model_name = model_manager._get_model_name()
                
                print(f"  ✓ {config_info['name']}: {config_info['model']} -> {model_name}")
                
            except Exception as e:
                print(f"  ✗ {config_info['name']}: 配置错误: {e}")
                
        return True
        
    except Exception as e:
        print(f"✗ 模型配置测试失败: {e}")
        return False

def suggest_working_configuration():
    """建议可用的配置"""
    print("\n=== 建议的配置 ===")
    
    print("基于当前错误，建议使用以下配置之一:")
    print()
    
    print("1. 本地LM Studio配置 (推荐):")
    print("   MODEL_PROVIDER=lm_studio")
    print("   MODEL_NAME=local-model")
    print("   BASE_URL=http://localhost:1234/v1")
    print("   API_KEY=not-needed")
    print()
    
    print("2. OpenAI配置 (需要有效API密钥):")
    print("   MODEL_PROVIDER=openai")
    print("   MODEL_NAME=gpt-3.5-turbo")
    print("   API_KEY=your-openai-api-key")
    print("   BASE_URL=")
    print()
    
    print("3. Ollama配置 (本地运行):")
    print("   MODEL_PROVIDER=ollama")
    print("   MODEL_NAME=llama2")
    print("   BASE_URL=http://localhost:11434")
    print("   API_KEY=not-needed")
    print()
    
    print("当前Gemini配置遇到的问题:")
    print("  - 配额限制 (每日50次免费请求)")
    print("  - 模型过载 (503错误)")
    print("  - 建议切换到本地模型或其他提供商")

def check_current_env():
    """检查当前环境配置"""
    print("\n=== 当前环境配置检查 ===")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    env_vars = [
        "MODEL_PROVIDER",
        "MODEL_NAME", 
        "API_KEY",
        "BASE_URL",
        "OLLAMA_BASE_URL",
        "OLLAMA_EMBEDDING_MODEL"
    ]
    
    print("当前.env文件配置:")
    for var in env_vars:
        value = os.getenv(var, "未设置")
        # 隐藏API密钥的部分内容
        if "API_KEY" in var and value != "未设置" and len(value) > 8:
            display_value = value[:4] + "..." + value[-4:]
        else:
            display_value = value
        print(f"  {var}: {display_value}")
    
    # 检查Gemini配额问题
    provider = os.getenv("MODEL_PROVIDER")
    if provider == "gemini":
        print("\n⚠️  检测到使用Gemini配置")
        print("   根据日志显示，当前遇到配额限制问题")
        print("   建议切换到本地模型或其他提供商")

def main():
    """主测试函数"""
    print("开始测试修复后的模型处理\n")
    
    tests = [
        ("模型响应解析", test_model_response_parsing),
        ("模型配置", test_model_configurations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 检查环境配置
    check_current_env()
    
    # 提供配置建议
    suggest_working_configuration()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 模型处理修复成功！")
        print("💡 建议根据上述配置建议调整.env文件以避免API限制问题")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
