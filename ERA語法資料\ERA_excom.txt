﻿
OSDN (2014) 命令 
Online Available: http://osdn.jp/projects/emuera/wiki/excom
Last Access 2015/5/30




Outline
==================================================
PRINT系
　　PRINT(|V|S|FORM|FORMS)(|K|D)(|L|W)
　　PRINTSINGLE(|V|S|FORM|FORMS)(|K|D)
　　PRINT(|FORM)(C|LC)(|K|D)
　　PRINTDATA(|K|D)(|L|W)
　　PRINTBUTTON(|C|LC) <文本式>, <数式 or 文本式>
　　PRINTPLAIN(|FORM)
　　CUSTOMDRAWLINE <字符串>
　　DRAWLINEFORM <FORM字符串>
　　REUSELASTLINE <書式付字符串>
　　CLEARLINE <消す行数>
表示操作·フォント操作·表示仕様参照
　　SETCOLOR <R>, <G>, <B>
　　SETCOLOR <RGB>
　　RESETCOLOR
　　SETBGCOLOR <R>, <G>, <B>
　　SETBGCOLOR <RGB>
　　RESETBGCOLOR
　　SETCOLORBYNAME <字符串>
　　SETBGCOLORBYNAME <字符串>
　　GETCOLOR
　　GETDEFCOLOR
　　GETBGCOLOR
　　GETDEFBGCOLOR
　　GETFOCUSCOLOR
　　FONTBOLD
　　FONTITALIC
　　FONTREGULAR
　　FONTSTYLE <数式>
　　GETSTYLE
　　CHKFONT <文本式>
　　SETFONT <文本式>
　　GETFONT
　　FORCEKANA <数式>
　　ALIGNMENT <LEFT or CENTER or RIGHT>
　　CURRENTALIGN
　　REDRAW <数式>
　　CURRENTREDRAW
　　PRINTCPERLINE
　　LINEISEMPTY
　　BARSTR <变量>, <最大値>, <長さ>
　　MONEYSTR <数値>{, <書式指定子>}
　　SKIPDISP <数値>
　　NOSKIP
　　ENDNOSKIP
　　ISSKIP
　　MOUSESKIP
字符串操作·参照
　　TOUPPER <文本式>
　　TOLOWER <文本式>
　　TOHALF <文本式>
　　TOFULL <文本式>
　　TOSTR <数式>, <書式指定子>
　　ISNUMERIC <文本式>
　　TOINT <文本式>
　　STRLEN <字符串>
　　STRLENS <文本式>
　　STRLENFORM <書式付字符串>
　　STRLENU <字符串>
　　STRLENSU <文本式>
　　STRLENFORMU <書式付字符串>
　　SUBSTRING <文本式>, <数式>, <数式>
　　SUBSTRINGU <文本式>, <数式>, <数式>
　　CHARATU <文本式>, <文字位置>
　　STRFIND <文本式>, <文本式>(, <数式>)
　　STRFINDU <検索対象>, <検索する字符串>{, <開始インデックス>}
　　STRCOUNT <検索対象字符串>, <検索字符串>
　　SPLIT <文本式>, <文本式>, <字符串变量>
　　REPLACE <置換対象字符串>, <置換対象パターン>, <置換後の字符串>
　　ESCAPE <字符串>
　　UNICODE <数式>
　　ENCODETOUNI <対象字符串(FORM型字符串)>
算術
　　POWER <变量>, <数式>, <数式>
　　ABS <数式>
　　SIGN <数式>
　　SQRT <数式>
　　GETBIT <数式>, <数式>
　　MAX <数式>(, <数式>...)
　　MIN <数式>(, <数式>...)
　　LIMIT <数式>, <数式>, <数式>
　　INRANGE <数式>, <数式>, <数式>
　　SETBIT <数値型变量>, <数式>{, <数式>,...}
　　CLEARBIT <数値型变量>, <数式>{, <数式>,...}
　　NVERTBIT <数値型变量>, <数式>{, <数式>,...}
キャラ操作·参照
　　ADDCHARA <数式>(, <数式>, <数式>, ...)
　　ADDSPCHARA <数式>(, <数式>, <数式>, ...)
　　DELCHARA <数式>(, <数式>, <数式>, ...)
　　SWAPCHARA <数式>, <数式>
　　SORTCHARA <キャラクタ变量> {, <FORWARDorBACK>}
　　GETCHARA <キャラ番号(NO:XXXの方)>, (<0 or 0以外>省略可)
　　GETSPCHARA <キャラ番号(NO:XXXの方)>
　　ADDDEFCHARA
　　ADDVOIDCHARA
　　DELALLCHARA
　　PICKUPCHARA <対象キャラ>(, <対象キャラ>, ....)
　　EXISTCSV <数式>, <数式>
　　FINDCHARA <キャラクタ变量>, <式>(, <数式>, <数式>)
　　FINDLASTCHARA <キャラクタ变量>, <式>(, <数式>, <数式>)
　　COPYCHARA <数式>, <数式>
　　ADDCOPYCHARA <数式>
变量操作·变量参照·CSV参照
　　VARSIZE <变量名>
　　RESETDATA
　　RESETGLOBAL
　　RESET_STAIN <数式>
　　SWAP <变量1>, <变量2>
　　CSVNAME <数式>(, <数式>)
　　CSVCALLNAME <数式>(, <数式>)
　　CSVNICKNAME <数式>(, <数式>)
　　CSVMASTERNAME <数式>(, <数式>)
　　CSVBASE <数式>, <数式>(, <数式>)
　　CSVCSTR <数式>, <数式>(, <数式>)
　　CSVABL <数式>, <数式>(, <数式>)
　　CSVTALENT <数式>, <数式>(, <数式>)
　　CSVMARK <数式>, <数式>(, <数式>)
　　CSVEXP <数式>, <数式>(, <数式>)
　　CSVRELATION <数式>, <数式>, <数式>
　　CSVJULE <数式>, <数式>(, <数式>)
　　CSVEQUIP <数式>, <数式>(, <数式>)
　　CSVCFLAG <数式>, <数式>,(, <数式>)
　　GETNUM <变量名>, <文本式>
　　GETPALAMLV <数式>, <判定するLVの上限>
　　GETEXPLV <数式>, <判定するLVの上限>
　　FINDELEMENT <一次元配列变量>, <検索対象(变量と同型)>, <検索初位置>, <検索終位置>, <厳密一致かのフラグ>
　　FINDLASTELEMENT <一次元配列变量>, <検索対象(变量と同型)>, <検索初位置>, <検索終位置>, <厳密一致かのフラグ>
　　VARSET <变量名>{, <数式 or 文本式>, <配列範囲初値>, <配列範囲終値+1>}
　　CVARSET <キャラクタ变量>{, <数式>, <式>, <キャラクタ範囲初値>, <キャラクタ範囲終値+1>}
　　ARRAYSHIFT <対象变量>, <ずらす数>, <ずらしてできた空白領域の初期値>{, <ずらす配列範囲の初値>, <ずらす配列要素の範囲の数>}
　　ARRAYREMOVE <対象变量>, <消す範囲初値>, <消す要素数>
　　ARRAYSORT <対象变量>{, <ソート方式(FORWARD or BACK)>, <開始インデックス>, <対象要素数>}
　　ARRAYCOPY <コピー元变量名>, <コピー先变量名>
　　CUPCHECK <登録キャラクター番号>
セーブデータ操作
　　SAVEDATA <数式>, <文本式>
　　LOADDATA <数式>
　　DELDATA <数式>
　　CHKDATA <数式>
　　SAVENOS <数値变量>
　　SAVEGLOBAL
　　LOADGLOBAL
　　OUTPUTLOG
日付·時刻取得
　　GETTIME
　　GETMILLISECOND
　　ETSECOND
入力·ウェイト
　　FORCEWAIT
　　INPUT {<数値>}
　　INPUTS {<字符串>}
　　TINPUT <数値>, <数値>{, <数値>, <字符串>}
　　TINPUTS <数値>, <文本式>{, <数値>, <字符串>}
　　TWAIT <数値>, <数値>
　　ONEINPUT {<数値>}
　　ONEINPUTS {<字符串>}
　　TONEINPUT <数値>, <数値>{, <数値>, <字符串>}
　　TONEINPUTS <数値>, <文本式>{, <数値>, <字符串>}
　　WAITANYKEY
ループ·分岐構文
　　FOR <数値型变量>, <数式>, <数式>{, <数式>}
　　NEXT
　　WHILE <数式>
　　WEND
　　DO
　　LOOP <数式>
　　SELECTCASE <式>
　　CASE <CASE条件式>(, <CASE条件式>, <CASE条件式> ……)
　　CASEELSE
　　ENDSELECT
乱数制御
　　RANDOMIZE <数式>
　　DUMPRAND
　　INITRAND
デバッグ補助·システムフロー制御
　　BEGIN <キーワード>
　　CALLTRAIN <コマンド数>
　　DOTRAIN <数式>
　　THROW <FORM構文>
　　CALL·JUMP·GOTO系
　　TRYJUMP <字符串> (, 引数1, 引数2……)
　　TRYCALL <字符串> (, 引数1, 引数2……)
　　TRYGOTO <字符串>
　　JUMPFORM <書式付字符串> (, 引数1, 引数2……)
　　CALLFORM <書式付字符串> (, 引数1, 引数2……)
　　GOTOFORM <書式付字符串>
　　TRYJUMPFORM <書式付字符串> (, 引数1, 引数2……)
　　TRYCALLFORM <書式付字符串> (, 引数1, 引数2……)
　　TRYGOTOFORM <書式付字符串>
　　CALLF <字符串> (, 引数1, 引数2……)
　　CALLFORMF <書式付字符串> (, 引数1, 引数2……)
　　CALL·JUMP·GOTO系2 (TRYC-CATCH-ENDCATCH)
　　TRYCJUMP <字符串> (, 引数1, 引数2……)
　　TRYCCALL <字符串> (, 引数1, 引数2……)
　　TRYCGOTO <字符串>
　　TRYCJUMPFORM <書式付字符串> (, 引数1, 引数2……)
　　TRYCCALLFORM <書式付字符串> (, 引数1, 引数2……)
　　TRYCGOTOFORM <書式付字符串>
　　CATCH
　　ENDCATCH
　　TRYCALLLIST
　　TRYJUMPLIST
　　TRYGOTOLIST
　　FUNC <字符串> (, 引数1, 引数2……)
　　ENDFUNC
　　RETURN系
　　RETURN <数式>(, <数式>, <数式>, ...)
　　RETURNFORM <書式付字符串>(, <書式付字符串>, <書式付字符串>, ...)
　　RETURNF <式>
　　DEBUG系
　　DEBUGPRINT <字符串>
　　DEBUGPRINTL <字符串>
　　DEBUGPRINTFORM <書式付字符串>
　　DEBUGPRINTFORML <書式付字符串>
　　ASSERT <数式>
HTML系
　　HTML_PRINT <文本式>
　　TML_TAGSPLIT <文本式>　　

　　
　　
　　
　　
　　
　　
　　
　　
　　
　　
　　
　　
　　
　　
PRINT系
==================================================

PRINT(|V|S|FORM|FORMS)(|K|D)(|L|W)
--------------------------------------------------

第1个括号内的关键词指定了参数类型：

* (无)　　-　　(<字符串>)
* V　　　　-　　(<数式>, <数式>, <数式> ...)
* S　　　　-　　<文本式>
* FORM　　-　　(<带格式的字符串>)
* FORMS　　-　　<带格式的文本式>

第2个括号内的关键字控制绘制方式：

* (无)　　-　　忽略FORCEKANA指令，应用SETCOLOR指令指定的颜色来绘制。
* K　　　　-　　应用FORCEKANA指令来绘制。
* D　　　　-　　忽略SETCOLOR指令，使用设置文件指定的默认颜色来绘制

注：SETCOLOR指令用来指定字体颜色，FORCEKANA指令与日文平假转换有关。

第3个括号内的关键字控制绘制文字后的是否换行，以及是否WAIT：

* (无)　　-　　PRINT后不换行也不WAIT
* L　　　　-　　PRINT后换行
* W　　　　-　　PRINT后执行WAIT

注：WAIT指令用于等待用户键入回车符。

以上关键字可以组合使用，
例如PRINTSDW表示，参数<文本式>，默认字体颜色，PRINT后执行WAIT指令。


PRINTSINGLE(|V|S|FORM|FORMS)(|K|D)
--------------------------------------------------

PRINTSIGLE系与PRINTL指令大致相同，
不同点在于PRINTSIGLE绘制文本超出画面宽度时不会自动换行。
超出画面的文字不会被绘制。
因为在输出后会换行，所以没有(|L|W)关键字。
其他关键字与PRINT的关键字意义相同。


PRINT(|FORM)(C|LC)(|K|D)
--------------------------------------------------

PRINTC系若要绘制的文本没有到达指定长度，则会用半角空格来补充。
设置“PRINTC文字长度”（默认25）指定了文本的长度。
在脚本中绘制文本按钮时建议考虑使用PRINTC指令。

第1个括号内的关键字指定了参数类型：

* (无)　　-　　<字符串>
* FORM　　-　　<带格式的字符串>

第2个括号内的参数指定了文字对齐方式：

* C　　　　-　　文字右对齐（左侧填充空格）
* LC　　-　　文字左对齐

第3个括号内的关键字与PRINT的关键字意义相同。


PRINTDATA(|K|D)(|L|W)
--------------------------------------------------

PRINTDATA系指令。根据私家改造版Readme：

　　书写格式：
　　　　PRINTDATA (数值变量，可省略)
　　　　　　DATA (字符串)
　　　　　　DATAFORM (带格式的字符串)
　　　　　　DATALIST
　　　　　　　　(DATA 或 DATAFORM 序列)
　　　　　　ENDLIST
　　　　ENDDATA
　　内容：
　　　　随机显示DATA, DATAFORM, DATATLIST~ENDLIST指定的文本。
　　　　不使用IF和RAND就能实现显示随机文本的功能。
　　　　当指定数值变量参数时，将根据变量值进入指定编号的DATA。
　　　　DATALIST~ENDLIST中每个DATA或DATAFORM都相当于一行。

如上所述。
此外，K, D, L, W关键字与PRINT的关键字意义相同。

请在使用PRINTDATA前就已经确定要显示的文本。
PRINTDATA~ENDDTA内只能使用DATA系指令来提供数据。
PRINTDATA~ENDDATA以及DATALIST~ENDLIST内不能表达上述以外的语法。


PRINTBUTTON(|C|LC) <文本式>, <数式或文本式>
--------------------------------------------------

PRINTBUTTON指令用于生成可以鼠标点击的按钮。
格式与PRINTS接近，但第二参数制定了单击时输入的数值或字符串，
且第一参数中的不能包含换行符。

Emuera会将“[300] 存档”这样的用[]封闭数字后跟文字这样的文本在绘制时自动转换成按钮。
PRINTBUTTON指令用来避免自动生成而是强制生成按钮。

例如如下：

　　PRINT 是要这样么？ [0] 是    [1] 否
　　INPUT

这种情况下Emuera并不能正确的识别，
会识别成按钮“是要这样么？ [0] 是”与按钮“[1] 否”。
可以使用PRINTBUTTON来重写这种情况：
　　
　　PRINTS "是要这样么？ "
　　PRINTBUTTON "[0] 是", 0
　　PRINTS "    "
　　PRINTBUTTON "[1] 否", 1
　　INPUT

（这里使用PRINTS而非PRINT是为了清晰的显示出来空格的数量）
这样就可以正确绘制文本和按钮了。
此外，PRINTBUTTON指令显示的文本并不需要包含“[0]”和“[1]”，
但仍然建议标注“[0]”和“[1]”来提示玩家这是按钮。

PRINTBUTTON指令不仅可以生成输入数值的按钮，也可以产生输入字符串的按钮。
但字符串需要使用INPUTS指令而非INPUT指令。

　　PRINTL 请输入名字
　　PRINTBUTTON "[穗月]", "穗月"
　　PRINTBUTTON "[美穗]", "美穗"
　　PRINTBUTTON "[其他]", "其他"
　　INPUTS

括号内的关键字与PRINTC相同，设置了对齐方向。


PRINTPLAIN (| FORM）
--------------------------------------------------

输出纯文本。不会转化成按钮。

