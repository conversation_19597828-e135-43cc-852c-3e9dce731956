﻿eramaou_PREGNANCY_20140620

※(eramaou　ver.0.302+パッチまとめ適用後を推奨)

加筆・改変・再アップロードはご自由にどうぞ。

■使い方
　eramaou_v302_パッチまとめ_20140614.zip
　以上のパッチファイルを適用してから
　全てのERBファイルをERBフォルダに上書きコピーしてください。

■仕様
　・子供を産んだ後に５日間の育児をするようになります。
　　・出産後に調教可能状態か育児室にいた場合育児室で育児をします（育児室にいる場合調教不可）
　　・臨月時や育児時で育児室にキャラがいる場合、能力の表示から訪問することが出来ます。
　　・勇者同士の子供はモンスターではないので戦力にはなりません。
　　・崩壊して産んだ子供を里子に出した場合、戦力にはなりません。
　・晒し台で妊娠可能になりました。
　・育児室での口上を追加しました。
　・ＮＴＲ出産の地の文追記。
　・能力の表示で[調教可][苗床][晒し台]を表示するようにしました。
　・口上テンプレ更新。
　
■追加・修正箇所
　■eramaouフラグまとめ.txt
　CFLAG:273 = 育児室口上
　CFLAG:274 = 親離れ口上
　以上を追加
　
　■CHARA_INFO.ERB
　61行目の魔王様の現在地を空白に。
　122～136行目に[調教可][苗床][晒し台]の分岐を追加。
　160行目に以下の文を追加。
　CALL SHOW_BUTTON_CHILD_CARE(5,ARG) ;育児室の訪問
　185～187行目に育児室の分岐を追記。
　
　■CHARA_TEMPTATION.ERB
　77行目に以下の文を追加。
　CALL PARTY_DEL(ARG)

　■EVENT_K0～K10.ERB
　育児室での口上、妊娠中、育児中、親離れ時を追加。

　■EVENT_NEXTDAY.ERB
　68行目～91行目の出産･育児室関連をFOR~NEXTループに変更。
　親離れ関数@DEPEARENT(ARG)の呼び出しと育児中の表示を追加。

　1350行目、晒し台関数@PILLORYに妊娠チェック追加。

　■EVENT_PREGNANCY.ERB
　192行目を以下の文に修正、193行目のELSEを削除。
　IF GETBIT(FLAG:5,2) == 0

　634行目の臨月関数@REACH_FULL_TERM(ARG)を以下のように修正。
　COUNTをARGに置換。迎撃中、晒し台、苗床の場合は育児室に行かない。
　但し妊娠・臨月調教が可能なら迎撃中でも育児室に行く。
　
　669行目の出産関数@CHILD_BIRTH(ARG)を以下のように修正。
　母親が調教中か育児室にいる場合は育児関数@CHILD_CARE(ARG)を呼び出し。
　母親がNTR時はどんな子供でも処分、迎撃時、苗床、晒し台はその場で産んで即戦力にしたあと各フラグをリセットします。
　
　804行目以降に育児関数@CHILD_CARE(ARG)を追記。
　母親が崩壊していた場合、乳母を選ぶか里子に出すかチェックします。
　母親が崩壊していなかった場合、母親は育児室に入ります。
　※レアなケースですが迎撃で帰ってきて即出産の場合でも育児室に入ります。
　育児をしてようやく【母性】を取得します。
　母親が崩壊していた場合妊娠時の各フラグがリセットされ、子供は里子に出されます（戦力になりません）
　
　907行目以降に親離れ関数@DEPEARENT(ARG)を追記。
　親離れをしたときにSUMMON_MONSTER(ARG)でモンスターを追加します。
　その後妊娠時の各フラグをリセットします。　
　
　944行目以降に[育児室の訪問]ボタンを表示する関数を追記。
　@SHOW_BUTTON_CHILD_CARE(NUM, ARG)
　@CHECK_ABLE_TO_CHILD_CARE(ARG)
　@CHILD_CARE_CHARA(ARG)
　の３つを追記。
　
　■NTR.ERB
　336行目に父が奴隷か助手の場合の分岐を追加。

　■SUMMON_MONSTER.ERB
　ループ文をFOR~NEXTに変更。
　83～89行目に父親が助手か奴隷と、父親が狂王か見ず知らずの男の場合の分岐を追加。


　■EVENT_KXX.ERB
　育児室口上、親離れ口上を追加。

