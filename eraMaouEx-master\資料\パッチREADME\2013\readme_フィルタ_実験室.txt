﻿eramaou_フィルタ_実験室_20131009

※(eramaou　ver.0.270推奨)

加筆・改変・再アップロードはご自由にどうぞ。

■使い方
　eramaou_0.270パッチまとめ20131005.zipを適用してから
　全てのERBファイルを上書きしてお使いください

■仕様
・メインメニューで「Assistant」を選んで助手を選択するときに「助手を無し」を選べるようにしました。
・調教開始時に助手がいない場合「助手は無し」「（メインメニューに）戻る」を選べるようにしました。
・調教中/コンフィグで愛撫系、器具系、Ａ性交系、Ｖ性交系、SM系コマンドをフィルタリング可能にしました。
・実験室で職業名ではなく名前が表示されるようにしました。
・勇者がレベルアップするときにまとめてレベルアップするように修正。

2013/10/09
■USERCOM.ERB
@SHOW_USERCOM関数に
FLAG:25 & 1 愛撫系フィルタ
FLAG:25 & 2 器具系フィルタ
FLAG:25 & 4 Ａ性交系フィルタ
FLAG:25 & 8 Ｖ性交系フィルタ 
FLAG:25 & 16 ＳＭ系フィルタ
を追加。

■COMABLE.ERB
各種コマンドにフィルタのチェックを追加
フィルタされていたらRETURN 0を返すようにします。

■SHOP.ERB
80行目に以下を追加
SIF RESULT == 2
	RETURN 0

272～273行目を以下に修正
PRINTL [100] 助手は無し
PRINTL [101] 戻る

280行目を以下に修正
ELSEIF RESULT >= A && RESULT != 100 && RESULT != 101

284～290行目を以下に修正
IF RESULT == 100
	ASSI = -1
	RETURN 0
ENDIF

SIF RESULT == 101
	RETURN 2

@CONFIG関数に調教時フィルタを追加
FLAG:5のフラグ管理をビット演算に

■SHOP_LABO.ERB
%NAME:*%となっていたところを%SAVESTR:*%に置換

■LVUP.ERB
9～18行目に$LVUP_REPEATラベルを追加

