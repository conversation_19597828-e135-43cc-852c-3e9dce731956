"""
LangGraph多代理系统核心管理器
基于ERA语法的游戏生成工作流
"""

from typing import Dict, Any, List, Optional, TypedDict
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.sqlite import SqliteSaver
import sqlite3
import asyncio
import logging
from datetime import datetime

from .types import AgentState, AgentConfig, GameConfig, AgentType, AgentMessage
from .era_constants import SYSTEM_FUNCTIONS, GAME_FLOW_STATES

class GraphState(TypedDict):
    """LangGraph状态定义"""
    agent_state: AgentState
    messages: List[AgentMessage]
    current_step: str
    error: Optional[str]
    completed_steps: List[str]

class ERAAgentWorkflow:
    """ERA代理工作流管理器"""
    
    def __init__(self, db_path: str = "era_agent.db"):
        self.db_path = db_path
        self.checkpointer = SqliteSaver.from_conn_string(f"sqlite:///{db_path}")
        self.graph = None
        self.agents = {}
        self.logger = logging.getLogger(__name__)
        
    def initialize_agents(self, agent_configs: List[AgentConfig]):
        """初始化所有代理"""
        for config in agent_configs:
            self.agents[config.agent_id] = {
                'config': config,
                'status': 'idle',
                'last_updated': datetime.now()
            }
            
    def build_workflow(self) -> StateGraph:
        """构建工作流图"""
        workflow = StateGraph(GraphState)
        
        # 添加节点
        workflow.add_node("initialize", self.initialize_step)
        workflow.add_node("analyze_requirements", self.analyze_requirements_step)
        workflow.add_node("generate_structure", self.generate_structure_step)  
        workflow.add_node("create_csv_data", self.create_csv_data_step)
        workflow.add_node("generate_erb_scripts", self.generate_erb_scripts_step)
        workflow.add_node("implement_system_flow", self.implement_system_flow_step)
        workflow.add_node("create_characters", self.create_characters_step)
        workflow.add_node("integrate_game_logic", self.integrate_game_logic_step)
        workflow.add_node("validate_and_finalize", self.validate_and_finalize_step)
        
        # 设置入口点
        workflow.set_entry_point("initialize")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "initialize",
            self.should_continue,
            {
                "continue": "analyze_requirements",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "analyze_requirements", 
            self.should_continue,
            {
                "continue": "generate_structure",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "generate_structure",
            self.should_continue, 
            {
                "continue": "create_csv_data",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "create_csv_data",
            self.should_continue,
            {
                "continue": "generate_erb_scripts", 
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "generate_erb_scripts",
            self.should_continue,
            {
                "continue": "implement_system_flow",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "implement_system_flow",
            self.should_continue,
            {
                "continue": "create_characters",
                "error": END  
            }
        )
        
        workflow.add_conditional_edges(
            "create_characters",
            self.should_continue,
            {
                "continue": "integrate_game_logic",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "integrate_game_logic", 
            self.should_continue,
            {
                "continue": "validate_and_finalize",
                "error": END
            }
        )
        
        workflow.add_edge("validate_and_finalize", END)

        # Compile the workflow without checkpointer for now to avoid version issues
        self.graph = workflow.compile()
        return self.graph
        
    async def initialize_step(self, state: GraphState) -> GraphState:
        """初始化步骤"""
        try:
            self.logger.info("Starting ERA game generation workflow")
            
            # 更新状态
            state["current_step"] = "initialize"
            state["agent_state"].current_step = "initialize"
            
            # 创建输出目录
            if state["agent_state"].game_config:
                import os
                os.makedirs(state["agent_state"].game_config.output_path, exist_ok=True)
                
            # 发送初始化消息
            init_message = AgentMessage(
                sender="workflow",
                receiver="all",
                message_type="initialize",
                content={"status": "started"},
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(init_message)
            state["completed_steps"].append("initialize")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def analyze_requirements_step(self, state: GraphState) -> GraphState:
        """需求分析步骤"""
        try:
            self.logger.info("Analyzing game requirements")
            
            state["current_step"] = "analyze_requirements"
            
            # 分析游戏配置和需求
            game_config = state["agent_state"].game_config
            if not game_config:
                raise ValueError("Game configuration not provided")
                
            # 创建需求分析消息
            analysis_message = AgentMessage(
                sender="workflow",
                receiver="main_agent", 
                message_type="analyze",
                content={
                    "game_title": game_config.game_title,
                    "description": game_config.game_description,
                    "features": game_config.features,
                    "character_count": game_config.character_count
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(analysis_message)
            state["completed_steps"].append("analyze_requirements")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Requirements analysis failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def generate_structure_step(self, state: GraphState) -> GraphState:
        """生成项目结构步骤"""
        try:
            self.logger.info("Generating project structure")
            
            state["current_step"] = "generate_structure"
            
            # 创建项目结构
            game_config = state["agent_state"].game_config
            structure = {
                "CSV": ["Chara", "Abl.csv", "Talent.csv", "Train.csv", "Item.csv", "Str.csv"],
                "ERB": ["SYSTEM.ERB", "TITLE.ERB", "VARIABLES.ERH", "TRAIN_MAIN.ERB"],
                "resources": ["img.csv"],
                "config": ["_default.config", "_fixed.config", "_replace.csv"]
            }
            
            # 发送结构生成消息
            structure_message = AgentMessage(
                sender="workflow",
                receiver="main_agent",
                message_type="create_structure", 
                content={"structure": structure},
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(structure_message)
            state["completed_steps"].append("generate_structure")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Structure generation failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def create_csv_data_step(self, state: GraphState) -> GraphState:
        """创建CSV数据步骤"""
        try:
            self.logger.info("Creating CSV data files")
            
            state["current_step"] = "create_csv_data"
            
            # 发送CSV生成消息给数据代理
            csv_message = AgentMessage(
                sender="workflow",
                receiver="csv_data_agent",
                message_type="generate_csv",
                content={
                    "character_count": state["agent_state"].game_config.character_count,
                    "features": state["agent_state"].game_config.features
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(csv_message)
            state["completed_steps"].append("create_csv_data")
            
            return state
            
        except Exception as e:
            self.logger.error(f"CSV data creation failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def generate_erb_scripts_step(self, state: GraphState) -> GraphState:
        """生成ERB脚本步骤"""
        try:
            self.logger.info("Generating ERB scripts")

            state["current_step"] = "generate_erb_scripts"

            # 实际调用主代理生成游戏内容
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from agents.era_generators import get_game_generator
            generator = get_game_generator()

            game_config = state["agent_state"].game_config
            self.logger.info(f"Starting game generation for: {game_config.game_title}")

            results = await generator.generate_complete_game(game_config)
            self.logger.info(f"Game generation completed. Results keys: {list(results.keys())}")

            # 保存生成的文件
            if "error" not in results:
                self.logger.info(f"No errors in results. ERB files: {len(results.get('erb_files', {}))}, CSV files: {len(results.get('csv_files', {}))}")
                saved_files = await generator.save_game_files(game_config, results)
                state["agent_state"].generated_files = saved_files
                self.logger.info(f"Generated and saved {len(saved_files)} files to {game_config.output_path}")

                # 列出保存的文件
                for file_path in saved_files:
                    self.logger.info(f"Saved file: {file_path}")
            else:
                self.logger.error(f"Game generation failed: {results['error']}")
                state["error"] = results["error"]
                return state

            # 发送ERB生成消息给脚本代理
            erb_message = AgentMessage(
                sender="workflow",
                receiver="erb_script_agent",
                message_type="generate_erb",
                content={
                    "system_functions": SYSTEM_FUNCTIONS,
                    "game_flows": GAME_FLOW_STATES,
                    "features": state["agent_state"].game_config.features,
                    "generated_files": len(saved_files)
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(erb_message)
            state["completed_steps"].append("generate_erb_scripts")

            return state

        except Exception as e:
            self.logger.error(f"ERB script generation failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def implement_system_flow_step(self, state: GraphState) -> GraphState:
        """实现系统流程步骤"""
        try:
            self.logger.info("Implementing system flow")
            
            state["current_step"] = "implement_system_flow"
            
            # 发送系统流程实现消息
            flow_message = AgentMessage(
                sender="workflow",
                receiver="system_flow_agent",
                message_type="implement_flow",
                content={
                    "flows": GAME_FLOW_STATES,
                    "system_functions": SYSTEM_FUNCTIONS
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(flow_message)
            state["completed_steps"].append("implement_system_flow")
            
            return state
            
        except Exception as e:
            self.logger.error(f"System flow implementation failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def create_characters_step(self, state: GraphState) -> GraphState:
        """创建角色步骤"""
        try:
            self.logger.info("Creating game characters")
            
            state["current_step"] = "create_characters"
            
            # 发送角色创建消息
            character_message = AgentMessage(
                sender="workflow",
                receiver="character_agent",
                message_type="create_characters",
                content={
                    "character_count": state["agent_state"].game_config.character_count,
                    "game_theme": state["agent_state"].game_config.game_description
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(character_message)
            state["completed_steps"].append("create_characters")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Character creation failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def integrate_game_logic_step(self, state: GraphState) -> GraphState:
        """整合游戏逻辑步骤"""
        try:
            self.logger.info("Integrating game logic")
            
            state["current_step"] = "integrate_game_logic"
            
            # 发送游戏逻辑整合消息
            logic_message = AgentMessage(
                sender="workflow",
                receiver="game_logic_agent",
                message_type="integrate_logic",
                content={
                    "features": state["agent_state"].game_config.features,
                    "generated_files": state["agent_state"].generated_files
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(logic_message)
            state["completed_steps"].append("integrate_game_logic")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Game logic integration failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    async def validate_and_finalize_step(self, state: GraphState) -> GraphState:
        """验证和完成步骤"""
        try:
            self.logger.info("Validating and finalizing game")
            
            state["current_step"] = "validate_and_finalize"
            
            # 验证生成的文件
            validation_message = AgentMessage(
                sender="workflow",
                receiver="main_agent",
                message_type="validate",
                content={
                    "generated_files": state["agent_state"].generated_files,
                    "output_path": state["agent_state"].game_config.output_path
                },
                timestamp=datetime.now().timestamp()
            )
            state["messages"].append(validation_message)
            state["completed_steps"].append("validate_and_finalize")
            
            self.logger.info("ERA game generation completed successfully")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Validation and finalization failed: {str(e)}")
            state["error"] = str(e)
            return state
            
    def should_continue(self, state: GraphState) -> str:
        """判断是否继续执行"""
        if state.get("error"):
            return "error"
        return "continue"
        
    async def run_workflow(self, game_config: GameConfig, thread_id: str = "default") -> Dict[str, Any]:
        """运行工作流"""
        try:
            # 初始化状态
            initial_state: GraphState = {
                "agent_state": AgentState(),
                "messages": [],
                "current_step": "init",
                "error": None,
                "completed_steps": []
            }
            
            # 设置游戏配置
            initial_state["agent_state"].game_config = game_config
            
            # 构建并运行工作流
            if not self.graph:
                self.build_workflow()
                
            # 执行工作流
            final_state = await self.graph.ainvoke(initial_state)
            
            return {
                "status": "success" if not final_state.get("error") else "error",
                "error": final_state.get("error"),
                "generated_files": final_state["agent_state"].generated_files,
                "completed_steps": final_state["completed_steps"],
                "messages": final_state["messages"]
            }
            
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "generated_files": [],
                "completed_steps": [],
                "messages": []
            }