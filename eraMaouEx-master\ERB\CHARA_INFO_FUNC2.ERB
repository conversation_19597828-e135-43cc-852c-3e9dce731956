﻿@TRANSFER_SOUL, ARG, MODE
#DIM MODE = 0
SIF MODE
	GOTO TSTAG
PRINTFORML 要将%CALLNAME:MASTER%的灵魂转移到%SAVESTR:ARG%上么？
PRINTL [0] 是  [1] 否

INPUT

SIF RESULT != 0
	RETURN ARG
	
$TSTAG
;魔王身体状态确认
CALL BODYCHECK_MAOU

IF (CFLAG:ARG:601 >= 900 && CFLAG:ARG:601 <= 902) || CFLAG:ARG:601 == 0
	SWAP CFLAG:MASTER:601, CFLAG:ARG:601
	SWAP CFLAG:MASTER:609, CFLAG:ARG:609
ELSE
	CFLAG:MASTER:621 = CFLAG:ARG:601
	CALL SEARCH_FAMILY,ARG,"MARRIAGE"
	IF EX_TALENT:ARG:2 && RESULT < 0
	ELSEIF RESULT < 0
	ELSE
		CFLAG:RESULT:621 = CFLAG:RESULT:601
		CFLAG:RESULT:601 = 901
		CFLAG:RESULT:609 = CFLAG:RESULT:6
	ENDIF
ENDIF

;角色交换
SWAPCHARA MASTER, ARG
CALL TRANSFERAPP, ARG

;灵魂错位素质添加，有debuff
EX_TALENT:ARG:0 = RAND(5) + 1
SIF EX_TALENT:MASTER:0
	EX_TALENT:ARG:0 = EX_TALENT:MASTER:0 + 1
EX_TALENT:MASTER:0 = 0
PRINTFORML %SAVESTR:ARG%陷入了【%EX_TALENTNAME:0%】
;IF TALENT:ARG:76 || TALENT:ARG:85
;	PRINTFORML %SAVESTR:ARG%可以作为【%EX_TALENTNAME:3%】了
;	EX_TALENT:ARG:3 = 1
;ENDIF

@TRANSFERAPP, ARG

;等级、战斗力防御力、体力气力交换
SWAP CFLAG:ARG:9, CFLAG:MASTER:9
SWAP CFLAG:ARG:11, CFLAG:MASTER:11
SWAP CFLAG:ARG:12, CFLAG:MASTER:12
SWAP CFLAG:ARG:13, CFLAG:MASTER:13
SWAP CFLAG:ARG:14, CFLAG:MASTER:14
SWAP BASE:ARG:0, BASE:MASTER:0
SWAP BASE:ARG:1, BASE:MASTER:1
SWAP MAXBASE:ARG:0, MAXBASE:MASTER:0
SWAP MAXBASE:ARG:1, MAXBASE:MASTER:1


;成为勇者的经历
SWAP TALENT:ARG:315, TALENT:MASTER:315
SWAP TALENT:ARG:316, TALENT:MASTER:316

;好感度、调教次数、爱慕、癖
SWAP CFLAG:ARG:10, CFLAG:MASTER:10
SWAP CFLAG:ARG:2, CFLAG:MASTER:2
SWAP TALENT:ARG:85, TALENT:MASTER:85
SWAP TALENT:ARG:86, TALENT:MASTER:86
SWAP TALENT:ARG:76, TALENT:MASTER:76
SWAP TALENT:ARG:魔界知识, TALENT:MASTER:魔界知识
SWAP TALENT:ARG:淫魔知识, TALENT:MASTER:淫魔知识
SWAP TALENT:ARG:魔虫知识, TALENT:MASTER:魔虫知识
SWAP TALENT:ARG:调合知识, TALENT:MASTER:调合知识
SWAP TALENT:ARG:癖, TALENT:MASTER:癖

;基本状态
CFLAG:ARG:1 = 0
CFLAG:MASTER:1 = 0

;名称交换
SWAP NAME:MASTER, NAME:ARG
SWAP CALLNAME:MASTER, CALLNAME:ARG
SWAP SAVESTR:MASTER, SAVESTR:ARG
SWAP CFLAG:ARG:6, CFLAG:MASTER:6
SAVESTR:ARG = %CALLNAME:ARG%
SAVESTR:MASTER = %CALLNAME:MASTER%

;CALL BODYLOCK, ARG
;CALL PERSONALOCK, ARG
;角色交换
;SWAPCHARA MASTER, ARG

;RELATION表交换
CALL RELATION_SWAP_REBUILD(MASTER, ARG)

CALL PERSONALOCK, ARG

RETURN MASTER

;-----------------------------------------------------
@BODYCHECK_MAOU, ARG
;-----------------------------------------------------
;种族年龄
SIF CFLAG:MASTER:452 == 0 && CFLAG:MASTER:314 > 0
	CFLAG:MASTER:452 = 666
SIF CFLAG:MASTER:452 == 0 && CFLAG:MASTER:314 == 0
	CFLAG:MASTER:452 = 21
;肉体年龄
SIF CFLAG:MASTER:451 == 0
	CFLAG:MASTER:451 = 21
;身体概况
IF CFLAG:MASTER:453 == 0 || CFLAG:MASTER:454 == 0 || CFLAG:MASTER:455 == 0 || CFLAG:MASTER:456 == 0 || CFLAG:MASTER:457 == 0
	CALL CHAR_SIZE_GENERATE, MASTER
	CFLAG:MASTER:453 = RESULT:2
	CFLAG:MASTER:454 = RESULT:3
	CFLAG:MASTER:455 = RESULT:4
	CFLAG:MASTER:456 = RESULT:5
	CFLAG:MASTER:457 = RESULT:6
ENDIF
;外貌
IF TALENT:MASTER:300 == 0 || TALENT:MASTER:301 == 0 || TALENT:MASTER:302 == 0 || TALENT:MASTER:303 == 0 || TALENT:MASTER:304 == 0
	TALENT:MASTER:300 = 5
	TALENT:MASTER:301 = 1
	TALENT:MASTER:302 = 300
	TALENT:MASTER:303 = 1
	TALENT:MASTER:304 = 1
ENDIF
IF TALENT:MASTER:305 == 0 || TALENT:MASTER:306 == 0 || TALENT:MASTER:307 == 0 
	TALENT:MASTER:305 = 3
	TALENT:MASTER:306 = 3
	TALENT:MASTER:307 = 4
ENDIF
IF TALENT:MASTER:308 == 0 || TALENT:MASTER:309 == 0 || TALENT:MASTER:310 == 0
	TALENT:MASTER:308 = 150
	TALENT:MASTER:309 = 3
	TALENT:MASTER:310 = 100
ENDIF
;-----------------------------------------------------
@PERSONALOCK, ARG
;-----------------------------------------------------
#DIM TC
TC = 0
FOR TC, 10, 19
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 20, 29
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 30, 38
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 50, 58
	SIF TC == 55
		CONTINUE
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 60, 65
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 69, 73
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 79, 84
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 87, 89
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 91, 94
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

SWAP TALENT:MASTER:117, TALENT:ARG:117
SWAP TALENT:MASTER:118, TALENT:ARG:118
SWAP TALENT:MASTER:123, TALENT:ARG:123
SWAP TALENT:MASTER:127, TALENT:ARG:127

FOR TC, 131, 132
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

SWAP TALENT:MASTER:134, TALENT:ARG:134

FOR TC, 136, 137
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 140, 143
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 150, 152
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 155, 156
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 160, 179
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT
FOR TC, 101, 201
	SWAP EX_TALENT:MASTER:TC, EX_TALENT:ARG:TC
NEXT

SWAP TALENT:MASTER:182, TALENT:ARG:182

FOR TC, 200, 212
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 221, 223
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 240, 244
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 250, 253
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

SWAP TALENT:MASTER:258, TALENT:ARG:258

FOR TC, 281, 283
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 291, 293
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT

FOR TC, 470, 486
	SWAP TALENT:MASTER:TC, TALENT:ARG:TC

NEXT



;ABL
TC = 0
FOR TC, 10, 18
	SWAP ABL:MASTER:TC, ABL:ARG:TC

NEXT

FOR TC, 20, 23
	SWAP ABL:MASTER:TC, ABL:ARG:TC

NEXT

FOR TC, 100, 103
	SWAP ABL:MASTER:TC, ABL:ARG:TC

NEXT
;-----------------------------------------------------
@BODYLOCK, ARG
;-----------------------------------------------------
#DIM TC

;基本素质[处女童贞]（TALENT:0-1）
;FOR TC, 0, 2
;SWAP TALENT:MASTER:TC, TALENT:ARG:TC
;TC += 1
;NEXT

;基本素质[体质]（TALENT:40-49）
FOR TC, 40, 49
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;基本素质[特殊性癖]（TALENT:74-78）
FOR TC, 74, 78
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;基本素质[魅了]（TALENT:91-93）
FOR TC, 91, 93
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;基本素质[身体特征]（TALENT:99-116,119-159）
FOR TC, 99, 116
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT
FOR TC, 119, 159
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;基本素质[卖春类]（TALENT:180-187）
;FOR TC, 180, 187
;SWAP TALENT:MASTER:TC, TALENT:ARG:TC
;TC += 1
;NEXT

;基本素质[体调不良]（TALENT:190-193）
FOR TC, 190, 193
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;基本素质[职业]（TALENT:200-222,先把魔界将军和神官两个上位除外）
FOR TC, 200, 209
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

FOR TC, 220, 222
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;强化素质（TALENT:230-233）
FOR TC, 230, 233
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;战斗技能（TALENT:240-269,先固定住身体特征相关的）
FOR TC, 244, 249
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

FOR TC, 253, 269
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;素质[特殊素质]（TALENT:270-289,）
FOR TC, 271, 273
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

FOR TC, 275, 280
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;境遇特征（TALENT:290-319,癖好经历爱好除外）
FOR TC, 290, 318
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

FOR TC, 314, 314
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

FOR TC, 318, 319
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;家族构成（TALENT:320-324,）
FOR TC, 320, 324
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;魔王知识（TALENT:325-328,锁定肉芽诅咒）
FOR TC, 326, 1
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;妊娠素质（TALENT:340-342,锁定肉芽诅咒）
FOR TC, 340, 3
SWAP TALENT:MASTER:TC, TALENT:ARG:TC
TC += 1
NEXT

;精英技巧（TALENT:400-499,）

;身体、杂项（CFLAG:410-610）
FOR TC, 410, 201
SWAP CFLAG:MASTER:TC, CFLAG:ARG:TC
TC += 1
NEXT

;身体经验/关系/衣着（CFLAG:7,21-59)
FOR TC, 7, 1
SWAP CFLAG:MASTER:TC, CFLAG:ARG:TC
TC += 1
NEXT

FOR TC, 21, 39
SWAP CFLAG:MASTER:TC, CFLAG:ARG:TC
TC += 1
NEXT

;身体附加（CFLAG:70-149)
FOR TC, 70, 80
SWAP CFLAG:MASTER:TC, CFLAG:ARG:TC
TC += 1
NEXT

;灵魂错位
@SOUL_DISLOCATION
#DIM TEMP
IF EX_TALENT:0 > 3
	TEMP = 3
ELSE
	TEMP = EX_TALENT:0
ENDIF

IF EX_TALENT:0 && !RAND(TEMP + 1)
	EX_TALENT:0--
	SIF EX_TALENT:0 < 0
		EX_TALENT:0 = 0
	SIF EX_TALENT:0 == 0
		PRINTFORML %SAVESTR:TARGET%从【%EX_TALENTNAME:0%】中恢复了
ENDIF
