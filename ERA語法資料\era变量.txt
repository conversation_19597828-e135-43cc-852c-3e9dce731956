﻿【ERA脚本中的变量】


== 变量的类型 ==


根据不同标准，变量可以分为不同类型。
从值类型上来说，ERA中的变量可以全部分为两种：
　　值为int64的【数值类型变量】，例如LOCAL, ARG；
　　值为string的【字符串类型变量】，例如LOCALS, ARGS。


从作用范围来说，分为三种：
　　仅作用于当前函数的【局部变量】，例如LOCAL，ARGS。
　　作用于当前游戏存档所有函数的【广域变量】，例如NO，ABL，TALENT，FLAG。
　　不仅在所有函数中生效，并且可以在不同存档之间共享的【全局变量】，例如GLOABL, GLOABLS。


从声明的主体可以分为【用户自定义变量】【系统内置变量】


从数据结构上来说，除了少数例外，ERA中的变量全部为数组。
所谓“数组”（Array）是指包含复数个元素的集合，这个集合通过“索引”（Index）来“指向”其中的某个子元素。
例如“LOCAL:0”，“FLAG:81”，“FLAG:人間界の侵攻度”，“FLAG:ARG”


Emuera中根据变量“是否用于记录角色状态”进一步划分为
　　【数组变量】数组大小固定的变量，例如LOCAL, ARGS，FLAG，SAVESTR。
　　【角色变量】数组的元素数量会随游戏中角色删减而变化的变量，CALLNAME, ABL, TALENT, CFLAG。


根据数组的“维”（元），变量可以划分因此分为一元变量、二元变量、三元变量、、、、多元变量。

（数轴是一个一维空间，平面直角坐标系是一个二维空间，空间直角坐标系是一个三维空间）
当变量为多元时，我们需要多个索引（Index）才能指定数组中的一个元素。

（这和空间直角坐标系需要(x,y,z)才能确定空间中一点的道理一样）


== 变量的书写 ==


ERA中的变量使用冒号“:”来分隔变量名与索引，索引与索引。
例如“ABL:ARG:1”，“CFLAG:TARGET:15”，“DA:1:0”
在这种书写规则下，索引也被Emuera通称为变量的【参数】。
“ABL:ARG:1”中【ABL】为变量名，【ARG】为变量的第一参数，【1】为第二参数。


因为变量的参数（索引）可以另一个变量，因此会出现嵌套的情况。
“ABL:(ARG:0):1”中【ABL】为变量名，【ARG:0】为ABL的第一参数，【1】为ABL的第二参数。
同时【ARG】为变量名，【0】为ARG的第一参数。


变量的参数在特定情况下可以省略，这种省略非常普遍，这有两种情况：
* 一元【数组变量】省略参数0。
* 【角色变量】省略第一参数TARGET。


例如，LOCAL为一元数组变量，因此LOCAL:0可以简写为LOCAL。
ARG为一元数组变量，因此“ABL:(ARG:0):1”实际上与“ABL:ARG:1”等同。
而DA虽然是数组变量，但是多元的，DA:0:1或DA:1:0“不能”简写为DA:1。
而NAME虽然是一元的，但不是【数组变量】，因此NAME:0不能简写成NAME


例如，TALENT为二元数组，同时为角色变量，因此TALENT:TARGET:5可以简写为TALENT:5。
同理，CFLAG:TARGET:21可以简写为CFLAG:21。
同理，CALLNAME:TARGET可以简写为CALLNAME。




== 人物数据的存储 ==

与C++等面向对象的语言不通，ERA中没有“类/对象”的概念。因此ERA将人物数据分散到多个变量来存储，这些变量的第一维对应着游戏登录的所有角色的序号，随着游戏中角色的删减而变化，这些变量就是众多的【角色变量】。

例如CALLNAME是一个系统内置的、字符串类型的、一元的角色变量。它记录了每个人物被称呼的名字（呼名）。
CALLNAME:1表示1号角色（游戏登录的第2个角色）的称呼。

再例如ABL是一个系统内置的、数值类型的、二元的角色变量，记录了某个人物的一系列能力的等级（例如C感觉）。ABL:2:10表示2号角色的编号10的能力数值。


也因为如此，ERB脚本中经常用一个数字来代表某个角色，然后这个数字在多个函数中传递。
当需要获取这个角色


== 函数参数变量 ==

与LOCAL、LOCALS类似，ARG、ARGS是Emuera为每个函数预声明的局部变量。
特别的是，ARG、ARGS仅用于函数声明中的参数。
若函数声明中没有使用ARG，那么函数中任何出现的ARG都会导致Emuera抛出错误。


函数声明的示例：
“

@FOO_1	;不带参数的函数

;do something...

@FOO_2, ARG	;带一个数值类型参数的函数

;do something...

@FOO_3, ARG, ARG:1, ARGS, ARGS:1	;带两个数值类型参数和两个字符串类型参数的函数

;do something...

@FOO_4 (ARG:0, ARGS:0)		;函数声明也可以用括号封闭参数

;do something...

@FOO_5 (L_ID, L_STR)	;也可以不使用ARG参数
#DIM L_ID
#DIMS L_STR

”

示例函数的调用
“
CALL FOO_1
CALL FOO_2, 233
CALL FOO_3, 2, 33, "二", "二三三"
CALL FOO_4 (2, "二")
CALL FOO_5 (33, "二三三")
”

一个示例函数
“
@PRINT_CHARA_INFO(ARG)

PRINTFORM {CALLNAME:ARG}是个萌哒哒的

IF TALENT:ARG:性别男
	PRINT 男孩子
ELSE
	PRINT 女孩子
ENDIF

PRINTW 哟~☆

”

