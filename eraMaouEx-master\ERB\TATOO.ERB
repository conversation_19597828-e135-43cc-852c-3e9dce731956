﻿
;キャラクターが持っている刺青の位置のリストを返す
;CALL GET_TATOO(CHARA)
;IF RESULT
;    FOR LOCAL, 1, RESULT + 1
;        PRINTFORML %TATOO_LOCATE_NAME(RESULT:LOCAL)%の刺青 … %CSTR:CHARA:(RESULT:LOCAL)%
;    NEXT
;ENDIF
;で内容のリストを取得できる
;
;ARG:0 = 対象キャラ
;RESULT = 刺青の数
;RESULT:1～10 = 刺青の位置(10～20)
@GET_TATOO, ARG:0
#LOCALSIZE 2
LOCAL:1 = 0
FOR LOCAL, 10, 20
	IF CSTR:(ARG:0):LOCAL != "" && CSTR:(ARG:0):LOCAL != "狂王的纹章"
		LOCAL:1 += 1
		RESULT:(LOCAL:1) = LOCAL
	ENDIF
NEXT
RETURN LOCAL:1


;刺青を入れる箇所の名前
;ARG:0 = 刺青の位置番号（10～19）
@TATOO_LOCATE_NAME, ARG:0
#FUNCTIONS
SELECTCASE ARG:0
CASE 10
	RETURNF "脸"
CASE 11
	RETURNF "胸"
CASE 12
	RETURNF "背"
CASE 13
	RETURNF "下腹"
CASE 14
	RETURNF "屁股"
CASE 15
	RETURNF "性器"
CASE 16
	RETURNF "肛門"
CASE 17
	RETURNF "大腿"
ENDSELECT
RETURNF ""


