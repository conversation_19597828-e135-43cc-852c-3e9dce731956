"""
MCP (Model Context Protocol) 工具调用接口
支持ERA游戏生成过程中的各种工具调用
"""

import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import inspect

class ToolType(Enum):
    FILE_OPERATION = "file_operation"
    ERA_SYNTAX = "era_syntax" 
    DATA_QUERY = "data_query"
    VALIDATION = "validation"
    GENERATION = "generation"

@dataclass
class ToolParameter:
    """工具参数定义"""
    name: str
    type: str  # string, integer, boolean, array, object
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[Any]] = None

@dataclass 
class ToolDefinition:
    """工具定义"""
    name: str
    description: str
    category: ToolType
    parameters: List[ToolParameter]
    function: Callable
    async_function: bool = False

class MCPToolManager:
    """MCP工具管理器"""
    
    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self.logger = logging.getLogger(__name__)
        
    def register_tool(self, 
                     name: str,
                     description: str,
                     category: ToolType,
                     parameters: List[ToolParameter],
                     function: Callable,
                     async_function: bool = False):
        """注册工具"""
        tool = ToolDefinition(
            name=name,
            description=description,
            category=category,
            parameters=parameters,
            function=function,
            async_function=async_function
        )
        
        self.tools[name] = tool
        self.logger.info(f"Registered tool: {name}")

    def get_tool_schema(self, tool_name: str) -> Dict[str, Any]:
        """获取工具schema"""
        if tool_name not in self.tools:
            return None

        tool = self.tools[tool_name]

        schema = {
            "name": tool.name,
            "description": tool.description,
            "category": tool.category.value,
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }

        for param in tool.parameters:
            schema["parameters"]["properties"][param.name] = {
                "type": param.type,
                "description": param.description
            }

            if param.enum:
                schema["parameters"]["properties"][param.name]["enum"] = param.enum

            if param.default is not None:
                schema["parameters"]["properties"][param.name]["default"] = param.default

            if param.required:
                schema["parameters"]["required"].append(param.name)

        return schema

    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """調用工具"""
        try:
            if tool_name not in self.tools:
                return {
                    "success": False,
                    "error": f"Tool '{tool_name}' not found"
                }

            tool = self.tools[tool_name]

            # 驗證參數
            validation_result = self._validate_parameters(tool, parameters)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Parameter validation failed: {validation_result['error']}"
                }

            # 調用工具函數
            if tool.async_function:
                result = await tool.function(**parameters)
            else:
                result = tool.function(**parameters)

            return {
                "success": True,
                "result": result
            }

        except Exception as e:
            self.logger.error(f"Tool execution failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _validate_parameters(self, tool: ToolDefinition, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """驗證工具參數"""
        try:
            # 檢查必需參數
            for param in tool.parameters:
                if param.required and param.name not in parameters:
                    return {
                        "valid": False,
                        "error": f"Missing required parameter: {param.name}"
                    }

            # 檢查參數類型（簡單驗證）
            for param_name, param_value in parameters.items():
                param_def = next((p for p in tool.parameters if p.name == param_name), None)
                if param_def:
                    # 這裡可以添加更詳細的類型驗證
                    pass

            return {"valid": True}

        except Exception as e:
            return {
                "valid": False,
                "error": str(e)
            }

    def get_all_schemas(self) -> List[Dict[str, Any]]:
        """獲取所有工具的 schema"""
        return [self.get_tool_schema(name) for name in self.tools.keys()]

# 全局工具管理器实例
_tool_manager = None

def get_tool_manager() -> MCPToolManager:
    """获取工具管理器实例"""
    global _tool_manager
    if _tool_manager is None:
        _tool_manager = MCPToolManager()
    return _tool_manager

def initialize_mcp_tools(db, rag_system) -> MCPToolManager:
    """初始化MCP工具"""
    global _tool_manager

    _tool_manager = MCPToolManager()

    # 註冊基本工具
    _register_basic_tools(_tool_manager)

    logging.info("Initialized MCP tools")
    return _tool_manager

def _register_basic_tools(manager: MCPToolManager):
    """註冊基本工具"""

    # 角色數據創建工具
    def create_character_data(character_info: Dict[str, Any]) -> Dict[str, Any]:
        """創建角色數據"""
        return {
            "id": character_info.get("id", 0),
            "name": character_info.get("name", "未命名角色"),
            "callname": character_info.get("callname", character_info.get("name", "未命名")),
            "nickname": character_info.get("nickname", ""),
            "description": character_info.get("description", ""),
            "base_stats": [100, 100, 0, 0, 0, 0],  # 基礎屬性
            "abilities": [0] * 100,  # 能力值
            "talents": [0] * 1000,   # 素質
            "flags": [0] * 1000,     # 標誌
            "marks": [0] * 1000,     # 標記
            "experience": [0] * 1000, # 經驗
            "parameters": [0] * 1000  # 參數
        }

    manager.register_tool(
        name="create_character_data",
        description="創建角色數據結構",
        category=ToolType.GENERATION,
        parameters=[
            ToolParameter("character_info", "object", "角色信息")
        ],
        function=create_character_data
    )

    # CSV 格式化工具
    def format_csv_data(data: List[Dict[str, Any]], csv_type: str) -> str:
        """格式化CSV數據"""
        if csv_type == "Chara" and data:
            char = data[0]
            csv_content = f"""@TITLE
角色數據

@DATA
{char.get('id', 0)},{char.get('name', '未命名')},{char.get('callname', '')},{char.get('nickname', '')}
"""
            return csv_content

        return f"@TITLE\n{csv_type}數據\n\n@DATA\n"

    manager.register_tool(
        name="format_csv_data",
        description="格式化CSV數據",
        category=ToolType.GENERATION,
        parameters=[
            ToolParameter("data", "array", "要格式化的數據"),
            ToolParameter("csv_type", "string", "CSV類型")
        ],
        function=format_csv_data
    )