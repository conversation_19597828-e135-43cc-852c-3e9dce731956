﻿;eraIM@Sから流用しました

;=================================================
;@SOURCE_CHECK関連の関数群２
;=================================================
;-------------------------------------------------
;女性同士の場合のチェック
;-------------------------------------------------
@SOURCE_LESBIAN_SEX_CHECK
;ABL:百合气质をみる
IF ABL:22 == 0
	TIMES SOURCE:8 , 0.80

	TIMES SOURCE:14 , 0.80

	TIMES SOURCE:13 , 0.90
ELSEIF ABL:22 == 1
	SOURCE:7 += 100
	TIMES SOURCE:8 , 0.60
	TIMES SOURCE:14 , 0.60

	TIMES SOURCE:13 , 0.75

	TIMES SOURCE:0 , 1.10
	TIMES SOURCE:1 , 1.10
	TIMES SOURCE:2 , 1.10
	TIMES SOURCE:5 , 1.10
	TIMES SOURCE:17 , 1.10
ELSEIF ABL:22 == 2
	SOURCE:7 += 200
	TIMES SOURCE:8 , 0.40
	TIMES SOURCE:14 , 0.40

	TIMES SOURCE:13 , 0.60

	TIMES SOURCE:0 , 1.20
	TIMES SOURCE:1 , 1.20
	TIMES SOURCE:2 , 1.20
	TIMES SOURCE:5 , 1.20
	TIMES SOURCE:17 , 1.20
ELSEIF ABL:22 == 3
	SOURCE:7 += 350

	TIMES SOURCE:8 , 0.25
	TIMES SOURCE:14 , 0.25

	TIMES SOURCE:13 , 0.45

	TIMES SOURCE:0 , 1.30
	TIMES SOURCE:1 , 1.30
	TIMES SOURCE:2 , 1.30
	TIMES SOURCE:5 , 1.30
	TIMES SOURCE:17 , 1.30
ELSEIF ABL:22 == 4
	SOURCE:7 += 500

	TIMES SOURCE:8 , 0.15
	TIMES SOURCE:14 , 0.15

	TIMES SOURCE:13 , 0.30

	TIMES SOURCE:0 , 1.40
	TIMES SOURCE:1 , 1.40
	TIMES SOURCE:2 , 1.40
	TIMES SOURCE:5 , 1.40
	TIMES SOURCE:17 , 1.40
ELSE
	SOURCE:7 += 750

	TIMES SOURCE:8 , 0.10
	TIMES SOURCE:14 , 0.10

	TIMES SOURCE:13 , 0.15

	TIMES SOURCE:0 , 1.60
	TIMES SOURCE:1 , 1.60
	TIMES SOURCE:2 , 1.60
	TIMES SOURCE:5 , 1.60
	TIMES SOURCE:17 , 1.60
ENDIF
	
;ABL:百合中毒をみる
IF ABL:33 == 0
	TIMES SOURCE:8 , 0.80
	TIMES SOURCE:14 , 0.80

	TIMES SOURCE:0 , 1.00
	TIMES SOURCE:1 , 1.00
	TIMES SOURCE:2 , 1.00
	TIMES SOURCE:5 , 1.00
	TIMES SOURCE:17 , 1.00
ELSEIF ABL:33 == 1
	TIMES SOURCE:8 , 0.60
	TIMES SOURCE:14 , 0.60

	TIMES SOURCE:0 , 1.20
	TIMES SOURCE:1 , 1.20
	TIMES SOURCE:2 , 1.20
	TIMES SOURCE:5 , 1.20
	TIMES SOURCE:17 , 1.20
ELSEIF ABL:33 == 2
	TIMES SOURCE:8 , 0.40
	TIMES SOURCE:14 , 0.40

	TIMES SOURCE:0 , 1.40
	TIMES SOURCE:1 , 1.40
	TIMES SOURCE:2 , 1.40
	TIMES SOURCE:5 , 1.40
	TIMES SOURCE:17 , 1.40
ELSEIF ABL:33 == 3
	TIMES SOURCE:8 , 0.30
	TIMES SOURCE:14 , 0.30

	TIMES SOURCE:0 , 1.60
	TIMES SOURCE:1 , 1.60
	TIMES SOURCE:2 , 1.60
	TIMES SOURCE:5 , 1.60
	TIMES SOURCE:17 , 1.60
ELSEIF ABL:33 == 4
	TIMES SOURCE:8 , 0.20
	TIMES SOURCE:14 , 0.20

	TIMES SOURCE:0 , 1.80
	TIMES SOURCE:1 , 1.80
	TIMES SOURCE:2 , 1.80
	TIMES SOURCE:5 , 1.80
	TIMES SOURCE:17 , 1.80
ELSE
	TIMES SOURCE:8 , 0.10
	TIMES SOURCE:14 , 0.10

	TIMES SOURCE:0 , 2.00
	TIMES SOURCE:1 , 2.00
	TIMES SOURCE:2 , 2.00
	TIMES SOURCE:5 , 2.00
	TIMES SOURCE:17 , 2.00
ENDIF

;調教者のABL:百合气质
IF ABL:PLAYER:22 == 0
	TIMES SOURCE:0 , 0.40
	TIMES SOURCE:1 , 0.40
	TIMES SOURCE:2 , 0.40
	TIMES SOURCE:3 , 0.20
	TIMES SOURCE:4 , 0.30
	TIMES SOURCE:5 , 0.30
	TIMES SOURCE:17 , 0.40
ELSEIF ABL:PLAYER:22 == 1
	TIMES SOURCE:0 , 0.70
	TIMES SOURCE:1 , 0.70
	TIMES SOURCE:2 , 0.70
	TIMES SOURCE:3 , 0.60
	TIMES SOURCE:4 , 0.70
	TIMES SOURCE:5 , 0.70
	TIMES SOURCE:17 , 0.70
ELSEIF ABL:PLAYER:22 == 2
	TIMES SOURCE:0 , 1.00
	TIMES SOURCE:1 , 1.00
	TIMES SOURCE:2 , 1.00
	TIMES SOURCE:3 , 1.00
	TIMES SOURCE:4 , 1.00
	TIMES SOURCE:5 , 1.00
	TIMES SOURCE:17 , 1.00
ELSEIF ABL:PLAYER:22 == 3
	TIMES SOURCE:0 , 1.10
	TIMES SOURCE:1 , 1.10
	TIMES SOURCE:2 , 1.10
	TIMES SOURCE:3 , 1.40
	TIMES SOURCE:4 , 1.30
	TIMES SOURCE:5 , 1.30
	TIMES SOURCE:17 , 1.10
ELSEIF ABL:PLAYER:22 == 4
	TIMES SOURCE:0 , 1.20
	TIMES SOURCE:1 , 1.20
	TIMES SOURCE:2 , 1.20
	TIMES SOURCE:3 , 1.80
	TIMES SOURCE:4 , 1.60
	TIMES SOURCE:5 , 1.60
	TIMES SOURCE:17 , 1.20
ELSE
	TIMES SOURCE:0 , 1.30
	TIMES SOURCE:1 , 1.30
	TIMES SOURCE:2 , 1.30
	TIMES SOURCE:3 , 2.50
	TIMES SOURCE:4 , 2.00
	TIMES SOURCE:5 , 2.00
	TIMES SOURCE:17 , 1.30
ENDIF

;調教者のABL:百合中毒
IF ABL:PLAYER:33 == 1
	TIMES SOURCE:0 , 1.10
	TIMES SOURCE:1 , 1.10
	TIMES SOURCE:2 , 1.10
	TIMES SOURCE:3 , 1.50
	TIMES SOURCE:4 , 1.50
	TIMES SOURCE:5 , 1.50
	TIMES SOURCE:17 , 1.10
ELSEIF ABL:PLAYER:33 == 2
	TIMES SOURCE:0 , 1.20
	TIMES SOURCE:1 , 1.20
	TIMES SOURCE:2 , 1.20
	TIMES SOURCE:3 , 2.00
	TIMES SOURCE:4 , 2.00
	TIMES SOURCE:5 , 2.00
	TIMES SOURCE:17 , 1.20
ELSEIF ABL:PLAYER:33 == 3
	TIMES SOURCE:0 , 1.40
	TIMES SOURCE:1 , 1.40
	TIMES SOURCE:2 , 1.40
	TIMES SOURCE:3 , 2.50
	TIMES SOURCE:4 , 2.50
	TIMES SOURCE:5 , 2.50
	TIMES SOURCE:17 , 1.40
ELSEIF ABL:PLAYER:33 == 4
	TIMES SOURCE:0 , 1.60
	TIMES SOURCE:1 , 1.60
	TIMES SOURCE:2 , 1.60
	TIMES SOURCE:3 , 3.50
	TIMES SOURCE:4 , 3.00
	TIMES SOURCE:5 , 3.00
	TIMES SOURCE:17 , 1.60
ELSE
	TIMES SOURCE:0 , 1.80
	TIMES SOURCE:1 , 1.80
	TIMES SOURCE:2 , 1.80
	TIMES SOURCE:3 , 5.00
	TIMES SOURCE:4 , 4.00
	TIMES SOURCE:5 , 4.00
	TIMES SOURCE:17 , 1.80
ENDIF

;調教者の克制
IF TALENT:PLAYER:20
	TIMES SOURCE:4 , 0.50
	TIMES SOURCE:5 , 0.50
ENDIF
;
;-------------------------------------------------
;男性同士の場合のチェック
;-------------------------------------------------
@SOURCE_GAY_SEX_CHECK
;ABL:ホモっ気をみる
IF ABL:23 == 0
	TIMES SOURCE:8 , 4.00
	TIMES SOURCE:14 , 4.00

	TIMES SOURCE:5 , 0.50
ELSEIF ABL:23 == 1
	SOURCE:7 += 10

	TIMES SOURCE:8 , 2.00
	TIMES SOURCE:14 , 2.00

	TIMES SOURCE:5 , 0.70

	TIMES SOURCE:0 , 1.10
	TIMES SOURCE:1 , 1.10
	TIMES SOURCE:2 , 1.10
	TIMES SOURCE:17 , 1.10
ELSEIF ABL:23 == 2
	SOURCE:7 += 40

	TIMES SOURCE:8 , 1.40
	TIMES SOURCE:14 , 1.40

	TIMES SOURCE:5 , 0.90

	TIMES SOURCE:0 , 1.20
	TIMES SOURCE:1 , 1.20
	TIMES SOURCE:2 , 1.20
	TIMES SOURCE:17 , 1.20
ELSEIF ABL:23 == 3
	SOURCE:7 += 100

	TIMES SOURCE:8 , 1.00
	TIMES SOURCE:14 , 1.00

	TIMES SOURCE:5 , 1.10

	TIMES SOURCE:0 , 1.30
	TIMES SOURCE:1 , 1.30
	TIMES SOURCE:2 , 1.30
	TIMES SOURCE:17 , 1.30
ELSEIF ABL:23 == 4
	SOURCE:7 += 200

	TIMES SOURCE:8 , 0.70
	TIMES SOURCE:14 , 0.70

	TIMES SOURCE:5 , 1.20

	TIMES SOURCE:0 , 1.40
	TIMES SOURCE:1 , 1.40
	TIMES SOURCE:2 , 1.40
	TIMES SOURCE:17 , 1.40
ELSEIF ABL:23 == 5
	SOURCE:7 += 350

	TIMES SOURCE:8 , 0.50
	TIMES SOURCE:14 , 0.50

	TIMES SOURCE:5 , 1.30

	TIMES SOURCE:0 , 1.50
	TIMES SOURCE:1 , 1.50
	TIMES SOURCE:2 , 1.50
	TIMES SOURCE:17 , 1.50
ENDIF

;調教者の克制
IF TALENT:PLAYER:20
	TIMES SOURCE:4 , 0.50
	TIMES SOURCE:5 , 0.50
ENDIF
;-------------------------------------------------
;親族関係の判定
;-------------------------------------------------
;现在のTARGETから見てPLAYERが何にあたるかの判定をTFLAG:14に代入
;（0:親族関係无. 1:父か母, 2:息子か娘, 3:兄か姉, 4:弟か妹, 5:従姉,6:従弟）
;親族判定は、@SOURCE_CHECK以外にも
;あっちこっちの関数で使ってるので注意！
;-------------------------------------------------
@INCEST

TFLAG:14 = 0

;関数の汎用性を考えて、敢えてREPEAT文の使用は避ける
SIF CFLAG:21 && CFLAG:21 % 100 == NO:PLAYER
	TFLAG:14 = (CFLAG:21 / 100) + 1
SIF CFLAG:22 && CFLAG:22 % 100 == NO:PLAYER
	TFLAG:14 = (CFLAG:22 / 100) + 1
SIF CFLAG:23 && CFLAG:23 % 100 == NO:PLAYER
	TFLAG:14 = (CFLAG:23 / 100) + 1
SIF CFLAG:24 && CFLAG:24 % 100 == NO:PLAYER
	TFLAG:14 = (CFLAG:24 / 100) + 1
SIF CFLAG:25 && CFLAG:25 % 100 == NO:PLAYER
	TFLAG:14 = (CFLAG:24 / 100) + 1

;主人が父親か母親である場合の処理
IF PLAYER == MASTER && (CFLAG:21 == -1 || CFLAG:22 == -1 || CFLAG:23 == -1 || CFLAG:24 == -1 || CFLAG:25 == -1)
	TFLAG:14 = 1
ENDIF
;
;
;
;-------------------------------------------------
;灵魂错位处理
;-------------------------------------------------
@SOUL_DISLOCATION_DEBUFF
#DIM TEMP
FOR LOCAL,0,19
	TEMP = 100 - 15 * EX_TALENT:0
	SOURCE:LOCAL *= TEMP
	SOURCE:LOCAL /= 100
NEXT
