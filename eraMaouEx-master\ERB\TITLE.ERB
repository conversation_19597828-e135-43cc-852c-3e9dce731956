﻿@SYSTEM_TITLE
$PRINT_TITLE
RESETCOLOR

[IF_DEBUG]
[ELSE]
CLEARLINE LINECOUNT
[ENDIF]

LOADGLOBAL

LOCALS = {GAMEBASE_VERSION / 1000}.{GAMEBASE_VERSION - GAMEBASE_VERSION /1000 *1000}
;WINDOW_TITLE = %GAMEBASE_TITLE% %LOCALS%

DRAWLINE

ALIGNMENT CENTER
HTML_PRINT "<shape type='space' param='2180'><img src='TITLE' width='2000' height='360' ypos='-30'>"
PRINTL 
PRINTL 
SETFONT "ARIEL BLACK"
FONTBOLD
;PRINTFORML %GAMEBASE_TITLE%

PRINTFORML Ver%LOCALS%
PRINTFORML %GAMEBASE_AUTHOR%
SIF STRLENS(GAMEBASE_YEAR) > 0
	PRINTFORML (%GAMEBASE_YEAR%)
PRINTL
FONTREGULAR
IF GLOBAL:99 == 0
PRINTFORML ※本版本由Delicious基于谦悟制作的0.60EX制作，仅作为汉化交流及代码练习使用，请于18小时内删除※
PRINTFORML ※私自传播及运行本程序所产生的一切后果，请自行负责※
PRINTFORML ※本作在许多方面已偏离原作较远，敬请留意※
PRINTFORML 
PRINTFORML 地文及指令素质汉化：谦悟、匿名神人、干掉人龙、魔法少女张春华、幽灵 轻^3、文文、撸撸睡、Delicious
PRINTFORML 技术支持：风飏、stick、红茶与枪、你见不到我、谦悟、雄霸天、看飞机、墨镜马赛克、不科学灰骑士、毒菇、Delicious
PRINTFORML 测试校对及润色：钢笔、谦悟、猫出没注意、醉饮千殇不知愁、imightcatchsth
PRINTFORML
PRINTFORML 口上组成员：
PRINTFORML 大众性格：谦悟、文文、匿名神人、干掉人龙、歪闷林、華胥の亡靈、Delicious
PRINTFORML 专用口上：毛线夜、谦悟、幽灵 轻^3、魔法少女张春华、文文、喝奶茶呛着了、社会废人
PRINTFORML 原创口上及剧情：白告姬、红茶与枪、幽灵 轻^3、毛线夜、谦悟

PRINTFORML 
PRINTFORML ※衷心感谢首席代码君：风飏。没有昨天的你就没有今天的我，在此祝愿你的明天会更好※
PRINTFORM ※特别鸣谢安东尼，感谢日本网友的原创，也感谢群内所有人的测试与指导※
PRINTBUTTON " <<", 9
PRINTL
ELSE
PRINTFORML ※本版本由谦悟制作，仅作为汉化交流及代码练习使用，请于18小时内删除※
PRINTFORM ※私自传播及运行本程序所产生的一切后果，请自行负责
PRINTBUTTON " >>", 9
PRINTL
ENDIF
PRINTFORM %GAMEBASE_INFO%
IF GLOBAL:98 == 0
	PRINTFORML
	PRINTFORM 版本推进出问题 
	PRINTBUTTON ">>", 8
ELSE
	PRINTFORML
	PRINTFORM 群里@Delicious或者小窗
	PRINTBUTTON "<<", 8
ENDIF

SETFONT
PRINTFORML
DRAWLINE


PRINTFORML [0] 旧的奴隶
PRINTFORML [1] 新的猎物
INPUT

IF RESULT == 1
	CLEARLINE 1
	DRAWLINE
	ALIGNMENT LEFT
	RESETDATA
	ADDCHARA 0
	CALL ADDCHARA_EX, CHARANUM-1
	BEGIN FIRST
ELSEIF RESULT == 0
	CLEARLINE 1
	DRAWLINE
	ALIGNMENT LEFT
	CALL SYSTEM_LOADGAME
	RESTART
ELSEIF RESULT == 8
	GLOBAL:98 = (GLOBAL:98 + 1)%2
	SAVEGLOBAL
	RESTART
ELSEIF RESULT == 9
	GLOBAL:99 = (GLOBAL:99 + 1)%2
	SAVEGLOBAL
	RESTART
ELSE
	RESTART
ENDIF
RETURN RESULT
