﻿
;=================================================
;   SYSTEM:读取処理関数
;-------------------------------------------------
@SYSTEM_LOADGAME
;-------------------------------------------------
#DIM L_LEN
#DIM L_POS
#DIM L_IDX
#DIM L_LINECOUNT

L_POS = L_POS < 0 ? 0 # L_POS
L_LEN = SAVENOS()
L_LINECOUNT = LINECOUNT

$DRAW_PAGE
REDRAW 0
CUSTOMDRAWLINE =
PRINTL 【读取存档】要载入以下哪个存档？
DRAWLINE

CALL SYSTEM_LIST_DATA, L_POS, L_POS + L_LEN

CHKDATA 99
IF RESULT == 0
	DRAWLINE 
	SIF 99 == LASTLOAD_NO
		SETCOLORBYNAME DEEPSKYBLUE
	PRINTFORML  [{99,2}] %RESULTS%
	RESETCOLOR
	DRAWLINE 
ELSE
	DRAWLINE 
ENDIF

PRINTFORMLC [101] 上一页
PRINTFORMLC [100] 返回
PRINTFORMLC [102] 下一页
PRINTL

REDRAW 1
$INPUT_LOOP
INPUT 99

L_IDX = RESULT
SELECTCASE L_IDX
;[100] 返回
CASE 100
	RETURN L_POS

;[101] 上一页
CASE 101
	IF L_POS - L_LEN >= 0
		L_POS -= L_LEN
		CLEARLINE LINECOUNT - L_LINECOUNT
		GOTO DRAW_PAGE
	ENDIF

;[102] 下一页
CASE 102
	IF L_POS + L_LEN < 99
		L_POS += L_LEN
		CLEARLINE LINECOUNT - L_LINECOUNT
		GOTO DRAW_PAGE
	ENDIF

;- 範囲内
CASE 0 TO 99
	CHKDATA L_IDX
	IF RESULT == 0
		REDRAW 1
		;- 读取(実行後、@EVENTLOADへ遷移)
		LOADDATA L_IDX
		SIF EX_FLAG:2801 < 10
			EX_FLAG:2801 = 10
		RETURN L_POS
	ENDIF
ENDSELECT

;无效输入
CLEARLINE 1
GOTO INPUT_LOOP


;=================================================
;   SYSTEM:保存処理関数
;-------------------------------------------------
@SYSTEM_SAVEGAME
;-------------------------------------------------
#DIM L_LEN
#DIM L_POS
#DIM L_IDX
#DIM L_LINECOUNT

L_POS = L_POS < 0 ? 0 # L_POS
L_LEN = SAVENOS()
L_LINECOUNT = LINECOUNT

$DRAW_PAGE
REDRAW 0
CUSTOMDRAWLINE =
PRINT 【保存存档】
IF STRLENS(CSTR:MASTER:99) > 0
	PRINTFORM 当前的故事名为『%CSTR:MASTER:99%』，
ELSE
	PRINT 当前故事还没有名字，
ENDIF
PRINTL 要保存到以下哪个存档？
DRAWLINE

CALL SYSTEM_LIST_DATA, L_POS, L_POS + L_LEN

DRAWLINE 
PRINTFORMLC [200] 为故事命名
PRINTFORMLC [300] 删除存档
PRINTL
PRINTFORMLC [101] 上一页
PRINTFORMLC [100] 返回
PRINTFORMLC [102] 下一页
PRINTL

REDRAW 1
$INPUT_LOOP
INPUT

L_IDX = RESULT
SELECTCASE L_IDX
;[100] 返回
CASE 100
	RETURN L_POS

;[101] 上一页
CASE 101
	IF L_POS - L_LEN >= 0
		L_POS -= L_LEN
		CLEARLINE LINECOUNT - L_LINECOUNT
		GOTO DRAW_PAGE
	ENDIF

;[102] 下一页
CASE 102
	IF L_POS + L_LEN < 99
		L_POS += L_LEN
		CLEARLINE LINECOUNT - L_LINECOUNT
		GOTO DRAW_PAGE
	ENDIF

	
;[200] 为这个故事命名
CASE 200
	DRAWLINE
	GOTO SET_NAME

;[300] 删除存档
CASE 300
	CLEARLINE LINECOUNT - L_LINECOUNT
	CALL SYSTEM_DELDATA, L_POS
	L_POS = RESULT
	GOTO DRAW_PAGE
	
;- 範囲内
CASE 0 TO 98
	CHKDATA L_IDX
	IF RESULT == 0
		PRINTL 存档已经存在，确定要覆盖么？
		PRINTL [1] 确定    [0] 取消
		INPUT 0
		IF RESULT != 1
			CLEARLINE LINECOUNT - L_LINECOUNT
			GOTO DRAW_PAGE
		ENDIF
	ENDIF
	GOTO SAVE_GAME
ENDSELECT

;无效输入
CLEARLINE 1
GOTO INPUT_LOOP


$SAVE_GAME
SAVEDATA_TEXT = 
CALL SAVEINFO
LOCALS = %GETTIMES()% %SAVEDATA_TEXT%
SAVEDATA L_IDX, LOCALS
ARRAYSHIFT LASTSAVE_NO, 1, L_IDX
SAVEGLOBAL

PRINTFORMW 已将游戏保存为{L_IDX}号存档……
RETURN L_POS

$SET_NAME
PRINTFORM 请输入一个名称故事：
IF STRLENS(CSTR:MASTER:99) > 0
	LOCALS =（%CSTR:MASTER:99%）
	PRINTBUTTON LOCALS, CSTR:MASTER:99
ENDIF
PRINTL

INPUTS
IF STRLENS(RESULTS) > 32
	CSTR:MASTER:99 '= SUBSTRING(RESULTS,0,32)
	PRINTFORMW 将故事命名为『%RESULTS%』
ELSEIF STRLENS(RESULTS) > 0
	CSTR:MASTER:99 '= RESULTS
	PRINTFORMW 将故事命名为『%RESULTS%』
ELSE
	CSTR:MASTER:99 =
	PRINTFORMW 消去了故事的名字
ENDIF

; CLEARLINE LINECOUNT - L_LINECOUNT
GOTO DRAW_PAGE



;=================================================
;   SYSTEM:删除存档処理関数
;-------------------------------------------------
@SYSTEM_DELDATA, L_POS = -1
;-------------------------------------------------
#DIM L_LEN
#DIM L_POS
#DIM L_IDX
#DIM L_LINECOUNT

L_POS = L_POS < 0 ? 0 # L_POS
L_LEN = SAVENOS()
L_LINECOUNT = LINECOUNT

$DRAW_PAGE
REDRAW 0
CUSTOMDRAWLINE =
PRINTL 【删除存档】要删除以下哪个存档？
DRAWLINE

CALL SYSTEM_LIST_DATA, L_POS, L_POS + L_LEN

DRAWLINE 
PRINTL

PRINTFORMLC [101] 上一页
PRINTFORMLC [100] 返回
PRINTFORMLC [102] 下一页
PRINTL

REDRAW 1
$INPUT_LOOP
INPUT 99

L_IDX = RESULT
SELECTCASE L_IDX
;[100] 返回
CASE 100
	RETURN L_POS

;[101] 上一页
CASE 101
	IF L_POS - L_LEN >= 0
		L_POS -= L_LEN
		CLEARLINE LINECOUNT - L_LINECOUNT
		GOTO DRAW_PAGE
	ENDIF

;[102] 下一页
CASE 102
	IF L_POS + L_LEN < 99
		L_POS += L_LEN
		CLEARLINE LINECOUNT - L_LINECOUNT
		GOTO DRAW_PAGE
	ENDIF

;- 範囲内
CASE 0 TO 99
	CHKDATA L_IDX
	IF RESULT == 0
		PRINTL 确定要删除这个存档么？
		PRINTL [1] 确定    [0] 取消
		INPUT 0
		IF RESULT != 1
			CLEARLINE LINECOUNT - L_LINECOUNT
			GOTO DRAW_PAGE
		ELSE
			DELDATA L_IDX
			GOTO DRAW_PAGE
		ENDIF
	ENDIF
ENDSELECT

;无效输入
CLEARLINE 1
GOTO INPUT_LOOP

;=================================================
;   SYSTEM:显示存档列表
;-------------------------------------------------
@SYSTEM_LIST_DATA, ARG, ARG:1
;-------------------------------------------------
#DIM L_I

FOR L_I, ARG, ARG:1
	IF L_I >= 99
		PRINTL
		CONTINUE
	ENDIF
	
	SIF L_I == LASTLOAD_NO
		SETCOLORBYNAME DEEPSKYBLUE
	SIF L_I == LASTSAVE_NO
		SETCOLORBYNAME LIGHTGREEN
	
	CHKDATA L_I
	IF RESULT == 0
		PRINTFORML  [{L_I,2}] %RESULTS%
	ELSE
		SETCOLORBYNAME GRAY
		PRINTFORML  [{L_I,2}] ----
		RESETCOLOR
	ENDIF
	
	SIF L_I == LASTLOAD_NO || L_I == LASTSAVE_NO
		RESETCOLOR
NEXT

;=================================================
;   SYSTEM:读取结束関数: 兼容性检查
;-------------------------------------------------
@SYSTEM_LOADEND
;-------------------------------------------------
#DIM L_C
#DIM L_N
#DIM L_F_ANY	;有没有任何家族关系（-1没有任何）

;初始化固定名列表
CALL CHARA_NAME_INIT
;EX素质名初始化
CALL EX_TALENTNAME_INIT

; SIF LASTLOAD_VERSION >= GAMEBASE_VERSION
	; RETURN

L_F_ANY = -1


; 检查角色信息完整
FOR L_C, 0, CHARANUM
	
	A = L_C
	L_N = NO:L_C
	
	SELECTCASE L_N
	CASE 0
	;魔王
		EX_TALENT:L_C:200 = 1
	;勇者
	CASE 1 TO 16
	;精英部下
	CASE 201 TO 210
		;魔之刻印
		TALENT:L_C:254 = 1
		;等级补正		
		IF CFLAG:MASTER:9 > 2 
			LOCAL = CFLAG:MASTER:9 * 6
			LOCAL += RAND:(CFLAG:MASTER:9) * 2
			LOCAL /= 10
			LOCAL -= CFLAG:L_C:9
			IF LOCAL > 0
				REPEAT LOCAL
					CALL ST_UP, L_C
				REND
			ENDIF
		ENDIF
	;玛奥
	CASE 17
		;清除口上素质
		VARSET TALENT:L_C:0, 0, 160, 179
		;玛奥専用性格
		TALENT:L_C:165 = 1
	;莉莉
	CASE 24	
		;清除口上素质
		VARSET TALENT:L_C:0, 0, 160, 179
		;莉莉専用性格
		TALENT:L_C:171 = 1
	;扑克牌
	CASE 20 TO 23
		;口上素质由CSV决定
		FOR LOCAL,160,179
			TALENT:L_C:LOCAL = CSVTALENT(L_N,LOCAL)
		NEXT
	;贡品
	CASE 31 TO 35
		;口上素质由CSV决定
		FOR LOCAL,160,179
			TALENT:L_C:LOCAL = CSVTALENT(L_N,LOCAL)
		NEXT
		CFLAG:L_C:800 = L_N - 30
	ENDSELECT
	
	;年齢or身長などを表示する設定の場合は身体データを設定
	IF GETBIT(FLAG:5,12) || GETBIT(FLAG:5,15)
		SIF CFLAG:L_C:451 == 0 || CFLAG:L_C:453 == 0
			CALL CHAR_BODY_GENERATE_WAPPED, L_C
	ENDIF
	
	;能力者技能
	IF !(TALENT:L_C:275 || TALENT:L_C:276 || TALENT:L_C:277 || TALENT:L_C:278 || TALENT:L_C:279)
		;火の能力者
		SIF RAND:40 == 0
			TALENT:L_C:275 = 1
		;氷の能力者
		SIF RAND:40 == 0
			TALENT:L_C:276 = 1
		;雷の能力者
		SIF RAND:40 == 0
			TALENT:L_C:277 = 1
		;光の能力者
		SIF RAND:40 == 0
			TALENT:L_C:278 = 1
		;闇の能力者
		SIF RAND:40 == 0
			TALENT:L_C:279 = 1
	ENDIF
	
	;规范命名
	LOCALS '= SAVESTR:L_C
	CALL CHARA_NAME_DEFINE(L_C)
	SAVESTR:L_C '= LOCALS
	;SIF L_C != MASTER
		CALLNAME:L_C '= LOCALS
	
	;第一人称
	IF STRLENSU(CSTR:L_C:60) == 0
		CFLAG:L_C:450 = 0
		CALL RANDOM_SELF_CALL, L_C
	ENDIF
	
	SIF L_F_ANY <= 0
		L_F_ANY = RF_FIRST(L_C)

NEXT


;RELATION表检查
CALL RELATION_REBUILD

;注册家族
IF L_F_ANY <= 0 && LASTLOAD_VERSION >= GAMEBASE_VERSION
	FOR L_C, 1, CHARANUM
		SIF RAND:4
			CONTINUE
			
		CALL FAMILY_REGISTER(L_C)
		SIF RESULT > 0
			PRINTFORML %CALLNAME:L_C%找到了失散多年的%F_TYPE_TO_STR2(RESULT:1)%%CALLNAME:RESULT%
	NEXT
ENDIF


;莉莉与玛奥的家族关系
L_C = FINDCHARA(TALENT:0:村娘Ａ,1)
IF L_C > 0
	LOCAL = FINDCHARA(TALENT:0:村娘Ｂ,1)
	SIF LOCAL >= 0
		CALL FAMILY_SETBOTH(L_C,LOCAL,2)
ENDIF


PRINTL 兼容性修正中……
