"""
RAG系统实现 - 基于Ollama嵌入的ERA语法知识检索
"""

import os
import json
import logging
import asyncio
import numpy as np
import pickle
import re
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import requests
from datetime import datetime

try:
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    logging.warning("Optional dependencies missing. Install with: pip install numpy scikit-learn faiss-cpu")
    FAISS_AVAILABLE = False

from data.database import ERADatabase

class OllamaEmbedding:
    """Ollama嵌入服务客户端"""

    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.model = model or os.getenv("OLLAMA_EMBEDDING_MODEL", "nomic-embed-text")
        self.logger = logging.getLogger(__name__)

    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取文本嵌入向量"""
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model,
                    "prompt": text
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if "embedding" in result:
                    return result["embedding"]
                else:
                    self.logger.error(f"No embedding in response: {result}")
                    return None
            else:
                self.logger.error(f"Embedding request failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to get embedding: {e}")
            return None

    def get_embedding_sync(self, text: str) -> Optional[List[float]]:
        """同步获取文本嵌入向量"""
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model,
                    "prompt": text
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if "embedding" in result:
                    return result["embedding"]
                else:
                    self.logger.error(f"No embedding in response: {result}")
                    return None
            else:
                self.logger.error(f"Embedding request failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to get embedding: {e}")
            return None

    def test_connection(self) -> bool:
        """测试Ollama连接"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]

                if self.model in model_names:
                    self.logger.info(f"Ollama connection successful, embedding model {self.model} available")
                    return True
                else:
                    self.logger.warning(f"Embedding model {self.model} not found. Available models: {model_names}")
                    return False
            else:
                self.logger.error(f"Failed to connect to Ollama: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Ollama connection test failed: {e}")
            return False

class ERAKnowledgeBase:
    """ERA知识库管理类"""

    def __init__(self, db: ERADatabase, embedding_client: OllamaEmbedding):
        self.db = db
        self.embedding_client = embedding_client
        self.logger = logging.getLogger(__name__)
        self.index = None
        self.embeddings = []
        self.documents = []

        # 文件分类映射
        self.file_categories = {
            "ERA_ERH.txt": "variables",
            "ERA_excom.txt": "commands",
            "ERA_exfunc.txt": "functions",
            "ERA_form.txt": "formatting",
            "ERA_macro.txt": "macros",
            "ERA构文讲座.txt": "syntax",
            "ERA游戏的实现.txt": "game_design",
            "era_excom_WIKI.txt": "commands",
            "era_变量.txt": "variables",
            "era_文本规范.txt": "text_standards"
        }

    def _categorize_file(self, filename: str) -> str:
        """根据文件名分类"""
        return self.file_categories.get(filename, "general")

    def _chunk_text(self, text: str, max_length: int = 500, overlap: int = 50) -> List[str]:
        """将文本分块"""
        if len(text) <= max_length:
            return [text]

        chunks = []
        lines = text.split('\n')
        current_chunk = ""

        for line in lines:
            if len(current_chunk) + len(line) + 1 <= max_length:
                current_chunk += line + '\n'
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = line + '\n'

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符但保留中文和基本标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef.,;:!?()[\]{}"\'-]', '', text)
        return text.strip()

    def load_era_files(self, era_syntax_path: str) -> bool:
        """加载ERA语法文件到知识库"""
        try:
            era_path = Path(era_syntax_path)
            if not era_path.exists():
                self.logger.error(f"ERA syntax path not found: {era_syntax_path}")
                return False

            self.logger.info(f"Loading ERA syntax files from: {era_syntax_path}")

            # 清空现有知识库
            self._clear_knowledge_base()

            processed_files = 0
            total_chunks = 0

            for file_path in era_path.glob("*.txt"):
                try:
                    self.logger.info(f"Processing file: {file_path.name}")

                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    if not content.strip():
                        self.logger.warning(f"Empty file: {file_path.name}")
                        continue

                    # 清理文本
                    content = self._clean_text(content)

                    # 分类文件
                    category = self._categorize_file(file_path.name)

                    # 分块文本
                    chunks = self._chunk_text(content)

                    # 为每个块生成嵌入并保存
                    for i, chunk in enumerate(chunks):
                        if len(chunk.strip()) < 10:  # 跳过太短的块
                            continue

                        # 生成嵌入
                        embedding = self.embedding_client.get_embedding_sync(chunk)
                        if embedding is None:
                            self.logger.warning(f"Failed to generate embedding for chunk {i} in {file_path.name}")
                            continue

                        # 保存到数据库
                        embedding_bytes = pickle.dumps(embedding)
                        metadata = {
                            "file_name": file_path.name,
                            "chunk_index": i,
                            "chunk_count": len(chunks)
                        }

                        title = f"{file_path.stem} - 第{i+1}部分"
                        self.db.save_era_knowledge(
                            category=category,
                            title=title,
                            content=chunk,
                            embedding=embedding_bytes,
                            metadata=metadata
                        )

                        total_chunks += 1

                    processed_files += 1
                    self.logger.info(f"Processed {file_path.name}: {len(chunks)} chunks")

                except Exception as e:
                    self.logger.error(f"Error processing file {file_path.name}: {e}")
                    continue

            self.logger.info(f"Knowledge base loading completed: {processed_files} files, {total_chunks} chunks")

            # 构建FAISS索引
            self._build_faiss_index()

            return processed_files > 0

        except Exception as e:
            self.logger.error(f"Failed to load ERA files: {e}")
            return False

    def _clear_knowledge_base(self):
        """清空知识库"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM era_knowledge")
                conn.commit()
            self.logger.info("Knowledge base cleared")
        except Exception as e:
            self.logger.error(f"Failed to clear knowledge base: {e}")

    def _build_faiss_index(self):
        """构建FAISS索引"""
        if not FAISS_AVAILABLE:
            self.logger.warning("FAISS not available, using fallback similarity search")
            return

        try:
            # 从数据库加载所有嵌入
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id, embedding, content, category, title FROM era_knowledge WHERE embedding IS NOT NULL")
                results = cursor.fetchall()

            if not results:
                self.logger.warning("No embeddings found in database")
                return

            # 准备数据
            embeddings = []
            documents = []

            for result in results:
                doc_id, embedding_bytes, content, category, title = result
                if embedding_bytes:
                    embedding = pickle.loads(embedding_bytes)
                    embeddings.append(embedding)
                    documents.append({
                        "id": doc_id,
                        "content": content,
                        "category": category,
                        "title": title
                    })

            if not embeddings:
                self.logger.warning("No valid embeddings found")
                return

            # 构建FAISS索引
            embeddings_array = np.array(embeddings).astype('float32')
            dimension = embeddings_array.shape[1]

            self.index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
            self.index.add(embeddings_array)
            self.documents = documents

            self.logger.info(f"FAISS index built with {len(embeddings)} embeddings, dimension: {dimension}")

        except Exception as e:
            self.logger.error(f"Failed to build FAISS index: {e}")

    def search_similar(self, query: str, top_k: int = 5, category: str = None) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        try:
            # 生成查询嵌入
            query_embedding = self.embedding_client.get_embedding_sync(query)
            if query_embedding is None:
                self.logger.error("Failed to generate query embedding")
                return []

            if FAISS_AVAILABLE and self.index is not None:
                return self._faiss_search(query_embedding, top_k, category)
            else:
                return self._fallback_search(query_embedding, top_k, category)

        except Exception as e:
            self.logger.error(f"Search failed: {e}")
            return []

    def _faiss_search(self, query_embedding: List[float], top_k: int, category: str = None) -> List[Dict[str, Any]]:
        """使用FAISS进行搜索"""
        try:
            query_vector = np.array([query_embedding]).astype('float32')
            scores, indices = self.index.search(query_vector, min(top_k * 2, len(self.documents)))

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx == -1:  # FAISS返回-1表示无效索引
                    continue

                doc = self.documents[idx]
                if category and doc["category"] != category:
                    continue

                results.append({
                    "content": doc["content"],
                    "category": doc["category"],
                    "title": doc["title"],
                    "score": float(score),
                    "id": doc["id"]
                })

                if len(results) >= top_k:
                    break

            return results

        except Exception as e:
            self.logger.error(f"FAISS search failed: {e}")
            return []

    def _fallback_search(self, query_embedding: List[float], top_k: int, category: str = None) -> List[Dict[str, Any]]:
        """回退搜索方法（使用sklearn）"""
        try:
            # 从数据库获取所有嵌入
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                query_sql = "SELECT id, embedding, content, category, title FROM era_knowledge WHERE embedding IS NOT NULL"
                params = []

                if category:
                    query_sql += " AND category = ?"
                    params.append(category)

                cursor.execute(query_sql, params)
                results = cursor.fetchall()

            if not results:
                return []

            # 计算相似度
            embeddings = []
            documents = []

            for result in results:
                doc_id, embedding_bytes, content, cat, title = result
                if embedding_bytes:
                    embedding = pickle.loads(embedding_bytes)
                    embeddings.append(embedding)
                    documents.append({
                        "id": doc_id,
                        "content": content,
                        "category": cat,
                        "title": title
                    })

            if not embeddings:
                return []

            # 计算余弦相似度
            similarities = cosine_similarity([query_embedding], embeddings)[0]

            # 排序并返回top_k结果
            sorted_indices = np.argsort(similarities)[::-1][:top_k]

            search_results = []
            for idx in sorted_indices:
                doc = documents[idx]
                search_results.append({
                    "content": doc["content"],
                    "category": doc["category"],
                    "title": doc["title"],
                    "score": float(similarities[idx]),
                    "id": doc["id"]
                })

            return search_results

        except Exception as e:
            self.logger.error(f"Fallback search failed: {e}")
            return []

class ERARAGSystem:
    """ERA RAG系统主类"""

    def __init__(self, db_path: str = "era_agent.db",
                 ollama_base_url: str = None,
                 embedding_model: str = None):
        self.logger = logging.getLogger(__name__)
        self.db = ERADatabase(db_path)
        self.embedding_client = OllamaEmbedding(ollama_base_url, embedding_model)
        self.knowledge_base = None
        self.initialized = False

    def initialize(self, era_syntax_path: str) -> bool:
        """初始化RAG系统"""
        try:
            self.logger.info("Initializing ERA RAG system...")

            # 测试Ollama连接
            if not self.embedding_client.test_connection():
                self.logger.error("Ollama connection test failed")
                return False

            # 初始化数据库
            self.db.initialize_database()

            # 创建知识库
            self.knowledge_base = ERAKnowledgeBase(self.db, self.embedding_client)

            # 加载ERA语法文件
            if not self.knowledge_base.load_era_files(era_syntax_path):
                self.logger.error("Failed to load ERA syntax files")
                return False

            self.initialized = True
            self.logger.info("ERA RAG system initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize RAG system: {e}")
            return False

    def query(self, question: str, category: str = None, top_k: int = 5) -> Dict[str, Any]:
        """查询ERA语法知识"""
        if not self.initialized or not self.knowledge_base:
            return {
                "context": "RAG系统未初始化",
                "sources": [],
                "error": "System not initialized"
            }

        try:
            # 搜索相关文档
            results = self.knowledge_base.search_similar(question, top_k, category)

            if not results:
                return {
                    "context": "未找到相关的ERA语法信息",
                    "sources": [],
                    "error": "No relevant documents found"
                }

            # 构建上下文
            context_parts = []
            sources = []

            for result in results:
                context_parts.append(f"【{result['title']}】\n{result['content']}")
                sources.append({
                    "title": result["title"],
                    "category": result["category"],
                    "score": result["score"]
                })

            context = "\n\n".join(context_parts)

            return {
                "context": context,
                "sources": sources,
                "query": question,
                "category": category
            }

        except Exception as e:
            self.logger.error(f"Query failed: {e}")
            return {
                "context": f"查询出错: {e}",
                "sources": [],
                "error": str(e)
            }

    def get_era_guidance(self, task_type: str, specific_question: str = "") -> str:
        """获取ERA开发指导"""
        if not self.initialized:
            return "RAG系统未初始化，无法提供ERA语法指导。"

        try:
            # 根据任务类型构建查询
            query_map = {
                "erb_script": "ERB脚本编写 函数定义 变量使用",
                "csv_data": "CSV文件格式 数据结构 角色数据",
                "system_flow": "系统流程 游戏逻辑 状态管理",
                "character": "角色定义 属性设置 能力值",
                "game_logic": "游戏逻辑 事件处理 条件判断"
            }

            base_query = query_map.get(task_type, task_type)
            full_query = f"{base_query} {specific_question}".strip()

            # 查询相关信息
            result = self.query(full_query, top_k=3)

            if result.get("error"):
                return f"查询ERA语法信息时出错: {result['error']}"

            context = result.get("context", "")
            if not context or context == "未找到相关的ERA语法信息":
                return f"未找到关于'{task_type}'的相关ERA语法信息。"

            # 格式化返回结果
            guidance = f"根据ERA语法资料，关于'{task_type}'的指导信息：\n\n{context}"

            return guidance

        except Exception as e:
            self.logger.error(f"Failed to get ERA guidance: {e}")
            return f"获取ERA语法指导时出错: {e}"

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()

                # 总文档数
                cursor.execute("SELECT COUNT(*) FROM era_knowledge")
                total_docs = cursor.fetchone()[0]

                # 按类别统计
                cursor.execute("SELECT category, COUNT(*) FROM era_knowledge GROUP BY category")
                category_stats = dict(cursor.fetchall())

                # 有嵌入的文档数
                cursor.execute("SELECT COUNT(*) FROM era_knowledge WHERE embedding IS NOT NULL")
                embedded_docs = cursor.fetchone()[0]

                return {
                    "total_documents": total_docs,
                    "embedded_documents": embedded_docs,
                    "categories": category_stats,
                    "embedding_coverage": embedded_docs / total_docs if total_docs > 0 else 0
                }

        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}

# 全局RAG系统实例
_rag_system = None

def get_rag_system() -> ERARAGSystem:
    """获取RAG系统实例"""
    global _rag_system
    if _rag_system is None:
        _rag_system = ERARAGSystem()
    return _rag_system

def initialize_rag(era_syntax_path: str) -> bool:
    """初始化RAG系统"""
    try:
        rag = get_rag_system()
        return rag.initialize(era_syntax_path)
    except Exception as e:
        logging.error(f"Failed to initialize RAG: {e}")
        return False