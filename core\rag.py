"""
RAG系统实现 - 基于Ollama嵌入的ERA语法知识检索
"""

import os
import json
import logging
import asyncio
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import requests
from datetime import datetime

try:
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    import faiss
except ImportError:
    logging.warning("Optional dependencies missing. Install with: pip install numpy scikit-learn faiss-cpu")

from data.database import ERADatabase

class OllamaEmbedding:
    """Ollama嵌入服务客户端"""
    
    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.model = model or os.getenv("OLLAMA_EMBEDDING_MODEL", "nomic-embed-text")
        self.logger = logging.getLogger(__name__)
        
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本嵌入向量"""
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model,
                    "prompt": text
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if "embedding" in result:
                    return result["embedding"]
                else:
                    self.logger.error(f"No embedding in response: {result}")
                    return None
            else:
                self.logger.error(f"Embedding request failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to get embedding: {e}")
            return None

    def get_embedding_sync(self, text: str) -> List[float]:
        """同步获取文本嵌入向量"""
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model,
                    "prompt": text
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if "embedding" in result:
                    return result["embedding"]
                else:
                    self.logger.error(f"No embedding in response: {result}")
                    return None
            else:
                self.logger.error(f"Embedding request failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to get embedding: {e}")
            return None

    def test_connection(self) -> bool:
        """测试Ollama连接"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]

                if self.model in model_names:
                    self.logger.info(f"Ollama connection successful, embedding model {self.model} available")
                    return True
                else:
                    self.logger.warning(f"Embedding model {self.model} not found. Available models: {model_names}")
                    return False
            else:
                self.logger.error(f"Failed to connect to Ollama: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Ollama connection test failed: {e}")
            return False

class ERARAGSystem:
    """ERA RAG系统主类"""

    def __init__(self, db_path: str = "era_agent.db",
                 ollama_base_url: str = None,
                 embedding_model: str = None):
        self.logger = logging.getLogger(__name__)

    def initialize(self, era_syntax_path: str):
        """初始化RAG系统"""
        try:
            self.logger.info("ERA RAG system initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize RAG system: {e}")
            return False

    def get_era_guidance(self, task_type: str, specific_question: str = "") -> str:
        """获取ERA开发指导"""
        return "ERA语法参考信息暂不可用。"

# 全局RAG系统实例
_rag_system = None

def get_rag_system() -> ERARAGSystem:
    """获取RAG系统实例"""
    global _rag_system
    if _rag_system is None:
        _rag_system = ERARAGSystem()
    return _rag_system

def initialize_rag(era_syntax_path: str) -> bool:
    """初始化RAG系统"""
    try:
        rag = get_rag_system()
        return rag.initialize(era_syntax_path)
    except Exception as e:
        logging.error(f"Failed to initialize RAG: {e}")
        return False