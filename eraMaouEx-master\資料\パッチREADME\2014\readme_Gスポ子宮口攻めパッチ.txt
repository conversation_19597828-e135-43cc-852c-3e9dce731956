﻿
　　- 挿入Gスポ・子宮口攻めパッチ -　　2014.06.09

 eramaou0302で動作確認


■何なの？

　・実装されてるけど実行できない『挿入Gスポ攻め』と『挿入子宮口攻め』を使用可能にするパッチです。


■どうなるの？

　・条件『調教者の技巧3以上』
　 を満たした状態で同一セックス系コマンドを連続入力すると『挿入Gスポ攻め』『挿入子宮口攻め』へ自動で変化するようになります。

　・派生系コマンド(『正常位・キス』とか『後背位SP』とか)からも発動するようになっています。

　・体位変更時のメッセージも対応させました。(膣内から抜かずに、再び腰を…みたいなやつ)


■変更箇所■

　◆COMF5.ERB
　　-正常位系、後背位系コマンドを経由した『Gスポ・子宮口攻め』から『正常/後背位・胸愛撫』へ分岐する条件を追加

　◆COMF6.ERB
　　-正常位系コマンドを経由した『Gスポ・子宮口攻め』から『正常位・キス』へ分岐する条件を追加
　　-後背位系コマンドを経由した『Gスポ・子宮口攻め』から『立ちバック』へ分岐する条件を追加

　◆COMF40.ERB
　　-後背位系コマンドを経由した『Gスポ・子宮口攻め』から『後背位・スパンキング』へ分岐する条件を追加
　
　◆COMF20.ERB
　　-正常位SPから『Gスポ・子宮口攻め』へ分岐するよう条件を追記
　　-FLAG:71の判定をキャンセル

　◆COMF21.ERB
　　-後背位系コマンドから『Gスポ・子宮口攻め』へ分岐するよう条件を追記
　　-FLAG:71の判定をキャンセル

　◆COMF22.ERB
　　-FLAG:71の判定をキャンセル

　◆COMF23.ERB
　　-FLAG:71の判定をキャンセル

　◆COMF34.ERB
　　-FLAG:71の判定をキャンセル

　◆EVENT_TRAIN_MESSAGE_B.ERB
　　-体位変更時の調教メッセージを各体位の派生コマンドとそれを経由した『Gスポ・子宮口攻め』実行時に表示するよう条件を追加
　　-『Gスポ・子宮口攻め』実行時の調教メッセージを正常位・後背位の各派生コマンドからも表示するよう追記
　　-『正常位・キス』の調教メッセージが改行されていないのを修正


■メモ■

FLAG:71の判定キャンセルで実行自体はできるけど
調教中に流れが途切れるので色々繋げてみた

調教難易度的な事は全く考えてないので難易度下がるかもしれない

あんまりこういうの弄った事無いのでなんか色々間違ってたらごめんなさい

