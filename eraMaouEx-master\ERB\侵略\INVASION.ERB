﻿;-----------------------------------
;世界征服
;-----------------------------------

;---------------------------
@INVASION
#DIM AREA, 1
#DIM SINDO, 1
#DIM SINKOU, 1
#DIM INV_TYPE
#DIM YUSYA_I
#DIM TMP2_I
#DIM MON_ID
#DIM MON_ATK
#DIM MON_NUM
#DIM TEMP
;列表追加
#DIM NO_PAGE = 0
#DIM NUM_PAGE = 26
#DIM MAX_PAGE
#DIM T_LCOUNT
#DIM L_LCOUNT
;---------------------------

;地上征服後
CLEARLINE LINECOUNT
CUSTOMDRAWLINE =
IF FLAG:82
	    PRINTFORML 地上的魔界领土　侵攻度　　%BARSTR(FLAG:81, 10000, 50)%
	IF FLAG:87 >= 1
		PRINTFORML 黑暗精灵的领土　侵攻度　　%BARSTR(FLAG:86, 10000, 50)%
	ELSE
		PRINTFORML 精灵族的领域　  侵攻度　　%BARSTR(FLAG:86, 10000, 50)%
	ENDIF
	IF FLAG:89 >= 1
		PRINTFORML 混沌龙之山　    侵攻度　　%BARSTR(FLAG:88, 10000, 50)%
	ELSE
		PRINTFORML 龙之山脉　      侵攻度　　%BARSTR(FLAG:88, 10000, 50)%
	ENDIF
	IF FLAG:91 >= 1
		PRINTFORML 堕天使的淫界　  侵攻度　　%BARSTR(FLAG:90, 10000, 50)%
	ELSE
		PRINTFORML 天界　          侵攻度　　%BARSTR(FLAG:90, 10000, 50)%
	ENDIF
	IF (EX_FLAG:2810 >= 501 &&EX_FLAG:2810 < 540) || (EX_FLAG:2810 >= 541 && EX_FLAG:2810 < 560) 
		PRINTFORML 天神宫　        侵攻度　　%BARSTR(EX_FLAG:101, 10000, 50)%
	ELSEIF EX_FLAG:102 >= 4
		PRINTFORML 淫乱意志的神宫  侵攻度　　%BARSTR(EX_FLAG:101, 10000, 50)%
	ENDIF
	DRAWLINE
	PRINTL 地面上已被你征服了，你指挥着你的军队准备进攻其他领土………
		PRINTL [0] - 巡视地上的魔界领土（已征服）
	IF FLAG:87 >= 1
		PRINTL [1] - 巡视黑暗精灵的领土（已征服）
	ELSE
		PRINTL [1] - 入侵精灵族的领域
	ENDIF
	IF FLAG:89 >= 1
		PRINTL [2] - 巡视混沌龙之山（已征服）
	ELSE
		PRINTL [2] - 入侵龙之山脉
	ENDIF
	IF FLAG:91 >= 1
		PRINTL [3] - 巡视堕天使的淫界（已征服）
	ELSE
		PRINTL [3] - 入侵天界
	ENDIF
	IF FLAG:92 == 15
		PRINTL [4] - 巡视圣灵骑士的卖春堡垒（已征服）
	ELSE
		PRINTL [4] - 攻略圣灵骑士的堡垒
	ENDIF
	IF EX_FLAG:102 >= 4
		PRINTL [5] - 巡视淫乱意志的神宫（已征服）
	ELSEIF EX_FLAG:102 >= 1
		PRINTL [5] - 天神宫广场
	ELSEIF (EX_FLAG:2810 >= 501 &&EX_FLAG:2810 < 540) || (EX_FLAG:2810 >= 541 && EX_FLAG:2810 < 560) 
		PRINTL [5] - 攻略天神宫
	ENDIF
	PRINTL [9] - 向著世界之外
	DRAWLINE
		PRINT [999] - 退出			
		PRINTFORML [1000]向城裏投放水晶球[{EX_FLAG:9011}/{EX_FLAG:9010}]		
		;PRINTL [1001] - 擾亂工作
	$INPUT_LOOP2
	INPUT
	
	IF RESULT == 999
		RETURN 0
	ELSEIF RESULT == 1000
		CALL SENGEN_VIDEO
		RETURN 0
	ELSEIF RESULT == 1001
		CALL AGENT_MENU
		RETURN 0
	;キャンペーン
	ELSEIF RESULT == 9
		CALL CAMPAIGN_MENU
		RETURN 0
	ELSEIF RESULT == 5 && EX_FLAG:2810 <= 500
		GOTO INPUT_LOOP2
	ELSEIF RESULT >= 6  && RESULT != 1000 && RESULT != 999
		GOTO INPUT_LOOP2
	ELSEIF RESULT < 0
		GOTO INPUT_LOOP2
	ENDIF
	
	;人間界
	IF RESULT == 0
		AREA = 81
		SINDO = 82
	;エルフの領域
	ELSEIF RESULT == 1
		AREA = 86
		SINDO = 87
	;ドラゴンの山
	ELSEIF RESULT == 2
		AREA = 88
		SINDO = 89
	;天界
	ELSEIF RESULT == 3
		AREA = 90
		SINDO = 91
	;圣灵ナイトの砦
	ELSEIF RESULT == 4
		CALL ARCANA_FORT
		IF RESULT == 1
			RETURN 1
		ELSE
			RETURN 0
		ENDIF
	;天神宮
	ELSEIF RESULT == 5
		AREA = 101
		SINDO = 102
		SIF EX_FLAG:102 >= 3
			EX_FLAG:102 += 1
	ENDIF
ELSE
	AREA = 81
	SINDO = 82
ENDIF
$START1
DRAWLINE
MON_NUM = 0
REPEAT 90
	MON_ID = COUNT + 100
	SIF ITEM:MON_ID < 1
		CONTINUE
	MON_NUM += ITEM:MON_ID
REND
IF AREA == 81
	PRINTFORML 　侵攻度　%BARSTR(FLAG:AREA, 10000, 50)%
	PRINTFORML 你的气力　%BARSTR(BASE:0:1,MAXBASE:0:1,32)% ({BASE:0:1, 4}/{MAXBASE:0:1})
ELSEIF AREA == 86
	PRINTFORML 精灵族领域的侵攻度　%BARSTR(FLAG:AREA, 10000, 50)%
	PRINTFORML 　　　　　你的气力　%BARSTR(BASE:0:1,MAXBASE:0:1,32)% ({BASE:0:1, 4}/{MAXBASE:0:1})
ELSEIF AREA == 88
	PRINTFORML 龙之山脉的侵攻度　%BARSTR(FLAG:AREA, 10000, 50)%
	PRINTFORML 　　　　你的气力　%BARSTR(BASE:0:1,MAXBASE:0:1,32)% ({BASE:0:1, 4}/{MAXBASE:0:1})
ELSEIF AREA == 90
	PRINTFORML 天界的侵攻度　%BARSTR(FLAG:AREA, 10000, 50)%
	PRINTFORML 　  你的气力　%BARSTR(BASE:0:1,MAXBASE:0:1,32)% ({BASE:0:1, 4}/{MAXBASE:0:1})
ELSEIF AREA == 101
	PRINTFORML 天神宫的侵攻度　%BARSTR(EX_FLAG:AREA, 10000, 50)%
	PRINTFORML 　    你的气力　%BARSTR(BASE:0:1,MAXBASE:0:1,32)% ({BASE:0:1, 4}/{MAXBASE:0:1})
ENDIF
DRAWLINE
PRINTFORML 你的怪物数量 {MON_NUM}只

;PRINTL  
DRAWLINE
IF MON_NUM < 600
	PRINTL [-] - 怪物数量不足。至少需要600只
ELSE
	PRINTL [0] - 使用现有怪物的一半去进攻（资金・俘虏）
ENDIF
PRINTL [1] - 使用魔王的魔力（经验值）
IF MON_NUM < 600
	PRINTL [-] - 怪物数量不足。至少需要600只
ELSE
	PRINTL [2] - 派遣勇者带三分之一的怪物去进攻（资金・经验值・俘虏）
ENDIF
PRINTL [3] - 派遣勇者前去掠夺资金（资金・经验值）
DRAWLINE
PRINTL [999] - 返回

$INPUT_LOOP
INPUT
IF RESULT == 999
	RETURN 0
ELSEIF RESULT >= 4
	GOTO INPUT_LOOP
ELSEIF RESULT < 0
	GOTO INPUT_LOOP
ELSEIF RESULT == 0 && MON_NUM < 600
	GOTO INPUT_LOOP
ELSEIF RESULT == 2 && MON_NUM < 600
	GOTO INPUT_LOOP
ENDIF
;進行種別
INV_TYPE = RESULT
;侵攻点
SINKOU = 0
;MONSTER_DATAで使う隊列。ここでは座標指定。A = 0はバグ対策
A = 0
B = 0

;怪物侵攻
IF INV_TYPE == 0
	REPEAT 90
		MON_ID = COUNT + 100
		MON_ATK = 0
		SIF ITEM:MON_ID < 1
			CONTINUE
		
		CALL MONSTER_DATA, MON_ID, 0, 0
		
		MON_ATK += E:2
		MON_ATK += E:3
		MON_ATK += E:4
		
		SIF E:5 != 0
			MON_ATK += E:1
			
		SIF E:6 != 0
			MON_ATK += E:1
		
		ITEM:MON_ID /= 2
		
		SINKOU += MON_ATK * ((ITEM:MON_ID / 9) + 1)
	REND
	;怪物カンストで最大約19万の1/20
	SINKOU /= 20
;===== 威望值 MOD =====
IF EX_FLAG:99 <= 20 && EX_FLAG:99 >= 0
	PRINTL 威望值是【岌岌可危】
	SINKOU = 0
	PRINTW 侵攻失败
	RETURN 1
ELSEIF EX_FLAG:99 <= 40 && EX_FLAG:99 > 20
	PRINTL 威望值是【动荡不安】
	PRINTW 侵攻战斗力减少
	SINKOU /= 4
ELSEIF EX_FLAG:99 <= 60 && EX_FLAG:99 > 40
	PRINTl 威望值是【略受质疑】
	TEMP = EX_FLAG:99
	TEMP -= 60
	SINKOU *= (100 + TEMP * 2)
	SINKOU /= 100
ELSEIF EX_FLAG:99 <= 80 && EX_FLAG:99 > 60
	PRINTl 威望值是【相安无事】
ELSEIF EX_FLAG:99 <= 100 && EX_FLAG:99 > 80
	PRINTl 威望值是【广受爱戴】
	TEMP = EX_FLAG:99
	TEMP -= 80
	SINKOU *= (100 + TEMP)
	SINKOU /= 100
ENDIF


;=======================
	PRINTFORMW 怪物的战斗力　{SINKOU}点

;魔王の魔法
ELSEIF INV_TYPE == 1
	SINKOU = BASE:0:1 / 25
	BASE:0:1 /= 2
;===== 威望值 MOD =====
	IF EX_FLAG:99 <= 20 && EX_FLAG:99 >= 0
PRINTL 威望值是【岌岌可危】
	SINKOU = 0
	PRINTW 侵攻失败
	RETURN 1
ELSEIF EX_FLAG:99 <= 40 && EX_FLAG:99 > 20
	PRINTL 威望值是【动荡不安】
	PRINTW 侵攻战斗力减少
	SINKOU /= 4
ELSEIF EX_FLAG:99 <= 60 && EX_FLAG:99 > 40
	PRINTl 威望值是【略受质疑】
	TEMP = EX_FLAG:99
	TEMP -= 60
	SINKOU *= (100 + TEMP * 2)
	SINKOU /= 100
ELSEIF EX_FLAG:99 <= 80 && EX_FLAG:99 > 60
	PRINTl 威望值是【相安无事】
ELSEIF EX_FLAG:99 <= 100 && EX_FLAG:99 > 80
PRINTl 威望值是【广受爱戴】
	TEMP = EX_FLAG:99
	TEMP -= 80
	SINKOU *= (100 + TEMP)
	SINKOU /= 100
ENDIF

;=====================
	PRINTFORMW 战斗力　{SINKOU}点

;勇者の侵攻
ELSEIF INV_TYPE == 2
LIST_POS = 0
PREV_PAGE = 0
PREV_LIST_POS = 0
YUSYA_I = 0

	REPEAT CHARANUM
	{
		SIF COUNT == 0 ||
			BASE:COUNT:0 < 1 ||
			!CFLAG:COUNT:0 == 2 ||
			!(CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 7) ||
			!(TALENT:COUNT:85 == 1 || TALENT:COUNT:76 == 1) ||
			(TALENT:COUNT:153 == 1 && GETBIT(FLAG:5,10) == 0)
	}
			CONTINUE		;魔王、非可助手、非爱慕或淫乱、非调教中或苗床、妊娠中
		YUSYA_I++
	REND
IF (YUSYA_I % NUM_PAGE) > 0
	MAX_PAGE = ( YUSYA_I / NUM_PAGE ) + 1
ELSE
	MAX_PAGE = YUSYA_I / NUM_PAGE
ENDIF
MAX_PAGE--
	IF YUSYA_I == 0
		PRINTW 没有勇者可进行侵攻。
		RESTART
	ENDIF	
$INPUT_LOOP_TMPO2
;-------------------------------------------------
;缓存、重置列表信息
IF NO_PAGE == 0
	LIST_POS = 0
	PREV_PAGE = 0
	PREV_LIST_POS = 0
ELSEIF NO_PAGE < PREV_PAGE
	SWAP LIST_POS, PREV_LIST_POS
ELSEIF NO_PAGE == PREV_PAGE
	LIST_POS = PREV_LIST_POS
ELSE
	PREV_LIST_POS = LIST_POS
ENDIF
;-------------------------------------------------
CUSTOMDRAWLINE =
PRINTL 派遣谁去侵攻呢？
DRAWLINE
L_LCOUNT = LINECOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
FOR COUNT, LIST_POS, CHARANUM
{
	SIF		COUNT == 0 ||
			BASE:COUNT:0 < 1 ||
			!CFLAG:COUNT:0 == 2 ||
			!(CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 7) ||
			!(TALENT:COUNT:85 == 1 || TALENT:COUNT:76 == 1) ||
			(TALENT:COUNT:153 == 1 && GETBIT(FLAG:5,10) == 0) ||
			T_LCOUNT >= (NO_PAGE + 1)*NUM_PAGE ||
			T_LCOUNT < NO_PAGE* NUM_PAGE + 1
}
		CONTINUE
	CALL LIFE_LIST_ITEM(COUNT)
	T_LCOUNT++
	LIST_POS = COUNT
NEXT
L_LCOUNT = LINECOUNT - L_LCOUNT
	IF L_LCOUNT < (NUM_PAGE + 1)
		REPEAT (NUM_PAGE - L_LCOUNT)
			PRINTL
		REND
	ENDIF
DRAWLINE
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页

	INPUT

	IF RESULT == 999
		RESTART
	ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_TMPO2
	ELSEIF RESULT == 1001		;下一页
	IF NO_PAGE < MAX_PAGE
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_TMPO2
	ELSEIF !INRANGE(RESULT, 0, CHARANUM-1)
		CLEARLINE 1
		GOTO INPUT_LOOP_TMPO2
{
	ELSEIF RESULT == 0 ||
		BASE:RESULT:0 < 1 ||
		!CFLAG:RESULT:0 == 2 ||
		!(CFLAG:RESULT:1 == 0 || CFLAG:RESULT:1 == 7) ||
		!(TALENT:RESULT:85 == 1 || TALENT:RESULT:76 == 1) ||
		(TALENT:RESULT:153 == 1 && GETBIT(FLAG:5,10) == 0)
}
		CLEARLINE 1
		GOTO INPUT_LOOP_TMPO2
	ENDIF
	
	YUSYA_I = RESULT
	
	REPEAT 90
		MON_ID = COUNT + 100
		MON_ATK = 0
		SIF ITEM:MON_ID < 1
			CONTINUE
		
		CALL MONSTER_DATA, MON_ID, 0, YUSYA_I
		
		MON_ATK += E:2
		MON_ATK += E:3
		MON_ATK += E:4
		
		SIF E:5 != 0
			MON_ATK += E:1
			
		SIF E:6 != 0
			MON_ATK += E:1
		ITEM:MON_ID /= 3
		SINKOU += MON_ATK * ((ITEM:MON_ID / 9) + 1)
		ITEM:MON_ID *= 2
	REND

	;怪物カンストで最大約12万の1/20
	SINKOU /= 20
	PRINTFORMW 怪物的战斗力　{SINKOU}点
	
	TMP2_I = CFLAG:YUSYA_I:9 + 100
	PRINTFORMW 勇者补正　　　x{TMP2_I/100}.%TOSTR(TMP2_I%100,"00")%
	
	SINKOU *= TMP2_I
	SINKOU /= 100
	;勲章補正
	CALL MEDAL_BONUS,YUSYA_I
	SINKOU *= RESULT
	SINKOU /= 100
ELSEIF INV_TYPE == 3
	;略奪
	;選択基準は迎撃に準じる
LIST_POS = 0
PREV_PAGE = 0
PREV_LIST_POS = 0
LOCAL = 0
YUSYA_I = 0
	REPEAT CHARANUM
	{
		SIF BASE:COUNT:0 < 1 ||
			COUNT == 0 ||
			CFLAG:COUNT:1 != 0 ||
			(CFLAG:COUNT:0 == 0 && TALENT:COUNT:254 == 0) ||
			(TALENT:COUNT:153 == 1 && GETBIT(FLAG:5,10) == 0)
	}
			CONTINUE
			YUSYA_I++	
	REND
IF (YUSYA_I % NUM_PAGE) > 0
	MAX_PAGE = ( YUSYA_I / NUM_PAGE ) + 1
ELSE
	MAX_PAGE = YUSYA_I / NUM_PAGE
ENDIF
MAX_PAGE--
	IF YUSYA_I == 0
		PRINTW 没有勇者可进行侵攻。
		RESTART
	ENDIF	
$INPUT_LOOP_TMPO3
;-------------------------------------------------
;缓存、重置列表信息
IF NO_PAGE == 0
	LIST_POS = 0
	PREV_PAGE = 0
	PREV_LIST_POS = 0
ELSEIF NO_PAGE < PREV_PAGE
	SWAP LIST_POS, PREV_LIST_POS
ELSEIF NO_PAGE == PREV_PAGE
	LIST_POS = PREV_LIST_POS
ELSE
	PREV_LIST_POS = LIST_POS
ENDIF
;-------------------------------------------------
CUSTOMDRAWLINE =
PRINTL 派遣谁去侵攻呢？
DRAWLINE
L_LCOUNT = LINECOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
FOR COUNT, LIST_POS, CHARANUM
{
	SIF		BASE:COUNT:0 < 1 ||
			COUNT == 0 ||
			CFLAG:COUNT:1 != 0 ||
			(CFLAG:COUNT:0 == 0 && TALENT:COUNT:254 == 0) ||
			(TALENT:COUNT:153 == 1 && GETBIT(FLAG:5,10) == 0) ||
			T_LCOUNT >= (NO_PAGE + 1)*NUM_PAGE ||
			T_LCOUNT < NO_PAGE* NUM_PAGE + 1
}
		CONTINUE
	CALL LIFE_LIST_ITEM(COUNT)
	T_LCOUNT++
	LIST_POS = COUNT
NEXT
L_LCOUNT = LINECOUNT - L_LCOUNT
	IF L_LCOUNT < (NUM_PAGE + 1)
		REPEAT (NUM_PAGE - L_LCOUNT)
			PRINTL
		REND
	ENDIF
DRAWLINE
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
	
	INPUT
	
	IF RESULT == 999
		RESTART
	ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_TMPO3
	ELSEIF RESULT == 1001		;下一页
	IF NO_PAGE < MAX_PAGE
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_TMPO3
	ELSEIF RESULT < 0 || RESULT >= CHARANUM
		CLEARLINE 1
		GOTO INPUT_LOOP_TMPO3
{
	ELSEIF RESULT == 0 ||
		BASE:RESULT:0 < 1 ||
		CFLAG:RESULT:1 != 0 ||
		(CFLAG:RESULT:0 == 0 && TALENT:RESULT:254 == 0) ||
		(TALENT:RESULT:153 == 1 && GETBIT(FLAG:5,10) == 0)
}
		CLEARLINE 1
		GOTO INPUT_LOOP_TMPO3
	ENDIF
	
	YUSYA_I = RESULT
	
	SINKOU = BASE:0:1 / 25
	BASE:0:1 /= 2
	PRINTFORMW 魔王的力量　{SINKOU}点
	
	TMP2_I = CFLAG:YUSYA_I:9 + 100
	PRINTFORMW 勇者补正　x{TMP2_I/100}.%TOSTR(TMP2_I%100,"00")%
	
	SINKOU *= TMP2_I
	SINKOU /= 100
	;勲章補正
	CALL MEDAL_BONUS,YUSYA_I
	SINKOU *= RESULT
	SINKOU /= 100
	
ENDIF

;-------------------------------------------------
;共通処理
;-------------------------------------------------
TMP2_I = CFLAG:0:9 + 100
PRINTFORMW 魔王补正　　　x{TMP2_I/100}.%TOSTR(TMP2_I%100,"00")%

SINKOU *= TMP2_I
SINKOU /= 100

IF TALENT:0:325 == 1
	PRINTFORMW 魔界知识补正　x1.50
	SINKOU *= 150
	SINKOU /= 100
ENDIF

IF TALENT:0:327 == 1
	PRINTFORMW 淫魔知识补正　x1.20
	SINKOU *= 120
	SINKOU /= 100
ENDIF

IF TALENT:0:328 == 1
	PRINTFORMW 魔虫知识补正　x1.10
	SINKOU *= 110
	SINKOU /= 100
ENDIF

;勲章補正
CALL MEDAL_BONUS,0
SINKOU *= RESULT
SINKOU /= 100


PRINTFORMW 合计　{SINKOU}点


CALL INVASION_EVENT, AREA, SINDO, INV_TYPE, SINKOU, YUSYA_I
SIF RESULT > 0
	RETURN RESULT

;----------------------------------
;侵攻結果共通
;----------------------------------

IF INV_TYPE == 3
	;略奪は侵攻力が激減
	FLAG:AREA += SINKOU / 20
	
ELSE
	FLAG:AREA += SINKOU
ENDIF

SIF FLAG:AREA >= 10000
	FLAG:AREA = 10000

IF INV_TYPE == 0
	DRAWLINE

	;人間界クリア済
	IF AREA == 81 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 10}点！
		MONEY += SINKOU * 10
		EX_FLAG:4444 += SINKOU * 10
	;エルフの領域クリア済
	ELSEIF AREA == 86 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 10}点！
		MONEY += SINKOU * 10
		EX_FLAG:4444 += SINKOU * 10
	;ドラゴンの山クリア済
	ELSEIF AREA == 88 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 10}点！
		MONEY += SINKOU * 10
		EX_FLAG:4444 += SINKOU * 10
	;天界クリア済
	ELSEIF AREA == 90 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 10}点！
		MONEY += SINKOU * 10
		EX_FLAG:4444 += SINKOU * 10
	ELSE
		PRINTFORMW 得到了{SINKOU * 10}点的战利品！
		MONEY += SINKOU * 10
		EX_FLAG:4444 += SINKOU * 10
	ENDIF

	DRAWLINE
	SIF AREA == 81
	PRINT 侵攻度 
	SIF AREA == 86
	PRINT 精灵族的领域　侵攻度 
	SIF AREA == 88
	PRINT 龙之山脉　侵攻度 
	SIF AREA == 90
	PRINT 天界　侵攻度 
	SIF AREA == 101
	PRINT 天神宫　侵攻度
	BAR FLAG:AREA, 10000, 50
	PRINTL  
	DRAWLINE
	WAIT

	;人間界
	IF AREA == 81
		CALL INVASION_RYOUZYOKU, 1, SINKOU
	;エルフの領域
	ELSEIF AREA == 86
		CALL INVASION_RYOUZYOKU, 2, SINKOU
	;ドラゴンの山
	ELSEIF AREA == 88
		CALL INVASION_RYOUZYOKU, 3, SINKOU
	;天界
	ELSEIF AREA == 90
		CALL INVASION_RYOUZYOKU, 4, SINKOU
	;天神宫
	ELSEIF AREA == 101
		CALL INVASION_RYOUZYOKU, 5, SINKOU
	ENDIF
	
	IF RAND:100 < 5
		PRINTFORMW 好像抓到了负隅顽抗的勇者…………
		CALL GET_ENEMY
		
		SIF RESULT == 0
			PRINTFORMW 犒赏士兵，捕获到的勇者被赏赐给部下了。
	ENDIF
	
ELSEIF INV_TYPE == 1
	;気力最大、レベル１００、知識を２つ所持で1440点
	PRINTFORML %SAVESTR:MASTER%的魔力爆发出来了！
	IF SINKOU < 100
		PRINTFORMW {SINKOU}点魔力形成飓风，将大树吹倒了！
	ELSEIF SINKOU < 300
		PRINTFORMW {SINKOU}点魔力形成火焰，将平原焚烧殆尽！
	ELSEIF SINKOU < 600
		PRINTFORMW {SINKOU}点魔力形成雷霆，将附近的村庄彻底摧毁！
	ELSEIF SINKOU < 900
		PRINTFORMW {SINKOU}点魔力形成洪水，将城镇淹没！
	ELSEIF SINKOU < 1200
		PRINTFORMW {SINKOU}点魔力形成剧毒气体，令骑士团窒息！
	ELSE
		PRINTFORMW {SINKOU}点魔力形成纯粹能量，将城市呑没！
	ENDIF
	DRAWLINE
	;人間界クリア済
	IF AREA == 81 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW %SAVESTR:MASTER%得到了{SINKOU/2}点经验值！
		EXP:0:80 += SINKOU / 2
	;エルフの領域クリア済
	ELSEIF AREA == 86 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW %SAVESTR:MASTER%得到了{SINKOU/2}点经验值！
		EXP:0:80 += SINKOU / 2
	;ドラゴンの山クリア済
	ELSEIF AREA == 88 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW %SAVESTR:MASTER%得到了{SINKOU/2}点经验值！
		EXP:0:80 += SINKOU / 2
	;天界クリア済
	ELSEIF AREA == 90 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW %SAVESTR:MASTER%得到了{SINKOU/2}点经验值！
		EXP:0:80 += SINKOU / 2
	;天神宫クリア済
	ELSEIF AREA == 101 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW %SAVESTR:MASTER%得到了{SINKOU/2}点经验值！
		EXP:0:80 += SINKOU / 2
	ELSE
		PRINTFORMW %SAVESTR:MASTER%得到了{SINKOU/2}点经验值！
		EXP:0:80 += SINKOU / 2
	ENDIF
	
	DRAWLINE
	SIF AREA == 81
	PRINT 侵攻度 
	SIF AREA == 86
	PRINT 精灵族的领域　侵攻度 
	SIF AREA == 88
	PRINT 龙之山脉　侵攻度 
	SIF AREA == 90
	PRINT 天界　侵攻度 
	SIF AREA == 101
	PRINT 天神宫　侵攻度
	IF AREA <= 100
		BAR FLAG:AREA, 10000, 50
	ELSE
		BAR EX_FLAG:AREA, 10000, 50
	ENDIF
	PRINTL  
ELSEIF INV_TYPE == 2
	PRINTFORM %SAVESTR:YUSYA_I%带着怪物到达了
	;人間界
	IF AREA == 81
		PRINT 人间界
	;エルフの領域
	ELSEIF AREA == 86
		PRINT 精灵族的领域
	;ドラゴンの山
	ELSEIF AREA == 88
		PRINT 龙之山脉
	;天界
	ELSEIF AREA == 90
		PRINT 天界
	ELSEIF AREA == 101
		PRINT 天神宫
	ENDIF
	PRINTW ，尽可能地施暴着。（善良值:-50） 
	CALL KARMA, YUSYA_I, -50
	;慈爱
	IF TALENT:YUSYA_I:160
		PRINTFORMW %SAVESTR:YUSYA_I%在侵略的时候依旧全程保持着慈爱的笑容，她终于明白到一切都是为了%SAVESTR:MASTER%而存在的………
	;自信家
	ELSEIF TALENT:YUSYA_I:161
		PRINTFORMW %SAVESTR:YUSYA_I%身先士卒，第一个飞跳入战场里，而且最后毫发无损。
	;懦弱
	ELSEIF TALENT:YUSYA_I:162
		PRINTFORMW %SAVESTR:YUSYA_I%是优秀的指挥官，带领着怪物们侵略了。
	;高贵
	ELSEIF TALENT:YUSYA_I:163
		PRINTFORMW %SAVESTR:YUSYA_I%穿着%SAVESTR:MASTER%赐予的被诅咒的铠甲，高声大笑着率领怪物们突击了………
	;冷静
	ELSEIF TALENT:YUSYA_I:164
		PRINTFORMW %SAVESTR:YUSYA_I%冷哼着耻笑跪求饶命的草民，随手将他们交给饥饿的巨兽了。
	;村娘
	ELSEIF TALENT:YUSYA_I:165
		PRINTFORMW %SAVESTR:YUSYA_I%一边发出异样的笑声，一边用手中的火把将四周都点燃了………
	;恶女,
	ELSEIF TALENT:YUSYA_I:166
		PRINTFORMW %SAVESTR:YUSYA_I%把侵略时所抢夺的金银财宝都献给了%SAVESTR:MASTER%………
	ELSE
		PRINTL
	ENDIF

	;人間界クリア済
	IF AREA == 81 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 5}点！
		MONEY += SINKOU * 5
		EX_FLAG:4444 += SINKOU * 5
		EXP:YUSYA_I:80 += SINKOU / 2
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/2}点经验值！
	;エルフの領域クリア済
	ELSEIF AREA == 86 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 5}点！
		MONEY += SINKOU * 5
		EX_FLAG:4444 += SINKOU * 5
		EXP:YUSYA_I:80 += SINKOU / 2
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/2}点经验值！
	;ドラゴンの山クリア済
	ELSEIF AREA == 88 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 5}点！
		MONEY += SINKOU * 5
		EX_FLAG:4444 += SINKOU * 5
		EXP:YUSYA_I:80 += SINKOU / 2
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/2}点经验值！
	;天界クリア済
	ELSEIF AREA == 90 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 5}点！
		MONEY += SINKOU * 5
		EX_FLAG:4444 += SINKOU * 5
		EXP:YUSYA_I:80 += SINKOU / 2
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/2}点经验值！
	ELSEIF AREA == 101 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强制征收了{SINKOU * 5}点！
		MONEY += SINKOU * 5
		EX_FLAG:4444 += SINKOU * 5
		EXP:YUSYA_I:80 += SINKOU / 2
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/2}点经验值！
	ELSE
		PRINTFORMW 得到了{SINKOU * 5}点的战利品！
		MONEY += SINKOU * 5
		EX_FLAG:4444 += SINKOU * 5
		EXP:YUSYA_I:80 += SINKOU / 2
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/2}点经验值！
	ENDIF

	DRAWLINE
	SIF AREA == 81
	PRINT 侵攻度 
	SIF AREA == 86
	PRINT 精灵族的领域　侵攻度 
	SIF AREA == 88
	PRINT 龙之山脉　侵攻度 
	SIF AREA == 90
	PRINT 天界　侵攻度
	SIF AREA == 101
	PRINT 天神宫　侵攻度	
	BAR FLAG:AREA, 10000, 50
	PRINTL  
	DRAWLINE
	WAIT
	
	;人間界
	IF AREA == 81
		CALL INVASION_RYOUZYOKU, 1, SINKOU
	;エルフの領域
	ELSEIF AREA == 86
		CALL INVASION_RYOUZYOKU, 2, SINKOU
	;ドラゴンの山
	ELSEIF AREA == 88
		CALL INVASION_RYOUZYOKU, 3, SINKOU
	;天界
	ELSEIF AREA == 90
		CALL INVASION_RYOUZYOKU, 4, SINKOU
	;天神宫
	ELSEIF AREA == 101
		CALL INVASION_RYOUZYOKU, 5, SINKOU
	ENDIF
	
	IF RAND:100 < 9
		PRINTFORMW 好像抓到了负隅顽抗的勇者…………
		CALL GET_ENEMY
			EX_FLAG:99 += 1
		SIF RESULT == 0
			PRINTFORMW 犒赏士兵，捕获到的勇者被赏赐给部下了。
	ENDIF
	
	
ELSEIF INV_TYPE == 3
	PRINTFORM %SAVESTR:YUSYA_I%得到了魔王的力量！
	;人間界
	IF AREA == 81
		PRINT 人间界
	;エルフの領域
	ELSEIF AREA == 86
		PRINT 精灵族的领域
	;ドラゴンの山
	ELSEIF AREA == 88
		PRINT 龙之山脉
	;天界
	ELSEIF AREA == 90
		PRINT 天界
	ELSEIF AREA == 101
		PRINT 天神宫
	ENDIF
	PRINTW 被掠夺了。（善恶值:-5） 
	CALL KARMA, YUSYA_I, -5
	
	;人間界クリア済
	IF AREA == 81 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强行征收到了{SINKOU}点！
		MONEY += SINKOU
		EX_FLAG:4444 += SINKOU
		EXP:YUSYA_I:80 += SINKOU / 20
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/20}点经验值！
	;エルフの領域クリア済
	ELSEIF AREA == 86 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强行征收到了{SINKOU}点！
		MONEY += SINKOU
		EX_FLAG:4444 += SINKOU
		EXP:YUSYA_I:80 += SINKOU / 20
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/20}点经验值！
	;ドラゴンの山クリア済
	ELSEIF AREA == 88 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强行征收到了{SINKOU}点！
		MONEY += SINKOU
		EX_FLAG:4444 += SINKOU
		EXP:YUSYA_I:80 += SINKOU / 20
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/20}点经验值！
	;天界クリア済
	ELSEIF AREA == 90 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强行征收到了{SINKOU}点！
		MONEY += SINKOU
		EX_FLAG:4444 += SINKOU
		EXP:YUSYA_I:80 += SINKOU / 20
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/20}点经验值！
	;天界クリア済
	ELSEIF AREA == 101 && FLAG:SINDO
		SINKOU = MIN( SINKOU, 10000 * 10 )
		PRINTFORMW 强行征收到了{SINKOU}点！
		MONEY += SINKOU
		EX_FLAG:4444 += SINKOU
		EXP:YUSYA_I:80 += SINKOU / 20
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/20}点经验值！
	ELSE
		PRINTFORMW 获得了{SINKOU}点的战利品！
		MONEY += SINKOU
		EX_FLAG:4444 += SINKOU
		EXP:YUSYA_I:80 += SINKOU / 20
		PRINTFORMW %SAVESTR:YUSYA_I%获得了{SINKOU/20}点经验值！
	ENDIF

	DRAWLINE
	SIF AREA == 81
	PRINT 侵攻度 
	SIF AREA == 86
	PRINT 精灵族的领域　侵攻度 
	SIF AREA == 88
	PRINT 龙之山脉　侵攻度 
	SIF AREA == 90
	PRINT 天界　侵攻度 
	SIF AREA == 101
	PRINT 天神宫　侵攻度 
	BAR FLAG:AREA, 10000, 50
	PRINTL  
	DRAWLINE
	WAIT
	
ENDIF
DRAWLINE
WAIT
EX_FLAG:99 += 2

;侵略イベント2017/1/1
;侵攻度2000、4000、6000、8000、10000でイベント開始
;人間界
IF AREA == 81
	CALL KYOTEN_EVENT, 1
;エルフの領域
ELSEIF AREA == 86
	CALL KYOTEN_EVENT, 2
;ドラゴンの山
ELSEIF AREA == 88
	CALL KYOTEN_EVENT, 3
;天界
ELSEIF AREA == 90
	CALL KYOTEN_EVENT, 4
ENDIF

CALL INVASION_CHECK
RETURN 1

@INVASION_CHECK

IF FLAG:81 >= 10000 && FLAG:82 == 0
	CALL ENDING_1
	EX_FLAG:99 += 10
	PRINTL 声望+10
ELSEIF FLAG:86 >= 10000 && FLAG:87 == 0
	CALL ENDING_3
	EX_FLAG:99 += 10
	PRINTL 声望+10
ELSEIF FLAG:88 >= 10000 && FLAG:89 == 0
	CALL ENDING_4
	EX_FLAG:99 += 10
	PRINTL 声望+10
ELSEIF FLAG:90 >= 10000 && FLAG:91 == 0
	CALL ENDING_5
	EX_FLAG:99 += 10
	PRINTL 声望+10
ELSEIF EX_FLAG:101 >= 10000 && EX_FLAG:102 == 0
	CALL END10_55
	EX_FLAG:99 += 10
	PRINTL 声望+10
ENDIF



;-----------------------------
@MEDAL_BONUS,ARG
;-----------------------------
;ARGのキャラの補正量を返す関数

LOCAL = 100

IF EXP:ARG:81 > 500
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.60
	LOCAL = 160
ELSEIF EXP:ARG:81 > 250
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.50
	LOCAL = 150
ELSEIF EXP:ARG:81 > 150
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.40
	LOCAL = 140
ELSEIF EXP:ARG:81 > 100
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.30
	LOCAL = 130
ELSEIF EXP:ARG:81 > 60
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.20
	LOCAL = 120
ELSEIF EXP:ARG:81 > 40
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.10
	LOCAL = 110
ELSEIF EXP:ARG:81 > 30
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.05
	LOCAL = 105
ELSEIF EXP:ARG:81 > 20
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.04
	LOCAL = 104
ELSEIF EXP:ARG:81 > 15
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.03
	LOCAL = 103
ELSEIF EXP:ARG:81 > 10
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.02
	LOCAL = 102
ELSEIF EXP:ARG:81 > 5
	PRINTFORMW %CALLNAME:ARG%的勋章补正　x1.01
	LOCAL = 101
ENDIF

RETURN LOCAL

;-----------------------------
@SENGEN_VIDEO
;-----------------------------
#DIM STOCK
$INPUT_LOOP
STOCK = EX_FLAG:9010 - EX_FLAG:9011
DRAWLINE
PRINTFORML 可用于投放的水晶球{STOCK,3}部		已投放{EX_FLAG:9011,3}部
IF EX_FLAG:9012 && EX_FLAG:9013
PRINTFORML         正流行的有{EX_FLAG:9012,3}部		{EX_FLAG:9013,2}天后将过时
ELSE
PRINTFORML  目前没有投放中的水晶球
ENDIF
DRAWLINE
IF (EX_FLAG:9010 - EX_FLAG:9011) > 0
PRINTL [1]投放水晶球
PRINTL [2]派奸商投放水晶球
PRINTL [3]增强流行效果
PRINTL [4]延长流行时间
ELSE
PRINTL 当前没有可以用于投放的水晶球
ENDIF
DRAWLINE
PRINTL [999]离开
INPUT
IF STOCK == 0 && RESULT != 999
	GOTO INPUT_LOOP
ELSEIF RESULT == 1
	DRAWLINE
	PRINTFORML 通过投放拍摄的影像，激起反抗魔王的决心。
	DRAWLINE
	PRINTFORML 请输入要投放的数量
	$INPUT_LOOP_TMP0
	INPUT
	IF RESULT == 0
		GOTO INPUT_LOOP
	ELSEIF RESULT > STOCK
		PRINTFORML 超出数量，请重新输入
		GOTO INPUT_LOOP_TMP0
	ELSE
		EX_FLAG:9011 += RESULT
		CALL SENGEN_VIDEO_BONUS, RESULT
		IF RESULT >= 1
			PRINTFORMW 成功投放{RESULT}部水晶球
			EX_FLAG:9012 += RESULT
			EX_FLAG:9013 += RESULT
		ELSE 
			PRINTFORMW 投放，似乎失败了。
		ENDIF
		GOTO INPUT_LOOP
	ENDIF
ELSEIF RESULT == 2
	DRAWLINE
	PRINTFORML 通过奸商代理投放拍摄的影像，或许更能激起反抗魔王的决心。
	PRINTFORML 但需要收取代理酬劳，每部5000G或是1枚勋章。
	DRAWLINE
	PRINTFORML 请输入要投放的数量
	$INPUT_LOOP_TMP1
	INPUT
	IF RESULT == 0
		GOTO INPUT_LOOP
	ELSEIF RESULT > STOCK
		PRINTFORML 超出可投放数量，请重新输入
		GOTO INPUT_LOOP_TMP1
	ELSEIF RESULT > EXP:0:81 && (RESULT * 5000) > MONEY
		PRINTFORML 没有足够的奖赏来打动奸商
		GOTO INPUT_LOOP_TMP1
	ELSE
		EX_FLAG:9011 += RESULT
		CALL SENGEN_VIDEO_BONUS, RESULT, 1
		IF RESULT >= 1
			PRINTFORMW 成功投放{RESULT}部水晶球
			EX_FLAG:9012 += RESULT
			EX_FLAG:9013 += RESULT
			SIF (M * 5000) < MONEY
				PRINTL [1]犒赏金币
			SIF M < EXP:0:81
				PRINTL [2]犒赏勋章
			$INPUT_LOOP_TMP2
			INPUT
			IF RESULT == 1 && (M * 5000) < MONEY
				PRINTFORML 犒赏了奸商{M * 5000}G
				MONEY -= (M * 5000)
				EX_FLAG:4444 -= (M * 5000)
			ELSEIF RESULT == 2 && M < EXP:0:81
				PRINTFORML 犒赏了奸商{M}枚勋章
				EXP:0:81 -= M
			ELSE
				GOTO INPUT_LOOP_TMP2
			ENDIF
		ELSE 
			PRINTFORMW 投放，似乎失败了。
		ENDIF
		GOTO INPUT_LOOP
	ENDIF
ELSEIF RESULT == 3
	DRAWLINE
	PRINTFORML 通过奸商代理投放拍摄的影像，增强投放的效果。
	PRINTFORML 将收取50000G或是5枚勋章。
	DRAWLINE
	PRINTFORML 请选择要支付方式
			SIF MONEY > 50000
				PRINTL [1]支付金币
			SIF EXP:0:81 > 5
				PRINTL [2]支付勋章
			PRINTL [999]离开
			$INPUT_LOOP_TMP3
			INPUT
			IF RESULT == 1 && MONEY > 50000
				PRINTFORML 犒赏了奸商50000G
				MONEY -= 50000
				EX_FLAG:4444 -= 50000			
			ELSEIF RESULT == 2 && EXP:0:81 > 5
				PRINTFORML 犒赏了奸商5枚勋章
				EXP:0:81 -= 5
			ELSEIF RESULT == 999
				GOTO INPUT_LOOP
			ELSE
				GOTO INPUT_LOOP_TMP3
			ENDIF
		M = EX_FLAG:9012
		TIMES EX_FLAG:9012, 1.20
		SIF RAND:5 == 0
			TIMES EX_FLAG:9012, 1.60
		SIF RAND:2 == 0
			TIMES EX_FLAG:9012, 1.20
		SIF EX_FLAG:9012 > (M * 2)
			EX_FLAG:9012 = M * 2
		PRINTFORML 因为剪辑出了更多的版本，投放效果增强了
		GOTO INPUT_LOOP
ELSEIF RESULT == 4
	DRAWLINE
	PRINTFORML 通过增加投放量延长流行时间。
	PRINTFORML 将收取50000G。
	DRAWLINE
			SIF MONEY > 50000
				PRINTL [1]支付
			PRINTL [999]算了
			$INPUT_LOOP_TMP4
			INPUT
			IF RESULT == 1 && MONEY > 50000
				PRINTFORML 支付了50000G
				MONEY -= 50000
				EX_FLAG:4444 -= 50000			
			ELSEIF RESULT == 999
				GOTO INPUT_LOOP
			ELSE
				GOTO INPUT_LOOP_TMP4
			ENDIF
		M = EX_FLAG:9013
		TIMES EX_FLAG:9013, 1.20
		SIF RAND:5 == 0
			TIMES EX_FLAG:9013, 1.60
		SIF RAND:2 == 0
			TIMES EX_FLAG:9013, 1.20
		SIF EX_FLAG:9013 > (M + 5)
			EX_FLAG:9013 = M + 5
		SIF (EX_FLAG:9013 - M) < 1
			EX_FLAG:9013 = M + 1
		PRINTFORML 流行时间延长了
		GOTO INPUT_LOOP
ELSEIF RESULT == 999
	RETURN 0
ELSE
ENDIF

;-----------------------------
@SENGEN_VIDEO_BONUS, RESULT, MODE=0
;-----------------------------
;获取RESULT作为基础量
;通过MODE选取加成模式,0为普通,1为商人
#DIM MODE
;-----------------------------
M = RESULT
SIF MODE == 0
	GOTO MODE_0
TIMES RESULT , 1.10
SIF RAND:2 == 0
	TIMES RESULT , 1.20
SIF RAND:3 == 0
	TIMES RESULT , 1.20
SIF RAND:4 == 0
	TIMES RESULT , 1.20
		
$MODE_0
SIF RAND:2 == 0
	TIMES RESULT , 1.20
SIF RAND:3 == 0
	TIMES RESULT , 0.80
SIF RESULT > M && MODE == 1
	PRINTFORML 奸商们制作更多的版本提升了投放效果。
SIF MODE == 1 && RESULT <= M
	RESULT = M
SIF RESULT > M && MODE == 0
	PRINTFORML 在投放过程中似乎传出了不同的版本，投放效果提升了。
SIF RESULT < M
	PRINTFORML 似乎有些水晶球投放不是太成功。
RETURN RESULT

;-----------------------------
@SENGEN_VIDEO_DE
;-----------------------------
EX_FLAG:9013--
SIF RAND:3
	EX_FLAG:9012--
IF EX_FLAG:9013 <= 0
	EX_FLAG:9013 = 0
	EX_FLAG:9012 = 0
ENDIF
IF EX_FLAG:9012 <= 0
	EX_FLAG:9013 = 0
	EX_FLAG:9012 = 0
ENDIF