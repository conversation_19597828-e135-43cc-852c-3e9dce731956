﻿
eramaou用 戦闘ログでのSKIP中断を追加するパッチ(20140515)

・編集元
eramaou v0.300

・何がしたかったのか
戦闘ログで奴隷の戦闘だけ見たかったのだけど、
一度でも右クリックを押すと全員飛ばしてしまうので不便だった
なので右クリック後でも一時停止させられるようにした

・何をするものか
戦闘ログでの各キャラの行動開始時、SKIP状態でも停止するようになる
ただしコンフィグの「戦闘ログでのSKIP中断」がONの場合のみ

・どうすれば動作を確認できるか
(1)eramaou v0.300に上書き
(2)コンフィグから「戦闘ログでのSKIP中断」をONにする
(3)勇者のダンジョン攻略ログを右クリックで飛ばしても、キャラごとに一時停止することを確かめる

・編集点
変数を新しく使用
　FLAG:5 = ゲーム設定(ビット演算)
	9ビット(&512):戦闘ログでのSKIP中断

[CONFIG.ERB]を新しく作った
関数@CONFIGを[SHOP.ERB]からこちらに移動して編集
　補助的な関数を追加
　項目の順序を変えた

[SHOP.ERB]を編集
関数@CONFIGを削除

[DUNGEON.ERB]を編集
関数@DUNGEONを編集
　コンフィグ確認とSKIP中断を追加した
　FORCEWAITで検索かけると出てきます
