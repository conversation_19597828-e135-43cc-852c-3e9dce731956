﻿;------------------------------------------------------------
;通信機能
;------------------------------------------------------------
@MAOUNET
#DIM LINE, 1
LINE = LINECOUNT
$START
CLEARLINE LINECOUNT - LINE
PRINTL [0] 将奴隶共享到其他存档
PRINTL [1] 从其他存档共享勇者
PRINTL [2] 去除从其他存档共享的勇者
PRINTFORML [3] 设定通信勇者的等级上限(现在:Lv{FLAG:76})
PRINTFORML [4] 通信勇者登场时为等级1(现在:\@FLAG:77?ＯＮ#ＯＦＦ\@)
CALL MAOUNET_MODPRINT
PRINTL [9] 返回
INPUT
IF RESULT == 0
	CALL EXPORT
ELSEIF RESULT == 1
	CALL INPORT_A
ELSEIF RESULT == 2
	VARSET GLOBALS, "", 0, 100
	SAVEGLOBAL
	PRINTW 已清除登录的通信勇者信息
ELSEIF RESULT == 3
	PRINTFORML 请输入允许从其他存档来袭的勇者的等级上限
	PRINTFORML 如果输入小于0的数值，则不会从其他存档共享勇者
	INPUT
	FLAG:76 = RESULT
ELSEIF RESULT == 4
	INVERTBIT FLAG:77, 0
ELSEIF RESULT >=5 && RESULT != 9
	CALL MAOUNET_MODSCRIPT
ELSEIF RESULT == 9
	RETURN 0
ENDIF
GOTO START

;------------------------------------------------------------
;出力キャラの決定関数
;------------------------------------------------------------
@EXPORT
#DIM CHOICE, 5
#DIM LINE, 2
VARSET CHOICE, -1
LOCAL:1 = 0
LINE = LINECOUNT
$START
CLEARLINE LINECOUNT - LINE
FOR LOCAL, 0, CHARANUM
	;主人は除外
	SIF LOCAL == MASTER
		CONTINUE
	;敵は除外
	SIF CFLAG:LOCAL:1
		CONTINUE
	RESETCOLOR
	;選択済みのキャラは水色
	SIF MATCH(CHOICE, LOCAL)
		SETCOLOR 0x66FFFF
 	PRINTFORM [{LOCAL}] %SAVESTR:LOCAL% %GET_JOB_NAME(LOCAL),8,LEFT% 
	;IF TALENT:LOCAL:200
		;PRINT 战士
	;ELSEIF TALENT:LOCAL:201
		;PRINT 魔法师
	;ELSEIF TALENT:LOCAL:202
		;PRINT 神官
	;ELSEIF TALENT:LOCAL:203
		;PRINT 盗贼
	;ELSEIF TALENT:LOCAL:204
		;PRINT 肉便器
	;ELSEIF TALENT:LOCAL:205
		;PRINT 骑士 
	;ELSEIF TALENT:LOCAL:206
		;PRINT 巫女 
	;ELSEIF TALENT:LOCAL:207
		;PRINT 忍者 
	;ELSEIF TALENT:LOCAL:208
		;PRINT 弓手 
	;ELSEIF TALENT:LOCAL:210
		;PRINT 魔界将军
	;ELSEIF TALENT:LOCAL:210
		;PRINT 魔界神官 
	;ELSE
		;PRINT 职业名未设定
	;ENDIF
	PRINTFORML LV{CFLAG:LOCAL:9,4,RIGHT}　HP {BASE:LOCAL:0}／{MAXBASE:LOCAL:0} 调教次数 {CFLAG:LOCAL:10}
NEXT
RESETCOLOR
DRAWLINE
PRINTFORML 请选择要共享到其他存档中的奴隶({5-MATCH(CHOICE,-1)}/5)
DRAWLINE
SIF MATCH(CHOICE,-1) == 5
	SETCOLOR 0x404040
PRINTFORML [ 99] 决定
RESETCOLOR
PRINTFORML [100] 取消
$INPUT_LOOP1
INPUT
IF RESULT == 100
	RETURN 0
ELSEIF RESULT == 99
	SIF MATCH(CHOICE,-1) == 5
		GOTO INPUT_LOOP1
	FOR LOCAL, 0, 5
		SIF CHOICE:LOCAL == -1
			CONTINUE
		PRINTFORML %SAVESTR:(CHOICE:LOCAL)%
	NEXT
	PRINTFORML 以上的{5-MATCH(CHOICE,-1)}名勇者就可以了吗？
	PRINTL [0] 好的
	PRINTL [1] 不要
	$INPUT_LOOP2
	INPUT
	IF RESULT == 0
		SAVEDATA 999, "数据保存"
		FOR LOCAL, 0, CHARANUM
			IF MATCH(CHOICE, LOCAL)
				NICKNAME:LOCAL = %SAVESTR:LOCAL%
				CFLAG:LOCAL:190 = GETMILLISECOND()+LOCAL
			ELSE
				NICKNAME:LOCAL = 削除
			ENDIF
		NEXT
		FOR LOCAL, CHARANUM-1, -1, -1
			SIF NICKNAME:LOCAL == "削除"
				DELCHARA LOCAL
		NEXT
		PRINTFORML 请输入队伍名
		LINE:1 = LINECOUNT
		$INPUT_LOOP3
		INPUTS
		PRINTFORML 「%RESULTS%」这个队伍名可以吗？
		PRINTL [0] 好的
		PRINTL [1] 不要
		INPUT
		IF RESULT == 0
			LOCALS = %RESULTS% 
			GETTIME
			LOCALS = %RESULTS% %LOCALS%
			SAVEDATA 1000, LOCALS
			PRINTFORMW 已経将队伍保存到了SAVE1000.sav，在其他的存档中可以从这个文件里召来勇者！
			LOADDATA 999
		ELSEIF RESULT == 1
			GOTO INPUT_LOOP3
		ELSE
			CLEARLINE 1
			GOTO INPUT_LOOP2
		ENDIF
		LOADDATA 1020
	ELSEIF RESULT == 1
		GOTO START
	ELSE
		CLEARLINE 1
		GOTO INPUT_LOOP2
	ENDIF
ELSE
	IF RESULT != MASTER && RESULT < CHARANUM && RESULT >= 0 && !CFLAG:RESULT:1
		IF MATCH(CHOICE, RESULT)
			FOR LOCAL, 0, 5
				SIF CHOICE:LOCAL == RESULT
					CHOICE:LOCAL = -1
			NEXT
		ELSE
			IF !MATCH(CHOICE,-1)
				PRINTFORMW 一次最多送出5人！
				GOTO START
			ELSE
				FOR LOCAL, 0, 5
					IF CHOICE:LOCAL == -1
						CHOICE:LOCAL = RESULT
						BREAK
					ENDIF
				NEXT
			ENDIF
		ENDIF
		GOTO START
	ELSE
		CLEARLINE 1
		GOTO INPUT_LOOP1
	ENDIF
ENDIF
GOTO START

@INPORT_A
#DIM SAVE
DRAWLINE
PRINTL 请选择载入的序号
DRAWLINE
FOR LOCAL, 0 , 20
	CHKDATA  LOCAL+1000
	PRINTFORML [{LOCAL ,3}] \@ !RESULT ? %RESULTS% # ---- \@
NEXT
PRINTL [99] 取消
$INPUT_LOOP
INPUT
SAVE = RESULT + 1000
IF RESULT == 99
	RETURN 0
ELSEIF RESULT >= 0 && RESULT <= 19
	CHKDATA SAVE
	IF RESULT
		CLEARLINE 1
		GOTO INPUT_LOOP
	ENDIF
	SAVEDATA 999, "操作"
	LOADDATA SAVE
ELSE
	CLEARLINE 1
	REUSELASTLINE 无效值
	GOTO INPUT_LOOP
ENDIF
GOTO INPUT_LOOP

;キャラデータをGLOBALに出力する
@INPORT_B
#DIM CHARA
#DIM ADD_NUM
FOR LOCAL, 0, 101
	IF LOCAL == 100
		PRINTW 从别处来的勇者已经满员，无法追加！
		BREAK
	ENDIF
	SIF CHARA >= CHARANUM
		BREAK
	SIF STRLENS(GLOBALS:LOCAL) > 0
		CONTINUE
	FOR LOCAL:1, 0, 100
		SPLIT GLOBALS:(LOCAL:1), "_", RESULTS
		LOCAL:2 = 0
		IF RESULTS == TOSTR(CFLAG:CHARA:190)
			LOCAL--
			CHARA++
			;PRINTW 存在重复的勇者！
			LOCAL:2 = 1
			BREAK
		ENDIF
	NEXT
	SIF LOCAL:2
		CONTINUE
	GLOBALS:LOCAL += @"{CFLAG:CHARA:190}_"
	GLOBALS:LOCAL += @"{NO:CHARA}_"
	GLOBALS:LOCAL += @"{CFLAG:CHARA:9}_"
	GLOBALS:LOCAL += @"%NICKNAME:CHARA%_"
	FOR LOCAL:1, 0, VARSIZE("ABL")
		SIF ABL:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{ABL:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("BASE")
		SIF BASE:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{BASE:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("BASE")
		SIF MAXBASE:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{MAXBASE:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("CFLAG")
		SIF CFLAG:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{CFLAG:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("EXP")
		SIF EXP:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{EXP:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("EQUIP")
		SIF EQUIP:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{EQUIP:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("PALAM")
		SIF JUEL:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{JUEL:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("TALENT")
		SIF TALENT:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{TALENT:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("MARK")
		SIF MARK:CHARA:(LOCAL:1)
			GLOBALS:LOCAL += @"{LOCAL:1},{MARK:CHARA:(LOCAL:1)}/"
	NEXT
	GLOBALS:LOCAL += "_"
	FOR LOCAL:1, 0, VARSIZE("CSTR")
		SIF STRLENS(CSTR:CHARA:(LOCAL:1))
			GLOBALS:LOCAL += @"{LOCAL:1},%CSTR:CHARA:(LOCAL:1)%/"
	NEXT
	CHARA++
	ADD_NUM++
NEXT
PRINTFORMW \@ADD_NUM ? {ADD_NUM}名勇者追加完毕#没有可以追加的勇者\@
SAVEGLOBAL
LOADDATA 999


