﻿
;----------------------------------
;税収
;----------------------------------
;10.20.30日に税収が発生する
;その都度計算する

@TAX_GET
#DIM TAX,4

;TAX変数に計算した税を入れていく
;TAX:0は合計値

;税収日
IF DAY:2 == 10 || DAY:2 == 20 || DAY:2 == 30
	PRINTW 今天宜收税，宜鬼畜，宜调教，宜激烈做爱；忌纯爱，忌良心发现……
ELSE
	RETURN 0
ENDIF

TAX:0 = 0


DRAWLINE
PRINTL 
PRINTL - - - 收税 - - -
PRINTL 

WAIT

;--------------
;魔界からの支援
;--------------

;TAX:0 += DAY:0 * 50
;TAX:0 += 3000000
;IF  TAX:0 > 10000
;	TAX:0 = 10000
;ENDIF

IF EX_FLAG:99 <= 20 && EX_FLAG:99 >= 0
PRINTL 威望值是【岌岌可危】
	TAX:0 = 0
ELSEIF EX_FLAG:99 <= 40 && EX_FLAG:99 > 20
PRINTL 威望值是【动荡不安】
TAX:0 += DAY:0 * 30 * EX_FLAG:99 / 100
IF  TAX:0 > 5000
	TAX:0 = 5000
	ENDIF
ELSEIF EX_FLAG:99 <= 60 && EX_FLAG:99 > 40
PRINTl 威望值是【略受质疑】
TAX:0 += DAY:0 * 50 * EX_FLAG:99 / 100
IF  TAX:0 > 10000
	TAX:0 = 10000
	ENDIF
ELSEIF EX_FLAG:99 <= 80 && EX_FLAG:99 > 60
PRINTl 威望值是【相安无事】
TAX:0 += DAY:0 * 50 * EX_FLAG:99 / 100
IF  TAX:0 > 30000
	TAX:0 = 30000
	ENDIF
ELSEIF EX_FLAG:99 <= 100 && EX_FLAG:99 > 80
PRINTl 威望值是【广受爱戴】
TAX:0 += DAY:0 * 100 * EX_FLAG:99 / 100
IF  TAX:0 > 50000
	TAX:0 = 50000
	ENDIF
ENDIF

PRINTL 
PRINTFORMW 来自魔界的支援 {TAX:0}

;--------------
;土地税
;--------------
;侵攻度に応じて税が徴収される
;陥落するとボーナス
;メイン収入源にしたい
;TAX:1に合計値を格納

;初期値
TAX:1 = 0

PRINTL *土地税*

IF FLAG:82
	PRINTFORML ├ 地上的魔界领土 1200
	TAX:1 += 1200
ELSEIF FLAG:81 > 10
	PRINTFORML ├ 人间界殖民地 {FLAG:81 / 10}
	TAX:1 += FLAG:81 / 10
ENDIF

IF FLAG:87 == 2
	PRINTFORML ├ 黑暗精灵的领土 1200
	TAX:1 += 1200
ELSEIF FLAG:86 > 10
	PRINTFORML ├ 精灵族领域殖民地 {FLAG:86 / 10}
	TAX:1 += FLAG:86 / 10
ENDIF

IF FLAG:89 == 2
	PRINTFORML ├ 混沌龙之山 1200
	TAX:1 += 1200
ELSEIF FLAG:88 > 10
	PRINTFORML ├ 龙之山脉殖民地 {FLAG:88 / 10}
	TAX:1 += FLAG:88 / 10
ENDIF

IF FLAG:91 == 2
	PRINTFORML ├ 堕天使的淫界 1200
	TAX:1 += 1200
ELSEIF FLAG:90 > 10
	PRINTFORML ├ 天界的殖民地 {FLAG:90 / 10}
	TAX:1 += FLAG:90 / 10
ENDIF

IF FLAG:92 == 15
	PRINTFORML ├ 圣灵骑士的卖春堡垒 1500
	TAX:1 += 1500
ENDIF

;ダンジョンの収入
;レベルによって増加量が変動する

PRINTFORM └ 地下城 
IF CFLAG:0:9 < 20
	PRINTFORML {CFLAG:0:9 * 50 + 100}
	TAX:1 += CFLAG:0:9 * 50 + 100
ELSEIF CFLAG:0:9 < 40
	PRINTFORML {CFLAG:0:9 * 40 + 300}
	TAX:1 += CFLAG:0:9 * 40 + 300
ELSEIF CFLAG:0:9 < 80
	PRINTFORML {CFLAG:0:9 * 30 + 700}
	TAX:1 += CFLAG:0:9 * 30 + 700
ELSEIF CFLAG:0:9 < 150
	PRINTFORML {CFLAG:0:9 * 20 + 1500}
	TAX:1 += CFLAG:0:9 * 20 + 1500
ELSEIF CFLAG:0:9 < 300
	PRINTFORML {CFLAG:0:9 * 10 + 3000}
	TAX:1 += CFLAG:0:9 * 10 + 3000
ELSE
	PRINTFORML {CFLAG:0:9 * 5 + 4500}
	TAX:1 += CFLAG:0:9 * 5 + 4500
ENDIF



PRINTL 
PRINTFORMW 合计 {TAX:1}

TAX:0 += TAX:1

;--------------
;肉便器税
;--------------
;肉便器処刑のひとたちも売り上げで貢献
;あんまり稼げない方が肉便器っぽい
;石像やはく製は観覧料
;TAX:2に合計値を格納

;初期値
TAX:2 = 0

PRINTL 
PRINTL *肉便器税*

IF FLAG:84 > 0
	PRINTFORML ├ 展品观赏税 {FLAG:84 * 10}
	TAX:2 += FLAG:84 * 10
ENDIF

IF FLAG:83 > 0
	PRINTFORML ├ 肉便器使用税 {FLAG:83 * 10}
	TAX:2 += FLAG:83 * 10
ENDIF


;淫魔売春婦の収入
;ITEM:143 女巫
;ITEM:152 魅魔
;ITEM:182 莉莉丝
;は売春してそうなのでカウント
PRINTFORML └ 淫魔卖春税 {ITEM:143 * 2 + ITEM:152 * 2 + ITEM:182 * 2 + 20}
TAX:2 += ITEM:143 * 2 + ITEM:152 * 2 + ITEM:182 * 2 + 20

FOR LOCAL,1,10
	;娼館街1個につきボーナス
	SIF FLAG:(LOCAL + 349) == 507
		TIMES TAX:2, 1.1
NEXT

PRINTL 
PRINTFORMW 合计 {TAX:2}

TAX:0 += TAX:2

;--------------
;税収補正
;--------------

PRINTL 
PRINTL *魔王特別税*

TAX:3 = TAX:0 * (FLAG:9 + 100)
TAX:3 /= 100
TAX:3 -= TAX:0

PRINTFORMW 合計 {TAX:3}

TAX:0 += TAX:3

FLAG:9 = 0

;--------------
;合計税収
;--------------

DRAWLINE
PRINTL 
IF EX_FLAG:2811 >= 51 && EX_FLAG:2811 < 100
	LOCAL:10 = EX_FLAG:2811 / 10
	PRINTFORML 黑方片的商业运营有方，收入乘以1.{LOCAL:10}
	TAX:0 = TAX:0 * (10+LOCAL:10)/10
ENDIF
PRINTFORMW 合计税收 {TAX:0}

MONEY += TAX:0
EX_FLAG:4444 += TAX:0
RETURN 0


