﻿;EVENTFIRSTで使う関数を切り出した

;----------------------------------------------
;ARG:0番のキャラの性格を表示. -1ならTARGETのを表示.
;見つかったら何番目の性格かを、もし見つけられなければ-1を返す.
;----------------------------------------------
@SHOW_CHARASTERISTIC(ARG:0 = -1)
#DIM CHARA_ID
#DIM TALENT_ID

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

;タレントのチェック
FOR LOCAL:0, 0, VARSIZE("ID_OF_GENERAL_CHARASTERISTICS")
	;チェックするタレント番号を配列から拾う
	TALENT_ID = ID_OF_GENERAL_CHARASTERISTICS:(LOCAL:0)
	IF TALENT:CHARA_ID:TALENT_ID
		PRINTFORM %TALENTNAME:TALENT_ID%
		RETURN LOCAL:0
ENDIF
NEXT
RETURN -1

;----------------------------------------------
;ARG:0番のキャラにランダムな性格をつける.何番めの性格かを返す.
;----------------------------------------------
@SET_RANDOM_CHARASTERISTIC(ARG:0 = -1)
#DIM CHARA_ID
#DIM TEMP
#DIM TALENT_ID

$CHARA_GENERAL_CHARASTERISTICS_FIRST

;事前初期化
CALL CLEAR_CHARASTERISTIC(ARG:0)

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

TEMP = RAND( VARSIZE("ID_OF_GENERAL_CHARASTERISTICS") )
TALENT_ID = ID_OF_GENERAL_CHARASTERISTICS:TEMP
SIF TALENT_ID == 174
	GOTO CHARA_GENERAL_CHARASTERISTICS_FIRST
TALENT:CHARA_ID:TALENT_ID = 1
RETURN TEMP


;----------------------------------------------
;ARG:0番のキャラに(ARG:1)番めの性格をつける.範囲チェックしてない.
;----------------------------------------------
@SET_CHARASTERISTIC(ARG:0 = -1, ARG:1)
#DIM CHARA_ID
#DIM TALENT_ID

;事前初期化
CALL CLEAR_CHARASTERISTIC(ARG:0)

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

TALENT_ID = ID_OF_GENERAL_CHARASTERISTICS:(ARG:1)
TALENT:CHARA_ID:TALENT_ID = 1

;----------------------------------------------
;ARG:0番のキャラの性格設定を消す
;----------------------------------------------
@CLEAR_CHARASTERISTIC(ARG:0 = -1)
#DIM CHARA_ID
#DIM TALENT_ID

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

FOR LOCAL:0, 0, VARSIZE("ID_OF_GENERAL_CHARASTERISTICS")
	TALENT_ID = ID_OF_GENERAL_CHARASTERISTICS:(LOCAL:0)
	TALENT:CHARA_ID:TALENT_ID = 0
NEXT


;----------------------------------------------
;ARG:0番のキャラの性格を選ぶ. (ARG:1)個表示するごとに改行
;----------------------------------------------
@CHOOSE_CHARASTERISTIC(ARG:0 = -1, ARG:1 = 3)
#DIM CHARA_ID
#DIM TALENT_ID
#DIM SIZE

;事前初期化
CALL CLEAR_CHARASTERISTIC(ARG:0)

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

;一覧表示
LOCAL:1 = 0
SIZE = VARSIZE("ID_OF_GENERAL_CHARASTERISTICS")
FOR LOCAL:0, 0, SIZE
	TALENT_ID = ID_OF_GENERAL_CHARASTERISTICS:(LOCAL:0)
	IF TALENT_ID == 174
		GOTO CHOOSE_CHARASTERISTIC_TAG
	ENDIF
	PRINTFORM [{LOCAL:0,2}] %TALENTNAME:TALENT_ID, 10, LEFT%
	
	;改行チェック
	LOCAL:1 += 1
	IF (LOCAL:1) % (ARG:1) == 0
		PRINTL 
	ENDIF
$CHOOSE_CHARASTERISTIC_TAG
NEXT
PRINTL 

;入力
$INPUT_LOOP
INPUT
IF RESULT < 0 || RESULT > SIZE
	GOTO INPUT_LOOP
ELSE
	TALENT_ID = ID_OF_GENERAL_CHARASTERISTICS:(RESULT)
	TALENT:CHARA_ID:TALENT_ID = 1
ENDIF

;----------------------------------------------
; 髪色を表示.髪色の番号を返す.未設定なら0.
;----------------------------------------------
@SHOW_HAIRCOLOR(ARG:0 = -1)
#DIM CHARA_ID
#DIM COLOR_ID

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

;色番号をタレントから取得して表示
COLOR_ID = TALENT:CHARA_ID:300
PRINTFORM %ARR_HAIRCOLOR:COLOR_ID%

RETURN COLOR_ID

;----------------------------------------------
; 髪色をランダムに決定.番号を返す
;----------------------------------------------
@SET_RANDOM_HAIRCOLOR(ARG:0 = -1)
#DIM CHARA_ID
#DIM COLOR_ID

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )


;REF LOOK.ERB>@LOOK_SET
; 0- 0 粉髪  1%
; 1-20 金髪 20%
;21-30 青髪 10%
;31-40 緑髪 10%
;41-60 栗毛 20%
;61-80 黒髪 20%
;81-97 赤毛 17%
;98-99 銀髪  2%

SELECTCASE RAND:100
CASE 0
	;粉髪
	COLOR_ID = 11	
CASE 1 TO 20
	;金髪
	COLOR_ID = 1
CASE 21 TO 30
	;青髪
	COLOR_ID = 6
CASE 31 TO 40
	;緑髪
	COLOR_ID = 7
CASE 41 TO 60
	;栗毛
	COLOR_ID = 2
CASE 61 TO 80
	;黒髪
	COLOR_ID = 3
CASE 81 TO 97
	;赤毛
	COLOR_ID = 4
CASE 98 TO 99
	;銀髪
	COLOR_ID = 5
ENDSELECT

TALENT:CHARA_ID:300 = COLOR_ID
RETURN COLOR_ID

;----------------------------------------------
; 髪色を設定する.番号を返す.範囲チェックしてない.
;----------------------------------------------
@SET_HAIRCOLOR(ARG:0 = -1, ARG:1)
#DIM CHARA_ID
#DIM COLOR_ID

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

TALENT:CHARA_ID:300 = ARG:1
RETURN ARG:1

;----------------------------------------------
;ARG:0番のキャラの髪色を選ぶ. (ARG:1)個表示するごとに改行
;----------------------------------------------
@CHOOSE_HAIRCOLOR(ARG:0 = -1, ARG:1 = 10)
#DIM CHARA_ID
#DIM COLOR_ID
#DIM SIZE

;キャラ番号
CHARA_ID =( ARG:0 < 0 )?( TARGET )#( ARG:0 )

;一覧表示
LOCAL:1 = 0
SIZE = 8
FOR COLOR_ID, 1, SIZE
	PRINTFORM [{COLOR_ID,2}] %ARR_HAIRCOLOR:COLOR_ID, 6, LEFT%
	
	;改行チェック
	LOCAL:1 += 1
	IF (LOCAL:1) % (ARG:1) == 0
		PRINTL 
	ENDIF
NEXT
PRINTL 

;入力
$INPUT_LOOP
INPUT
IF RESULT < 1 || RESULT > SIZE
	GOTO INPUT_LOOP
ELSE
	TALENT:CHARA_ID:300 = RESULT
ENDIF
