﻿@CHAR_DEBUG(ARG)
DRAWLINE
PRINTFORML [1] 登录角色
PRINTFORML [2] 修改素质
PRINTFORML [3] 修改金钱
PRINTL [0] 返回
INPUT

IF RESULT == 1
	CALL CHAR_CREATE(1)
ELSEIF RESULT == 2
	CALL CHAR_CUSTOM, ARG ,1
ELSEIF RESULT == 3
	CALL EDIT_MONEY
ELSE
	RETURN
ENDIF
RESTART

@CHAR_CREATE(ARG)
#DIM L_I
DRAWLINE
PRINTFORML 使用神奇的生命摇篮，凭空创造出一个角色
PRINTFORML 这角色的一切，完全由魔王大人你自己定制
SIF ARG == 0
    PRINTFORML 这将耗费大量的金钱，幸好只看不买是免费的
DRAWLINE
PRINTL
WAIT

PRINTL ■=== 勇者 ===■
FOR L_I, 1, 17
	PRINTFORM  [{L_I,2}] %CSVNAME(L_I),14,LEFT%
	SIF L_I % 5 == 0
		PRINTL
NEXT
SIF !LINEISEMPTY()
	PRINTL

PRINTL ■=== 精英 ===■
FOR L_I, 1, 11
	PRINTFORM  [{L_I+20,2}] %CSVNAME(L_I+200),14,LEFT%
	SIF L_I % 5 == 0
		PRINTL
NEXT
SIF !LINEISEMPTY()
	PRINTL

IF ARG == 1	
PRINTL ■=== 特殊 ===■
LOCAL = 0
FOR L_I, 17, 40
	SIF !EXISTCSV(L_I) || L_I == 19 || L_I == 18
		CONTINUE
	PRINTFORM  [{L_I+20,2}] %CSVNAME(L_I),14,LEFT%
	LOCAL ++
	SIF LOCAL % 5 == 0
		PRINTL
NEXT
SIF !LINEISEMPTY()
	PRINTL
ENDIF
	
DRAWLINE
PRINT [999] 返回

$INPUT_LOOP
INPUT 1

SELECTCASE RESULT
CASE 999
	RETURN 0
CASE 1 TO 16
	L_I = RESULT
CASE 21 TO 30
	L_I = RESULT -20 +200
CASE 37 TO 60
	L_I = RESULT -20
CASEELSE
	L_I = RESULT
ENDSELECT

IF !EXISTCSV(L_I)
	CLEARLINE 1
	GOTO INPUT_LOOP
ENDIF

A = -1

;启用当前已登录的角色
SIF INRANGE(L_I,17,40)
	A = FINDCHARA(NO, L_I)

;登录新的角色
IF A < 0
	CALL CHAR_APPEND(L_I, ARG)
	A = RESULT
	PRINTFORMW 你召唤出了%SAVESTR:A%……
ENDIF

CALL CHAR_CUSTOM, A ,ARG



; ARG：csv编号（需要保证可用）
@CHAR_APPEND(ARG, ARG:1)
ADDCHARA ARG
CALL ADDCHARA_EX, CHARANUM-1
LOCAL = TARGET
TARGET = CHARANUM - 1
A = CHARANUM - 1

SELECTCASE ARG
;勇者 REF ENTER_ENEMY.ERB
CASE 1 TO 16
	SIF ARG:1 == 1
	    CALL CHAR_MAKE

;精英部下 REF SHOP_MONSTER.ERB
CASE 201 TO 210
	SIF ARG:1 == 1
	    CALL CHAR_MAKE
	
	;等级调整
	
;玛奥 REF SYSTEM.ERB
CASE 17
	SAVESTR:A = %NAME:A%
	CSTR:A:1 = %NAME:A%
	CFLAG:420 = 1
	
	CFLAG:9 = 1
	CFLAG:1 = 0
	CFLAG:11 = 15
	CFLAG:12 = 15
	CFLAG:13 = 15
	CFLAG:14 = 15
	CFLAG:16 = -1
	
	CALL CHAR_BODY_GENERATE_WAPPED, 1
	
;莉莉 REF ENTER_ENEMY.ERB
CASE 24
	SAVESTR:A = %NAME:A%
	CSTR:A:1 = %NAME:A%
	;初期装備：剑
	CFLAG:A:550 = 40
	;着替え装着
	TARGET = A
	CALL WEARING_CLOTH_ABLE
	CALL CHAR_BODY_GENERATE_WAPPED, A

;扑克牌 REF ARCANA_FORT
CASE 20 TO 23
	;東の砦　黑方片 &1
	IF ARG == 22
		;初期装備：剑
		CFLAG:A:550 = 40
		;初期装備：強度
		CFLAG:A:550 += 9000
		;初期装備接頭語：ダーク
		CFLAG:A:550 += 900000
		;名前決定
		CFLAG:A:6 = RAND:80
	;西の砦　白梅花 &4
	ELSEIF ARG == 23
		ABL:A:31 = 1
		EXP:A:10 = 30
		;初期装備：法杖
		CFLAG:A:550 = 41
		;初期装備：強度
		CFLAG:A:550 += 9000
		;初期装備接頭語：アイス
		CFLAG:A:550 += 600000
		;名前決定
		CFLAG:A:6 = RAND:80
	;南の砦　银黑桃 &2
	ELSEIF ARG == 21
		EXP:A:10 = 10
		;初期装備：手里剑
		CFLAG:A:550 = 44
		;初期装備：強度
		CFLAG:A:550 += 9000
		;初期装備接頭語：デス
		CFLAG:A:550 += 300000
		;名前決定
		CFLAG:A:6 = RAND:80
	;北の砦　金红桃 &8
	ELSEIF ARG == 20
		EXP:A:0 = 20
		;狂王が男か扶她ならば精液经验
		SIF FLAG:500 == 0 || FLAG:500 == 2
			EXP:A:5 = EXP:A:0
		;初体験の相手は狂王
		CFLAG:A:15 = 105
		;初期装備：细剑
		CFLAG:A:550 = 50
		;初期装備：強度
		CFLAG:A:550 += 10000
		;初期装備接頭語：スラッシュ
		CFLAG:A:550 += 400000
		;名前決定
		CFLAG:A:6 = RAND:80
	ENDIF

	SAVESTR:A = %NAME:A%
	CSTR:A:1 = %NAME:A%
	
	;衣装全装備
	CALL WEARING_CLOTH_ABLE
	CALL CHAR_BODY_GENERATE_WAPPED, A

	;レベルアップ処理
	IF FLAG:60 > 0
		REPEAT FLAG:60
			CALL ST_UP, A
		REND
	ENDIF

	BASE:A:0 = MAXBASE:A:0
	BASE:A:1 = MAXBASE:A:1

;贡品 REF ENDING.ERB
CASE 35, 31 TO 33
	CALL CHAR_INIT
; 狂王替身 葵希罗
CASE 34
	FLAG:224 = 1

	A = CHARANUM - 1
	SAVESTR:A = %NAME:A%
	CSTR:A:1 = %NAME:A%

	;着替え装着
	TARGET = A
	CALL WEARING_CLOTH_ABLE
	CALL CHAR_BODY_GENERATE_WAPPED, A
ENDSELECT

IF ARG:1 == 0 
	PRINTFORMW 请问登陆的角色是什么性别呢？
	DRAWLINE
	PRINTFORMW [1] 男性			[2] 女性			[3] 扶她
	INPUT
	SIF RESULT == 1
		TALENT:A:122 = 1
	SIF RESULT == 3
		TALENT:A:121 = 1
	DRAWLINE
   $INPUT_LOOP
   PRINTFORML 新建人物的名字是？（不输入将随机生成名字）
   INPUTS
          CALL CHARA_NAME_RANDOM_DEFINE(A)
   LOCALS '= RESULTS
   SELECTCASE STRLENS(LOCALS)
   CASE IS > 16
	   PRINTFORMW 名字太长，请使用全角八字以下的名字。
	   GOTO INPUT_LOOP
   CASE IS > 0
       PRINTFORMW 新建人物今后被称呼为%LOCALS%。
	   CALLNAME:A '= LOCALS
	   SAVESTR:A '= LOCALS
	   NAME:A '= LOCALS
   CASEELSE
	   CALL CHARA_NAME_RANDOM_DEFINE(A)
	   PRINTFORMW 新建人物今后被称呼为%NAME:A%。
   ENDSELECT
ENDIF
CFLAG:A:1 = 0

TARGET = LOCAL
RETURN A

;--------------------------------------------------
;所持金変更処理
;--------------------------------------------------
@EDIT_MONEY
PRINTFORML (持有${MONEY:0} or 持有${MONEY:1} or 持有${MONEY:2})
DRAWLINE
FOR LOCAL, 0, 3
	PRINTFORML [{LOCAL, 3}]MONEY:{LOCAL, 3}  = {MONEY:LOCAL}
NEXT
PRINTLC [200]返回
PRINTL 
$INPUT_LOOP
INPUT
IF RESULT == 200
	RETURN 0
ELSEIF RESULT < 0 || RESULT > 3
	GOTO INPUT_LOOP
ELSE
	LOCAL = RESULT
	DRAWLINE
	PRINTFORML MONEY:{LOCAL} 修改为：(现在{MONEY:LOCAL})
	PRINTFORML 请输入数值[0-99999999]（输入无效数值则取消）
	INPUT
	SIF RESULT < 0 || RESULT > 99999999
		RESTART
	MONEY:LOCAL = RESULT
ENDIF
RESTART




