﻿<!-- eramaker的变量清单 -->

== 通用变量 ==

=== A-Z ===

从A到Z的以英文单字母命名的变量。

可以作为一次性的变量随意使用。但不应当用于长时间数据存储，因为其随时可能被其他函数重新赋值。

=== COUNT ===

使用REPEAT指令时，记录重复次数的计数变量。

为了保证运行的稳定和可靠，应当避免在REPEAT~REND的内部修改COUNT变量的值。

（注意：任何新的脚本应当避免使用REPEAT~REND，而应当使用FOR~NEXT作为代替。详见[[REPEAT~REND]]）

=== RESULT ===

记录了指令或函数数值返回值的变量。

由于不能确定什么时候会被重新赋值，应当在执行完指令或函数后立即对RESULT内的数据转移。

=== RESULTS ===

记录了指令或函数字符串返回值的变量。

由于不能确定什么时候会被重新赋值，应当在执行完指令或函数后立即对RESULT内的数据转移。

== 基础信息变量 ==

=== DAY ===

记录日期的变量。单位不定，请灵活使用。

=== TIME ===

记录时刻的变量。单位不定，请灵活使用。

=== MONEY ===

记录金钱的变量。
由于在商店购买物品时程序将读取此变量，因此应当仅在获得金钱或失去金钱时修改变量值。

== 调教基础信息变量 ==

=== MASTER ===

主人公的角色登录序号。注意并非CharaXX.csv中指定的番号（NO）。通常为0。

=== TARGET ===

调教目标的角色登录序号。注意并非CharaXX.csv中指定的番号（NO）。

=== ASSI ===

助手的角色登录序号。注意并非CharaXX.csv中指定的番号（NO）。

=== PLAYER ===

调教者的角色登录番号。通常为MASTER或ASSI一致。注意并非CharaXX.csv中指定的番号（NO）。

=== CHARANUM ===

当前登录角色的数量。包括主人公。此变量不能由用户修改变量值。

=== ASSIPLAY ===

指示当前是否为助手在进行调教的变量。1 - 助手在进行调教；0 - 否。

=== SELECTCOM ===

当前选择的调教指令。与Train.csv中的编号相同。

=== PREVCOM ===

上次选择的调教指令。用于在连续执行相同指令时产生惩罚。

== 调教变量 ==

=== LOSEBASE ===

由调教指令导致的基础数值（BASE）的损失。
通常LOSEBASE:0表示体力的消耗，LOSEBASE:1表示气力的消耗。

=== UP ===

由调教指令导致的调教PALAM的上升。
UP:X中的X为PALAM.CSV中指定的编号。

=== DOWN ===

由调教指令导致的调教PALAM的下降。
DOWN:X中的X为PALAM.CSV中指定的编号。

=== PALAMLV ===

调教PALAM等级的边界值。
若调教PALAM超过这个边界值，则调教结束后会得到更多的宝珠（JUEL）。

=== EXPLV ===

经验（EXP）等级的边界值。
若经验数值超过这个边界值，则调教效果会有增加（特别是V经验与A经验）。

=== EJAC ===

射精检查时使用的临时变量。
可読性のために独立した変数にしてありますが、実質はただのデータ入れです。

== 旗标 ==

=== FLAG ===

用来记录游戏各种状态的变量。例如使用FLAG:0判断调教中的角色是否处于休息状态。
另外可以用于经常性判断某一事件是否已发生。

=== TFLAG ===

用来记录游戏各种状态的变量。
例如射精等的记录以及奉仕系调教指令是否已经执行的记录。
请作为Temporary Flag（临时旗标）或Training Flag（调教旗标）来使用。
总之，它是类似FLAG的但却用于临时的变量。

注意：TFLAG将在调教开始时全部清零。

== 人物数据 ==

用语规定：
* 下文的用语服从于[[Emuera用语集]]中的规定。
* 区分'''角色变量'''与'''数组变量'''，不认为角色变量是一种特殊的数组变量。
* 区分'''数组'''与'''数组变量'''，认为角色变量不是数组变量，但是一个数组。
* 区分'''数组的元/维'''与'''数组变量的元/维'''。例如认为角色变量CFLAG是一个角色变量与一元数组变量的结合体，但CFLAG本身是一个二元数组。
* 双重数组。第1维为不同角色，第2维为该角色的不同元素的二元数组类型的角色变量。


大部分角色变量都是双重数组变量，形如<code>EXP:1:2</code>的表示方式。
（其中第1参数为角色序号，第2参数为经验）

有些时候也可以写作<code>EXP:0</code>。这种情况下将被解释为<code>EXP:TARGET:0</code>。
即当第1参数为调教目标时可以省略。

=== NO ===

角色编号。不是二重数组，通过形如NO:TARGET、NO:ASSI的方式访问。

=== BASE ===

角色的基础数值。例如BASE:0表示体力、BASE:1表示气力、BASE:2表示射精压力。

=== MAXBASE ===

角色的基础数值最大值。

=== ABL ===

角色的能力。通过Abl.csv中登录的能力编号来访问。

=== TALENT ===

角色的素质。通过Talent.csv中登录的素质编号来访问。

=== EXP ===

角色的经验。通过Exp.csv中登录的经验编号来访问。

=== MARK ===

角色的刻印。通过Mark.csv中登录的刻印编号来访问。

=== RELATION ===

=== JUEL ===

=== CFLAG ===

=== ISASSI ===

=== NAME === 

=== CALLNAME === 

=== TEQUIP === 

=== PALAM === 

=== STAIN === 

=== EX === 

=== SOURCE === 

=== NOWEX === 

=== GOTJUEL === 


== 物品数据 ==

== 名称数据 ==

== 文本数据 ==

== 随机数 ==

== 参见 ==

* [[数组用语规定]]
* [[Emuera扩展语法]]
* [[定量与变量]]

== 外部链接 ==

* {{Jp}} 漠々ト、獏 (2006), [http://cbaku.com/b/erakanon/eramavar.html  eramaker era basic変数リスト（暫定版） ]

[[分类:eramaker wiki]]

