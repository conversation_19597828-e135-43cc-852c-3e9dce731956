﻿;-------------------------------------------------
;能力値アップ
;-------------------------------------------------
@ABILITY_UP
#DIM SELECT_MENU = 998
#DIM TMP_TARGET
#DIM SORT_SELECT
#DIM SORT_ACT
#DIM NO_PAGE = 0
#DIM L_LCOUNT
#DIM NUM_PAGE = 24
#DIM T_LCOUNT
#DIM MAX_PAGE
;-------------------------------------------------
LIST_POS = 1
PREV_PAGE = 0
PREV_LIST_POS = 0
$INPUT_LOOP_MENU
;-------------------------------------------------
;缓存、重置列表信息
IF NO_PAGE == 0
	LIST_POS = 1
	PREV_PAGE = 0
	PREV_LIST_POS = 1
ELSEIF NO_PAGE < PREV_PAGE
	SWAP LIST_POS, PREV_LIST_POS
ELSEIF NO_PAGE == PREV_PAGE
	LIST_POS = PREV_LIST_POS
ELSE
	PREV_LIST_POS = LIST_POS
ENDIF
	
IF PREV_MODE != SELECT_MENU
	NO_PAGE = 0
	LIST_POS = 0
	PREV_PAGE = 0
	PREV_LIST_POS = 0
ENDIF
IF SELECT_MENU == 998
	LIST_POS++
	PREV_LIST_POS++
ENDIF

PREV_MODE = SELECT_MENU
;-------------------------------------------------
CUSTOMDRAWLINE =
SETFONT "ARIEL BLACK"
FONTBOLD
;ダンジョンレベル20以上からアンロック
SIF CFLAG:0:9 < 20
	SETCOLOR (GETDEFCOLOR() - 0x444444)
PRINTBUTTON @"%UNICODE(0x258c)%奴隶一览		", 998
PRINTBUTTON @"%UNICODE(0x258c)%勇者一览	", 997
;PRINTLC [998] - 奴隶一览 
;PRINTLC [997] - 勇者一览
PRINTL
RESETCOLOR
FONTREGULAR
SETFONT "ＭＳ ゴシック"
DRAWLINE

PRINTL 要提高谁的能力值？
DRAWLINE
L_LCOUNT = LINECOUNT
T_LCOUNT = NO_PAGE * NUM_PAGE + 1
IF SELECT_MENU == 997
	NUM_PAGE = 24
	CALL MAX_PAGE_ENEMY(NUM_PAGE)
	MAX_PAGE = RESULT - 1
	CALL LIFE_LIST_ENEMY(NO_PAGE,NUM_PAGE,LIST_POS)
ELSE
	;イレギュラーも含めて正規の値に戻す
	SELECT_MENU = 998
	NUM_PAGE = 23
	CALL MAX_PAGE_SALAVE(NUM_PAGE)
	MAX_PAGE = RESULT - 1
	PRINTFORML [{0,2}] %NAME:MASTER,12,LEFT% %"",6,LEFT% LV{CFLAG:MASTER:9,4,RIGHT}
	CALL LIFE_LIST_SALAVE(NO_PAGE,NUM_PAGE,LIST_POS)
ENDIF
L_LCOUNT = LINECOUNT - L_LCOUNT
	IF L_LCOUNT < (NUM_PAGE + 1)
		SIF SELECT_MENU == 998
			NUM_PAGE++
		REPEAT (NUM_PAGE - L_LCOUNT)
			PRINTL
		REND
		SIF SELECT_MENU == 998
			NUM_PAGE--
	ENDIF
	DRAWLINE	
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PREV_PAGE = NO_PAGE
PRINTL  

$INPUT_LOOP_0
INPUT
IF RESULT == 999
	RETURN 0
ELSEIF (RESULT > 990 && RESULT < 999) && CFLAG:0:9 < 20
	PRINTW 这个指令需要更高等级
	CLEARLINE 2
	GOTO INPUT_LOOP_0
ELSEIF RESULT > 990 && RESULT < 999
	;メニューは一応991～998に含みを持たせています
	;イレギュラーは全て奴隷一覧になります
	SELECT_MENU = RESULT
	GOTO INPUT_LOOP_MENU
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_MENU
ELSEIF RESULT == 1001		;下一页
	IF NO_PAGE < MAX_PAGE
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_MENU
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	PRINTW 数值已超出允许范围外
	CLEARLINE 2
	GOTO INPUT_LOOP_0
;臨死中のキャラは排除
ELSEIF  BASE:RESULT:0 < 1
	PRINTW 濒死中，无法选择
	CLEARLINE 2
	GOTO INPUT_LOOP_0
;自分は排除
; ELSEIF RESULT == 0
	; PRINTW 不能提高自己的能力
	; CLEARLINE 2
	; GOTO INPUT_LOOP_0
;奴隷待機以外は除外
ELSEIF CFLAG:RESULT:1 != 0 && SELECT_MENU == 998
	PRINTW 不能选择非待命状态的奴隶
	CLEARLINE 2
	GOTO INPUT_LOOP_0
;侵攻中勇者以外は除外
ELSEIF CFLAG:RESULT:1 != 2 && SELECT_MENU == 997
	PRINTW 不能选择非侵攻状态的勇者
	CLEARLINE 2
	GOTO INPUT_LOOP_0
ENDIF

SIF RESULT == 0
	RESULT = MASTER

CALL ABILITY_UP_CORE(RESULT)
RESTART

@ABILITY_UP_CORE(ARG)
T = TARGET
TARGET = ARG

$INPUT_LOOP_1
DRAWLINE
PRINTFORML %SAVESTR:TARGET%
CUSTOMDRAWLINE ‥
CALL SHOW_INFO_EXP
CALL SHOW_JUEL
CALL SHOW_ABLUP_SELECT

INPUT

;阴蒂感觉
IF RESULT == 0
	CALL ABLUP0
;乳房感觉
ELSEIF RESULT == 1
	CALL ABLUP1
;私处感觉
ELSEIF RESULT == 2
	CALL ABLUP2
;肛门感觉
ELSEIF RESULT == 3
	CALL ABLUP3
;局部感覚
ELSEIF RESULT == 4
	CALL ABLUP4
;顺从
ELSEIF RESULT == 10
	CALL ABLUP10
;欲望
ELSEIF RESULT == 11
	CALL ABLUP11
;技巧
ELSEIF RESULT == 12
	CALL ABLUP12
;侍奉技术
ELSEIF RESULT == 13
	CALL ABLUP13
;性交技术
ELSEIF RESULT == 14
	CALL ABLUP14
;话术
ELSEIF RESULT == 15
	CALL ABLUP15
;侍奉精神
ELSEIF RESULT == 16
	CALL ABLUP16
;露出癖
ELSEIF RESULT == 17
	CALL ABLUP17
;抖S气质
ELSEIF RESULT == 20
	CALL ABLUP20
;抖M气质
ELSEIF RESULT == 21
	CALL ABLUP21
;百合气质
ELSEIF RESULT == 22
	CALL ABLUP22
;ホモっ気
ELSEIF RESULT == 23
	CALL ABLUP23
;性交中毒
ELSEIF RESULT == 30
	CALL ABLUP30
;自慰中毒
ELSEIF RESULT == 31
	CALL ABLUP31
;精液中毒
ELSEIF RESULT == 32
	CALL ABLUP32
;百合中毒
ELSEIF RESULT == 33
	CALL ABLUP33
;卖淫中毒
ELSEIF RESULT == 37
	CALL ABLUP37
;兽奸中毒
ELSEIF RESULT == 39
	CALL ABLUP39
;局部中毒
ELSEIF RESULT == 40
	CALL ABLUP40
;反抗刻印
ELSEIF RESULT == 99
	CALL ABLUP99
ELSEIF RESULT == 100
	CALL ABLUP100
ELSEIF RESULT == 999
	CALL YOKUBO_UP_CHECK
	CALL CHECK_SELLASSIABLE
	;CALL CHECK_SPECIALSKIL
	TARGET = T
	RETURN 0
ENDIF

GOTO INPUT_LOOP_1

;-----------------------------------
@INTERCEPT
#DIM SELECT
#DIM ROOMID
#DIM ROOM
#DIM FLOOR
#DIM WORK
#DIM ITEM_GET
#DIM COST
#DIM NO_PAGE = 0
#DIM NUM_PAGE = 26
#DIM MAX_PAGE
#DIM T_LCOUNT
#DIM L_LCOUNT
;------------------------------------
;迎撃指定
;SELECT = 指定された迎撃奴隷
;ROOMID = 设施の場所
;ROOM = 设施
;FLOOR = 出発階層
;WORK  = 迎撃時の行動
;ITEM_GET  = アイテム補給
LIST_POS = 0
PREV_PAGE = 0
PREV_LIST_POS = 0

FOR COUNT, 0, CHARANUM
{
	SIF	BASE:COUNT:0 < 1 ||
		COUNT == 0 ||
		CFLAG:COUNT:1 != 0 ||
		(CFLAG:COUNT:0 == 0 && TALENT:COUNT:254 == 0) ||
		(TALENT:COUNT:153 == 1 && GETBIT(FLAG:5,10) == 0) ||
		(EX_TALENT:COUNT:1 && EX_TALENT:COUNT:2 == 0) ||
		(EX_TALENT:COUNT:1 && EX_TALENT:COUNT:2 && GETBIT(EX_FLAG:9000,1) == 0) 
}
		CONTINUE
	MAX_PAGE++
NEXT
IF (MAX_PAGE % NUM_PAGE) > 0
	MAX_PAGE = ( MAX_PAGE / NUM_PAGE ) + 1
ELSE
	MAX_PAGE = MAX_PAGE / NUM_PAGE
ENDIF
MAX_PAGE--
$INPUT_LOOP_MAIN0
;-------------------------------------------------
;缓存、重置列表信息
IF NO_PAGE == 0
	LIST_POS = 0
	PREV_PAGE = 0
	PREV_LIST_POS = 0
ELSEIF NO_PAGE < PREV_PAGE
	SWAP LIST_POS, PREV_LIST_POS
ELSEIF NO_PAGE == PREV_PAGE
	LIST_POS = PREV_LIST_POS
ELSE
	PREV_LIST_POS = LIST_POS
ENDIF
;-------------------------------------------------
CUSTOMDRAWLINE =
PRINT 派遣谁前去迎击勇者？
COST = 6000
PRINTFORML <状态若不为[可被卖]、将需要{COST}pt资金来派遣>
DRAWLINE
L_LCOUNT = LINECOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
FOR COUNT, LIST_POS, CHARANUM
{
	SIF (
		BASE:COUNT:0 < 1 ||
		COUNT == 0 ||
		CFLAG:COUNT:1 != 0 ||
		(CFLAG:COUNT:0 == 0 && TALENT:COUNT:254 == 0) ||
		(TALENT:COUNT:153 == 1 && GETBIT(FLAG:5,10) == 0) ||
		(EX_TALENT:COUNT:1 && EX_TALENT:COUNT:2 == 0) ||
		(EX_TALENT:COUNT:1 && EX_TALENT:COUNT:2 && GETBIT(EX_FLAG:9000,1) == 0) 
		) &&
		T_LCOUNT < (NO_PAGE + 1)*NUM_PAGE + 1 && T_LCOUNT >= NO_PAGE* NUM_PAGE + 1
}
		CONTINUE
	CALL LIFE_LIST_ITEM(COUNT)
NEXT
L_LCOUNT = LINECOUNT - L_LCOUNT
	IF L_LCOUNT < (NUM_PAGE + 1)
		REPEAT (NUM_PAGE - L_LCOUNT)
			PRINTL
		REND
	ENDIF
DRAWLINE
PRINTLC [1000] - 上一页
PRINTLC [999] - 返  回
PRINTLC [1001] - 下一页
PREV_PAGE = NO_PAGE

$INPUT_LOOP_2
INPUT
IF RESULT == 999
	RETURN 0
ELSEIF RESULT == 1000		;上一页
	IF NO_PAGE > 0
		NO_PAGE --
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_MAIN0
ELSEIF RESULT == 1001		;下一页
	IF NO_PAGE < MAX_PAGE
		NO_PAGE ++
		CLEARLINE LINECOUNT-L_LCOUNT
	ENDIF
	GOTO INPUT_LOOP_MAIN0
ELSEIF RESULT < 0 || RESULT >= CHARANUM
	CLEARLINE 1
	GOTO INPUT_LOOP_2
;臨死中のキャラは排除
ELSEIF  BASE:RESULT:0 < 1
	CLEARLINE 1
	GOTO INPUT_LOOP_2
;自分は排除
ELSEIF RESULT == 0
	PRINTW 魔王大人，亲自迎击的话，这几天就不能爱爱了哦！才不要！
	CLEARLINE 2
	GOTO INPUT_LOOP_2
;奴隷待機以外は除外
ELSEIF CFLAG:RESULT:1 != 0
	CLEARLINE 1
	GOTO INPUT_LOOP_2
;売却可以外は除外
ELSEIF CFLAG:RESULT:0 == 0 && TALENT:RESULT:254 == 0
	PRINTFORMW %SAVESTR:RESULT%还未被驯服，拒绝你的命令了。
	CLEARLINE 2
	GOTO INPUT_LOOP_2
;お金が必要
ELSEIF CFLAG:RESULT:0 == 0 && TALENT:RESULT:254 == 1 && MONEY < COST
	PRINTFORMW 金钱不足，%SAVESTR:RESULT%无视了你的命令
	GOTO INPUT_LOOP_2
;妊婦は除外
ELSEIF TALENT:RESULT:153 == 1 && GETBIT(FLAG:5,10) == 0
	PRINTFORMW %SAVESTR:RESULT%怀孕了，派孕妇打仗是违反月内瓦条约的～
	CLEARLINE 2
	GOTO INPUT_LOOP_2
;近卫兵
ELSEIF EX_TALENT:RESULT:1 && EX_TALENT:RESULT:2 == 0
	PRINTW 待着身边的才叫近卫嘛。
	CLEARLINE 2
	GOTO INPUT_LOOP_2
;后代
ELSEIF EX_TALENT:RESULT:2 && GETBIT(EX_FLAG:9000,1) == 0
	PRINTW 毕竟是自己的孩子，怎么忍心随意放手嘛。
	CLEARLINE 2
	GOTO INPUT_LOOP_2
ELSEIF EX_TALENT:RESULT:2 && GETBIT(EX_FLAG:9000,1)
ENDIF

SIF CFLAG:RESULT:0 == 0
	PRINT 支付了金钱
PRINTFORMW *%SAVESTR:RESULT%作为你的爪牙外出迎击了*

SELECT = RESULT

FLOOR = 9
WORK = CFLAG:SELECT:500
ITEM_GET = 0

$INPUT_LOOP_MAIN
PRINTFORML %SAVESTR:SELECT%的迎击设定
DRAWLINE
PRINTFORML [0] 出发阶层　　　- {FLOOR}层
 PRINTFORM [1] 迎击时顺便　　- 
IF WORK == 1
	PRINTL 卖淫
ELSEIF WORK == 2
	PRINTL 补充陷阱
ELSEIF WORK == 3
	PRINTL 扩张设施(要2000G)
ELSEIF WORK == 4
	PRINTL 潜入敌方
ELSEIF WORK == 5
	PRINTL 训练
ELSE
	PRINTL 内职
ENDIF
 PRINTFORM [2] 道具的补给　　- 
IF ITEM_GET == 1
	PRINTL 全副整装(要2000G)
ELSE
	PRINTL 裸奔吧，奴隶！
ENDIF
PRINTL  
SETCOLORBYNAME DarkSeaGreen
PRINTFORM [998] 去吧！皮卡丘！　　
RESETCOLOR
PRINTFORML [999] 返回

INPUT

IF RESULT == 999
	GOTO INPUT_LOOP_MAIN0
ELSEIF RESULT == 0
	GOTO INPUT_LOOP_4
ELSEIF RESULT == 1
	GOTO INPUT_LOOP_3
ELSEIF RESULT == 2 && ITEM_GET == 0
	IF MONEY < 2000 
		PRINTFORMW * 魔王大人，你怎么这么穷 *
		GOTO INPUT_LOOP_MAIN
	ELSEIF CFLAG:SELECT:0 == 0 && MONEY < (COST + 2000)
		PRINTFORMW * 魔王大人，你怎么这么穷 *
		GOTO INPUT_LOOP_MAIN
	ENDIF
	ITEM_GET = 1
	GOTO INPUT_LOOP_MAIN
ELSEIF RESULT == 2
	ITEM_GET = 0
	GOTO INPUT_LOOP_MAIN
ELSEIF RESULT != 998
	GOTO INPUT_LOOP_MAIN
ENDIF

;出撃決定
CFLAG:SELECT:1 = 3
CFLAG:SELECT:500 = WORK
CFLAG:SELECT:501 = FLOOR
CFLAG:SELECT:502 = 90
CFLAG:SELECT:505 = 0
IF CFLAG:SELECT:0 == 0
	MONEY -= COST
	EX_FLAG:4444 -= COST
ENDIF

IF WORK == 3
	MONEY -= 2000
	EX_FLAG:4444 -= 2000
ENDIF
IF ITEM_GET == 1
	MONEY -= 2000
	EX_FLAG:4444 -= 2000
	;入手アイテム数
	LOCAL:2 = 0
	FOR LOCAL:1, 0, 3
		CALL ADD_EX_ITEM, -1, SELECT , 2
		SIF RESULT > 0
			LOCAL:2 += 1
	NEXT
	;返金
	IF LOCAL:2 == 0
		PRINTFORML 补给已满，资金被退还了。
		MONEY += 2000
		EX_FLAG:4444 += 2000
	ENDIF
ENDIF

CALL GOHOUBI_REQUEST, SELECT

RETURN 0

;------------------------
;出発階層設定
;------------------------
$INPUT_LOOP_4
PRINTL 出发层设定
PRINTFORML 可用魔王的力量把%SAVESTR:SELECT%传送到任意阶层。
PRINTL 从那一层出发？ (1-9)
PRINTL [1] [2] [3] [4] [5] [6] [7] [8] [9]

INPUT
IF RESULT >= 1 && RESULT <= 9
	FLOOR = RESULT
ELSE
	GOTO INPUT_LOOP_4
ENDIF

IF WORK == 3
	ROOMID = FLOOR + 349
	
	ROOM = FLAG:ROOMID
	
	IF ROOM == 0
		PRINTFORMW {FLOOR}层没有任何设施
		GOTO INPUT_LOOP_MAIN
	ENDIF
	
	ROOMID += 10
	LOCAL = FLAG:ROOMID
	
	IF LOCAL == 3
		PRINTFORMW {FLOOR}层的%ITEMNAME:ROOM%已经扩张到极限了。
		GOTO INPUT_LOOP_MAIN
	ENDIF
ENDIF

GOTO INPUT_LOOP_MAIN

;------------------------
;行動設定
;------------------------
$INPUT_LOOP_3

PRINTL 在地下城内的行动为
DRAWLINE
PRINTL [0] 内职 
IF CFLAG:0:9 >= 10
	PRINTL [1] 卖淫
ELSE
	SETCOLOR 80,80,80
	PRINTL [---] （魔王等级不足） 
	RESETCOLOR
ENDIF

IF CFLAG:0:9 >= 20
	PRINTL [2] 补充陷阱
ELSE
	SETCOLOR 80,80,80
	PRINTL [---] （魔王等级不足）
	RESETCOLOR
ENDIF


IF CFLAG:0:9 >= 30
	PRINTL [3] 扩张设施(要2000G) 
ELSE
	SETCOLOR 80,80,80
	PRINTL [---] （魔王等级不足） 
	RESETCOLOR
ENDIF

IF CFLAG:0:9 >= 40
	PRINTL [4] 潜入工作 
ELSE
	SETCOLOR 80,80,80
	PRINTL [---] （魔王等级不足） 
	RESETCOLOR
ENDIF

IF CFLAG:0:9 >= 50
	PRINTL [5] 训练
ELSE
	SETCOLOR 80,80,80
	PRINTL [---] （魔王等级不足） 
	RESETCOLOR
ENDIF

DRAWLINE


INPUT
IF RESULT < 0
	GOTO INPUT_LOOP_3
ELSEIF RESULT == 1 && CFLAG:0:9 < 10
	GOTO INPUT_LOOP_3
ELSEIF RESULT == 2 && CFLAG:0:9 < 20
	GOTO INPUT_LOOP_3
ELSEIF RESULT == 3 && CFLAG:0:9 < 30
	GOTO INPUT_LOOP_3
ELSEIF RESULT == 4 && CFLAG:0:9 < 40
	GOTO INPUT_LOOP_3
ELSEIF RESULT == 5 && CFLAG:0:9 < 50
	GOTO INPUT_LOOP_3
ELSEIF RESULT >= 6
	GOTO INPUT_LOOP_3
ENDIF

IF RESULT == 1
	PRINTW 得到了在地下城中对怪物们卖淫的许可
ELSEIF RESULT == 2
	PRINTW 将进行陷阱的补充作业
ELSEIF RESULT == 3
	ROOMID = FLOOR + 349
	
	ROOM = FLAG:ROOMID
	
	IF ROOM == 0
		PRINTFORMW {FLOOR}层没有任何设施
		GOTO INPUT_LOOP_MAIN
	ENDIF
	
	ROOMID += 10
	LOCAL = FLAG:ROOMID
	
	IF LOCAL == 3
		PRINTFORMW {FLOOR}层的%ITEMNAME:ROOM%已经扩张到极限了。
		GOTO INPUT_LOOP_MAIN
	ENDIF
	
	PRINTFORMW {FLOOR}层的%ITEMNAME:ROOM%扩张需要2000资金。
	
	IF MONEY < 2000 
		PRINTFORMW * 魔王大人，你怎么这么穷 *
		GOTO INPUT_LOOP_MAIN
	ENDIF

ELSEIF RESULT == 4
	PRINTW 对勇者队伍的潜入工作进行中
ELSEIF RESULT == 5
	PRINTW 在迎击的过程中进行了训练并得到了经验值
ELSE
	PRINTW 得到了收入
ENDIF
WORK = RESULT

GOTO INPUT_LOOP_MAIN

RETURN 0

;------------------------
@GOHOUBI_REQUEST, ARG:0
#DIM SELECT
#DIM WISH
;------------------------
;SELECT = 選択された奴隷
;WISH = 希望するもの

SELECT = ARG:0

IF TALENT:SELECT:136 == 1 && RAND:3 == 0
	WISH = RAND:3 + 1
ELSEIF TALENT:SELECT:85 == 1
	WISH = RAND:3 + 4
	IF WISH == 6 && !TALENT:MASTER:121 && !TALENT:MASTER:122
		WISH = 4
	ENDIF
ELSEIF TALENT:SELECT:76 == 1
	WISH = RAND:3 + 7
ELSE
	WISH = 0
ENDIF


CFLAG:SELECT:504 = WISH
A = SELECT

CALL GOHOUBI_REQUEST_KOUJO

A = 0



