﻿eramaou_PREGNANCY_20140526

※(eramaou　ver.0.301を推奨)

加筆・改変・再アップロードはご自由にどうぞ。

■使い方
　全てのERBファイルをERBフォルダに上書きコピーしてください。

■仕様
　・奴隷選択時、助手選択時、迎撃選択時、能力表示時に愛、淫乱、未陥落を表示、妊娠した後の臨月時に育児室の表示。
　　・妊娠時には迎撃や侵攻が不可になります。（ＮＴＲ先で出産というのもアリですが迷宮内でもＮＴＲ先でも出産されると困るので…）
　　・それに伴い臨月時には調教が不可になります。
　・妊娠時の崩壊の条件をちょっと変更。【母性】が入手出来るように。
　・妊娠関連の関数で１文字変数を使わないように。
　　・モンスター召喚関数も１文字変数を使わないように。
　　・侵攻関数も１文字変数を使わないようにしていますが@MONSTER_DATA関数の関係で一部残してあります。

■追加・修正箇所
　■eramaouフラグまとめ.txt
　CFLAG:10に育児室を追加。

　■ARCANA_FORT.ERB
　125、156、160行目で【妊娠】素質のチェックを入れて出撃が出来ないように。
　
　■CHARA_INFO.ERB
　76行目に愛と淫乱と未陥落の表示。
　113行目に育児室の表示。

　■DUNGEON_TRAP.ERB
　@SUMMON_TRAP関数を修正。
　Z = A
　A = 0
　CALL SUMMON_MONSTER
　A = Z
　を
　CALL SUMMON_MONSTER, 0
　に修正。

　■EVENT_NEXTDAY.ERB
　妊娠出産関連で83行目のC = COUNTを削除
　CALL CHILD_BIRTHをCALL CHILD_BIRTH(COUNT)に修正。
　「出産20日で親離れで調教可能に」のCALL DEPEARENTの部分を削除

　■EVENT_PREGNANCY.ERB
　１文字変数を使わないように。
　521行目で陥落素質がついていないときのストレスを+50に修正。未陥落時は出産経験数でストレスが増えます。
　569行目、臨月到達時に育児室に移動するので調教や朝フェラなどが不可に。
　645行目、【愛】持ちで父親が主人なら【母性】に目覚める。
　　今後育児関連を入れるのならここを弄る。
　育児放棄をした場合と親離れ@DEPEARENT関数削除。
　使用されていなかったので育児室にいるかの判定@CHECK_CHILD_CARE関数削除。

　■INVASION.ERB
　186、216、220行目で【妊娠】素質のチェックを入れて侵攻が出来ないように。
　1文字変数を出来るだけ使わないように。（@MONSTER_DATA関数で使うのでA,B,E,X,Zは使用）

　■INVASION_RYOUZYOKU.ERB
　バグ対策にA=0を追加。@MONSTER_DATE関数でダンジョン探索中の勇を参照するときに1文字変数Aを使用するのでここはなんとかしたい。
　各関数にARGを使用。IF RAND文の1文字変数削除。

　■LIFE_LIST.ERB
　11行目に愛、淫乱、未陥落の表示を追加。

　■SHOP_2.ERB
　163行目に妊婦は除外追加。

　■SHOP_FUNCTION.ERB
　15、43行目に愛、淫乱、未陥落の表示を追加。

　■SUMMON_MONSTER.ERB
　SUMMON_MONSTER関数にARGを使用。
　１文字変数を使わないように。

　■SYSTEM.ERB
　291行目を以下に修正して育児室を0に戻さないように。
　SIF CFLAG:A:1 != 2 && CFLAG:A:1 != 3 && CFLAG:A:1 != 7 && CFLAG:A:1 != 8 && CFLAG:A:1 != 9 && CFLAG:A:1 != 10

