"""
LM Studio本地模型接入配置
使用litelm提供统一的OpenAI兼容接口
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass
import asyncio
from pathlib import Path

try:
    from litellm import completion, acompletion, embedding
    import litellm
except ImportError:
    logging.error("litellm not installed. Please install with: pip install litellm")
    raise

@dataclass
class ModelConfig:
    """模型配置"""
    model_name: str
    api_base: str
    api_key: str = "not-needed"
    model_type: str = "lm_studio"  # openai, anthropic, cohere等
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    streaming: bool = True

class ModelManager:
    """統一模型管理器"""

    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 從配置獲取供應商信息
        self.model_provider = config.model_provider
        self.model_name = config.model_name
        self.api_key = config.api_key
        self.base_url = config.base_url

        # 設置LiteLLM配置
        litellm.set_verbose = False

        # 根據供應商設置環境變量
        self._setup_provider()

        # 用於追蹤需要清理的資源
        self._sessions = []

        self.logger.info(f"Configured {self.model_provider} with model: {self.model_name}")

    def _setup_provider(self):
        """根據供應商設置環境變量"""
        if self.model_provider == "openai":
            os.environ["OPENAI_API_KEY"] = self.api_key
            if self.base_url:
                os.environ["OPENAI_API_BASE"] = self.base_url
        elif self.model_provider == "gemini":
            os.environ["GEMINI_API_KEY"] = self.api_key
            if self.base_url:
                os.environ["GEMINI_API_BASE"] = self.base_url
        elif self.model_provider == "claude":
            os.environ["ANTHROPIC_API_KEY"] = self.api_key
        elif self.model_provider == "lm_studio":
            # LM Studio 不需要 API key
            pass

    def _get_model_name(self):
        """獲取完整的模型名稱"""
        if self.model_provider == "lm_studio":
            return f"lm_studio/{self.model_name}"
        elif self.model_provider == "openai":
            return self.model_name
        elif self.model_provider == "gemini":
            return f"gemini/{self.model_name}"
        elif self.model_provider == "claude":
            return f"claude-3-{self.model_name}"
        else:
            return self.model_name

    async def chat_completion_async(self, messages, **kwargs):
        """異步聊天完成"""
        try:
            model_name = self._get_model_name()

            # 設置請求參數
            params = {
                "model": model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.default_max_tokens),
                "temperature": kwargs.get("temperature", self.config.default_temperature),
                "top_p": kwargs.get("top_p", self.config.default_top_p),
            }

            # 為 LM Studio 添加 base_url
            if self.model_provider == "lm_studio":
                params["api_base"] = self.base_url

            response = await acompletion(**params)

            # 检查响应是否有效
            if not response or not response.choices or len(response.choices) == 0:
                self.logger.error("Invalid response from model: no choices returned")
                return ""

            content = response.choices[0].message.content

            # 解析模型響應
            return self._parse_model_response(content)

        except Exception as e:
            self.logger.error(f"Async chat completion failed: {e}")
            raise

    def chat_completion_sync(self, messages, **kwargs):
        """同步聊天完成"""
        try:
            model_name = self._get_model_name()

            # 設置請求參數
            params = {
                "model": model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.default_max_tokens),
                "temperature": kwargs.get("temperature", self.config.default_temperature),
                "top_p": kwargs.get("top_p", self.config.default_top_p),
            }

            # 為 LM Studio 添加 base_url
            if self.model_provider == "lm_studio":
                params["api_base"] = self.base_url

            response = completion(**params)

            # 检查响应是否有效
            if not response or not response.choices or len(response.choices) == 0:
                self.logger.error("Invalid response from model: no choices returned")
                return ""

            content = response.choices[0].message.content

            # 解析模型響應
            return self._parse_model_response(content)

        except Exception as e:
            self.logger.error(f"Sync chat completion failed: {e}")
            raise

    def _parse_model_response(self, content: str) -> str:
        """解析模型響應，提取實際內容"""
        import re

        # 检查内容是否为None或空
        if content is None:
            self.logger.warning("Model response content is None")
            return ""

        if not isinstance(content, str):
            self.logger.warning(f"Model response content is not a string: {type(content)}")
            return str(content) if content else ""

        # 嘗試提取 <answer> 標籤中的內容
        answer_match = re.search(r'<answer>(.*?)</answer>', content, re.DOTALL)
        if answer_match:
            return answer_match.group(1).strip()

        # 如果沒有 <answer> 標籤，嘗試移除 <think> 標籤
        think_removed = re.sub(r'</?think>.*?</think>', '', content, flags=re.DOTALL)
        think_removed = re.sub(r'</?think>', '', think_removed)

        # 清理其他可能的標籤
        cleaned = re.sub(r'<[^>]+>', '', think_removed)

        return cleaned.strip() if cleaned.strip() else content

    def test_connection(self):
        """測試模型連接"""
        try:
            test_messages = [{"role": "user", "content": "請回答：OK"}]
            response = self.chat_completion_sync(test_messages)
            self.logger.info(f"Model connection test successful: {response[:50]}...")
            return True
        except Exception as e:
            self.logger.error(f"Model connection test failed: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return False
    async def cleanup(self):
        """清理資源"""
        try:
            # 嘗試關閉 LiteLLM 的內部連接
            import aiohttp
            import gc

            # 方法1: 嘗試關閉 LiteLLM 的客戶端會話
            if hasattr(litellm, '_client_session') and litellm._client_session:
                await litellm._client_session.close()

            # 方法2: 查找並關閉所有 aiohttp 會話
            for obj in gc.get_objects():
                if isinstance(obj, aiohttp.ClientSession):
                    if not obj.closed:
                        try:
                            await obj.close()
                        except:
                            pass

            # 方法3: 強制垃圾回收
            gc.collect()

            # 等待連接關閉
            await asyncio.sleep(0.25)

            self.logger.info("Model manager resources cleaned up")
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")

    def __del__(self):
        """析構函數 - 確保資源被清理"""
        try:
            # 如果還有未關閉的資源，嘗試清理
            if hasattr(self, '_sessions'):
                for session in self._sessions:
                    if hasattr(session, 'close'):
                        try:
                            asyncio.create_task(session.close())
                        except:
                            pass
        except:
            pass





# 全局模型管理器實例
_model_manager = None

def get_model_manager(config=None):
    """獲取模型管理器實例"""
    global _model_manager
    if _model_manager is None:
        if config is None:
            from config.settings import get_config
            config = get_config()
        _model_manager = ModelManager(config)
    return _model_manager

