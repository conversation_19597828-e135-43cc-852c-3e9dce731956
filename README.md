# ERA AI Agent System

这是一个基于多代理系统的ERA游戏生成AI系统，能够自动生成完整的ERA游戏项目。

## 系统特性

- **多代理架构**: 使用LangGraph构建的多代理工作流
- **本地模型支持**: 通过LM Studio运行本地大语言模型
- **RAG知识检索**: 基于Ollama嵌入的ERA语法知识库
- **MCP工具调用**: 模块化的工具调用接口
- **完整项目生成**: 自动生成ERA游戏的所有必需文件

## 快速开始

### 1. 环境要求

- Python 3.8+
- LM Studio (用于本地模型)
- Ollama (用于嵌入模型)

### 2. 安装系统

```bash
# 克隆项目
git clone <repository-url>
cd era_ai_agent

# 运行安装脚本
python setup.py
```

### 3. 配置环境

复制并编辑配置文件：
```bash
cp .env.example .env
```

主要配置项：
```env
# LM Studio配置
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_MODEL=your-model-name

# Ollama配置  
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# ERA语法数据路径
ERA_SYNTAX_DATA_PATH=F:/limiya/ERA語法資料
```

### 4. 启动服务

启动LM Studio：
```bash
# 启动LM Studio并加载模型
# 确保在 http://localhost:1234 运行
```

启动Ollama：
```bash
# 安装并启动Ollama
ollama pull nomic-embed-text
ollama serve
```

### 5. 生成游戏

```bash
# 基本用法
python -m era_ai_agent.main --game-title "我的ERA游戏" --character-count 5

# 交互模式
python -m era_ai_agent.main --interactive

# 验证配置
python -m era_ai_agent.main --validate-config
```

## 系统架构

### 核心代理

1. **MainAgent**: 主控制器，协调工作流
2. **ERBScriptAgent**: 生成ERB脚本文件
3. **CSVDataAgent**: 生成CSV数据文件  
4. **SystemFlowAgent**: 实现游戏系统流程
5. **CharacterAgent**: 创建游戏角色
6. **GameLogicAgent**: 整合游戏逻辑

### 技术栈

- **LangGraph**: 多代理工作流管理
- **SQLite**: 数据存储和状态管理
- **LiteLLM**: 统一的模型接口
- **Ollama**: 向量嵌入服务
- **FAISS**: 向量相似度搜索
- **MCP**: 模型上下文协议工具调用

### 生成的文件结构

```
generated_game/
├── ERB/
│   ├── SYSTEM.ERB
│   ├── TITLE.ERB
│   ├── VARIABLES.ERH
│   ├── TRAIN_MAIN.ERB
│   └── ...
├── CSV/
│   ├── Chara/
│   │   ├── Chara0.csv
│   │   └── ...
│   ├── Abl.csv
│   ├── Talent.csv
│   └── Train.csv
├── resources/
│   └── img.csv
├── _default.config
├── _fixed.config
└── _replace.csv
```

## 高级功能

### RAG知识检索

系统自动分析ERA语法资料，构建知识库：
- 语法规则检索
- 函数模板生成
- 最佳实践推荐

### MCP工具调用

内置多种工具：
- ERB语法验证
- CSV数据格式化
- 函数模板生成
- 知识库查询

### 工作流定制

支持自定义生成流程：
- 修改代理配置
- 添加新的生成步骤
- 定制输出格式

## 开发指南

### 添加新代理

```python
from era_ai_agent.agents.era_generators import BaseERAAgent

class MyCustomAgent(BaseERAAgent):
    def __init__(self):
        super().__init__("my_agent", "custom")
        
    async def generate_content(self, prompt: str) -> str:
        # 实现自定义生成逻辑
        return await super().generate_content(prompt)
```

### 添加新工具

```python
from era_ai_agent.tools.mcp_tools import MCPToolManager, ToolParameter, ToolType

def my_custom_tool(param1: str, param2: int) -> dict:
    # 工具实现
    return {"result": "success"}

# 注册工具
tool_manager.register_tool(
    name="my_custom_tool",
    description="自定义工具",
    category=ToolType.GENERATION,
    parameters=[
        ToolParameter("param1", "string", "参数1"),
        ToolParameter("param2", "integer", "参数2")
    ],
    function=my_custom_tool
)
```

## 故障排除

### 常见问题

1. **模型连接失败**
   - 检查LM Studio是否运行在正确端口
   - 确认模型已正确加载

2. **嵌入服务不可用**
   - 检查Ollama是否运行
   - 确认嵌入模型已下载

3. **ERA语法数据未找到**
   - 检查ERA_SYNTAX_DATA_PATH配置
   - 确认路径存在且包含.txt文件

### 日志调试

```bash
# 启用调试模式
export DEBUG_MODE=true
export LOG_LEVEL=DEBUG

# 查看日志
tail -f era_agent.log
```

## 性能优化

### 并发控制

```env
# 调整并发代理数量
MAX_CONCURRENT_AGENTS=3

# 调整超时时间
AGENT_TIMEOUT=300
```

### 缓存设置

```env
# 启用缓存
ENABLE_CACHE=true

# 保存中间结果
SAVE_INTERMEDIATE_RESULTS=true
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交改动
4. 发起 Pull Request

## 许可证

本项目遵循 MIT 许可证。

## 致谢

- ERA游戏引擎开发者
- LangGraph团队
- LiteLLM项目
- Ollama项目