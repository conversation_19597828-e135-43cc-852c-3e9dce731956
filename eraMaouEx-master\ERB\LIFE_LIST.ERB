﻿@LIFE_LIST(NO_PAGE = 0,MODE = 1,NUM_PAGE = 20)
#DIM MAX_NUM_LEN = 0
#DIM MAX_NAME_LEN = 0
#DIM MAX_LV_LEN = 0
#DIM MAX_ATK_LEN = 0
#DIM MAX_DEF_LEN = 0
#DIM NO_PAGE
#DIM MODE
#DIM NUM_PAGE
;-----------------------------------------------
;存在するキャラの一覧表示
;全員出してるので表示人数は{CHARANUM}人となる

;キャラの番号、名前の文字数、レベル、攻撃、防御の最大桁数をそれぞれ取得
MAX_NUM_LEN = STRLENS(TOSTR((NO_PAGE+1)*NUM_PAGE));番号
FOR COUNT, 0, CHARANUM
	MAX_NAME_LEN = MAX(STRLENS(SAVESTR:COUNT), MAX_NAME_LEN);名前
	MAX_LV_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:9)), MAX_LV_LEN);レベル
	MAX_ATK_LEN = MAX(STRLENS(TOSTR(CFLAG:COUNT:13)), MAX_ATK_LEN);攻撃
	MAX_DEF_LEN = MAX (STRLENS(TOSTR(CFLAG:COUNT:14)), MAX_DEF_LEN);防御
NEXT
LOCALS = [{0,MAX_NUM_LEN}]
IF MODE == 0 && !MASTER
	PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT%  %"你（可强化地下城）", MAX_NAME_LEN,LEFT%LV{CFLAG:0:9, MAX_LV_LEN,RIGHT}
ELSEIF MODE == 0 && MASTER
	LOCALS:2 = %SAVESTR:MASTER%(可强化地下城)
	PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT%  %LOCALS:2, MAX_NAME_LEN,LEFT%LV{CFLAG:MASTER:9, MAX_LV_LEN,RIGHT}
ELSEIF MODE == 2

ELSE
	PRINTFORML %LOCALS, MAX_NUM_LEN+2, RIGHT%  %SAVESTR:MASTER, MAX_NAME_LEN,LEFT% %"",6,LEFT% LV{CFLAG:MASTER:9, MAX_LV_LEN,RIGHT}
ENDIF
;绘制列表
FOR COUNT, NO_PAGE*NUM_PAGE + 1, (NO_PAGE+1)*NUM_PAGE +1
	
	IF COUNT >= CHARANUM
		PRINTL
		CONTINUE
	ELSEIF COUNT == MASTER
		CONTINUE
	ENDIF
LOCALS = [{COUNT,MAX_NUM_LEN}]
PRINTFORM %LOCALS, MAX_NUM_LEN + 2, RIGHT% 
PRINTFORM  %SAVESTR:COUNT,MAX_NAME_LEN,LEFT% %GET_JOB_NAME(COUNT),6,LEFT% LV{CFLAG:COUNT:9,MAX_LV_LEN,RIGHT}
		
;爱、淫乱
IF TALENT:COUNT:爱慕
	SETCOLOR 255,100,100
	PRINT <爱  慕>
	RESETCOLOR
ELSEIF TALENT:COUNT:淫乱
	SETCOLOR 255,100,100
	PRINT <淫  乱>
	RESETCOLOR
ELSE
	SETCOLOR 100,100,100
	PRINT <未沦陷>
	RESETCOLOR
ENDIF

;お気に入り
IF CFLAG:COUNT:700
	PRINTFORM  [☆]
ELSE
	PRINTS " "*5
ENDIF
SIF CFLAG:COUNT:1 == 0 &&CFLAG:COUNT:0 > 0 && COUNT != 0 && BASE:COUNT:0 > 0
	PRINT [可被卖]
SIF CFLAG:COUNT:1 == 0 &&CFLAG:COUNT:0 == 2 && COUNT != 0 && BASE:COUNT:0 > 0
	PRINT [可作为助手]
SIF TALENT:COUNT:190 != 0 || TALENT:COUNT:191 != 0 || TALENT:COUNT:192 != 0 || TALENT:COUNT:193 != 0		
	PRINT [虫寄生]
SETCOLOR 100,255,100
IF TALENT:COUNT:153 != 0 || TALENT:COUNT:341 != 0 || TALENT:COUNT:342 != 0
    PRINT [妊娠]
ELSEIF TALENT:COUNT:341 != 0
	PRINT [乳内妊娠]
ELSEIF TALENT:COUNT:342 != 0
	PRINT [精巣妊娠]
ENDIF
IF CFLAG:COUNT:1 == 12
	SETCOLOR 100,200,100
	PRINT [派遣]
	RESETCOLOR
ENDIF
PRINTL    
RESETCOLOR
NEXT

DRAWLINE
RETURN 0


;-------------------------------
@LIFE_LIST_ITEM(ARG)
;-------------------------------
PRINTFORM [{ARG,2}] %SAVESTR:ARG,12,LEFT% %GET_JOB_NAME(ARG),6,LEFT% LV{CFLAG:ARG:9,4,RIGHT}
		
;爱、淫乱
IF TALENT:ARG:爱慕
	SETCOLOR 255,100,100
	PRINT <爱  慕>
	RESETCOLOR
ELSEIF TALENT:ARG:淫乱
	SETCOLOR 255,100,100
	PRINT <淫  乱>
	RESETCOLOR
ELSE
	SETCOLOR 100,100,100
	PRINT <未沦陷>
	RESETCOLOR
ENDIF


;お気に入り
IF CFLAG:COUNT:700
	PRINTFORM  [☆]
ELSE
	PRINTS " "*5
ENDIF
SIF CFLAG:ARG:1 == 0 &&CFLAG:ARG:0 > 0 && ARG != 0 && BASE:ARG:0 > 0
	PRINT [可被卖]
SIF CFLAG:ARG:1 == 0 &&CFLAG:ARG:0 == 2 && ARG != 0 && BASE:ARG:0 > 0
	PRINT [可作为助手]
SIF TALENT:ARG:190 != 0 || TALENT:ARG:191 != 0 || TALENT:ARG:192 != 0 || TALENT:ARG:193 != 0		
	PRINT [虫寄生]
SETCOLOR 100,255,100
IF TALENT:COUNT:153 != 0 || TALENT:COUNT:341 != 0 || TALENT:COUNT:342 != 0
    PRINT [妊娠]
ELSEIF TALENT:COUNT:341 != 0
	PRINT [乳内妊娠]
ELSEIF TALENT:COUNT:342 != 0
	PRINT [精巣妊娠]
ENDIF
IF CFLAG:COUNT:1 == 12
	SETCOLOR 100,200,100
	PRINT [派遣]
	RESETCOLOR
ENDIF
PRINTL    
RESETCOLOR 

;----------------------------
@LIFE_LIST_ENEMY(NO_PAGE = 0,NUM_PAGE = 20,LIST_POS)
;----------------------------
;逆に、勇者だけの選択肢です
#DIM NO_PAGE
#DIM NUM_PAGE
#DIM T_LCOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
FOR COUNT, LIST_POS, CHARANUM
	IF CFLAG:COUNT:1 == 2 && COUNT != MASTER && BASE:COUNT:0 > 0 && T_LCOUNT < (NO_PAGE + 1)*NUM_PAGE + 1 && T_LCOUNT >= NO_PAGE* NUM_PAGE + 1
		CALL LIFE_LIST_ITEM_E(COUNT)
		T_LCOUNT++
		LIST_POS = COUNT
		SIF T_LCOUNT >= (NO_PAGE + 1)*NUM_PAGE + 1
			CONTINUE
	ENDIF
NEXT

RETURN 0

@MAX_PAGE_ENEMY(NUM_PAGE)
#DIM NUM_PAGE
LOCAL = 0
FOR COUNT,0,CHARANUM
	SIF CFLAG:COUNT:1 == 2 && COUNT != 0 && BASE:COUNT:0 > 0
		LOCAL++
NEXT
IF (LOCAL % NUM_PAGE) > 0
	LOCAL = ( LOCAL / NUM_PAGE ) + 1
ELSE
	LOCAL = LOCAL / NUM_PAGE
ENDIF
RETURN LOCAL

;----------------------------
@LIFE_LIST_SALAVE(NO_PAGE = 0,NUM_PAGE = 20,LIST_POS)
;----------------------------
;逆に、勇者だけの選択肢です
#DIM NO_PAGE
#DIM NUM_PAGE
#DIM T_LCOUNT
T_LCOUNT = NUM_PAGE * NO_PAGE + 1
FOR COUNT, LIST_POS, CHARANUM
	SIF COUNT == MASTER
		CONTINUE
	IF (CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 3 || CFLAG:COUNT:1 == 5 || CFLAG:COUNT:1 == 6 || CFLAG:COUNT:1 == 7 || CFLAG:COUNT:1 == 10) && COUNT != 0 && BASE:COUNT:0 > 0 && T_LCOUNT < (NO_PAGE + 1)*NUM_PAGE + 1 && T_LCOUNT >= NO_PAGE* NUM_PAGE + 1
		CALL LIFE_LIST_ITEM_E(COUNT)
		T_LCOUNT++
		LIST_POS = COUNT
		SIF T_LCOUNT >= (NO_PAGE + 1)*NUM_PAGE + 1
			CONTINUE
	ENDIF
NEXT

RETURN 0

@MAX_PAGE_SALAVE(NUM_PAGE)
#DIM NUM_PAGE
LOCAL = 0
FOR COUNT,0,CHARANUM
	SIF (CFLAG:COUNT:1 == 0 || CFLAG:COUNT:1 == 3 || CFLAG:COUNT:1 == 5 || CFLAG:COUNT:1 == 6 || CFLAG:COUNT:1 == 7 || CFLAG:COUNT:1 == 10) && COUNT != 0 && BASE:COUNT:0 > 0
		LOCAL++
NEXT
IF (LOCAL % NUM_PAGE) > 0
	LOCAL = ( LOCAL / NUM_PAGE ) + 1
ELSE
	LOCAL = LOCAL / NUM_PAGE
ENDIF
RETURN LOCAL

;----------------------------
@LIFE_LIST_ITEM_E(ARG)
;----------------------------
PRINTFORM [{ARG,2}] %SAVESTR:ARG,12,LEFT% %GET_JOB_NAME(ARG),6,LEFT% LV{CFLAG:ARG:9,4,RIGHT}  调教回数:{CFLAG:ARG:10,3,LEFT}
		
;种族・性格
LOCALS = [%GET_LOOK_INFO(ARG, "种族")% - %GET_LOOK_INFO(ARG, "性格")%]
PRINTFORM  %LOCALS,20,LEFT% 

;愛、淫乱
IF TALENT:ARG:爱慕
	SETCOLOR 255,100,100
	PRINT <爱  慕>
	RESETCOLOR
ELSEIF TALENT:ARG:淫乱
	SETCOLOR 255,100,100
	PRINT <淫  乱>
	RESETCOLOR
ELSE
	SETCOLOR 100,100,100
	PRINT <未沦陷>
	RESETCOLOR
ENDIF

;性别表示
IF TALENT:ARG:122
	PRINT   <男>
ELSEIF !TALENT:ARG:122 && TALENT:ARG:121
	PRINT <扶她>
ELSEIF !TALENT:ARG:122
	PRINT   <女>
ENDIF

;お気に入り
IF CFLAG:ARG:700
	PRINT [☆]
ENDIF

SIF CFLAG:ARG:1 == 0 &&CFLAG:ARG:0 > 0 && ARG != 0 && BASE:ARG:0 > 0
	PRINT [可被卖]
SIF CFLAG:ARG:1 == 0 &&CFLAG:ARG:0 == 2 && ARG != 0 && BASE:ARG:0 > 0
	PRINT [可作为助手]
SIF TALENT:ARG:190 != 0 || TALENT:ARG:191 != 0 || TALENT:ARG:192 != 0 || TALENT:ARG:193 != 0
	PRINT [虫寄生]
SETCOLOR 100,255,100
IF TALENT:COUNT:153 != 0 || TALENT:COUNT:341 != 0 || TALENT:COUNT:342 != 0
    PRINT [妊娠]
ELSEIF TALENT:COUNT:341 != 0
	PRINT [乳内妊娠]
ELSEIF TALENT:COUNT:342 != 0
	PRINT [精巣妊娠]
ENDIF
IF CFLAG:COUNT:1 == 12
	SETCOLOR 100,200,100
	PRINT [派遣]
	RESETCOLOR
ENDIF
PRINTL    
RESETCOLOR 

;-------------------------------
@SELECT_YES_NO
;-------------------------------
;はいかいいえを選択するだけの関数です

$INPUT_LOOP

PRINTL   [0] 是的   [1] 不要

INPUT

IF RESULT == 0 || RESULT == 1
	RETURN RESULT
ELSE
	GOTO INPUT_LOOP
ENDIF



