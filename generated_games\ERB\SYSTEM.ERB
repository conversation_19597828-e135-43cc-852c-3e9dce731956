﻿﻿@EVENTFIRST
#DIM CHARA, 1
#DIM ID_OF_NEWCHARA
#DIM TEMP

TARGET = -1
BOUGHT = -1

DAY:1 = 1

;基础设置
FLAG:5 = 17179934119 ;战斗系统启用
FLAG:35 = 0 ;游戏结束标志
FLAG:37 = 1 ;游戏进行中标志

;骑士与玩偶特有标志
FLAG:100 = 0 ;玩偶收集数量
FLAG:101 = 0 ;当前持有金币

RETURN

@SYSTEM_TITLE
PRINTFORML 骑士与玩偶
RETURN

@SYSTEM_LOADEND
RETURN

@SYSTEM_SAVEEND
RETURN

@SYSTEM_TURNEND
;每日结算
IF DAY:1 > 1
    ;金币增加
    GOLD += 100
ENDIF
DAY:1 += 1
RETURN

@SHOP
;商店系统
PRINT "欢迎来到商店！"
PRINT "1.购买玩偶A (100金币)"
PRINT "2.购买玩偶B (200金币)"
PRINT "3.离开商店"

INPUT BOUGHT,1,3

IF BOUGHT == 1
    IF GOLD >= 100
        GOLD -= 100
        FLAG:100 += 1
        PRINT "购买成功！你获得了一个玩偶A！"
    ELSE
        PRINT "金币不足！"
    ENDIF
ELSEIF BOUGHT == 2
    IF GOLD >= 200
        GOLD -= 200
        FLAG:100 += 1
        PRINT "购买成功！你获得了一个玩偶B！"
    ELSE
        PRINT "金币不足！"
    ENDIF
ENDIF

RETURN

@TRAINING
;训练系统
PRINT "训练系统"
;此处添加训练逻辑
RETURN