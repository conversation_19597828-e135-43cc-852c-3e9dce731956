# ERA AI Agent System Requirements

## Python Dependencies

# Core framework
langgraph>=0.0.60
langchain>=0.1.0
langchain-community>=0.0.20

# Local model integration  
litellm>=1.35.0

# Database and storage
sqlite3  # Built-in with Python
aiosqlite>=0.19.0

# Vector operations and embeddings
numpy>=1.24.0
scikit-learn>=1.3.0
faiss-cpu>=1.7.4

# HTTP requests and async
requests>=2.31.0
httpx>=0.24.0

# Data processing
pandas>=2.0.0

# Logging and configuration
pydantic>=2.0.0
python-dotenv>=1.0.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Optional: UI and visualization
streamlit>=1.28.0  # For web interface
plotly>=5.15.0     # For data visualization

# Optional: Advanced NLP
sentence-transformers>=2.2.0
transformers>=4.30.0