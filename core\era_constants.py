"""
ERA游戏引擎核心常量定义
基于ERA语法资料的系统函数和变量定义
"""

# ERA系统函数 (System Functions)
SYSTEM_FUNCTIONS = {
    # 流程控制函数
    "SYSTEM_TITLE": "标题画面函数",
    "EVENTFIRST": "首次游戏初始化",
    "SHOP": "商店/主页面",
    "TRAIN": "调教界面",
    "ABLUP": "能力提升",
    "AFTERTRAIN": "调教后处理",
    "TURNEND": "日期变更",
    "LOADGAME": "加载游戏",
    "SAVEGAME": "保存游戏",
    "LOADDATAEND": "加载存档完成",
    
    # 角色相关函数
    "SHOW_STATUS": "显示角色状态",
    "SHOW_JUEL": "显示装饰品",
    "COM_CUSTOM": "角色定制",
    "BUYITEM": "购买道具",
    "USERCOM": "用户自定义指令",
}

# ERA内置变量 (Built-in Variables)
BUILTIN_VARIABLES = {
    # 数值变量
    "FLAG": "游戏标志位数组",
    "TFLAG": "临时标志位数组", 
    "ABL": "角色能力数组",
    "TALENT": "角色素质数组",
    "EXP": "角色经验数组",
    "MARK": "角色标记数组",
    "PALAM": "角色参数数组",
    "JUEL": "装饰品数组",
    "ITEM": "道具数组",
    "MONEY": "金钱",
    "TIME": "时间",
    "DAY": "日期",
    
    # 字符串变量
    "STR": "字符串数组",
    "TSTR": "临时字符串数组",
    "NAME": "角色姓名",
    "CALLNAME": "角色称呼",
    "NICKNAME": "角色昵称",
    "MASTERNAME": "主人名",
    
    # 角色相关
    "TARGET": "目标角色编号",
    "MASTER": "主人角色编号",
    "ASSI": "助手角色编号",
    "NO": "当前角色编号",
    "PREVCOM": "前一个指令",
    "NEXTCOM": "下一个指令",
    "UP": "能力上升值",
    "DOWN": "能力下降值",
    "LOSEBASE": "基础值损失",
    "SOURCE": "快感来源",
    "SELECTCOM": "选择的指令",
    "ASSIPLAY": "助手参与",
    "BOUGHT": "购买数量",
    "NOWEX": "当前体位",
    
    # 系统相关
    "RESULT": "函数返回值",
    "COUNT": "计数器",
    "RAND": "随机数",
    "ISASSI": "是否为助手",
    "ISTRAIN": "是否在调教中",
}

# ERA指令集 (ERA Commands)
ERA_COMMANDS = {
    # 输出指令
    "PRINT": "输出文本",
    "PRINTL": "输出文本并换行", 
    "PRINTW": "输出文本并等待",
    "PRINTT": "输出文本到指定位置",
    "PRINTFORM": "格式化输出",
    "PRINTFORML": "格式化输出并换行",
    "PRINTFORMW": "格式化输出并等待",
    "PRINTFORMT": "格式化输出到指定位置",
    "PRINTBUTTON": "输出按钮",
    "PRINTPLAIN": "输出纯文本",
    "PRINTPLAINFORM": "格式化输出纯文本",
    "CUSTOMDRAWLINE": "自定义绘制行",
    "DRAWLINE": "绘制分割线",
    "REUSELASTLINE": "重用上一行",
    "CLEARLINE": "清除行",
    "PRINT_ABL": "输出能力值",
    "PRINT_TALENT": "输出素质",
    "PRINT_MARK": "输出标记",
    "PRINT_EXP": "输出经验值",
    "PRINT_PALAM": "输出参数",
    
    # 输入指令
    "INPUT": "输入处理",
    "INPUTS": "字符串输入",
    "ONEINPUT": "单项输入",
    "ONEINPUTS": "单项字符串输入",
    "TWOINPUT": "双项输入",
    "TINPUT": "定时输入",
    "TINPUTS": "定时字符串输入",
    "TONEINPUT": "定时单项输入",
    "TONEINPUTS": "定时单项字符串输入",
    "TTWOINPUT": "定时双项输入",
    "WAIT": "等待按键",
    "WAITANYKEY": "等待任意键",
    "FORCEWAIT": "强制等待",
    
    # 控制指令
    "IF": "条件判断",
    "ELSEIF": "否则如果",
    "ELSE": "否则",
    "ENDIF": "结束条件",
    "SIF": "单行条件",
    "SELECTCASE": "选择语句",
    "CASE": "条件分支",
    "CASEELSE": "默认分支",
    "ENDSELECT": "结束选择",
    "FOR": "循环开始",
    "NEXT": "循环结束",
    "WHILE": "条件循环",
    "WEND": "循环结束",
    "DO": "循环开始",
    "LOOP": "循环结束",
    "REPEAT": "重复循环",
    "REND": "重复结束",
    "CONTINUE": "继续循环",
    "BREAK": "跳出循环",
    "GOTO": "跳转",
    "CALL": "调用函数",
    "RETURN": "返回",
    "RETURNFORM": "格式化返回",
    "THROW": "抛出异常",
    "QUIT": "退出游戏",
    "BEGIN": "开始块",
    "END": "结束块",
    
    # 数值操作
    "SETBIT": "设置位",
    "CLEARBIT": "清除位",
    "INVERTBIT": "反转位",
    "GETBIT": "获取位",
    "MOUSEX": "鼠标X坐标",
    "MOUSEY": "鼠标Y坐标",
    "MOUSESKIP": "鼠标跳过",
    "RANDOMIZE": "初始化随机数",
    "DUMPRAND": "转储随机数",
    "INITRAND": "初始化随机数种子",
    "MIN": "最小值",
    "MAX": "最大值",
    "LIMIT": "限制范围",
    "ABS": "绝对值",
    "SIGN": "符号",
    "SQRT": "平方根",
    "POWER": "幂运算",
    "SUMARRAY": "数组求和",
    "MAXARRAY": "数组最大值",
    "MINARRAY": "数组最小值",
    "INRANGE": "范围检查",
    "INRANGEARRAY": "数组范围检查",
    "NOSAMES": "去除重复",
    "ALLSAMES": "检查全部相同",
    "ARRAYMSORT": "数组多重排序",
    "ARRAYSORT": "数组排序",
    "ARRAYREMOVE": "移除数组元素",
    "ARRAYSHIFT": "数组移位",
    "ARRAYCOPY": "数组复制",
    
    # 字符串操作
    "STRLEN": "字符串长度",
    "STRLENS": "字符串长度（字节）",
    "SUBSTRING": "子字符串",
    "CHARATU": "获取字符",
    "STRFIND": "查找字符串",
    "STRFINDU": "查找字符串（Unicode）",
    "STRCOUNT": "统计字符串",
    "REPLACE": "替换字符串",
    "UNICODE": "获取Unicode",
    "UNICODESTR": "Unicode转字符串",
    "ISNUMERIC": "是否为数字",
    "TOINT": "转换为整数",
    "TOSTR": "转换为字符串",
    "SPLIT": "分割字符串",
    "TIMES": "重复字符串",
    "SETCOLOR": "设置颜色",
    "RESETCOLOR": "重置颜色",
    "SETCOLORBYNAME": "按名称设置颜色",
    "SETBGCOLOR": "设置背景色",
    "RESETBGCOLOR": "重置背景色",
    "SETBGCOLORBYNAME": "按名称设置背景色",
    "FONTBOLD": "粗体字体",
    "FONTITALIC": "斜体字体",
    "FONTUNDERLINE": "下划线字体",
    "FONTSTRIKETHROUGH": "删除线字体",
    "FONTSIZE": "字体大小",
    "FONTSTYLE": "字体样式",
    "ALIGNMENT": "对齐方式",
    "REDRAW": "重绘",
    "CURRENTALIGN": "当前对齐",
    "CURRENTREDRAW": "当前重绘",
    "BARSTR": "进度条字符串",
    
    # 文件操作
    "LOADDATA": "加载数据",
    "SAVEDATA": "保存数据",
    "CHECKDATA": "检查数据",
    "DELDATA": "删除数据",
    "LOADGAME": "加载游戏",
    "SAVEGAME": "保存游戏",
    "LOADCHARA": "加载角色",
    "SAVECHARA": "保存角色",
    "LOADTEXT": "加载文本",
    "SAVETEXT": "保存文本",
    "LOADVARTEXT": "加载变量文本",
    "SAVEVARTEXT": "保存变量文本",
    "EXISTCSV": "检查CSV存在",
    "CSVNAME": "CSV名称",
    "CSVNUMS": "CSV数量",
    "GETLINECOUNT": "获取行数",
    "EXISTFILE": "检查文件存在",
    "DELETEFILE": "删除文件",
    "SAVENOS": "保存编号",
    "LOADNOS": "加载编号",
    
    # 系统操作  
    "HTML_PRINT": "HTML输出",
    "HTML_PUTTFORM": "HTML格式化输出",
    "PUTFORM": "格式化输出",
    "VARSIZE": "变量大小",
    "GETTIME": "获取时间",
    "GETMILLISECOND": "获取毫秒",
    "GETSECOND": "获取秒",
    "GETMINUTE": "获取分钟",
    "GETHOUR": "获取小时",
    "GETDAY": "获取日",
    "GETMONTH": "获取月",
    "GETYEAR": "获取年",
    "GETWEEK": "获取星期",
    "ADDCHARA": "添加角色",
    "DELCHARA": "删除角色",
    "SWAPCHARA": "交换角色",
    "SORTCHARA": "排序角色",
    "PICKUPCHARA": "选择角色",
    "ADDDEFCHARA": "添加默认角色",
    "FIND_CHARA": "查找角色",
    "FINDCHARA": "查找角色",
    "CFINDCHARA": "条件查找角色",
    "COPYCHARA": "复制角色",
    "CHKCHARADATA": "检查角色数据",
    "SETANIMETIMER": "设置动画计时器",
    "GETANIMETIMER": "获取动画计时器",
    "SKIPDISP": "跳过显示",
    "MOUSESKIP": "鼠标跳过",
    "MESSKIP": "消息跳过",
    "KEYPOP": "弹出按键",
    "KEYTOP": "顶部按键",
    "CLEARBIT": "清除位",
    "POWER_ON": "开机",
    "CLEARTEXTBOX": "清除文本框",
    "OUTPUTLOG": "输出日志",
    "GETNUMBER": "获取数字",
    "UPCHECK": "上升检查",
    "CUPCHECK": "累计上升检查",
    "GCREATE": "创建图形",
    "GCREATEFROMFILE": "从文件创建图形",
    "GDISPOSE": "释放图形",
    "GCLEAR": "清除图形",
    "SPRITECREATE": "创建精灵",
    "SPRITEDISPOSE": "释放精灵",
    "SPRITESETPOS": "设置精灵位置",
    "SPRITESETORIGIN": "设置精灵原点",
    "SPRITESETSCALE": "设置精灵缩放",
    "SPRITESETANGLE": "设置精灵角度",
    "SPRITESETCOLOR": "设置精灵颜色",
    "SPRITEANIMESTART": "开始精灵动画",
    "SPRITEANIMEADDFRAME": "添加动画帧",
    "GFILLRECTANGLE": "填充矩形",
    "GDRAWRECTANGLE": "绘制矩形",
    "GFILLELLIPSE": "填充椭圆",
    "GDRAWELLIPSE": "绘制椭圆",
    "GDRAWLINE": "绘制直线",
    "GDRAWSTRING": "绘制字符串",
    "GDRAWGWITHNAME": "按名称绘制图形",
    "GDRAWG": "绘制图形",
    "GDRAWSPRITE": "绘制精灵",
}

# 文件扩展名定义
FILE_EXTENSIONS = {
    "erb": ".ERB",
    "erh": ".ERH", 
    "csv": ".csv",
    "config": ".config",
    "txt": ".txt",
    "sav": ".sav"
}

# 编码设置
ENCODING = {
    "default": "utf-8-sig",  # UTF-8 with BOM for ERA files
    "shift_jis": "shift_jis",
    "utf8": "utf-8"
}

# 游戏流程状态
GAME_FLOW_STATES = [
    "TITLE",      # 标题画面
    "FIRST",      # 首次游戏
    "SHOP",       # 商店/主页
    "TRAIN",      # 调教
    "ABLUP",      # 能力提升
    "AFTERTRAIN", # 调教后
    "TURNEND",    # 日期变更
    "LOADGAME",   # 加载游戏
    "SAVEGAME",   # 保存游戏
    "LOADDATAEND" # 加载完成
]

# 角色默认属性
DEFAULT_CHARACTER_ATTRIBUTES = {
    "NAME": "",
    "CALLNAME": "",
    "NICKNAME": "",
    "BASE": [0] * 6,  # 基础属性 [体力, 気力, 反感, 欲情, 羞恥, 痛み]
    "ABL": [0] * 100,  # 能力值
    "TALENT": [0] * 1000,  # 素质
    "CFLAG": [0] * 1000,  # 角色标志
    "MARK": [0] * 1000,  # 标记
    "EXP": [0] * 1000,  # 经验值
    "PALAM": [0] * 1000,  # 参数
}

# CSV文件结构定义  
CSV_STRUCTURE = {
    "Chara": ["编号", "名前", "呼び名", "愛称", "素質", "基礎", "能力", "经验", "フラグ", "マーク", "パラム"],
    "Abl": ["编号", "名前", "表示名", "説明"],
    "Talent": ["编号", "名前", "説明"], 
    "Train": ["编号", "名前", "説明", "条件"],
    "Item": ["编号", "名前", "价格", "説明"],
    "Str": ["编号", "内容"],
    "Mark": ["编号", "名前", "説明"],
    "Palam": ["编号", "名前", "表示名", "説明"],
    "Exp": ["编号", "名前", "説明"]
}