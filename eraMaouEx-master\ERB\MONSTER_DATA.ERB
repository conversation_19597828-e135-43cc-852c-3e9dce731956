﻿;-----------------------------------------------------
@MONSTER_DATA, ARG:0, ARG:1, ARG:2 = -1, ARG:3 = -1, GROUP = -1
#DIM LV
#DIM LVUP
#DIM LVCOUNT
#DIM INUM
#DIM LINE
#DIM TOP
#DIM EXTRA
#DIM GROUP
INUM = ARG:0	;怪物アイテム番号
LINE = ARG:1	;隊列
TOP = LINE*100	;列の先頭
;ARG:2 勇者Aを格納 (LINE == 3とSKELETON戦に使用)
;ARG:3 奴隷と戦闘した勇者Tを格納 (LINE == 4)
;-----------------------------------------------------
;INUMは怪物アイテム番号。LINEは隊列 eraWIZを参考に
;LINE == 3 のときは勇者配下怪物 LINE == 4 のときは奴隷と戦闘した勇者(T)の配下怪物
;LINE == 5 のときはXの怪物のデータ取得
;COUNT変数使用禁止

;E:  0- 99 == 第1列
;E:100-199 == 第2列
;E:200-299 == 第3列
;E:300-399 == 勇者(A)配下
;E:400-499 == 勇者(T)配下
;E:500-599 == 結婚・陵辱

;E:Y    == 怪物番号
;E:Y+1  == レベル
;E:Y+2  == 攻撃力
;E:Y+3  == 防御力
;E:Y+4  == 速度
;E:Y+5  == 特殊
;E:Y+6  == 魔法
;E:Y+7  == 凌辱
;E:Y+8  == ボス化フラグ（空欄）
;E:Y+9  == 状態異常
;	0ビット(&  1):毒
;	1ビット(&  2):
;	2ビット(&  4):
;	3ビット(&  8):
;	4ビット(& 16):
;	5ビット(& 32):
;	6ビット(& 64):
;	7ビット(&128):
;	8ビット(&256):
;E:Y+10 == 耐性
;	0ビット(&  1):火炎
;	1ビット(&  2):冷気
;	2ビット(&  4):電撃
;	3ビット(&  8):
;	4ビット(& 16):
;	5ビット(& 32):
;	6ビット(& 64):
;	7ビット(&128):
;	8ビット(&256):
;E:Y+99 == 怪物数

;-------------------------------------------------------
;怪物种族一覧
;-------------------------------------------------------
;0 无
;1 亜人
;2 史莱姆
;3 昆虫
;4 植物
;5 触手
;6 妖精
;7 巨人
;8 男魔族
;9 女魔族
;10 獣
;11 脑奸
;12 馬

;-----------------------------------------------------
;怪物特殊一覧
;-----------------------------------------------------
;0  无
;1  粘液捕获
;2  落穴捕获
;3  藤蔓捕获
;4  铠破坏
;5  透明
;6  再生
;7  拟态
;8  迷惑
;9  吐息
;10 麻痹
;11 诱惑
;12 混乱
;13 经验值吸取
;14 破铠吐息
;15 魔力吸取
;16 射撃
;17 地形能力

;-------------------------------------------------------
;怪物生成レベル
;-------------------------------------------------------
;怪物のステータス補正
;余りは確率で切り上げされる

IF INUM >= 600
	;イベント領域のモンスター
	CALL CAMPAIGN_DUNGEON_LV
	LV = RESULT / 10 + 2
	SIF RAND:10 < (RESULT % 10)
		LV += 1
ELSE
	LV = CFLAG:0:9 / 12 + 2
	SIF RAND:10 < (CFLAG:0:9 % 10)
		LV += 1
ENDIF

IF GROUP == -1
	IF INUM >= 600
		;イベント領域のモンスター
		LOCAL:1 = RAND:10 + 1
	ELSEIF LINE >= 0 && LINE < 3
		IF ITEM:INUM <= 0
			;全滅の場合骷髅兵
			;数は2回ダイス振って小さい方
			LOCAL:1 = RAND:10 + 1
			INUM = 190
		ELSEIF ITEM:INUM > 10
			LOCAL:1 = RAND:10 + 1
		ELSE
			LOCAL:1 = ITEM:INUM
		ENDIF
	ELSEIF LINE == 3
		INUM = CFLAG:(ARG:2):570
		IF INUM < 100
			E:TOP = 0
			RETURN 0
		ENDIF
		LOCAL:1 = 1
	ELSEIF LINE == 4
		INUM = CFLAG:(ARG:3):570
		IF INUM < 100
			E:TOP = 0
			RETURN 0
		ENDIF
		LOCAL:1 = 1
	ELSEIF LINE == 5
		IF INUM < 100
			E:TOP = 0
			RETURN 0
		ENDIF
		LOCAL:1 = 1
	ENDIF
ELSE
;GROUP战用
	IF LINE >= 0 && LINE < 3
		IF ITEM:INUM <= 100
			;全滅の場合骷髅兵
			;数は2回ダイス振って小さい方
			LOCAL:1 = RAND:100 + 200
			INUM = 190
		ELSEIF ITEM:INUM > 300
			LOCAL:1 = RAND:100 + 200
		ELSE
			LOCAL:1 = ITEM:INUM
		ENDIF
	ELSEIF LINE == 3
		SIF INUM < 1000
			INUM = 1000 + RAND:10 + 10 * (GROUP - 1)
		LOCAL:1 = RAND:100 + 200
	ELSEIF LINE == 4
		SIF INUM < 1000
			INUM = 1000 + RAND:10 + 10 * (GROUP - 1)
		LOCAL:1 = RAND:100 + 200
	ELSEIF LINE == 5
		SIF INUM < 1000
			INUM = 1000 + RAND:10 + 10 * (GROUP - 1)
		LOCAL:1 = RAND:100 + 200
	ENDIF
ENDIF
;状態異常初期化
E:(TOP+9) = 0

IF INUM == 190
	;ボス化初期化
	;骷髅兵の場合ボスにならない
	E:(TOP+8) = 0
ELSEIF (LOCAL:1 <= 1 && LINE < 3) || (LOCAL:1 <= 1 && LINE < 3 && GROUP)
	;ボス化
	E:(TOP+8) = 1
ELSE
	;ボス化初期化
	E:(TOP+8) = 0
ENDIF

;ダイス2回目
IF GROUP == -1
	IF LINE >= 0 && LINE < 3
		IF INUM >= 600
			LOCAL:0 = RAND:10 + 1
		ELSEIF ITEM:INUM <= 0 || ITEM:INUM > 10
			LOCAL:0 = RAND:10 + 1
		ELSE
			LOCAL:0 = ITEM:INUM
		ENDIF
	ELSE
		LOCAL:0 = 1
	ENDIF
ELSE
	IF LINE >= 0 && LINE < 3
		IF ITEM:INUM <= 100 || ITEM:INUM > 300
			LOCAL:0 = RAND:100 + 200
		ELSE
			LOCAL:0 = ITEM:INUM
		ENDIF
	ELSE
		LOCAL:0 = RAND:150 + 100
	ENDIF
ENDIF

;E:Y+99（その列の怪物の数）を決定
E:(TOP+99) = LOCAL:0

IF INUM == 100
	CALL COBALT, TOP
ELSEIF INUM == 101
	CALL GOBLIN, TOP
ELSEIF INUM == 102
	CALL OOZE, TOP
ELSEIF INUM == 103
	CALL DRAGONFLY, TOP
ELSEIF INUM == 104
	CALL ZOMBIE, TOP
ELSEIF INUM == 110
	CALL ORC, TOP
ELSEIF INUM == 111
	CALL HOBGOBRIN, TOP
ELSEIF INUM == 112
	CALL MONSTERIVY, TOP
ELSEIF INUM == 113
	CALL ZOMBIEWORM, TOP
ELSEIF INUM == 114
	CALL ZOMBIEHOUND, TOP
ELSEIF INUM == 120
	CALL LIZARDMAN, TOP
ELSEIF INUM == 121
	CALL OGRE, TOP
ELSEIF INUM == 122
	CALL ARMOREATER, TOP
ELSEIF INUM == 123
	CALL FAIRY, TOP
ELSEIF INUM == 124
	CALL POISON_CENTIPEDE, TOP
ELSEIF INUM == 130
	CALL TROLL, TOP
ELSEIF INUM == 131
	CALL GARGOYLE, TOP
ELSEIF INUM == 132
	CALL IMP, TOP
ELSEIF INUM == 133
	CALL WIZARD, TOP
ELSEIF INUM == 134
	CALL BANSHEE, TOP
ELSEIF INUM == 140
	CALL LESSERDEMON, TOP
ELSEIF INUM == 141
	CALL HELLHOUND, TOP
ELSEIF INUM == 142
	CALL SLIME, TOP
ELSEIF INUM == 143
	CALL WITCH, TOP
ELSEIF INUM == 144
	CALL VAMPIRE_TREE, TOP
ELSEIF INUM == 150
	CALL GIANT, TOP
ELSEIF INUM == 151
	CALL CHIMERA, TOP
ELSEIF INUM == 152
	CALL SUCCUBUS, TOP
ELSEIF INUM == 153
	CALL PRIEST, TOP
ELSEIF INUM == 154
	CALL KNIGHTGIRL, TOP
ELSEIF INUM == 160
	CALL KUNOICHI, TOP
ELSEIF INUM == 161
	CALL DARKKNIGHT, TOP
ELSEIF INUM == 162
	CALL BRAINEATER, TOP
ELSEIF INUM == 163
	CALL PRIESTES, TOP
ELSEIF INUM == 164
	CALL PIXIE, TOP
ELSEIF INUM == 170
	CALL NINJA, TOP
ELSEIF INUM == 171
	CALL DULLAHAN, TOP
ELSEIF INUM == 172
	CALL VAMPIRE, TOP
ELSEIF INUM == 173
	CALL WISP, TOP
ELSEIF INUM == 174
	CALL DEATH_SCORPION, TOP
ELSEIF INUM == 180
	CALL ARCHDEMON, TOP
ELSEIF INUM == 181
	CALL LORDOFDEATH, TOP
ELSEIF INUM == 182
	CALL LILITH, TOP
ELSEIF INUM == 183
	CALL NIGHTMARE, TOP
ELSEIF INUM == 184
	CALL EVIL_EYE, TOP
ELSEIF INUM == 191
	CALL DARKMESSIAH, TOP
ELSEIF INUM == 192
	CALL NINETAIL, TOP
ELSEIF INUM == 193
	CALL CHAOSDRAGON, TOP
ELSEIF INUM == 600
	CALL BARBARIAN, TOP
ELSEIF INUM == 601
	CALL GIANTANT, TOP
ELSEIF INUM == 602
	CALL REDORC, TOP
ELSEIF INUM == 603
	CALL CARNIVOREFLOWER, TOP
ELSEIF INUM == 604
	CALL GORILLA, TOP
ELSEIF INUM == 605
	CALL TATOOGIRL, TOP
ELSEIF INUM == 606
	CALL ELEPHANT, TOP
ELSEIF INUM == 607
	CALL REDSHAMAN, TOP
ELSEIF INUM == 608
	CALL TROLLMAGE, TOP
ELSEIF INUM == 609
	CALL FORESTDRAGON, TOP
ELSEIF INUM >= 1000 && INUM < 2000
	CALL ENEMY_DATA_CHECK, INUM, TOP
ELSE
	CALL SKELETON, TOP, ARG:2
ENDIF

;精英のお供はボス化しない。
SIF ARG:2 == -1
	ARG:2 = 0
SIF TALENT:(ARG:2):220 == 1 && (CFLAG:(ARG:2):0 >= 1 || ARG:2 == 0)
	E:(TOP+8) = 0


;精英のお供は数が多い
IF TALENT:(ARG:2):220 == 1 && (CFLAG:(ARG:2):0 >= 1 || ARG:2 == 0)
	;2回の抽選の結果、多い方を採用
	SIF LOCAL:0 < LOCAL:1
		LOCAL:0 = LOCAL:1
ELSE
	;2回の抽選の結果、少ない方を採用
	SIF LOCAL:0 > LOCAL:1
		LOCAL:0 = LOCAL:1
ENDIF

;兵種による特性変化
LOCAL = INUM + 700
EXTRA = FLAG:LOCAL

LOCAL:2 = EXTRA
LOCAL:2 %= 100

IF LOCAL:2 == 1
	;上級化
	E:(TOP+2) += RAND:LV / 2 + 1
	E:(TOP+3) += RAND:LV / 2 + 1
ELSEIF LOCAL:2 == 2
	;地形適応
	E:(TOP+5) = 17
	;防御強化
	E:(TOP+3) += RAND:LV / 2 + 1
ELSEIF LOCAL:2 == 3
	;酸性
	;鎧破壊
	E:(TOP+5) = 4
	;攻撃強化
	E:(TOP+2) += RAND:LV / 2 + 1
ELSEIF LOCAL:2 == 4
	;猛毒
	;攻撃強化
	E:(TOP+2) += RAND:LV + 1
ELSEIF LOCAL:2 == 5
	;装甲
	;防御強化
	E:(TOP+3) += RAND:LV + 1
ENDIF

LOCAL:2 = EXTRA
LOCAL:2 /= 100
LOCAL:2 %= 100
IF LOCAL:2 == 1
	;弓兵（射撃）
	E:(TOP+5) = 16
	;攻撃強化
	E:(TOP+2) += RAND:LV + 1
ELSEIF LOCAL:2 == 2
	;魔導兵（エナジーボルト）
	E:(TOP+6) = 3
	;攻撃強化
	E:(TOP+2) += RAND:LV + 1
ELSEIF LOCAL:2 == 3
	;催眠師（スリープ）
	E:(TOP+6) = 2
	;防御強化
	E:(TOP+3) += RAND:LV + 1
ELSEIF LOCAL:2 == 4 && FLAG:83 > 0
	;肉鎧兵（肉鎧）
	E:(TOP+5) = 18
	;防御強化
	E:(TOP+3) += RAND:LV + 1
ENDIF

;怪物ステータス補正
LOCAL = E:(TOP+1)
E:(TOP+1) += 4

;テコ入れ
LOCAL *= 2

;知識による特定の怪物の強化
;淫魔知识で女魔族強化(1.5倍補正)
SIF TALENT:0:327 && E:(TOP+7) == 9
	LOCAL += LOCAL / 2
;魔虫知识で昆虫強化(1.5倍補正)
SIF TALENT:0:328 && E:(TOP+7) == 3
	LOCAL += LOCAL / 2

;ダンジョンレベルに応じて強化された敵を生成
;怪物レベルの分だけダイスを振る
LVUP = 0
FOR LVCOUNT, 0, LOCAL
	LVUP += RAND:LV
NEXT

E:(TOP+2) *= 2
E:(TOP+2) += LVUP

E:(TOP+3) *= 2
E:(TOP+3) += LVUP

;精英のお供を強化
;精英のLv分だけ攻撃力と防御力をプラス
SIF TALENT:(ARG:2):220 == 1 && (CFLAG:(ARG:2):0 >= 1 || ARG:2 == 0)
	E:(TOP+2) += CFLAG:(ARG:2):9
SIF TALENT:(ARG:2):220 == 1 && (CFLAG:(ARG:2):0 >= 1 || ARG:2 == 0)
	E:(TOP+3) += CFLAG:(ARG:2):9

;精英と怪物が同じ种族なら更に強化
SIF TALENT:(ARG:2):220 == 1 && (CFLAG:(ARG:2):0 >= 1 || ARG:2 == 0) && TALENT:(ARG:2):319 == E:(TOP+7)
	E:(TOP+2) += CFLAG:(ARG:2):9
SIF TALENT:(ARG:2):220 == 1 && (CFLAG:(ARG:2):0 >= 1 || ARG:2 == 0) && TALENT:(ARG:2):319 == E:(TOP+7)
	E:(TOP+3) += CFLAG:(ARG:2):9

RETURN 0

;---------------------------------------------------------
@COBALT, ARG:0
;---------------------------------------------------------
;狗头人

;怪物番号
E:(ARG:0) = 100

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 1

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@GOBLIN, ARG:0
;---------------------------------------------------------
;哥布林

;怪物番号
E:(ARG:0) = 101

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 1

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@OOZE, ARG:0
;---------------------------------------------------------
;黏液怪

;怪物番号
E:(ARG:0) = 102

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 1

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = -1

;特殊能力（1=粘液捕获）
E:(ARG:0+5) = 1

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（2=史莱姆）
E:(ARG:0+7) = 2

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@DRAGONFLY, ARG:0
;---------------------------------------------------------
;龙头苍蝇

;怪物番号
E:(ARG:0) = 103

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 1

;防御力
E:(ARG:0+3) = 1

;速度
E:(ARG:0+4) = 1

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（3=昆虫）
E:(ARG:0+7) = 3

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@ZOMBIE, ARG:0
;---------------------------------------------------------
;丧尸

;怪物番号
E:(ARG:0) = 104

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = -1

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（0=无）
E:(ARG:0+7) = 0

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@ORC, ARG:0
;---------------------------------------------------------
;兽人

;怪物番号
E:(ARG:0) = 110

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 3

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@HOBGOBRIN, ARG:0
;---------------------------------------------------------
;熊地精

;怪物番号
E:(ARG:0) = 111

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@MONSTERIVY, ARG:0
;---------------------------------------------------------
;藤蔓怪

;怪物番号
E:(ARG:0) = 112

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = 0

;特殊能力（3=藤蔓捕获）
E:(ARG:0+5) = 3

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（4=蔦触手）
E:(ARG:0+7) = 4

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@ZOMBIEWORM, ARG:0
;---------------------------------------------------------
;丧尸虫

;怪物番号
E:(ARG:0) = 113

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 3

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = -3

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（5=触手）
E:(ARG:0+7) = 5

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@ZOMBIEHOUND, ARG:0
;---------------------------------------------------------
;丧尸猎犬

;怪物番号
E:(ARG:0) = 114

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 1

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@LIZARDMAN, ARG:0
;---------------------------------------------------------
;蜥蜴人

;怪物番号
E:(ARG:0) = 120

;レベル
E:(ARG:0+1) = 3

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@OGRE, ARG:0
;---------------------------------------------------------
;食人魔

;怪物番号
E:(ARG:0) = 121

;レベル
E:(ARG:0+1) = 3

;攻撃力
E:(ARG:0+2) = 3

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@ARMOREATER, ARG:0
;---------------------------------------------------------
;食铠者

;怪物番号
E:(ARG:0) = 122

;レベル
E:(ARG:0+1) = 3

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = 0

;特殊能力（4=铠破坏）
E:(ARG:0+5) = 4

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（5=触手）
E:(ARG:0+7) = 5

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@FAIRY, ARG:0
;---------------------------------------------------------
;小仙子

;怪物番号
E:(ARG:0) = 123

;レベル
E:(ARG:0+1) = 3

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = 2

;特殊能力（5=透明）
E:(ARG:0+5) = 5

;魔法（1=テレポート）
E:(ARG:0+6) = 1

;陵辱タイプ（6=妖精）
E:(ARG:0+7) = 6

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@POISON_CENTIPEDE, ARG:0
;---------------------------------------------------------
;毒蜈蚣

;怪物番号
E:(ARG:0) = 124

;レベル
E:(ARG:0+1) = 3

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = -1

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（3=昆虫）
E:(ARG:0+7) = 3

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@TROLL, ARG:0
;---------------------------------------------------------
;巨魔

;怪物番号
E:(ARG:0) = 130

;レベル
E:(ARG:0+1) = 4

;攻撃力
E:(ARG:0+2) = 5

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = 0

;特殊能力（6=再生）
E:(ARG:0+5) = 6

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（7=巨人）
E:(ARG:0+7) = 7

;耐性(冷気・電撃)
E:(ARG:0+10) = 2 + 4

RETURN 0

;---------------------------------------------------------
@GARGOYLE, ARG:0
;---------------------------------------------------------
;石像鬼

;怪物番号
E:(ARG:0) = 131

;レベル
E:(ARG:0+1) = 4

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 5

;速度
E:(ARG:0+4) = 0

;特殊能力（7=拟态）
E:(ARG:0+5) = 7

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@IMP, ARG:0
;---------------------------------------------------------
;小恶魔

;怪物番号
E:(ARG:0) = 132

;レベル
E:(ARG:0+1) = 4

;攻撃力
E:(ARG:0+2) = 3

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = 2

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（2=スリープ）
E:(ARG:0+6) = 2

;陵辱タイプ（6=妖精）
E:(ARG:0+7) = 6

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@WIZARD, ARG:0
;---------------------------------------------------------
;男巫

;怪物番号
E:(ARG:0) = 133

;レベル
E:(ARG:0+1) = 4

;攻撃力
E:(ARG:0+2) = 0

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（3=エナジーボルト）
E:(ARG:0+6) = 3

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@BANSHEE, ARG:0
;---------------------------------------------------------
;报丧女妖

;怪物番号
E:(ARG:0) = 134

;レベル
E:(ARG:0+1) = 4

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = 2

;特殊能力（8=迷惑）
E:(ARG:0+5) = 8

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@LESSERDEMON, ARG:0
;---------------------------------------------------------
;下等恶魔

;怪物番号
E:(ARG:0) = 140

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 5

;速度
E:(ARG:0+4) = 0

;特殊能力（4=铠破坏）
E:(ARG:0+5) = 4

;魔法（4=エナジードレイン）
E:(ARG:0+6) = 4

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@HELLHOUND, ARG:0
;---------------------------------------------------------
;地狱猎犬

;怪物番号
E:(ARG:0) = 141

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 5

;防御力
E:(ARG:0+3) = 6

;速度
E:(ARG:0+4) = 0

;特殊能力（9=吐息）
E:(ARG:0+5) = 9

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@SLIME, ARG:0
;---------------------------------------------------------
;史莱姆

;怪物番号
E:(ARG:0) = 142

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 8

;速度
E:(ARG:0+4) = 0

;特殊能力（1=粘液捕获）
E:(ARG:0+5) = 1

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（2=史莱姆）
E:(ARG:0+7) = 2

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@WITCH, ARG:0
;---------------------------------------------------------
;女巫

;怪物番号
E:(ARG:0) = 143

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 0

;防御力
E:(ARG:0+3) = 5

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（5=ファイアボール）
E:(ARG:0+6) = 5

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@VAMPIRE_TREE, ARG:0
;---------------------------------------------------------
;吸血树

;怪物番号
E:(ARG:0) = 144

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 7

;防御力
E:(ARG:0+3) = 5

;速度
E:(ARG:0+4) = 0

;特殊能力（3=藤蔓捕获）
E:(ARG:0+5) = 3

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（4=蔦触手）
E:(ARG:0+7) = 4

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@GIANT, ARG:0
;---------------------------------------------------------
;巨人

;怪物番号
E:(ARG:0) = 150

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 8

;防御力
E:(ARG:0+3) = 6

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（7=巨人）
E:(ARG:0+7) = 7

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@CHIMERA, ARG:0
;---------------------------------------------------------
;奇美拉

;怪物番号
E:(ARG:0) = 151

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 7

;速度
E:(ARG:0+4) = 0

;特殊能力（9=吐息）
E:(ARG:0+5) = 9

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@SUCCUBUS, ARG:0
;---------------------------------------------------------
;魅魔

;怪物番号
E:(ARG:0) = 152

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 5

;防御力
E:(ARG:0+3) = 6

;速度
E:(ARG:0+4) = 0

;特殊能力（11=诱惑）
E:(ARG:0+5) = 11

;魔法（4=エナジードライン）
E:(ARG:0+6) = 4

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@PRIEST, ARG:0
;---------------------------------------------------------
;男祭司

;怪物番号
E:(ARG:0) = 153

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 6

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（6=ヒール）
E:(ARG:0+6) = 6

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@KNIGHTGIRL, ARG:0
;---------------------------------------------------------
;夜少女

;怪物番号
E:(ARG:0) = 154

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 8

;速度
E:(ARG:0+4) = 0

;特殊能力（15=魔力吸取）
E:(ARG:0+5) = 15

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@KUNOICHI, ARG:0
;---------------------------------------------------------
;女忍

;怪物番号
E:(ARG:0) = 160

;レベル
E:(ARG:0+1) = 7

;攻撃力
E:(ARG:0+2) = 8

;防御力
E:(ARG:0+3) = 7

;速度
E:(ARG:0+4) = 0

;特殊能力（11=诱惑）
E:(ARG:0+5) = 11

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@DARKKNIGHT, ARG:0
;---------------------------------------------------------
;黑暗骑士

;怪物番号
E:(ARG:0) = 161

;レベル
E:(ARG:0+1) = 7

;攻撃力
E:(ARG:0+2) = 8

;防御力
E:(ARG:0+3) = 9

;速度
E:(ARG:0+4) = -1

;特殊能力（15=魔力吸取）
E:(ARG:0+5) = 15

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@BRAINEATER, ARG:0
;---------------------------------------------------------
;食脑魔

;怪物番号
E:(ARG:0) = 162

;レベル
E:(ARG:0+1) = 7

;攻撃力
E:(ARG:0+2) = 10

;防御力
E:(ARG:0+3) = 5

;速度
E:(ARG:0+4) = 0

;特殊能力（12=混乱）
E:(ARG:0+5) = 12

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（11=脑奸）
E:(ARG:0+7) = 11

;耐性(火炎・冷気・電撃)
E:(ARG:0+10) = 1 + 2 + 4

RETURN 0

;---------------------------------------------------------
@PRIESTES, ARG:0
;---------------------------------------------------------
;女祭司

;怪物番号
E:(ARG:0) = 163

;レベル
E:(ARG:0+1) = 7

;攻撃力
E:(ARG:0+2) = 7

;防御力
E:(ARG:0+3) = 7

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（6=ヒール）
E:(ARG:0+6) = 6

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@PIXIE, ARG:0
;---------------------------------------------------------
;小精灵

;怪物番号
E:(ARG:0) = 164

;レベル
E:(ARG:0+1) = 7

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 6

;速度
E:(ARG:0+4) = 0

;特殊能力（8=迷惑）
E:(ARG:0+5) = 8

;魔法（2=スリープ）
E:(ARG:0+6) = 2

;陵辱タイプ（6=妖精）
E:(ARG:0+7) = 6

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@NINJA, ARG:0
;---------------------------------------------------------
;忍者

;怪物番号
E:(ARG:0) = 170

;レベル
E:(ARG:0+1) = 8

;攻撃力
E:(ARG:0+2) = 9

;防御力
E:(ARG:0+3) = 8

;速度
E:(ARG:0+4) = 0

;特殊能力（8=迷惑）
E:(ARG:0+5) = 8

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(火炎・冷気)
E:(ARG:0+10) = 1 + 2

RETURN 0

;---------------------------------------------------------
@DULLAHAN, ARG:0
;---------------------------------------------------------
;无头骑士

;怪物番号
E:(ARG:0) = 171

;レベル
E:(ARG:0+1) = 8

;攻撃力
E:(ARG:0+2) = 9

;防御力
E:(ARG:0+3) = 9

;速度
E:(ARG:0+4) = 0

;特殊能力（15=魔力吸取）
E:(ARG:0+5) = 15

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@VAMPIRE, ARG:0
;---------------------------------------------------------
;吸血鬼

;怪物番号
E:(ARG:0) = 172

;レベル
E:(ARG:0+1) = 8

;攻撃力
E:(ARG:0+2) = 8

;防御力
E:(ARG:0+3) = 8

;速度
E:(ARG:0+4) = 0

;特殊能力（11=诱惑）
E:(ARG:0+5) = 11

;魔法（4=エナジードレイン）
E:(ARG:0+6) = 4

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@WISP, ARG:0
;---------------------------------------------------------
;幽灵

;怪物番号
E:(ARG:0) = 173

;レベル
E:(ARG:0+1) = 8

;攻撃力
E:(ARG:0+2) = 0

;防御力
E:(ARG:0+3) = 9

;速度
E:(ARG:0+4) = 0

;特殊能力（8=迷惑）
E:(ARG:0+5) = 8

;魔法（4=エナジードレイン）
E:(ARG:0+6) = 4

;陵辱タイプ（0=无）
E:(ARG:0+7) = 0

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@DEATH_SCORPION, ARG:0
;---------------------------------------------------------
;死亡蝎

;怪物番号
E:(ARG:0) = 174

;レベル
E:(ARG:0+1) = 8

;攻撃力
E:(ARG:0+2) = 11

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = 2

;特殊能力（4=铠破坏）
E:(ARG:0+5) = 4

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（3=昆虫）
E:(ARG:0+7) = 3

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@ARCHDEMON, ARG:0
;---------------------------------------------------------
;大恶魔

;怪物番号
E:(ARG:0) = 180

;レベル
E:(ARG:0+1) = 9

;攻撃力
E:(ARG:0+2) = 12

;防御力
E:(ARG:0+3) = 10

;速度
E:(ARG:0+4) = 0

;特殊能力（12=混乱）
E:(ARG:0+5) = 12

;魔法（5=ファイアボール）
E:(ARG:0+6) = 5

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(火炎・冷気)
E:(ARG:0+10) = 1 + 2

RETURN 0

;---------------------------------------------------------
@LORDOFDEATH, ARG:0
;---------------------------------------------------------
;死亡领主

;怪物番号
E:(ARG:0) = 181

;レベル
E:(ARG:0+1) = 9

;攻撃力
E:(ARG:0+2) = 10

;防御力
E:(ARG:0+3) = 12

;速度
E:(ARG:0+4) = 2

;特殊能力（13=经验值吸取）
E:(ARG:0+5) = 13

;魔法（6=ヒール）
E:(ARG:0+6) = 6

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(電撃)
E:(ARG:0+10) = 4

RETURN 0

;---------------------------------------------------------
@LILITH, ARG:0
;---------------------------------------------------------
;莉莉丝

;怪物番号
E:(ARG:0) = 182

;レベル
E:(ARG:0+1) = 9

;攻撃力
E:(ARG:0+2) = 10

;防御力
E:(ARG:0+3) = 10

;速度
E:(ARG:0+4) = 0

;特殊能力（11=诱惑）
E:(ARG:0+5) = 11

;魔法（4=エナジードレイン）
E:(ARG:0+6) = 4

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@NIGHTMARE, ARG:0
;---------------------------------------------------------
;梦魇

;怪物番号
E:(ARG:0) = 183

;レベル
E:(ARG:0+1) = 9

;攻撃力
E:(ARG:0+2) = 11

;防御力
E:(ARG:0+3) = 11

;速度
E:(ARG:0+4) = 2

;特殊能力（10=麻痹）
E:(ARG:0+5) = 10

;魔法（2=スリープ）
E:(ARG:0+6) = 2

;陵辱タイプ（12=馬）
E:(ARG:0+7) = 12

;耐性(冷気)
E:(ARG:0+10) = 2

RETURN 0

;---------------------------------------------------------
@EVIL_EYE, ARG:0
;---------------------------------------------------------
;眼魔

;怪物番号
E:(ARG:0) = 184

;レベル
E:(ARG:0+1) = 9

;攻撃力
E:(ARG:0+2) = 11

;防御力
E:(ARG:0+3) = 11

;速度
E:(ARG:0+4) = 0

;特殊能力（12=混乱）
E:(ARG:0+5) = 12

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（5=触手）
E:(ARG:0+7) = 5

;耐性(冷気・電撃)
E:(ARG:0+10) = 2 + 4

RETURN 0

;---------------------------------------------------------
@SKELETON, ARG:0, ARG:1
#DIM LV
;---------------------------------------------------------
;骷髅兵
;階層によって強くなる

IF ARG:1 < 0
	LV = RAND:9 + 1
ELSE
	LV = CFLAG:(ARG:1):501
ENDIF

;怪物番号
E:(ARG:0) = 190

;レベル
E:(ARG:0+1) = LV + 1

;攻撃力
E:(ARG:0+2) = LV + 1

;防御力
E:(ARG:0+3) = LV + 1

;速度
E:(ARG:0+4) = 0

;特殊能力（0=无）
E:(ARG:0+5) = 0

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（0=无）
E:(ARG:0+7) = 0

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------
@DARKMESSIAH, ARG:0
;----------------------------------
;黑暗救世主

;怪物番号
E:(ARG:0) = 191

;レベル
E:(ARG:0+1) = 30

;攻撃力
E:(ARG:0+2) = 35

;防御力
E:(ARG:0+3) = 30

;速度
E:(ARG:0+4) = 1

;特殊能力（13=经验值吸取）
E:(ARG:0+5) = 13

;魔法（5=ファイアボール）
E:(ARG:0+6) = 5

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(冷気・電撃)
E:(ARG:0+10) = 2 + 4

RETURN 0

;---------------------------------
@NINETAIL, ARG:0
;----------------------------------
;九尾

;怪物番号
E:(ARG:0) = 192

;レベル
E:(ARG:0+1) = 30

;攻撃力
E:(ARG:0+2) = 30

;防御力
E:(ARG:0+3) = 40

;速度
E:(ARG:0+4) = 1

;特殊能力（15=魔力吸取）
E:(ARG:0+5) = 15

;魔法（9=レベルドレイン）
E:(ARG:0+6) = 9

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(火炎・電撃)
E:(ARG:0+10) = 1 + 4

RETURN 0

;---------------------------------
@CHAOSDRAGON, ARG:0
;----------------------------------
;混沌龙

;怪物番号
E:(ARG:0) = 193

;レベル
E:(ARG:0+1) = 30

;攻撃力
E:(ARG:0+2) = 50

;防御力
E:(ARG:0+3) = 40

;速度
E:(ARG:0+4) = 0

;特殊能力（14=破铠吐息）
E:(ARG:0+5) = 14

;魔法（0=无）
E:(ARG:0+6) = 0

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(火炎・冷気)
E:(ARG:0+10) = 1 + 2

RETURN 0


;---------------------------------------------------------
@BARBARIAN, ARG:0
;---------------------------------------------------------
;バーバリアン

;モンスター番号
E:(ARG:0) = 600

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 1

;速度
E:(ARG:0+4) = 0

;特殊能力（0=なし）
E:(ARG:0+5) = 0

;魔法（0=なし）
E:(ARG:0+6) = 0

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@GIANTANT, ARG:0
;---------------------------------------------------------
;ジャイアントアント

;モンスター番号
E:(ARG:0) = 601

;レベル
E:(ARG:0+1) = 1

;攻撃力
E:(ARG:0+2) = 3

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = -1

;特殊能力（0=なし）
E:(ARG:0+5) = 0

;魔法（0=なし）
E:(ARG:0+6) = 0

;陵辱タイプ（3=昆虫）
E:(ARG:0+7) = 3

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@REDORC, ARG:0
;---------------------------------------------------------
;レッドオーク

;モンスター番号
E:(ARG:0) = 602

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 2

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = 0

;特殊能力（0=なし）
E:(ARG:0+5) = 0

;魔法（0=なし）
E:(ARG:0+6) = 0

;陵辱タイプ（1=カタコト）
E:(ARG:0+7) = 1

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@CARNIVOREFLOWER, ARG:0
;---------------------------------------------------------
;カニバルフラワー

;モンスター番号
E:(ARG:0) = 603

;レベル
E:(ARG:0+1) = 2

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 2

;速度
E:(ARG:0+4) = -1

;特殊能力（3=蔦捕獲）
E:(ARG:0+5) = 3

;魔法（0=なし）
E:(ARG:0+6) = 0

;陵辱タイプ（4=蔦触手）
E:(ARG:0+7) = 4

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@GORILLA, ARG:0
;---------------------------------------------------------
;ゴリラ

;モンスター番号
E:(ARG:0) = 604

;レベル
E:(ARG:0+1) = 3

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = -1

;特殊能力（0=なし）
E:(ARG:0+5) = 0

;魔法（0=なし）
E:(ARG:0+6) = 0

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@TATOOGIRL, ARG:0
;---------------------------------------------------------
;タトゥーガール

;モンスター番号
E:(ARG:0) = 605

;レベル
E:(ARG:0+1) = 4

;攻撃力
E:(ARG:0+2) = 3

;防御力
E:(ARG:0+3) = 3

;速度
E:(ARG:0+4) = 1

;特殊能力（16=射撃）
E:(ARG:0+5) = 16

;魔法（6=ヒール）
E:(ARG:0+6) = 6

;陵辱タイプ（9=女）
E:(ARG:0+7) = 9

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@ELEPHANT, ARG:0
;---------------------------------------------------------
;エレファント

;モンスター番号
E:(ARG:0) = 606

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 6

;速度
E:(ARG:0+4) = -1

;特殊能力（4=鎧破壊）
E:(ARG:0+5) = 4

;魔法（0=なし）
E:(ARG:0+6) = 0

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(無し)
E:(ARG:0+10) = 0

RETURN 0

;---------------------------------------------------------
@REDSHAMAN, ARG:0
;---------------------------------------------------------
;レッドシャーマン

;モンスター番号
E:(ARG:0) = 607

;レベル
E:(ARG:0+1) = 5

;攻撃力
E:(ARG:0+2) = 4

;防御力
E:(ARG:0+3) = 4

;速度
E:(ARG:0+4) = 1

;特殊能力（12=混乱）
E:(ARG:0+5) = 12

;魔法（5=ファイアボール）
E:(ARG:0+6) = 5

;陵辱タイプ（8=男）
E:(ARG:0+7) = 8

;耐性(火炎)
E:(ARG:0+10) = 1

RETURN 0

;---------------------------------------------------------
@TROLLMAGE, ARG:0
;---------------------------------------------------------
;トロールメイジ

;モンスター番号
E:(ARG:0) = 608

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 6

;防御力
E:(ARG:0+3) = 7

;速度
E:(ARG:0+4) = 0

;特殊能力（6=再生）
E:(ARG:0+5) = 6

;魔法（4=エナジードレイン）
E:(ARG:0+6) = 4

;陵辱タイプ（7=巨人）
E:(ARG:0+7) = 7

;耐性(冷気・電撃)
E:(ARG:0+10) = 2 + 4

RETURN 0

;---------------------------------------------------------
@FORESTDRAGON, ARG:0
;---------------------------------------------------------
;フォレストドラゴン

;モンスター番号
E:(ARG:0) = 609

;レベル
E:(ARG:0+1) = 6

;攻撃力
E:(ARG:0+2) = 8

;防御力
E:(ARG:0+3) = 8

;速度
E:(ARG:0+4) = 0

;特殊能力（9=ブレス）
E:(ARG:0+5) = 9

;魔法（2=スリープ）
E:(ARG:0+6) = 2

;陵辱タイプ（10=獣）
E:(ARG:0+7) = 10

;耐性(冷気・電撃)
E:(ARG:0+10) = 2 + 4

RETURN 0

;----------------------------------
@MONSTER_SETUP(ARG)
#DIM EXTRA
#DIM SELECT
#DIM FLOOR
#DIM ROOM
;----------------------------------

;エラー弾き
SIF ARG == 999
	RETURN 999
SIF ARG < 100
	RETURN 999
SIF ARG > 199
	RETURN 999

IF ARG < 190
	FLOOR = ((ARG - 100) / 10) + 1
	LOCAL = FLOOR + 349
	ROOM = FLAG:LOCAL
ELSE
	FLOOR = 0
	ROOM = 0
ENDIF

$START

LOCAL = ARG + 700
EXTRA = FLAG:LOCAL

CALL MONSTER_NAME,ARG,0
PRINTL 的改造
PRINTFORML 所持金　{MONEY}
IF ROOM >= 500 && ROOM < 510
	PRINTFORML 设施：%ITEMNAME:ROOM%
ELSE
	PRINTL 设施：道路
ENDIF

LOCAL:1 = 0
LOCAL:2 = EXTRA
LOCAL:2 %= 100
FOR LOCAL,0,10
	CALL MONSTER_SETUP_ABLE,ARG,LOCAL
	IF LOCAL == 0
		PRINT [0] 无改造
	ELSEIF LOCAL == 1 && RESULT == 1
		PRINT [1] 上级化
	ELSEIF LOCAL == 2 && RESULT == 1
		PRINT [2] 土地适应
	ELSEIF LOCAL == 3 && RESULT == 1
		PRINT [3] 酸性化
	ELSEIF LOCAL == 4 && RESULT == 1
		PRINT [4] 猛毒化
	ELSEIF LOCAL == 5 && RESULT == 1
		PRINT [5] 装甲化
	ENDIF
	
	IF LOCAL:2 == LOCAL
		PRINT * 
	ELSE
		PRINT  
	ENDIF
	
	LOCAL:1 += 1
	LOCAL:1 %= 6
	SIF LOCAL:1 == 0
		PRINTL  
	
NEXT

SIF LOCAL:1 != 0
	PRINTL  

;魔族・女魔族・亜人・妖精・巨人は兵種も選択できる

CALL MONSTER_DATA,ARG,0

IF E:7 == 1 || E:7 == 6 || E:7 == 7 || E:7 == 8 || E:7 == 9
	PRINTL --- 兵种 ---

	LOCAL:1 = 0
	LOCAL:2 = EXTRA
	LOCAL:2 /= 100
	LOCAL:2 %= 100
	LOCAL:2 += 50
	FOR LOCAL,50,60
		CALL MONSTER_SETUP_ABLE,ARG,LOCAL
		IF LOCAL == 50
			PRINT [50] 普通兵
		ELSEIF LOCAL == 51 && RESULT == 1
			PRINT [51] 弓兵
		ELSEIF LOCAL == 52 && RESULT == 1
			PRINT [52] 魔导兵
		ELSEIF LOCAL == 53 && RESULT == 1
			PRINT [53] 催眠师
		ELSEIF LOCAL == 54 && RESULT == 1
			PRINT [54] 肉铠兵
		ENDIF
		
		IF LOCAL:2 == LOCAL
			PRINT * 
		ELSE
			PRINT  
		ENDIF
		
		LOCAL:1 += 1
		LOCAL:1 %= 6
		SIF LOCAL:1 == 0
			PRINTL  
		
	NEXT
	
	SIF LOCAL:1 != 0
		PRINTL  
	
ENDIF

DRAWLINE
PRINTL [999] 返回

INPUT

SIF RESULT == 999
	RETURN KAI_LIST

SELECT = RESULT

CALL MONSTER_SETUP_ABLE,ARG,SELECT

SIF RESULT == 0
	RETURN KAI_LIST

PRINTL 改造需要１０００资金
IF MONEY < 1000
	PRINTW 没钱还是用杂鱼兵吧！
	RETURN KAI_LIST
ENDIF

CALL SELECT_YES_NO
SIF RESULT == 1
	RETURN KAI_LIST

;該当区域を入れ替える
IF SELECT < 50
	LOCAL = EXTRA
	LOCAL /= 100
	LOCAL *= 100
	SELECT += LOCAL
	EXTRA = SELECT
ELSEIF SELECT >= 50 && SELECT < 100
	SELECT -= 50
	SELECT *= 100
	LOCAL = EXTRA
	LOCAL %= 100
	SELECT += LOCAL
	LOCAL = EXTRA
	LOCAL /= 10000
	LOCAL *= 10000
	SELECT += LOCAL
	EXTRA = SELECT
ENDIF

LOCAL = ARG + 700
FLAG:LOCAL = EXTRA
MONEY -= 1000
EX_FLAG:4444 -= 1000
GOTO START

;----------------------------------
@MONSTER_SETUP_ABLE(ARG,ARG:1)
;----------------------------------
;ARG = モンスターID
;ARG:1 = メニュー

;無印化は誰でも可
SIF ARG:1 == 0
	RETURN 1

;上級は誰でも可
SIF ARG:1 == 1
	RETURN 1

;土地適応はID190未満なら可
SIF ARG:1 == 2 && ARG < 190
	RETURN 1

CALL MONSTER_DATA,ARG,0

;酸性
;スライムか触手のみ
SIF (E:7 == 2 || E:7 == 5) && ARG:1 == 3
	RETURN 1

;装甲
;スライム以外
SIF E:7 != 2 && ARG:1 == 5
	RETURN 1

IF E:7 == 1 || E:7 == 6 || E:7 == 7 || E:7 == 8 || E:7 == 9
	;兵種
	;普通兵・弓兵・肉鎧兵は誰でも選択可
	SIF ARG:1 == 50 || ARG:1 == 51 || ARG:1 == 54
		RETURN 1
	;エナジーボルトを知らない場合、魔導兵選択可
	SIF E:6 != 3 && ARG:1 == 52
		RETURN 1
	;スリープを知らない場合、催眠師選択可
	SIF E:6 != 2 && ARG:1 == 53
		RETURN 1
	
ELSE
	;人間以外
	
	;猛毒
	SIF ARG:1 == 4
		RETURN 1
	
ENDIF

RETURN 0

;----------------------------------
@MONSTERNAME(L_ID)
#FUNCTIONS
#DIM L_ID
#DIM L_EXTRA
#DIM L_FLOOR
#DIM L_ROOM
;----------------------------------
;L_ID = モンスターID

LOCAL = L_ID + 700
L_EXTRA = FLAG:LOCAL
IF L_ID < 190
	L_FLOOR = ((L_ID - 100) / 10) + 1
	LOCAL = L_FLOOR + 349
	L_ROOM = FLAG:LOCAL
ELSE
	L_FLOOR = 0
	L_ROOM = 0
ENDIF

LOCALS =
LOCAL:2 = L_EXTRA
LOCAL:2 %= 100

IF LOCAL:2 == 1
	LOCALS += "上级"
ELSEIF LOCAL:2 == 2
	IF L_ROOM == 500
		LOCALS += "遗迹街的"
	ELSEIF L_ROOM == 501
		LOCALS += "沼地的"
	ELSEIF L_ROOM == 502
		LOCALS += "饲养的"
	ELSEIF L_ROOM == 503
		LOCALS += "冰封的"
	ELSEIF L_ROOM == 504
		LOCALS += "灼热的"
	ELSEIF L_ROOM == 505
		LOCALS += "迷宮的"
	ELSEIF L_ROOM == 506
		LOCALS += "标本的"
	ENDIF
ELSEIF LOCAL:2 == 3
	LOCALS += "酸性"
ELSEIF LOCAL:2 == 4
	LOCALS += "猛毒"
ELSEIF LOCAL:2 == 5
	LOCALS += "装甲"
ENDIF

LOCALS += ITEMNAME:L_ID

;兵種
LOCAL:2 = L_EXTRA
LOCAL:2 /= 100
LOCAL:2 %= 100

IF LOCAL:2 == 1
	LOCALS += "弓兵"
ELSEIF LOCAL:2 == 2
	LOCALS += "魔导兵"
ELSEIF LOCAL:2 == 3
	LOCALS += "催眠师"
ELSEIF LOCAL:2 == 4
	LOCALS += "肉铠兵"
ENDIF

RETURNF LOCALS



;----------------------------------
@MONSTER_NAME(ARG:0, ARG:1)
#DIM EXTRA
#DIM FLOOR
#DIM ROOM
#DIM NAME_LENG
;----------------------------------
;ARG:0 = モンスターID
;ARG:1 = (1=スペース配置)

LOCAL = ARG + 700
EXTRA = FLAG:LOCAL
IF ARG < 190
	FLOOR = ((ARG - 100) / 10) + 1
	LOCAL = FLOOR + 349
	ROOM = FLAG:LOCAL
ELSE
	FLOOR = 0
	ROOM = 0
ENDIF

NAME_LENG = 0

LOCAL:2 = EXTRA
LOCAL:2 %= 100

IF LOCAL:2 == 1
	PRINT 上級
	NAME_LENG += 4
ELSEIF LOCAL:2 == 2
	IF ROOM == 500
		PRINT 遗迹街的
		NAME_LENG += 8
	ELSEIF ROOM == 501
		PRINT 沼地的
		NAME_LENG += 6
	ELSEIF ROOM == 502
		PRINT 饲养的
		NAME_LENG += 8
	ELSEIF ROOM == 503
		PRINT 冰封的
		NAME_LENG += 6
	ELSEIF ROOM == 504
		PRINT 灼热的
		NAME_LENG += 6
	ELSEIF ROOM == 505
		PRINT 迷宮的
		NAME_LENG += 6
	ELSEIF ROOM == 506
		PRINT 标本的
		NAME_LENG += 6
	ENDIF
ELSEIF LOCAL:2 == 3
	PRINT 酸性
	NAME_LENG += 4
ELSEIF LOCAL:2 == 4
	PRINT 猛毒
	NAME_LENG += 4
ELSEIF LOCAL:2 == 5
	PRINT 装甲
	NAME_LENG += 4
ENDIF

PRINTS ITEMNAME:ARG

;兵種
LOCAL:2 = EXTRA
LOCAL:2 /= 100
LOCAL:2 %= 100

IF LOCAL:2 == 1
	PRINT 弓兵
	NAME_LENG += 4
ELSEIF LOCAL:2 == 2
	PRINT 魔导兵
	NAME_LENG += 6
ELSEIF LOCAL:2 == 3
	PRINT 催眠师
	NAME_LENG += 6
ELSEIF LOCAL:2 == 4
	PRINT 肉铠兵
	NAME_LENG += 6
ENDIF

IF ARG:1 == 1
	STRLENS ITEMNAME:ARG
	NAME_LENG += RESULT
	
	;ポイズンセンティピードの11文字
	NAME_LENG -= 22
	;接頭語の4文字
	NAME_LENG -= 8
	;兵種の3文字
	NAME_LENG -= 6
	
	WHILE NAME_LENG
		SIF NAME_LENG > 0
			BREAK
		PRINT  
		NAME_LENG += 1
	WEND
ENDIF

RETURN 0



