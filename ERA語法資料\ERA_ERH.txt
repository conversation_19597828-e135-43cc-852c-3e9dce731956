﻿
广域变量的定义
==================================================

ERH中定义广域变量可以省略GLOABL关键字。

<*.ERH>
	#DIM MY_INT
	#DIM MY_INT_ARRAY, 100
	#DIMS MY_STR
	#DIMS MY_STR_ARRAY, 100

广域变量可以直接在任何ERB中使用。
	
<*.ERB>
	MY_INT = 100
	MY_INT_ARRAY:10 = MY_INT_ARRAY:10 + 45
	MY_STR = あああ
	PRINTFORML {MY_INT_ARRAY:10} %MY_STR%


宏的定义	
==================================================

这里说的宏不是Emuera运行中按下F1~F12按键的键盘宏。
宏的概念可以参考C和C++中的#define指令
ERH中定义的宏可以在所有ERB中使用。


基本使用
--------------------------------------------------
<*.ERH>
	#DEFINE <置換元識別子> <置換先式>

例如：
<*.ERH>
	#DEFINE FIVE 5
<*.ERB>
	X = FIVE
(展开后)
	X = 5
	
定义宏的行仍然可以使用注释
<*.ERH>
	#DEFINE FIVE 5 ;定义宏
<*.ERB>
	X = FIVE + FIVE
(展开后)
	X = 5 + 5

在定义表达式的时候需要注意运算顺序
<*.ERH>
	#DEFINE SIX           1 + 5
	#DEFINE NINE          8 + 1
<*.ERB>
	X = SIX * NINE
(展開後)
	X = 1 + 5 * 8 + 1
	
	
	
	
	
<*.ERH>
	#DEFINE HOGE        "ほげほげ"
	#DEFINE PIYO        A
	#DEFINE FUGA        DA:10
	#DEFINE HOGERA      LOCAL + MY_FUNC(X, Y)
<*.ERB>
	X = STRLEN(HOGE)
	Y = PIYO + 5
	FUGA:20 += PIYO
	LOCAL = HOGERA

	@MY_FUNC(ARG, ARG:1)
	#FUNCTION
		～略～
(展開後)
	X = STRLEN("ほげほげ")
	Y = A + 5
	DA:10:20 += A
	LOCAL = LOCAL + MY_FUNC(X, Y)

	@MY_FUNC(ARG, ARG:1)
	#FUNCTION
		～略～
		
		
定义宏的时候注意不要影响脚本可读性
<*.ERH>
	#DEFINE PLUS       +
	#DEFINE FIVEPLUS   5 +
<*.ERB>
	X = 1 PLUS 2
	Y = FIVEPLUS 2
(展開後)
	X = 1 + 2
	Y = 5 + 2
	
	
宏的多重展开
--------------------------------------------------

<.ERH>
	#DEFINE FIVE_1 5
	#DEFINE FIVE_2 FIVE_1 + FIVE_1
	#DEFINE FIVE_3 FIVE_2 + FIVE_2
<.ERB>
	X = FIVE_3
(展開後)
	X = 5 + 5 + 5 + 5
	
	
注意避免自我引用和循环引用

<.ERH>
	#DEFINE HOGE HOGE
	#DEFINE PIYO FUGA + 1
	#DEFINE FUGA PIYO + 2
<.ERB>
;エラーになる
	X = HOGE
	Y = PIYO
	

宏的判断
--------------------------------------------------

<*.ERB>
	[IF HOGE]
		PRINTL HOGE被定义
	[ELSEIF PUYO]
		PRINTL HOGE没有被定义
		PRINTL PUYO被定义
	[ELSE]
		PRINTL HOGE和PUYO都没有被定义
	[ENDIF]
	
为了这种目的宏可以定义为空内容
<*.ERH>
	#DEFINE HOGE


宏的限制
--------------------------------------------------

不能在PRINT中展开。
<*.ERH>
	#DEFINE FIVE 5
<*.ERB>
	PRINT FIVE

PRINT结果为文本"FIVE"。


不能包含赋值运算符
<*.ERH>
;会发生错误
	#DEFINE HOGE =
	#DEFINE PUGE X = 1

不能包含语法错误，括号等必须对应
<*.ERH>
;会发生错误
	#DEFINE HOGE ( X +
	#DEFINE PUGE Y )
<*.ERB>
	Z = HOGE PUGE

	
不能置换指令和其他关键字。
<*.ERH>
	#DEFINE MY_PRINTL     PRINTL
<*.ERB>
	MY_PRINTL 执行PRINTL中
(展開後)
	;会发生错误

	
	