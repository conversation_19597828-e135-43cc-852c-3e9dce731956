﻿eramaou_不具合修正パッチ_20140919

※(eramaou　ver.0.304+eramaou_v0.304_パッチまとめ20140907の適用を推奨)

加筆・改変・再アップロード・まとめ収録はご自由にどうぞ。

■使い方
　eramaou_v0.304_パッチまとめ20140907.zipを適用してから
　全てのERBファイルをERBフォルダに上書きコピーしてください。

■仕様
・CHARA_MARRIAGE.ERBの修正
・INFRASTRUCTURE.ERBの修正
・LOOK.ERBの修正
・侵略完了時の奴隷の性格と髪色が固定出来るように

■追加・修正箇所
　■CHARA_MARRIAGE.ERB
　掲示板で指摘されていた死に分岐を修正

　ABL:A:39 >= 1
　PRINTFORMW %SAVESTR:A%
　等のケアレスミスを以下のように修正
　ABL:ARG:39 >= 1
　PRINTFORMW %SAVESTR:ARG%　
　
　■INFRASTRUCTURE.ERB
　掲示板で指摘されていた部分を修正
　161行目を以下に修正
　ELSEIF FLAG:609 >= 1

　■LOOK.ERB
　掲示板で指摘されていた部分を修正
　273行目に以下を追加
　TALENT:273 = 0

　■ENDING.ERB
　68行目～87行目、232～247行目、396～411行目まで書き換え
　LOCAL:160とLOCAL:300の保存数によって性格と髪色が決定
　
　138～164行目、302～328行目、466～492行目
　LOCAL:160に性格のフラグを保存
　
　171～174行目、335～338行目、499～502行目
　LOCAL:300に髪色のフラグを保存



